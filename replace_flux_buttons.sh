#!/bin/bash

# Replace primary buttons
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:button variant="primary" type="submit" class="w-full">\(.*\)<\/flux:button>/<button type="submit" class="w-full text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">\1<\/button>/g' {} \;

# Replace danger buttons
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:button variant="danger" type="submit">\(.*\)<\/flux:button>/<button type="submit" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 focus:outline-none dark:focus:ring-red-800">\1<\/button>/g' {} \;

# Replace secondary buttons
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:button variant="secondary" size="sm" wire:click="\([^"]*\)">\(.*\)<\/flux:button>/<button type="button" wire:click="\1" class="py-2 px-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">\2<\/button>/g' {} \;

# Replace filled buttons
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:button variant="filled">\(.*\)<\/flux:button>/<button type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">\1<\/button>/g' {} \;

# Replace links
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:link class="text-sm cursor-pointer" wire:click="\([^"]*\)">\(.*\)<\/flux:link>/<a wire:click="\1" class="text-sm cursor-pointer text-primary-600 hover:underline dark:text-primary-500">\2<\/a>/g' {} \;
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:link class="text-sm cursor-pointer" wire:click.prevent="\([^"]*\)">\(.*\)<\/flux:link>/<a wire:click.prevent="\1" class="text-sm cursor-pointer text-primary-600 hover:underline dark:text-primary-500">\2<\/a>/g' {} \;
