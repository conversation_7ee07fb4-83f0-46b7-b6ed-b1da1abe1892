<?php

// This is a temporary script to fix database issues
// IMPORTANT: Delete this file after you've fixed the database!

require __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(\Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

try {
    echo "<h1>Database Fix Script</h1>";
    
    // Check if roles table exists
    if (!Schema::hasTable('roles')) {
        echo "<p>Creating roles table...</p>";
        
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->timestamps();
        });
        
        // Insert default roles
        DB::table('roles')->insert([
            ['id' => 1, 'name' => 'Admin', 'description' => 'Administrator', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 2, 'name' => 'Director', 'description' => 'Director', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 3, 'name' => 'Manager', 'description' => 'Manager', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 4, 'name' => 'Supervisor', 'description' => 'Supervisor', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 5, 'name' => 'Quality Control', 'description' => 'Quality Control', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 6, 'name' => 'Team Leader', 'description' => 'Team Leader', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 7, 'name' => 'Agent', 'description' => 'Agent', 'created_at' => now(), 'updated_at' => now()],
        ]);
        
        echo "<p>Roles table created and populated with default roles.</p>";
    } else {
        echo "<p>Roles table already exists.</p>";
    }
    
    // Check if departments table exists
    if (!Schema::hasTable('departments')) {
        echo "<p>Creating departments table...</p>";
        
        Schema::create('departments', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->timestamps();
        });
        
        // Insert default departments
        DB::table('departments')->insert([
            ['id' => 1, 'name' => 'Administration', 'description' => 'Administration Department', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 2, 'name' => 'Human Resources', 'description' => 'HR Department', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 3, 'name' => 'IT', 'description' => 'IT Department', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 4, 'name' => 'Operations', 'description' => 'Operations Department', 'created_at' => now(), 'updated_at' => now()],
            ['id' => 5, 'name' => 'Sales', 'description' => 'Sales Department', 'created_at' => now(), 'updated_at' => now()],
        ]);
        
        echo "<p>Departments table created and populated with default departments.</p>";
    } else {
        echo "<p>Departments table already exists.</p>";
    }
    
    // Check if campaigns table exists
    if (!Schema::hasTable('campaigns')) {
        echo "<p>Creating campaigns table...</p>";
        
        Schema::create('campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->timestamps();
        });
        
        // Insert default campaign
        DB::table('campaigns')->insert([
            ['id' => 1, 'name' => 'Default Campaign', 'description' => 'Default Campaign', 'created_at' => now(), 'updated_at' => now()],
        ]);
        
        echo "<p>Campaigns table created and populated with default campaign.</p>";
    } else {
        echo "<p>Campaigns table already exists.</p>";
    }
    
    echo "<p>Database fix completed successfully!</p>";
    echo "<p><a href='/direct_login.php'>Click here to log in with the emergency admin</a></p>";
    
} catch (\Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " (Line: " . $e->getLine() . ")</p>";
}
