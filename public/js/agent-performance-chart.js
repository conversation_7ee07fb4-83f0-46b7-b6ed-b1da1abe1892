/**
 * Agent Performance Chart Initialization
 *
 * This script initializes the performance chart for the agent performance page.
 * It uses ApexCharts to create a line chart showing appointment trends.
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePerformanceChart();

    // Listen for Livewire updates
    document.addEventListener('livewire:load', function() {
        Livewire.hook('message.processed', (message, component) => {
            if (component.fingerprint.name === 'agents.agent-performance') {
                initializePerformanceChart();
            }
        });
    });
});

/**
 * Initialize the performance chart
 */
function initializePerformanceChart() {
    const chartElement = document.getElementById('performance-chart');
    if (!chartElement) return;

    // Get theme colors
    const isDark = document.documentElement.classList.contains('dark');
    const textColor = isDark ? '#9ca3af' : '#6b7280';
    const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

    // Get chart data from Livewire component
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
    } catch (error) {
        console.error('Error parsing chart data:', error);
        chartData = { labels: [], series: [] };
    }

    // If no data is available, show a message
    if (!chartData.labels || !chartData.series || chartData.labels.length === 0) {
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500">No data available for the selected period</p></div>';
        return;
    }

    // Define chart colors for different metrics
    const colors = {
        appointments: ['#3b82f6', '#10b981'], // Blue, Green
        calls: ['#6366f1'], // Indigo
        quality: ['#f59e0b'], // Yellow
        adherence: ['#8b5cf6'] // Purple
    };

    // Process series data to add types and colors
    const processedSeries = chartData.series.map((series, index) => {
        // Set default type to line if not specified
        if (!series.type) {
            // First two series are appointment data (columns)
            if (index < 2) {
                series.type = 'column';
            } else {
                series.type = 'line';
            }
        }

        // Assign colors based on series name
        if (series.name.includes('Appointment')) {
            series.color = colors.appointments[index % colors.appointments.length];
        } else if (series.name.includes('Call')) {
            series.color = colors.calls[0];
        } else if (series.name.includes('Quality') || series.name.includes('CSAT') || series.name.includes('FCR')) {
            series.color = colors.quality[0];
        } else if (series.name.includes('Adherence') || series.name.includes('Compliance')) {
            series.color = colors.adherence[0];
        }

        return series;
    });

    // Chart options
    const options = {
        chart: {
            height: 350,
            type: 'line',
            stacked: false,
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: true,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            fontFamily: 'Inter, sans-serif',
            background: 'transparent'
        },
        plotOptions: {
            bar: {
                columnWidth: '60%',
                borderRadius: 3
            }
        },
        stroke: {
            width: [0, 0, 3, 3, 3], // First two series are columns (0 width), rest are lines
            curve: 'smooth'
        },
        series: processedSeries,
        xaxis: {
            categories: chartData.labels || [],
            labels: {
                style: {
                    colors: textColor,
                    fontSize: '12px',
                    fontFamily: 'Inter, sans-serif',
                }
            },
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: [
            {
                // Appointments axis (left)
                title: {
                    text: 'Appointments',
                    style: {
                        color: textColor
                    }
                },
                labels: {
                    style: {
                        colors: textColor,
                        fontSize: '12px',
                        fontFamily: 'Inter, sans-serif',
                    }
                }
            },
            {
                // Call Volume axis (right)
                opposite: true,
                title: {
                    text: 'Call Volume',
                    style: {
                        color: colors.calls[0]
                    }
                },
                labels: {
                    style: {
                        colors: colors.calls[0],
                        fontSize: '12px',
                        fontFamily: 'Inter, sans-serif',
                    }
                },
                seriesName: 'Call Volume'
            },
            {
                // Quality Score axis (right)
                opposite: true,
                title: {
                    text: 'Quality Score',
                    style: {
                        color: colors.quality[0]
                    }
                },
                labels: {
                    style: {
                        colors: colors.quality[0],
                        fontSize: '12px',
                        fontFamily: 'Inter, sans-serif',
                    },
                    formatter: function(val) {
                        return val.toFixed(1);
                    }
                },
                seriesName: 'Quality Score',
                min: 0,
                max: 100,
                show: false
            },
            {
                // Adherence Rate axis (right)
                opposite: true,
                title: {
                    text: 'Adherence Rate',
                    style: {
                        color: colors.adherence[0]
                    }
                },
                labels: {
                    style: {
                        colors: colors.adherence[0],
                        fontSize: '12px',
                        fontFamily: 'Inter, sans-serif',
                    },
                    formatter: function(val) {
                        return val.toFixed(1) + '%';
                    }
                },
                seriesName: 'Adherence Rate',
                min: 0,
                max: 100,
                show: false
            }
        ],
        grid: {
            show: true,
            borderColor: gridColor,
            strokeDashArray: 4,
            padding: {
                left: 10,
                right: 10
            }
        },
        dataLabels: {
            enabled: false
        },
        tooltip: {
            theme: isDark ? 'dark' : 'light',
            shared: true,
            intersect: false,
            y: {
                formatter: function(value, { seriesIndex, dataPointIndex, w }) {
                    const seriesName = w.config.series[seriesIndex].name;
                    if (seriesName.includes('Appointment')) {
                        return value + ' appointments';
                    } else if (seriesName.includes('Call Volume')) {
                        return value + ' calls';
                    } else if (seriesName.includes('Quality') || seriesName.includes('Adherence') || seriesName.includes('FCR')) {
                        return value + '%';
                    }
                    return value;
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            offsetY: -20,
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
            labels: {
                colors: textColor
            },
            markers: {
                width: 12,
                height: 12,
                strokeWidth: 0,
                radius: 12
            },
            itemMargin: {
                horizontal: 8,
                vertical: 8
            }
        },
        responsive: [{
            breakpoint: 640,
            options: {
                legend: {
                    position: 'bottom',
                    horizontalAlign: 'center',
                    offsetY: 0
                }
            }
        }]
    };

    // Create chart
    const chart = new ApexCharts(chartElement, options);
    chart.render();

    // Listen for dark mode changes
    document.addEventListener('dark-mode', function() {
        const isDarkMode = document.documentElement.classList.contains('dark');
        chart.updateOptions({
            tooltip: {
                theme: isDarkMode ? 'dark' : 'light'
            },
            xaxis: {
                labels: {
                    style: {
                        colors: isDarkMode ? '#9ca3af' : '#6b7280'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: isDarkMode ? '#9ca3af' : '#6b7280'
                    }
                }
            },
            grid: {
                borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
            },
            legend: {
                labels: {
                    colors: isDarkMode ? '#9ca3af' : '#6b7280'
                }
            }
        });
    });

    // Clean up on page change
    return function cleanup() {
        chart.destroy();
    };
}

// Update chart when Livewire updates the data
document.addEventListener('livewire:update', function() {
    initializePerformanceChart();
});
