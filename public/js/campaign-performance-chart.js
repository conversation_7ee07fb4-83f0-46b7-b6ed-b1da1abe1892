/**
 * Campaign Performance Chart Initialization
 * 
 * This script initializes the performance chart for the campaign statistics page.
 * It uses ApexCharts to create a line chart showing appointment trends.
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeCampaignPerformanceChart();
    
    // Listen for Livewire updates
    document.addEventListener('livewire:load', function() {
        Livewire.hook('message.processed', (message, component) => {
            if (component.fingerprint.name === 'campaigns.campaign-statistics') {
                initializeCampaignPerformanceChart();
            }
        });
    });
});

/**
 * Initialize the campaign performance chart
 */
function initializeCampaignPerformanceChart() {
    const chartElement = document.getElementById('campaign-performance-chart');
    if (!chartElement) return;
    
    // Get theme colors
    const isDark = document.documentElement.classList.contains('dark');
    const textColor = isDark ? '#9ca3af' : '#6b7280';
    const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
    
    // Get chart data from Livewire component
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
    } catch (error) {
        console.error('Error parsing chart data:', error);
        chartData = { labels: [], series: [] };
    }
    
    // If no data is available, show a message
    if (!chartData.labels || !chartData.series || chartData.labels.length === 0) {
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500">No data available for the selected period</p></div>';
        return;
    }
    
    // Chart options
    const options = {
        chart: {
            height: 350,
            type: 'line',
            toolbar: {
                show: false
            },
            fontFamily: 'Inter, sans-serif',
            background: 'transparent'
        },
        colors: ['#3b82f6', '#10b981', '#ef4444', '#f59e0b'],
        series: chartData.series || [],
        xaxis: {
            categories: chartData.labels || [],
            labels: {
                style: {
                    colors: textColor,
                    fontSize: '12px',
                    fontFamily: 'Inter, sans-serif',
                }
            },
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: textColor,
                    fontSize: '12px',
                    fontFamily: 'Inter, sans-serif',
                }
            }
        },
        grid: {
            show: true,
            borderColor: gridColor,
            strokeDashArray: 4,
            padding: {
                left: 10,
                right: 10
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        dataLabels: {
            enabled: false
        },
        tooltip: {
            theme: isDark ? 'dark' : 'light',
            y: {
                formatter: function(value) {
                    return value + ' appointments';
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            offsetY: -20,
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
            labels: {
                colors: textColor
            },
            markers: {
                width: 12,
                height: 12,
                strokeWidth: 0,
                radius: 12
            },
            itemMargin: {
                horizontal: 8,
                vertical: 8
            }
        },
        responsive: [{
            breakpoint: 640,
            options: {
                legend: {
                    position: 'bottom',
                    horizontalAlign: 'center',
                    offsetY: 0
                }
            }
        }]
    };
    
    // Create chart
    const chart = new ApexCharts(chartElement, options);
    chart.render();
    
    // Clean up on page change
    return function cleanup() {
        chart.destroy();
    };
}

// Update chart when Livewire updates the data
document.addEventListener('livewire:update', function() {
    initializeCampaignPerformanceChart();
});
