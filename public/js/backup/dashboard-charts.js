/**
 * Dashboard Charts
 * 
 * This file contains the code for initializing and managing all dashboard charts.
 * It's loaded dynamically by the dashboard page.
 */

// Wrap everything in an IIFE to avoid global scope pollution
(function() {
    // Create a namespace for our dashboard charts
    window.DashboardCharts = window.DashboardCharts || {};
    
    // Flag to track initialization
    let initialized = false;
    
    /**
     * Initialize all dashboard charts
     */
    window.initializeDashboardCharts = function() {
        if (initialized) {
            console.log('Dashboard charts already initialized, skipping...');
            return;
        }
        
        console.log('Initializing dashboard charts...');
        
        // Initialize each chart
        initializeAgentStatusChart();
        initializeAppointmentStatusChart();
        initializeDailyAppointmentChart();
        initializeDailyAgentActivityChart();
        
        // Set the initialized flag
        initialized = true;
        console.log('Dashboard charts initialization complete');
    };
    
    /**
     * Reinitialize all dashboard charts
     */
    window.reinitializeDashboardCharts = function() {
        console.log('Reinitializing dashboard charts...');
        
        // Reset the initialized flag
        initialized = false;
        
        // Clean up existing charts
        document.querySelectorAll('.apexcharts-canvas').forEach(element => {
            element.remove();
        });
        
        // Initialize the charts again
        window.initializeDashboardCharts();
    };
    
    /**
     * Initialize the Agent Status Chart
     */
    function initializeAgentStatusChart() {
        const element = document.getElementById('agent-status-chart');
        if (!element) {
            console.warn('Agent status chart element not found');
            return;
        }
        
        // Sample data - replace with actual data from your backend
        const options = {
            series: [44, 55, 13, 33],
            chart: {
                type: 'donut',
                height: 300
            },
            labels: ['Active', 'Inactive', 'On Leave', 'Training'],
            colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#9061F9'],
            legend: {
                position: 'bottom',
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif',
                itemMargin: {
                    horizontal: 10,
                    vertical: 5
                }
            },
            dataLabels: {
                enabled: false
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%'
                    }
                }
            }
        };
        
        // Create the chart
        const chart = new ApexCharts(element, options);
        chart.render();
        
        // Store the chart instance
        window.DashboardCharts.agentStatusChart = chart;
    }
    
    /**
     * Initialize the Appointment Status Chart
     */
    function initializeAppointmentStatusChart() {
        const element = document.getElementById('appointment-status-chart');
        if (!element) {
            console.warn('Appointment status chart element not found');
            return;
        }
        
        // Sample data - replace with actual data from your backend
        const options = {
            series: [35, 45, 20],
            chart: {
                type: 'donut',
                height: 300
            },
            labels: ['Scheduled', 'Completed', 'Cancelled'],
            colors: ['#1A56DB', '#16BDCA', '#DC2626'],
            legend: {
                position: 'bottom',
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif',
                itemMargin: {
                    horizontal: 10,
                    vertical: 5
                }
            },
            dataLabels: {
                enabled: false
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%'
                    }
                }
            }
        };
        
        // Create the chart
        const chart = new ApexCharts(element, options);
        chart.render();
        
        // Store the chart instance
        window.DashboardCharts.appointmentStatusChart = chart;
    }
    
    /**
     * Initialize the Daily Appointment Chart
     */
    function initializeDailyAppointmentChart() {
        const element = document.getElementById('daily-appointment-chart');
        if (!element) {
            console.warn('Daily appointment chart element not found');
            return;
        }
        
        // Sample data - replace with actual data from your backend
        const options = {
            series: [{
                name: 'Appointments',
                data: [31, 40, 28, 51, 42, 109, 100]
            }],
            chart: {
                height: 300,
                type: 'area',
                toolbar: {
                    show: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth'
            },
            xaxis: {
                type: 'datetime',
                categories: [
                    '2023-01-01', '2023-01-02', '2023-01-03', 
                    '2023-01-04', '2023-01-05', '2023-01-06', 
                    '2023-01-07'
                ]
            },
            tooltip: {
                x: {
                    format: 'dd/MM/yy'
                }
            },
            colors: ['#1A56DB']
        };
        
        // Create the chart
        const chart = new ApexCharts(element, options);
        chart.render();
        
        // Store the chart instance
        window.DashboardCharts.dailyAppointmentChart = chart;
    }
    
    /**
     * Initialize the Daily Agent Activity Chart
     */
    function initializeDailyAgentActivityChart() {
        const element = document.getElementById('daily-agent-activity-chart');
        if (!element) {
            console.warn('Daily agent activity chart element not found');
            return;
        }
        
        // Sample data - replace with actual data from your backend
        const options = {
            series: [{
                name: 'Calls Made',
                data: [44, 55, 57, 56, 61, 58, 63]
            }, {
                name: 'Appointments Set',
                data: [76, 85, 101, 98, 87, 105, 91]
            }],
            chart: {
                type: 'bar',
                height: 300,
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            },
            yaxis: {
                title: {
                    text: 'Count'
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val
                    }
                }
            },
            colors: ['#1A56DB', '#16BDCA']
        };
        
        // Create the chart
        const chart = new ApexCharts(element, options);
        chart.render();
        
        // Store the chart instance
        window.DashboardCharts.dailyAgentActivityChart = chart;
    }
})();
