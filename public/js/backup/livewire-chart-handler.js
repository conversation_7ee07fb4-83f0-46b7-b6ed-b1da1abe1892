/**
 * Livewire Chart Handler
 *
 * This script handles chart initialization and reinitialization during Livewire navigation.
 * It ensures charts are properly rendered when navigating between pages using wire:navigate.
 */

// Global registry for chart instances
window.chartInstances = window.chartInstances || {};

// Function to initialize all charts on the current page
function initializeAllChartsOnPage() {
    console.log('Initializing all charts on page after Livewire navigation');

    // Clear existing chart instances to prevent duplicates
    Object.keys(window.chartInstances || {}).forEach(chartId => {
        if (window.chartInstances[chartId]) {
            try {
                window.chartInstances[chartId].destroy();
            } catch (e) {
                console.warn(`Failed to destroy chart ${chartId}:`, e);
            }
            delete window.chartInstances[chartId];
        }
    });

    // Ensure event handlers are preserved after chart initialization
    setTimeout(function() {
        if (typeof initializeEventHandlers === 'function') {
            initializeEventHandlers();
        }
    }, 500);

    // Check if we're on the dashboard page
    const isDashboardPage = document.querySelector('.dashboard-page') ||
        document.getElementById('users-chart') ||
        document.getElementById('agents-chart') ||
        document.getElementById('campaigns-chart') ||
        document.getElementById('user-distribution-chart');

    if (isDashboardPage) {
        console.log('Dashboard page detected, skipping global chart initialization');
        return;
    }

    // Find all chart containers on the page (excluding dashboard)
    const chartContainers = document.querySelectorAll('[id$="-chart"]');

    if (chartContainers.length === 0) {
        console.log('No chart containers found on this page');
        return;
    }

    console.log(`Found ${chartContainers.length} chart containers`);

    // Initialize each chart
    chartContainers.forEach(container => {
        const chartId = container.id;

        // Skip if container is empty or not visible
        if (!container || container.offsetParent === null) {
            console.log(`Skipping chart ${chartId} - container not visible`);
            return;
        }

        console.log(`Initializing chart: ${chartId}`);

        // Check if this is a special chart with its own initialization function
        if (chartId === 'monthly-payments-chart' || chartId === 'invoice-status-chart') {
            // These charts are initialized in their own script blocks
            // We'll handle them separately
            console.log(`${chartId} has its own initialization, will be handled separately`);
            return;
        }

        // Get chart data from data attributes
        let chartData;
        try {
            chartData = JSON.parse(container.getAttribute('data-chart') || '{}');
        } catch (e) {
            console.error(`Failed to parse chart data for ${chartId}:`, e);
            chartData = {};
        }

        // Get chart type from data attribute or default to 'line'
        const chartType = container.getAttribute('data-type') || 'line';

        // Create chart options based on chart type
        const options = createChartOptions(chartId, chartType, chartData);

        // Create and render the chart
        try {
            const chart = new ApexCharts(container, options);
            chart.render();

            // Store the chart instance
            window.chartInstances[chartId] = chart;
            console.log(`Chart ${chartId} initialized successfully`);
        } catch (e) {
            console.error(`Failed to initialize chart ${chartId}:`, e);
        }
    });

    // Handle special charts with their own initialization
    initializeSpecialCharts();
}

// Function to create chart options based on chart type
function createChartOptions(chartId, chartType, chartData) {
    // Determine if dark mode is enabled
    const isDarkMode = document.documentElement.classList.contains('dark');

    // Set colors based on chart type and theme
    const colors = getChartColors(chartType);

    // Map chart types (convert doughnut to donut for ApexCharts)
    let apexChartType = chartType;
    if (chartType === 'doughnut') apexChartType = 'donut';

    // Basic chart options
    const options = {
        chart: {
            type: apexChartType,
            height: 350,
            toolbar: {
                show: false
            },
            background: 'transparent'
        },
        tooltip: {
            theme: isDarkMode ? 'dark' : 'light'
        },
        colors: colors,
        legend: {
            labels: {
                colors: isDarkMode ? '#9ca3af' : '#6b7280'
            }
        },
        stroke: {
            width: apexChartType === 'donut' || apexChartType === 'pie' ? 0 : 2
        }
    };

    // Add series data
    if (chartData.series) {
        options.series = chartData.series;
    } else if (chartData.datasets) {
        options.series = chartData.datasets.map(dataset => ({
            name: dataset.label || '',
            data: dataset.data || []
        }));
    }

    // Add categories/labels
    if (chartData.labels || chartData.categories) {
        options.xaxis = {
            categories: chartData.categories || chartData.labels || [],
            labels: {
                style: {
                    colors: isDarkMode ? '#9ca3af' : '#6b7280'
                }
            }
        };
    }

    return options;
}

// Function to get chart colors based on chart type
function getChartColors(chartType) {
    // Default colors
    const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

    // Return colors based on chart type
    switch (chartType) {
        case 'pie':
        case 'donut':
        case 'doughnut':
            return ['#10b981', '#f59e0b', '#ef4444', '#3b82f6', '#8b5cf6'];
        default:
            return defaultColors;
    }
}

// Function to initialize special charts that have their own initialization
function initializeSpecialCharts() {
    // Check for accountant dashboard charts
    const monthlyPaymentsChart = document.getElementById('monthly-payments-chart');
    const invoiceStatusChart = document.getElementById('invoice-status-chart');

    if (monthlyPaymentsChart && invoiceStatusChart) {
        console.log('Found accountant dashboard charts, triggering their initialization');

        // Dispatch a custom event to trigger the initialization
        document.dispatchEvent(new CustomEvent('initialize-accountant-charts'));
    }

    // We're now skipping dashboard charts completely
    // Dashboard charts are handled by the inline script in dashboard.blade.php
}

// Listen for Livewire navigation events
document.addEventListener('livewire:navigated', function() {
    console.log('Livewire navigation detected, waiting for DOM to update');

    // Wait for DOM to be fully updated
    setTimeout(function() {
        // Check if ApexCharts is loaded
        if (typeof ApexCharts === 'undefined') {
            console.error('ApexCharts is not loaded. Charts will not be initialized.');
            return;
        }

        // Initialize all charts
        initializeAllChartsOnPage();
    }, 300);
});

// Also initialize charts on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing charts');

    // Check if ApexCharts is loaded
    if (typeof ApexCharts === 'undefined') {
        console.error('ApexCharts is not loaded. Charts will not be initialized.');
        return;
    }

    // Initialize all charts
    initializeAllChartsOnPage();
});
