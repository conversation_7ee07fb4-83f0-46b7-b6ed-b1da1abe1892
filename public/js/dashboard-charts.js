/**
 * Dashboard Charts
 *
 * This script handles all chart functionality for the dashboard:
 * 1. Initializes all chart types (horizontal, pie, line, area)
 * 2. Ensures ApexCharts is properly loaded
 * 3. Handles Livewire navigation
 * 4. Prevents duplicate chart initialization
 * 5. Provides utility functions for charts
 */

// Create a namespace for our dashboard charts
window.DashboardCharts = window.DashboardCharts || {};

// Track initialization state
window.DashboardCharts.initialized = false;
window.DashboardCharts.initializationInProgress = false;
window.DashboardCharts.instances = {};
window.DashboardCharts.initAttempts = 0;
window.DashboardCharts.MAX_INIT_ATTEMPTS = 3;

/**
 * Check if ApexCharts is loaded
 */
window.DashboardCharts.isApexChartsLoaded = function() {
    return typeof ApexCharts !== 'undefined';
};

/**
 * Load ApexCharts if it's not already loaded
 */
window.DashboardCharts.loadApexCharts = function(callback) {
    if (window.DashboardCharts.isApexChartsLoaded()) {
        callback();
        return;
    }

    console.log('ApexCharts is not loaded! Attempting to load it now...');
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.js';
    script.onload = function() {
        console.log('ApexCharts loaded successfully');
        callback();
    };
    script.onerror = function() {
        console.error('Failed to load ApexCharts');
    };
    document.head.appendChild(script);
};

/**
 * Clean up all charts
 */
window.DashboardCharts.cleanupAllCharts = function() {
    console.log('Cleaning up all charts...');

    // Check if we're on the dashboard page
    const isDashboardPage = document.querySelector('.dashboard-page') ||
        document.getElementById('users-chart') ||
        document.getElementById('user-distribution-chart');

    if (!isDashboardPage) {
        console.log('Not on dashboard page, skipping chart cleanup');
        return;
    }

    // Destroy all chart instances
    Object.keys(window.DashboardCharts.instances).forEach(chartId => {
        try {
            console.log(`Destroying chart: ${chartId}`);
            window.DashboardCharts.instances[chartId].destroy();
        } catch (e) {
            console.error(`Error destroying chart ${chartId}:`, e);
        }
    });

    // Clear the instances registry
    window.DashboardCharts.instances = {};

    // Remove all ApexCharts canvases from the DOM
    document.querySelectorAll('.apexcharts-canvas').forEach(element => {
        console.log('Removing ApexCharts canvas element');
        element.remove();
    });

    console.log('Chart cleanup complete');
};

/**
 * Initialize all dashboard charts
 */
window.DashboardCharts.initializeAllCharts = function(isRetry = false) {
    // Don't initialize if already in progress
    if (window.DashboardCharts.initializationInProgress && !isRetry) {
        console.log('Chart initialization already in progress, skipping...');
        return;
    }

    console.log(`Initializing all dashboard charts (${isRetry ? 'retry attempt ' + window.DashboardCharts.initAttempts : 'first attempt'})`);
    window.DashboardCharts.initializationInProgress = true;

    // Clean up any existing charts first
    window.DashboardCharts.cleanupAllCharts();

    // Make sure ApexCharts is loaded before initializing
    window.DashboardCharts.loadApexCharts(function() {
        try {
            // Initialize all chart types
            window.DashboardCharts.initializeHorizontalCharts();
            window.DashboardCharts.initializePieCharts();
            window.DashboardCharts.initializeLineCharts();
            window.DashboardCharts.initializeAreaCharts();

            // Check if charts were successfully initialized
            setTimeout(function() {
                const chartElements = document.querySelectorAll('.apexcharts-canvas');
                console.log(`Found ${chartElements.length} chart canvases after initialization`);

                // Trigger a resize event to ensure charts are properly sized
                window.DashboardCharts.handleResize();

                if (chartElements.length < 5 && window.DashboardCharts.initAttempts < window.DashboardCharts.MAX_INIT_ATTEMPTS) {
                    // Not all charts were rendered, retry initialization
                    console.log(`Only ${chartElements.length} charts were rendered, retrying initialization (attempt ${window.DashboardCharts.initAttempts + 1}/${window.DashboardCharts.MAX_INIT_ATTEMPTS})`);
                    window.DashboardCharts.initAttempts++;

                    // Use progressively longer delays for retries
                    const retryDelay = isRetry ? 1000 : 800;
                    setTimeout(function() {
                        window.DashboardCharts.initializeAllCharts(true);
                    }, retryDelay);
                } else {
                    // Charts were successfully initialized or we've reached max attempts
                    console.log(`Charts initialization complete with ${chartElements.length} charts rendered`);
                    window.DashboardCharts.initAttempts = 0;
                    window.DashboardCharts.initialized = true;
                    window.DashboardCharts.initializationInProgress = false;
                }
            }, 500);
        } catch (error) {
            console.error('Error during chart initialization:', error);
            window.DashboardCharts.initAttempts = 0;
            window.DashboardCharts.initialized = false;
            window.DashboardCharts.initializationInProgress = false;
        }
    });
};

/**
 * Initialize horizontal charts
 */
window.DashboardCharts.initializeHorizontalCharts = function() {
    const chartIds = ['users-chart', 'agents-chart', 'campaigns-chart'];
    chartIds.forEach(chartId => window.DashboardCharts.initializeHorizontalChart(chartId));
};

/**
 * Initialize pie charts
 */
window.DashboardCharts.initializePieCharts = function() {
    console.log('Initializing pie charts...');
    const chartIds = [
        'user-distribution-chart',
        'agent-formation-chart',
        'campaign-status-chart',
        'appointment-status-chart'
    ];
    chartIds.forEach(chartId => window.DashboardCharts.initializePieChart(chartId));
};

/**
 * Initialize line charts
 */
window.DashboardCharts.initializeLineCharts = function() {
    const chartIds = [
        'agent-performance-chart',
        'call-center-kpi-chart',
        'campaign-performance-chart'
    ];
    chartIds.forEach(chartId => window.DashboardCharts.initializeLineChart(chartId));
};

/**
 * Initialize area charts
 */
window.DashboardCharts.initializeAreaCharts = function() {
    const chartIds = ['daily-appointments-chart', 'daily-agent-activity-chart'];
    chartIds.forEach(chartId => window.DashboardCharts.initializeAreaChart(chartId));
};

// Function to check if dark mode is active
window.isDarkMode = function() {
    return document.documentElement.classList.contains('dark');
};

// Function to get theme-specific colors
window.getThemeColors = function() {
    const darkMode = window.isDarkMode();
    return {
        textColor: darkMode ? '#ffffff' : '#1f2937',
        borderColor: darkMode ? '#374151' : '#e5e7eb',
        backgroundColor: darkMode ? '#1f2937' : '#ffffff',
        gridColor: darkMode ? '#374151' : '#e5e7eb',
        labelColor: darkMode ? '#9ca3af' : '#6b7280',
        legendColor: darkMode ? '#ffffff' : '#1f2937'
    };
};

// Function to safely parse JSON with error handling
window.safeJsonParse = function(jsonString, defaultValue = {}) {
    if (!jsonString) return defaultValue;
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.error('Error parsing JSON:', error);
        return defaultValue;
    }
};

// Debug function to log chart data
function logChartData() {
    console.log('User Distribution Chart Data:',
        document.getElementById('user-distribution-chart')?.getAttribute('data-labels'),
        document.getElementById('user-distribution-chart')?.getAttribute('data-values'));
    console.log('Agent Formation Chart Data:',
        document.getElementById('agent-formation-chart')?.getAttribute('data-labels'),
        document.getElementById('agent-formation-chart')?.getAttribute('data-values'));
    console.log('Campaign Status Chart Data:',
        document.getElementById('campaign-status-chart')?.getAttribute('data-labels'),
        document.getElementById('campaign-status-chart')?.getAttribute('data-values'));
    console.log('Appointment Status Chart Data:',
        document.getElementById('appointment-status-chart')?.getAttribute('data-labels'),
        document.getElementById('appointment-status-chart')?.getAttribute('data-values'));
}

// Function to detect and remove duplicate charts
function setupChartObserver() {
    // Check if we already have an observer
    if (window.chartObserver) {
        return;
    }

    console.log('Setting up chart observer to detect duplicates');

    // Create a mutation observer to watch for duplicate charts
    window.chartObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check for duplicate ApexCharts canvases
                const chartContainers = document.querySelectorAll('[id]');
                chartContainers.forEach(container => {
                    const canvases = container.querySelectorAll('.apexcharts-canvas');
                    if (canvases.length > 1) {
                        console.log(`Detected duplicate charts in container: ${container.id}`);
                        // Keep only the first canvas
                        for (let i = 1; i < canvases.length; i++) {
                            console.log(`Removing duplicate chart canvas ${i} from ${container.id}`);
                            canvases[i].remove();
                        }
                    }
                });
            }
        });
    });

    // Start observing the document body for changes
    window.chartObserver.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * Function to initialize a horizontal chart
 */
window.DashboardCharts.initializeHorizontalChart = function(chartId) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.warn(`Chart element not found: ${chartId}`);
        return;
    }

    console.log(`Initializing horizontal chart: ${chartId}`);

    // Get chart data from data-chart attribute
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
    } catch (e) {
        console.warn(`Failed to parse chart data for ${chartId}:`, e);
        // Use default data instead of returning
        chartData = {
            series: [{ name: chartId.replace('-chart', ''), data: [0, 0, 0, 0, 0, 0, 0] }],
            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        };
    }

    if (!chartData.series || !chartData.categories) {
        console.warn(`Invalid chart data for ${chartId}, using defaults`);
        chartData = {
            series: [{ name: chartId.replace('-chart', ''), data: [0, 0, 0, 0, 0, 0, 0] }],
            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        };
    }

    // Set color based on chart ID
    let mainColor = '#3b82f6'; // Default blue
    if (chartId === 'agents-chart') {
        mainColor = '#10b981'; // Green
    } else if (chartId === 'campaigns-chart') {
        mainColor = '#6366f1'; // Purple
    }

    // Create chart options
    const options = {
        series: chartData.series,
        chart: {
            type: 'area',
            height: 100,
            toolbar: {
                show: false
            },
            sparkline: {
                enabled: true
            },
            animations: {
                enabled: true,
                speed: 800
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'dark',
                type: 'vertical',
                shadeIntensity: 0.5,
                gradientToColors: [mainColor],
                inverseColors: false,
                opacityFrom: 0.8,
                opacityTo: 0.2,
                stops: [0, 100]
            }
        },
        colors: [mainColor],
        xaxis: {
            categories: chartData.categories,
            labels: {
                show: false
            },
            axisBorder: {
                show: false
            },
            axisTicks: {
                show: false
            }
        },
        yaxis: {
            show: false
        },
        tooltip: {
            theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
            x: {
                show: false
            }
        },
        grid: {
            show: false
        }
    };

    // Create and render chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();

        // Store chart instance
        window.DashboardCharts.instances[chartId] = chart;
        console.log(`Chart ${chartId} rendered successfully`);
    } catch (error) {
        console.error(`Error rendering chart ${chartId}:`, error);
        chartElement.innerHTML = '<div class="flex items-center justify-center h-24"><p class="text-gray-500">Error rendering chart</p></div>';
    }
};

/**
 * Function to initialize a line chart
 */
window.DashboardCharts.initializeLineChart = function(chartId) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.warn(`Chart element not found: ${chartId}`);
        return;
    }

    console.log(`Initializing line chart: ${chartId}`);

    // Get chart data from data-chart attribute
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
    } catch (e) {
        console.warn(`Failed to parse chart data for ${chartId}:`, e);
        // Use default data instead of returning
        chartData = {
            series: [{
                name: chartId.replace('-chart', ''),
                data: [0, 0, 0, 0, 0, 0, 0]
            }],
            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        };
    }

    if (!chartData.series || !chartData.categories) {
        console.warn(`Invalid chart data for ${chartId}, using defaults`);
        chartData = {
            series: [{
                name: chartId.replace('-chart', ''),
                data: [0, 0, 0, 0, 0, 0, 0]
            }],
            categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        };
    }

    // Create chart options
    const options = {
        series: chartData.series,
        chart: {
            type: 'line',
            height: 350,
            toolbar: {
                show: false
            },
            animations: {
                enabled: true,
                speed: 800
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            categories: chartData.categories,
            labels: {
                style: {
                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                }
            }
        },
        tooltip: {
            theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
        },
        grid: {
            borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
            strokeDashArray: 4
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            labels: {
                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
            }
        }
    };

    // Create and render chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();

        // Store chart instance
        window.DashboardCharts.instances[chartId] = chart;
        console.log(`Chart ${chartId} rendered successfully`);
    } catch (error) {
        console.error(`Error rendering chart ${chartId}:`, error);
        chartElement.innerHTML = '<div class="flex items-center justify-center h-64"><p class="text-gray-500">Error rendering chart</p></div>';
    }
};

/**
 * Function to initialize an area chart
 */
window.DashboardCharts.initializeAreaChart = function(chartId) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.warn(`Chart element not found: ${chartId}`);
        return;
    }

    console.log(`Initializing area chart: ${chartId}`);

    // Get chart data from data-labels and data-values attributes
    let labels, values;
    try {
        labels = JSON.parse(chartElement.getAttribute('data-labels') || '[]');
        values = JSON.parse(chartElement.getAttribute('data-values') || '[]');
    } catch (e) {
        console.warn(`Failed to parse chart data for ${chartId}:`, e);
        // Use default data instead of returning
        labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        values = [0, 0, 0, 0, 0, 0, 0];
    }

    if (labels.length === 0 || values.length === 0) {
        console.warn(`Invalid chart data for ${chartId}, using defaults`);
        labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        values = [0, 0, 0, 0, 0, 0, 0];
    }

    // Create chart options
    const options = {
        series: [{
            name: chartId === 'daily-appointments-chart' ? 'Appointments' : 'Agent Activity',
            data: values
        }],
        chart: {
            type: 'area',
            height: '100%',
            width: '100%',
            toolbar: {
                show: false
            },
            animations: {
                enabled: true,
                speed: 800
            },
            parentHeightOffset: 0,
            redrawOnWindowResize: true,
            redrawOnParentResize: true,
            sparkline: {
                enabled: false
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        fill: {
            type: 'gradient',
            gradient: {
                shade: 'dark',
                type: 'vertical',
                shadeIntensity: 0.5,
                opacityFrom: 0.7,
                opacityTo: 0.2
            }
        },
        colors: [chartId === 'daily-appointments-chart' ? '#3b82f6' : '#10b981'],
        xaxis: {
            categories: labels,
            labels: {
                style: {
                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                }
            }
        },
        tooltip: {
            theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
        },
        grid: {
            borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
            strokeDashArray: 4
        }
    };

    // Create and render chart
    try {
        // Clear any existing chart
        chartElement.innerHTML = '';

        // Create new chart
        const chart = new ApexCharts(chartElement, options);

        // Render the chart
        chart.render();

        // Force a resize after rendering to ensure proper fit
        setTimeout(() => {
            if (chart && typeof chart.updateOptions === 'function') {
                chart.updateOptions({
                    chart: {
                        width: '100%',
                        height: '100%'
                    }
                }, false, true);
            }
        }, 100);

        // Store chart instance
        window.DashboardCharts.instances[chartId] = chart;
        console.log(`Chart ${chartId} rendered successfully`);
    } catch (error) {
        console.error(`Error rendering chart ${chartId}:`, error);
        chartElement.innerHTML = '<div class="flex items-center justify-center h-64"><p class="text-gray-500">Error rendering chart</p></div>';
    }
};

/**
 * Function to initialize a pie chart
 */
window.DashboardCharts.initializePieChart = function(chartId) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.warn(`Chart element not found: ${chartId}`);
        return;
    }

    console.log(`Initializing pie chart: ${chartId}`);

    // Try multiple data extraction methods
    let labels = [];
    let values = [];

    // Method 1: Try to get data from data-values and data-labels attributes
    try {
        const valuesAttr = chartElement.getAttribute('data-values');
        const labelsAttr = chartElement.getAttribute('data-labels');

        if (valuesAttr && labelsAttr) {
            const parsedValues = JSON.parse(valuesAttr);
            const parsedLabels = JSON.parse(labelsAttr);

            if (Array.isArray(parsedValues) && parsedValues.length > 0 &&
                Array.isArray(parsedLabels) && parsedLabels.length > 0) {
                values = parsedValues;
                labels = parsedLabels;
                console.log(`Found data in data-values and data-labels attributes for ${chartId}`);
            }
        }
    } catch (e) {
        console.warn(`Error parsing data-values/data-labels for ${chartId}:`, e);
    }

    // Method 2: If method 1 failed, try to extract from span elements
    if (labels.length === 0 || values.length === 0) {
        try {
            const labelElements = chartElement.querySelectorAll('[data-label]');
            const valueElements = chartElement.querySelectorAll('[data-value]');

            if (labelElements.length > 0 && valueElements.length > 0) {
                labels = Array.from(labelElements).map(el => el.getAttribute('data-label'));
                values = Array.from(valueElements).map(el => parseInt(el.getAttribute('data-value')));
                console.log(`Found data in span elements for ${chartId}`);
            }
        } catch (e) {
            console.warn(`Error extracting data from spans for ${chartId}:`, e);
        }
    }

    // If we still don't have data, show an error message
    if (labels.length === 0 || values.length === 0) {
        console.warn(`No data found for chart: ${chartId}`);
        chartElement.innerHTML = '<div class="flex items-center justify-center h-64"><p class="text-gray-500">No data available</p></div>';
        return;
    }

    // Create chart options
    const options = {
        series: values,
        chart: {
            type: 'donut',
            height: 320,
            toolbar: {
                show: false
            },
            animations: {
                enabled: true,
                speed: 800
            }
        },
        labels: labels,
        colors: chartId === 'agent-formation-chart'
            ? ['#f97316', '#3b82f6', '#10b981', '#8b5cf6', '#6366f1'] // Orange, Blue, Green, Purple, Indigo for formation levels
            : ['#3b82f6', '#10b981', '#6366f1', '#f59e0b', '#ef4444', '#8b5cf6'],
        legend: {
            position: 'bottom',
            horizontalAlign: 'center',
            fontSize: '14px',
            labels: {
                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
            }
        },
        stroke: {
            width: 0
        },
        dataLabels: {
            enabled: true,
            formatter: function(val, opts) {
                return opts.w.globals.series[opts.seriesIndex];
            }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '50%',
                    labels: {
                        show: true,
                        name: {
                            show: true,
                            fontSize: '14px'
                        },
                        value: {
                            show: true,
                            fontSize: '16px',
                            formatter: function(val) {
                                return val;
                            }
                        },
                        total: {
                            show: true,
                            label: 'Total',
                            formatter: function(w) {
                                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                            }
                        }
                    }
                }
            }
        }
    };

    // Create and render chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();

        // Store chart instance
        window.DashboardCharts.instances[chartId] = chart;
        console.log(`Chart ${chartId} rendered successfully`);
    } catch (error) {
        console.error(`Error rendering chart ${chartId}:`, error);
        chartElement.innerHTML = '<div class="flex items-center justify-center h-64"><p class="text-gray-500">Error rendering chart</p></div>';
    }
};

// Helper function to check if we're on the dashboard page
function isDashboardPage() {
    return document.querySelector('.dashboard-page') ||
           document.getElementById('users-chart') ||
           document.getElementById('user-distribution-chart');
}

// Helper function to load ApexCharts and initialize charts
function loadApexChartsAndInitialize() {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.js';
    script.onload = function() {
        console.log('ApexCharts loaded successfully after navigation');
        setTimeout(function() {
            window.DashboardCharts.initializeAllCharts();
        }, 200);
    };
    script.onerror = function() {
        console.error('Failed to load ApexCharts after navigation');
    };
    document.head.appendChild(script);
}

// Function to handle window resize events
window.DashboardCharts.handleResize = function() {
    // Only proceed if we have chart instances
    if (!window.DashboardCharts.instances) return;

    // Get area chart instances
    const areaChartIds = ['daily-appointments-chart', 'daily-agent-activity-chart'];

    // Redraw each area chart
    areaChartIds.forEach(chartId => {
        const chart = window.DashboardCharts.instances[chartId];
        if (chart && typeof chart.updateOptions === 'function') {
            console.log(`Resizing chart: ${chartId}`);

            // First update the options to ensure proper sizing
            chart.updateOptions({
                chart: {
                    width: '100%',
                    height: '100%'
                }
            }, false, true);

            // Then force a redraw
            setTimeout(() => {
                try {
                    // Get the chart element
                    const chartElement = document.getElementById(chartId);
                    if (chartElement) {
                        // Get the parent container dimensions
                        const parentWidth = chartElement.parentElement.clientWidth;
                        const parentHeight = chartElement.parentElement.clientHeight;

                        console.log(`Parent dimensions for ${chartId}: ${parentWidth}x${parentHeight}`);

                        // Update chart dimensions
                        chart.updateOptions({
                            chart: {
                                width: parentWidth,
                                height: parentHeight
                            }
                        }, true, true);
                    }
                } catch (error) {
                    console.error(`Error resizing chart ${chartId}:`, error);
                }
            }, 50);
        }
    });
};

// Set up event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard charts: DOMContentLoaded event');

    // Set up chart observer
    setupChartObserver();

    // Log chart data on page load
    setTimeout(logChartData, 1000);

    // Initialize charts when the page is fully loaded
    window.addEventListener('load', function() {
        console.log('Window load event - initializing charts');

        // Check if we're on the dashboard page
        if (isDashboardPage()) {
            window.DashboardCharts.initializeAllCharts();
        }
    });

    // Add resize event listener
    window.addEventListener('resize', function() {
        if (isDashboardPage()) {
            window.DashboardCharts.handleResize();
        }
    });

    // Handle Livewire navigation - this is critical for SPA navigation
    document.addEventListener('livewire:navigated', function() {
        console.log('Livewire navigation detected');

        // Reset initialization flag when navigating
        window.DashboardCharts.initialized = false;
        window.DashboardCharts.initializationInProgress = false;

        // Check if we're on the dashboard page
        if (isDashboardPage()) {
            console.log('Dashboard page detected after navigation');

            // Make sure ApexCharts is loaded
            if (typeof ApexCharts === 'undefined') {
                console.log('ApexCharts not loaded after navigation, loading it now...');
                loadApexChartsAndInitialize();
            } else {
                // Use a delay to ensure DOM is fully updated
                setTimeout(function() {
                    window.DashboardCharts.initializeAllCharts();
                }, 300);
            }
        }
    });

    // Listen for the dashboard-charts-ready event from the Livewire component
    document.addEventListener('dashboard-charts-ready', function() {
        console.log('Dashboard charts ready event received');

        // Reset initialization flag when data is refreshed
        window.DashboardCharts.initialized = false;
        window.DashboardCharts.initializationInProgress = false;

        // Check if we're on the dashboard page
        if (isDashboardPage()) {
            setTimeout(function() {
                window.DashboardCharts.initializeAllCharts();
            }, 200);
        }
    });
});