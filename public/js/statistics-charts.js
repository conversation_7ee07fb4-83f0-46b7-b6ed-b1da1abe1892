/**
 * Statistics Module Charts
 * 
 * This script handles the initialization and rendering of all charts
 * in the statistics module. It provides consistent chart styling and
 * behavior across all statistics pages.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Statistics charts initialization script loaded');
    initializeStatisticsCharts();

    // Listen for Livewire updates to reinitialize charts when data changes
    document.addEventListener('livewire:load', function() {
        Livewire.hook('message.processed', (message, component) => {
            if (component.fingerprint.name && component.fingerprint.name.startsWith('statistics.')) {
                console.log('Livewire update detected for statistics component, reinitializing charts');
                initializeStatisticsCharts();
            }
        });
    });
});

/**
 * Initialize all charts in the statistics module
 */
function initializeStatisticsCharts() {
    // Check if we're on a statistics page
    const isStatisticsPage = window.location.pathname.includes('/statistics');
    if (!isStatisticsPage) {
        return;
    }

    console.log('Initializing statistics charts');

    // Initialize charts based on the current page
    if (window.location.pathname.includes('/statistics/general')) {
        initializeGeneralStatisticsCharts();
    } else if (window.location.pathname.includes('/statistics/day')) {
        initializeDayStatisticsCharts();
    } else if (window.location.pathname.includes('/statistics/kpi')) {
        initializeKpiCharts();
    } else if (window.location.pathname.includes('/statistics/performance')) {
        initializePerformanceCharts();
    }
}

/**
 * Initialize charts for the general statistics page
 */
function initializeGeneralStatisticsCharts() {
    console.log('Initializing general statistics charts');

    // Users by Role Chart
    initializePieChart('users-by-role-chart', 'Users by Role');

    // Users by Status Chart
    initializePieChart('users-by-status-chart', 'Users by Status');

    // Campaigns by Status Chart
    initializePieChart('campaigns-by-status-chart', 'Campaigns by Status');

    // Users by Month Chart
    initializeLineChart('users-by-month-chart', 'User Registration Trend');
}

/**
 * Initialize charts for the day statistics page
 */
function initializeDayStatisticsCharts() {
    console.log('Initializing day statistics charts');

    // Daily Agent Activity Chart
    initializeBarChart('daily-agent-activity-chart', 'Agent Activity');

    // Daily Appointments Chart
    initializeLineChart('daily-appointments-chart', 'Appointments');

    // Daily Conversion Rate Chart
    initializeLineChart('daily-conversion-chart', 'Conversion Rate');
}

/**
 * Initialize charts for the KPI page
 */
function initializeKpiCharts() {
    console.log('Initializing KPI charts');

    // Campaign Success Rate Chart
    initializeDonutChart('campaign-success-chart', 'Campaign Success Rate');

    // Agent Performance Chart
    initializeRadarChart('agent-performance-chart', 'Agent Performance');

    // KPI Trends Chart
    initializeLineChart('kpi-trends-chart', 'KPI Trends');
}

/**
 * Initialize charts for the performance monitoring page
 */
function initializePerformanceCharts() {
    console.log('Initializing performance charts');

    // Agent Performance Chart
    initializeBarChart('agent-performance-bar-chart', 'Agent Performance');

    // Campaign Performance Chart
    initializeLineChart('campaign-performance-line-chart', 'Campaign Performance');

    // Performance Comparison Chart
    initializeRadarChart('performance-comparison-chart', 'Performance Comparison');
}

/**
 * Initialize a pie chart with the given ID and title
 */
function initializePieChart(chartId, chartTitle) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.log(`Chart element with ID "${chartId}" not found`);
        return;
    }

    // Get chart data from the element's data attributes
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '[]');
    } catch (error) {
        console.error(`Error parsing chart data for ${chartId}:`, error);
        chartData = [];
    }

    // Extract labels and series from chart data
    const labels = chartData.map(item => item.label || item.role || item.status || 'Unknown');
    const series = chartData.map(item => item.count || 0);

    // Create chart options
    const options = {
        series: series,
        chart: {
            type: 'pie',
            height: 320,
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: false
            }
        },
        labels: labels,
        legend: {
            position: 'bottom',
            fontFamily: 'Inter, sans-serif',
            labels: {
                colors: isDarkMode() ? '#9ca3af' : '#4b5563'
            }
        },
        dataLabels: {
            enabled: true,
            style: {
                fontFamily: 'Inter, sans-serif'
            }
        },
        stroke: {
            width: 1,
            colors: [isDarkMode() ? '#1f2937' : '#ffffff']
        },
        tooltip: {
            theme: isDarkMode() ? 'dark' : 'light'
        }
    };

    // Create and render the chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();
        console.log(`${chartTitle} chart rendered successfully`);
    } catch (error) {
        console.error(`Error creating ${chartTitle} chart:`, error);
    }
}

/**
 * Initialize a donut chart with the given ID and title
 */
function initializeDonutChart(chartId, chartTitle) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.log(`Chart element with ID "${chartId}" not found`);
        return;
    }

    // Get chart data from the element's data attributes
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '[]');
    } catch (error) {
        console.error(`Error parsing chart data for ${chartId}:`, error);
        chartData = [];
    }

    // Extract labels and series from chart data
    const labels = chartData.map(item => item.label || 'Unknown');
    const series = chartData.map(item => item.value || 0);

    // Create chart options
    const options = {
        series: series,
        chart: {
            type: 'donut',
            height: 320,
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: false
            }
        },
        labels: labels,
        legend: {
            position: 'bottom',
            fontFamily: 'Inter, sans-serif',
            labels: {
                colors: isDarkMode() ? '#9ca3af' : '#4b5563'
            }
        },
        dataLabels: {
            enabled: true,
            style: {
                fontFamily: 'Inter, sans-serif'
            }
        },
        stroke: {
            width: 1,
            colors: [isDarkMode() ? '#1f2937' : '#ffffff']
        },
        tooltip: {
            theme: isDarkMode() ? 'dark' : 'light'
        }
    };

    // Create and render the chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();
        console.log(`${chartTitle} chart rendered successfully`);
    } catch (error) {
        console.error(`Error creating ${chartTitle} chart:`, error);
    }
}

/**
 * Initialize a line chart with the given ID and title
 */
function initializeLineChart(chartId, chartTitle) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.log(`Chart element with ID "${chartId}" not found`);
        return;
    }

    // Get chart data from the element's data attributes
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
    } catch (error) {
        console.error(`Error parsing chart data for ${chartId}:`, error);
        chartData = { categories: [], series: [{ name: chartTitle, data: [] }] };
    }

    // Create chart options
    const options = {
        series: chartData.series || [{ name: chartTitle, data: [] }],
        chart: {
            type: 'line',
            height: 320,
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: false
            },
            animations: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            categories: chartData.categories || [],
            labels: {
                style: {
                    colors: isDarkMode() ? '#9ca3af' : '#4b5563',
                    fontFamily: 'Inter, sans-serif'
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: isDarkMode() ? '#9ca3af' : '#4b5563',
                    fontFamily: 'Inter, sans-serif'
                }
            }
        },
        tooltip: {
            theme: isDarkMode() ? 'dark' : 'light'
        },
        grid: {
            borderColor: isDarkMode() ? '#374151' : '#e5e7eb',
            strokeDashArray: 4
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            labels: {
                colors: isDarkMode() ? '#9ca3af' : '#4b5563'
            }
        }
    };

    // Create and render the chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();
        console.log(`${chartTitle} chart rendered successfully`);
    } catch (error) {
        console.error(`Error creating ${chartTitle} chart:`, error);
    }
}

/**
 * Initialize a bar chart with the given ID and title
 */
function initializeBarChart(chartId, chartTitle) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.log(`Chart element with ID "${chartId}" not found`);
        return;
    }

    // Get chart data from the element's data attributes
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
    } catch (error) {
        console.error(`Error parsing chart data for ${chartId}:`, error);
        chartData = { categories: [], series: [{ name: chartTitle, data: [] }] };
    }

    // Create chart options
    const options = {
        series: chartData.series || [{ name: chartTitle, data: [] }],
        chart: {
            type: 'bar',
            height: 320,
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: false
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                borderRadius: 4
            }
        },
        dataLabels: {
            enabled: false
        },
        xaxis: {
            categories: chartData.categories || [],
            labels: {
                style: {
                    colors: isDarkMode() ? '#9ca3af' : '#4b5563',
                    fontFamily: 'Inter, sans-serif'
                }
            }
        },
        yaxis: {
            labels: {
                style: {
                    colors: isDarkMode() ? '#9ca3af' : '#4b5563',
                    fontFamily: 'Inter, sans-serif'
                }
            }
        },
        tooltip: {
            theme: isDarkMode() ? 'dark' : 'light'
        },
        grid: {
            borderColor: isDarkMode() ? '#374151' : '#e5e7eb',
            strokeDashArray: 4
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            labels: {
                colors: isDarkMode() ? '#9ca3af' : '#4b5563'
            }
        }
    };

    // Create and render the chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();
        console.log(`${chartTitle} chart rendered successfully`);
    } catch (error) {
        console.error(`Error creating ${chartTitle} chart:`, error);
    }
}

/**
 * Initialize a radar chart with the given ID and title
 */
function initializeRadarChart(chartId, chartTitle) {
    const chartElement = document.getElementById(chartId);
    if (!chartElement) {
        console.log(`Chart element with ID "${chartId}" not found`);
        return;
    }

    // Get chart data from the element's data attributes
    let chartData;
    try {
        chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
    } catch (error) {
        console.error(`Error parsing chart data for ${chartId}:`, error);
        chartData = { categories: [], series: [{ name: chartTitle, data: [] }] };
    }

    // Create chart options
    const options = {
        series: chartData.series || [{ name: chartTitle, data: [] }],
        chart: {
            type: 'radar',
            height: 320,
            fontFamily: 'Inter, sans-serif',
            toolbar: {
                show: false
            }
        },
        xaxis: {
            categories: chartData.categories || [],
            labels: {
                style: {
                    colors: Array(10).fill(isDarkMode() ? '#9ca3af' : '#4b5563'),
                    fontFamily: 'Inter, sans-serif'
                }
            }
        },
        yaxis: {
            show: false
        },
        tooltip: {
            theme: isDarkMode() ? 'dark' : 'light'
        },
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            labels: {
                colors: isDarkMode() ? '#9ca3af' : '#4b5563'
            }
        },
        markers: {
            size: 4,
            colors: ['#3b82f6'],
            strokeColors: '#fff',
            strokeWidth: 2
        }
    };

    // Create and render the chart
    try {
        const chart = new ApexCharts(chartElement, options);
        chart.render();
        console.log(`${chartTitle} chart rendered successfully`);
    } catch (error) {
        console.error(`Error creating ${chartTitle} chart:`, error);
    }
}

/**
 * Check if dark mode is enabled
 */
function isDarkMode() {
    return document.documentElement.classList.contains('dark') || 
           localStorage.getItem('color-theme') === 'dark' ||
           (!('color-theme' in localStorage) && 
            window.matchMedia('(prefers-color-scheme: dark)').matches);
}
