/* Markdown Content Styling */
.prose {
    color: #1f2937; /* text-gray-900 */
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: #111827; /* text-gray-900 */
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
}

.prose p {
    margin-top: 1em;
    margin-bottom: 1em;
}

.prose ul, .prose ol {
    margin-top: 1em;
    margin-bottom: 1em;
    padding-left: 1.5em;
}

.prose li {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose a {
    color: #2563eb; /* text-blue-600 */
    text-decoration: underline;
}

.prose code {
    color: #111827; /* text-gray-900 */
    background-color: #f3f4f6; /* bg-gray-100 */
    padding: 0.2em 0.4em;
    border-radius: 0.25em;
    font-size: 0.875em;
}

.prose pre {
    color: #e5e7eb; /* text-gray-200 */
    background-color: #1f2937; /* bg-gray-800 */
    padding: 1em;
    border-radius: 0.375em;
    overflow-x: auto;
    margin-top: 1.5em;
    margin-bottom: 1.5em;
}

.prose pre code {
    color: inherit;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    font-size: inherit;
}

.prose blockquote {
    border-left-width: 4px;
    border-left-color: #e5e7eb; /* border-gray-200 */
    padding-left: 1em;
    font-style: italic;
    color: #4b5563; /* text-gray-600 */
}

.prose hr {
    border-color: #e5e7eb; /* border-gray-200 */
    margin-top: 2em;
    margin-bottom: 2em;
}

.prose table {
    width: 100%;
    table-layout: auto;
    text-align: left;
    margin-top: 2em;
    margin-bottom: 2em;
    border-collapse: collapse;
}

.prose thead {
    border-bottom-width: 1px;
    border-bottom-color: #e5e7eb; /* border-gray-200 */
}

.prose th {
    color: #111827; /* text-gray-900 */
    font-weight: 600;
    padding: 0.5em;
    vertical-align: bottom;
}

.prose td {
    padding: 0.5em;
    vertical-align: top;
}

.prose tbody tr {
    border-bottom-width: 1px;
    border-bottom-color: #e5e7eb; /* border-gray-200 */
}

/* Dark mode styles */
.dark .prose {
    color: #f3f4f6; /* dark:text-gray-100 */
}

.dark .prose h1, .dark .prose h2, .dark .prose h3, .dark .prose h4, .dark .prose h5, .dark .prose h6 {
    color: #f9fafb; /* dark:text-gray-50 */
}

.dark .prose a {
    color: #3b82f6; /* dark:text-blue-500 */
}

.dark .prose code {
    color: #f9fafb; /* dark:text-gray-50 */
    background-color: #374151; /* dark:bg-gray-700 */
}

.dark .prose pre {
    color: #f3f4f6; /* dark:text-gray-100 */
    background-color: #1f2937; /* dark:bg-gray-800 */
}

.dark .prose blockquote {
    border-left-color: #374151; /* dark:border-gray-700 */
    color: #9ca3af; /* dark:text-gray-400 */
}

.dark .prose hr {
    border-color: #374151; /* dark:border-gray-700 */
}

.dark .prose thead {
    border-bottom-color: #374151; /* dark:border-gray-700 */
}

.dark .prose th {
    color: #f9fafb; /* dark:text-gray-50 */
}

.dark .prose tbody tr {
    border-bottom-color: #374151; /* dark:border-gray-700 */
}
