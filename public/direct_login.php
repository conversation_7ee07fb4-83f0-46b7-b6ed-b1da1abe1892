<?php

// This is a temporary script to bypass normal authentication
// IMPORTANT: Delete this file after you've successfully logged in!

require __DIR__ . '/../vendor/autoload.php';

$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(\Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Support\Facades\DB;

// Create a new admin user without foreign key constraints
try {
    // Disable foreign key checks temporarily
    DB::statement('PRAGMA foreign_keys = OFF');

    // Check if admin exists
    $admin = User::where('email', '<EMAIL>')->first();

    if (!$admin) {
        // Create a new admin user
        $admin = new User();
        $admin->first_name = 'Emergency';
        $admin->last_name = 'Admin';
        $admin->email = '<EMAIL>';
        $admin->password = Hash::make('admin123');
        $admin->role_id = 1; // Admin role
        $admin->status = 'active';
        $admin->email_verified_at = now();
        $admin->registration_number = 'EMERGENCY-ADMIN';
        $admin->hire_date = now();
        $admin->save();

        echo "<p>Created new emergency admin user.</p>";
    } else {
        echo "<p>Emergency admin user already exists.</p>";
    }

    // Re-enable foreign key checks
    DB::statement('PRAGMA foreign_keys = ON');

    // Log the user in
    Auth::login($admin);

    // Check if login was successful
    if (Auth::check()) {
        echo "<p>Login successful! You are now logged in as: " . Auth::user()->email . "</p>";
        echo "<p>Redirecting to dashboard immediately...</p>";

        // Perform a server-side redirect instead of JavaScript
        header('Location: /dashboard');
        exit;
    } else {
        echo "<p>Login failed. Please check the logs for more information.</p>";
    }

} catch (\Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . " (Line: " . $e->getLine() . ")</p>";
}
