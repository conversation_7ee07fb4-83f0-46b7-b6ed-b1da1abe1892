<?php

// Simple file upload test script

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define upload directory
$uploadDir = __DIR__ . '/../storage/app/public/appointments/audio/';

// Create directory if it doesn't exist
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0775, true);
}

// Check if directory is writable
if (!is_writable($uploadDir)) {
    die("Error: Upload directory is not writable. Please check permissions.");
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['audio'])) {
    $file = $_FILES['audio'];
    
    // Check for errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the upload_max_filesize directive in php.ini',
            UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form',
            UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing a temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'A PHP extension stopped the file upload'
        ];
        
        $errorMessage = isset($errorMessages[$file['error']]) 
            ? $errorMessages[$file['error']] 
            : 'Unknown upload error';
        
        die("Upload Error: $errorMessage");
    }
    
    // Generate unique filename
    $fileName = 'test_' . time() . '_' . uniqid() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
    $targetPath = $uploadDir . $fileName;
    
    // Move the uploaded file
    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        echo "<div style='color: green; font-weight: bold;'>File uploaded successfully!</div>";
        echo "<div>File saved to: $targetPath</div>";
        echo "<div>File size: " . filesize($targetPath) . " bytes</div>";
        
        // Display audio player if it's an audio file
        $mimeType = mime_content_type($targetPath);
        if (strpos($mimeType, 'audio/') === 0) {
            echo "<div style='margin-top: 20px;'>";
            echo "<h3>Audio Preview:</h3>";
            echo "<audio controls>";
            echo "<source src='/storage/appointments/audio/$fileName' type='$mimeType'>";
            echo "Your browser does not support the audio element.";
            echo "</audio>";
            echo "</div>";
        }
    } else {
        echo "<div style='color: red; font-weight: bold;'>Failed to move uploaded file!</div>";
        echo "<div>From: {$file['tmp_name']}</div>";
        echo "<div>To: $targetPath</div>";
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Audio Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .btn {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Audio Upload Test</h1>
    
    <form action="" method="post" enctype="multipart/form-data">
        <div class="form-group">
            <label for="audio">Select Audio File:</label>
            <input type="file" name="audio" id="audio" accept=".mp3,.wav,.aac,.ogg,.flac,audio/*">
        </div>
        
        <button type="submit" class="btn">Upload File</button>
    </form>
    
    <div class="info">
        <h3>Server Information:</h3>
        <ul>
            <li>PHP Version: <?php echo phpversion(); ?></li>
            <li>Upload Max Filesize: <?php echo ini_get('upload_max_filesize'); ?></li>
            <li>Post Max Size: <?php echo ini_get('post_max_size'); ?></li>
            <li>Upload Directory: <?php echo $uploadDir; ?></li>
            <li>Directory Writable: <?php echo is_writable($uploadDir) ? 'Yes' : 'No'; ?></li>
        </ul>
    </div>
</body>
</html>
