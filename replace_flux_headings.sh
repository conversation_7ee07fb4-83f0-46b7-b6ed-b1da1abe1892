#!/bin/bash

# Replace flux:heading with h1, h2, h3 elements
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:heading size="xl" level="1">\(.*\)<\/flux:heading>/<h1 class="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">\1<\/h1>/g' {} \;
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:heading size="lg">\(.*\)<\/flux:heading>/<h2 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">\1<\/h2>/g' {} \;
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:heading>\(.*\)<\/flux:heading>/<h2 class="text-xl font-bold tracking-tight text-gray-900 dark:text-white">\1<\/h2>/g' {} \;

# Replace flux:subheading with p elements
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:subheading size="lg" class="mb-6">\(.*\)<\/flux:subheading>/<p class="mt-2 text-lg text-gray-500 dark:text-gray-400 mb-6">\1<\/p>/g' {} \;
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:subheading>\(.*\)<\/flux:subheading>/<p class="mt-2 text-base text-gray-500 dark:text-gray-400">\1<\/p>/g' {} \;
