<?php

namespace Tests\Feature;

use App\Models\Appointment;
use App\Models\User;
use App\Models\Campaign;
use App\Models\Customer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AppointmentAudioUploadTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a fake storage disk for testing
        Storage::fake('public');
    }

    /** @test */
    public function it_can_upload_an_audio_file_when_creating_an_appointment()
    {
        // Create a user
        $user = User::factory()->create([
            'role_id' => 1, // Admin role
        ]);
        
        // Create a campaign
        $campaign = Campaign::factory()->create();
        
        // Create a customer
        $customer = Customer::factory()->create();
        
        // Create a fake MP3 file
        $file = UploadedFile::fake()->create('audio.mp3', 100, 'audio/mpeg');
        
        // Login as the user
        $this->actingAs($user);
        
        // Make a POST request to create an appointment with the audio file
        $response = $this->post(route('appointments.store'), [
            'campaign_id' => $campaign->id,
            'customer_id' => $customer->id,
            'scheduled_at' => now()->addDay(),
            'agent_notes' => 'Test notes',
            'audio' => $file,
        ]);
        
        // Assert the appointment was created
        $this->assertDatabaseHas('appointments', [
            'campaign_id' => $campaign->id,
            'customer_id' => $customer->id,
            'user_id' => $user->id,
        ]);
        
        // Get the created appointment
        $appointment = Appointment::latest()->first();
        
        // Assert the audio file was stored
        $this->assertNotNull($appointment->audio_path);
        Storage::disk('public')->assertExists($appointment->audio_path);
    }

    /** @test */
    public function it_can_update_an_audio_file_for_an_existing_appointment()
    {
        // Create a user
        $user = User::factory()->create([
            'role_id' => 1, // Admin role
        ]);
        
        // Create a campaign
        $campaign = Campaign::factory()->create();
        
        // Create a customer
        $customer = Customer::factory()->create();
        
        // Create an appointment
        $appointment = Appointment::factory()->create([
            'user_id' => $user->id,
            'campaign_id' => $campaign->id,
            'customer_id' => $customer->id,
            'audio_path' => null,
        ]);
        
        // Create a fake MP3 file
        $file = UploadedFile::fake()->create('updated_audio.mp3', 100, 'audio/mpeg');
        
        // Login as the user
        $this->actingAs($user);
        
        // Make a PUT request to update the appointment with the audio file
        $response = $this->put(route('appointments.update', $appointment), [
            'campaign_id' => $campaign->id,
            'customer_id' => $customer->id,
            'scheduled_at' => now()->addDay(),
            'agent_notes' => 'Updated notes',
            'audio' => $file,
        ]);
        
        // Refresh the appointment from the database
        $appointment->refresh();
        
        // Assert the audio file was stored
        $this->assertNotNull($appointment->audio_path);
        Storage::disk('public')->assertExists($appointment->audio_path);
    }
}
