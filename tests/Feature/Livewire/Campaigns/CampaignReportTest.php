<?php

namespace Tests\Feature\Livewire\Campaigns;

use App\Livewire\Campaigns\CampaignReport;
use App\Models\Campaign;
use App\Models\Report;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Livewire\Livewire;
use Tests\TestCase;

class CampaignReportTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /** @test */
    public function the_component_can_render()
    {
        $user = User::factory()->create(['role_id' => 1]);
        $this->actingAs($user);

        $component = Livewire::test(CampaignReport::class);
        $component->assertStatus(200);
    }

    /** @test */
    public function it_can_filter_reports_by_campaign()
    {
        $user = User::factory()->create(['role_id' => 1]);
        $this->actingAs($user);

        $campaign1 = Campaign::factory()->create();
        $campaign2 = Campaign::factory()->create();

        $report1 = Report::factory()->create([
            'campaign_id' => $campaign1->id,
            'created_by' => $user->id,
        ]);

        $report2 = Report::factory()->create([
            'campaign_id' => $campaign2->id,
            'created_by' => $user->id,
        ]);

        $component = Livewire::test(CampaignReport::class);
        
        // Initially should see all reports
        $component->assertSeeText('Reports Summary');
        
        // Filter by campaign
        $component->set('selectedCampaignId', $campaign1->id);
        $component->call('render');
        
        // Should only see reports for campaign1
        $component->assertViewHas('reports', function ($reports) use ($campaign1) {
            return $reports->contains('campaign_id', $campaign1->id) && 
                   $reports->doesntContain('campaign_id', '!=', $campaign1->id);
        });
    }

    /** @test */
    public function it_can_filter_reports_by_date_range()
    {
        $user = User::factory()->create(['role_id' => 1]);
        $this->actingAs($user);

        $oldReport = Report::factory()->create([
            'created_by' => $user->id,
            'date' => now()->subMonths(2),
        ]);

        $newReport = Report::factory()->create([
            'created_by' => $user->id,
            'date' => now(),
        ]);

        $component = Livewire::test(CampaignReport::class);
        
        // Set date range to this month
        $component->set('dateRange', 'this_month');
        $component->call('setDateRange', 'this_month');
        $component->call('render');
        
        // Should only see reports from this month
        $component->assertViewHas('reports', function ($reports) use ($newReport, $oldReport) {
            return $reports->contains('id', $newReport->id) && 
                   $reports->doesntContain('id', $oldReport->id);
        });
    }

    /** @test */
    public function it_can_change_view_mode()
    {
        $user = User::factory()->create(['role_id' => 1]);
        $this->actingAs($user);

        $component = Livewire::test(CampaignReport::class);
        
        // Default view mode should be list
        $component->assertSet('viewMode', 'list');
        
        // Change to dashboard view
        $component->call('setViewMode', 'dashboard');
        $component->assertSet('viewMode', 'dashboard');
        
        // Change to grid view
        $component->call('setViewMode', 'grid');
        $component->assertSet('viewMode', 'grid');
    }

    /** @test */
    public function it_can_sort_reports()
    {
        $user = User::factory()->create(['role_id' => 1]);
        $this->actingAs($user);

        Report::factory()->count(3)->create([
            'created_by' => $user->id,
        ]);

        $component = Livewire::test(CampaignReport::class);
        
        // Default sort should be date desc
        $component->assertSet('sortField', 'date');
        $component->assertSet('sortDirection', 'desc');
        
        // Sort by title
        $component->call('sortBy', 'title');
        $component->assertSet('sortField', 'title');
        $component->assertSet('sortDirection', 'asc');
        
        // Sort by title again to toggle direction
        $component->call('sortBy', 'title');
        $component->assertSet('sortField', 'title');
        $component->assertSet('sortDirection', 'desc');
    }

    /** @test */
    public function it_can_reset_filters()
    {
        $user = User::factory()->create(['role_id' => 1]);
        $this->actingAs($user);

        $component = Livewire::test(CampaignReport::class);
        
        // Set some filters
        $component->set('reportType', 'daily');
        $component->set('reportCategory', 'performance');
        $component->set('reportStatus', 'approved');
        $component->set('reportPriority', 'high');
        $component->set('selectedTags', ['important', 'urgent']);
        $component->set('dateRange', 'custom');
        $component->set('startDate', '2023-01-01');
        $component->set('endDate', '2023-01-31');
        
        // Reset filters
        $component->call('resetFilters');
        
        // Check if filters are reset
        $component->assertSet('reportType', 'all');
        $component->assertSet('reportCategory', 'all');
        $component->assertSet('reportStatus', 'all');
        $component->assertSet('reportPriority', 'all');
        $component->assertSet('selectedTags', []);
        $component->assertSet('dateRange', 'this_month');
    }
}
