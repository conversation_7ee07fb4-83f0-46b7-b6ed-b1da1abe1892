<?php

// Get all PHP files in the app/Livewire directory
$files = glob('app/Livewire/*/*.php');

// Pattern to match the mount method with string type hint
$pattern = '/public\s+function\s+mount\s*\(\s*string\s+\$component\s*=\s*\'[^\']*\'\s*\)/';

// Replacement pattern without the string type hint
$replacement = 'public function mount($component = \'\')';

$fixed_files = [];

foreach ($files as $file) {
    // Skip the Page.php file itself
    if (strpos($file, 'Global/Page.php') !== false) {
        continue;
    }
    
    // Read the file content
    $content = file_get_contents($file);
    
    // Check if the file extends Page
    if (strpos($content, 'extends Page') !== false) {
        // Check if the file has the mount method with string type hint
        if (preg_match($pattern, $content)) {
            // Replace the mount method signature
            $new_content = preg_replace($pattern, $replacement, $content);
            
            // Write the updated content back to the file
            file_put_contents($file, $new_content);
            
            // Add the file to the list of fixed files
            $fixed_files[] = $file;
        }
    }
}

// Output the list of fixed files
echo "Fixed mount method in the following files:\n";
foreach ($fixed_files as $file) {
    echo "- $file\n";
}

if (empty($fixed_files)) {
    echo "No files needed fixing.\n";
}
