#!/bin/bash

# Replace flux:text with p elements
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:text class="text-center">\(.*\)<\/flux:text>/<p class="text-center text-gray-700 dark:text-gray-300">\1<\/p>/g' {} \;
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:text class="text-center font-medium !dark:text-green-400 !text-green-600">\(.*\)<\/flux:text>/<p class="text-center font-medium text-green-600 dark:text-green-400">\1<\/p>/g' {} \;
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:text class="mt-4">\(.*\)<\/flux:text>/<p class="mt-4 text-gray-700 dark:text-gray-300">\1<\/p>/g' {} \;
find resources/views -type f -name "*.blade.php" -exec sed -i 's/<flux:text class="mt-2 font-medium !dark:text-green-400 !text-green-600">\(.*\)<\/flux:text>/<p class="mt-2 font-medium text-green-600 dark:text-green-400">\1<\/p>/g' {} \;
