<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Equipment extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'type',
        'serial_number',
        'model',
        'manufacturer',
        'purchase_date',
        'warranty_expiry',
        'status',
        'platform_id',
        'notes',
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'warranty_expiry' => 'date',
    ];

    /**
     * Get the platform that owns the equipment.
     */
    public function platform()
    {
        return $this->belongsTo(Platform::class);
    }

    /**
     * Get the site through the platform.
     */
    public function site()
    {
        return $this->hasOneThrough(
            Site::class,
            Platform::class,
            'id', // Foreign key on platforms table
            'id', // Foreign key on sites table
            'platform_id', // Local key on equipment table
            'site_id' // Local key on platforms table
        );
    }
}
