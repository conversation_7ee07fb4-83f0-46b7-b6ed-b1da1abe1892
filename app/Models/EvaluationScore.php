<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationScore extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'evaluation_scores';

    protected $fillable = [
        'call_evaluation_id',
        'evaluation_criteria_id',
        'score',
        'comment',
    ];

    protected $casts = [
        'score' => 'float',
    ];

    /**
     * Get the evaluation this score belongs to.
     */
    public function evaluation()
    {
        return $this->belongsTo(CallEvaluation::class, 'call_evaluation_id');
    }

    /**
     * Get the criteria being scored.
     */
    public function criteria()
    {
        return $this->belongsTo(EvaluationCriteria::class, 'evaluation_criteria_id');
    }

    /**
     * Get the weighted score (score * criteria weight).
     */
    public function getWeightedScoreAttribute()
    {
        return $this->score * ($this->criteria->weight ?? 1);
    }

    /**
     * Get the performance rating description based on the score.
     */
    public function getRatingDescription()
    {
        if ($this->score === null) {
            return 'Not Rated';
        }

        if ($this->score >= 4.5) {
            return 'Exceptional';
        } elseif ($this->score >= 3.5) {
            return 'Exceeds Expectations';
        } elseif ($this->score >= 2.5) {
            return 'Meets Expectations';
        } elseif ($this->score >= 1.5) {
            return 'Needs Improvement';
        } else {
            return 'Unsatisfactory';
        }
    }
}
