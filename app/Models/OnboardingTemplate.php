<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OnboardingTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'role_id',
        'department_id',
        'is_active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the role associated with the template.
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'role_id');
    }

    /**
     * Get the department associated with the template.
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the user who created the template.
     */
    public function creator(): BelongsT<PERSON>
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the tasks associated with the template.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(OnboardingTemplateTask::class, 'template_id');
    }

    /**
     * Get the onboardings that use this template.
     */
    public function onboardings(): HasMany
    {
        return $this->hasMany(EmployeeOnboarding::class, 'template_id');
    }

    /**
     * Clone this template to create a new one.
     */
    public function cloneTemplate(string $newName): OnboardingTemplate
    {
        $newTemplate = $this->replicate(['name', 'created_by']);
        $newTemplate->name = $newName;
        $newTemplate->created_by = auth()->id();
        $newTemplate->save();

        // Clone all tasks
        foreach ($this->tasks as $task) {
            $newTask = $task->replicate();
            $newTask->template_id = $newTemplate->id;
            $newTask->save();
        }

        return $newTemplate;
    }

    /**
     * Scope a query to only include active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
