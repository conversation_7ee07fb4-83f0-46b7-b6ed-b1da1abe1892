<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class Shift extends Model
{
    /** @use HasFactory<\Database\Factories\ShiftFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'login_time',
        'logout_time',
        'hours_worked',
        'break_duration',
        'date',
        'is_completed',
        'on_break',
        'scheduled_start_time',
        'scheduled_end_time',
        'scheduled_duration_minutes',
        'adherence_percentage',
        'breaks',
        'lunch_duration',
        'training_duration',
        'personal_duration',
        'status',
        'shift_type',
        'approved_by',
        'notes',
    ];

    protected $casts = [
        'login_time' => 'datetime',
        'logout_time' => 'datetime',
        'scheduled_start_time' => 'datetime',
        'scheduled_end_time' => 'datetime',
        'date' => 'date',
        'breaks' => 'json',
        'adherence_percentage' => 'decimal:2',
    ];

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Calculate the total break time in minutes
     */
    public function getTotalBreakTimeAttribute()
    {
        return $this->break_duration + $this->lunch_duration + $this->training_duration + $this->personal_duration;
    }

    /**
     * Get the formatted total break time (hours:minutes)
     */
    public function getFormattedTotalBreakTimeAttribute()
    {
        $totalMinutes = $this->total_break_time;
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;

        return sprintf('%02d:%02d', $hours, $minutes);
    }

    /**
     * Calculate the adherence percentage based on scheduled vs actual times
     */
    public function calculateAdherence()
    {
        // If no scheduled times or not completed, return null
        if (!$this->scheduled_start_time || !$this->scheduled_end_time || !$this->logout_time) {
            return null;
        }

        // Calculate scheduled minutes
        $scheduledMinutes = $this->scheduled_start_time->diffInMinutes($this->scheduled_end_time);

        // Calculate actual minutes (login to logout minus breaks)
        $actualMinutes = $this->login_time->diffInMinutes($this->logout_time) - $this->total_break_time;

        // Calculate overlap between scheduled and actual
        $actualStart = $this->login_time;
        $actualEnd = $this->logout_time->copy()->subMinutes($this->total_break_time);

        $overlapStart = Carbon::max($this->scheduled_start_time, $actualStart);
        $overlapEnd = Carbon::min($this->scheduled_end_time, $actualEnd);

        // If no overlap, adherence is 0
        if ($overlapEnd->lt($overlapStart)) {
            return 0;
        }

        $overlapMinutes = $overlapStart->diffInMinutes($overlapEnd);

        // Calculate adherence percentage
        $adherence = ($overlapMinutes / $scheduledMinutes) * 100;

        return round($adherence, 2);
    }

    /**
     * Get the adherence status based on the percentage
     */
    public function getAdherenceStatusAttribute()
    {
        if ($this->adherence_percentage === null) {
            return 'unknown';
        }

        if ($this->adherence_percentage >= 95) {
            return 'excellent';
        } elseif ($this->adherence_percentage >= 90) {
            return 'good';
        } elseif ($this->adherence_percentage >= 85) {
            return 'average';
        } else {
            return 'poor';
        }
    }

    /**
     * Get the color class for the adherence status
     */
    public function getAdherenceColorAttribute()
    {
        switch ($this->adherence_status) {
            case 'excellent':
                return 'green';
            case 'good':
                return 'blue';
            case 'average':
                return 'yellow';
            case 'poor':
                return 'red';
            default:
                return 'gray';
        }
    }

    /**
     * Check if the shift is currently active
     */
    public function getIsActiveAttribute()
    {
        return $this->login_time && !$this->logout_time;
    }

    /**
     * Get the current status of the shift
     */
    public function getCurrentStatusAttribute()
    {
        if (!$this->login_time) {
            return 'not_started';
        }

        if ($this->logout_time) {
            return 'completed';
        }

        if ($this->on_break) {
            return 'on_break';
        }

        return 'active';
    }
}
