<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmployeeOnboarding extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'template_id',
        'start_date',
        'target_completion_date',
        'actual_completion_date',
        'status',
        'progress_percentage',
        'assigned_to',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'target_completion_date' => 'date',
        'actual_completion_date' => 'date',
        'progress_percentage' => 'integer',
    ];

    /**
     * Get the user being onboarded.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the template used for this onboarding.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(OnboardingTemplate::class, 'template_id');
    }

    /**
     * Get the HR person assigned to this onboarding.
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the tasks for this onboarding.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(OnboardingTask::class, 'onboarding_id');
    }

    /**
     * Get the documents for this onboarding.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(OnboardingDocument::class, 'onboarding_id');
    }

    /**
     * Get the completed tasks for this onboarding.
     */
    public function completedTasks()
    {
        return $this->tasks()->where('status', 'completed');
    }

    /**
     * Get the pending tasks for this onboarding.
     */
    public function pendingTasks()
    {
        return $this->tasks()->whereIn('status', ['pending', 'in_progress']);
    }

    /**
     * Get the overdue tasks for this onboarding.
     */
    public function overdueTasks()
    {
        return $this->tasks()
            ->whereIn('status', ['pending', 'in_progress'])
            ->whereNotNull('due_date')
            ->where('due_date', '<', now()->format('Y-m-d'));
    }

    /**
     * Calculate and update the progress percentage.
     */
    public function updateProgress()
    {
        $totalTasks = $this->tasks()->count();
        $completedTasks = $this->completedTasks()->count();
        
        if ($totalTasks > 0) {
            $this->progress_percentage = round(($completedTasks / $totalTasks) * 100);
        } else {
            $this->progress_percentage = 0;
        }
        
        // Update status based on progress
        if ($this->progress_percentage == 100) {
            $this->status = 'completed';
            $this->actual_completion_date = now();
        } elseif ($this->progress_percentage > 0) {
            $this->status = 'in_progress';
        }
        
        $this->save();
        
        return $this->progress_percentage;
    }

    /**
     * Create tasks from a template.
     */
    public function createTasksFromTemplate()
    {
        if (!$this->template) {
            return;
        }
        
        foreach ($this->template->tasks as $templateTask) {
            $dueDate = $templateTask->calculateDueDate($this->start_date);
            
            OnboardingTask::create([
                'onboarding_id' => $this->id,
                'template_task_id' => $templateTask->id,
                'name' => $templateTask->name,
                'description' => $templateTask->description,
                'category' => $templateTask->category,
                'due_date' => $dueDate,
                'status' => 'pending',
                'assigned_to' => null,
                'is_required' => $templateTask->is_required,
                'order' => $templateTask->order,
            ]);
        }
        
        // Update the status to in_progress
        $this->status = 'in_progress';
        $this->save();
    }
}
