<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrainingSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'start_date',
        'end_date',
        'status',
        'capacity',
        'instructor_id',
        'location',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    // Relationships
    public function instructor()
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    public function trainees()
    {
        return $this->belongsToMany(User::class, 'training_session_user')
            ->withPivot('status', 'enrollment_date', 'completion_date', 'grade')
            ->withTimestamps();
    }

    public function modules()
    {
        return $this->belongsToMany(TrainingModule::class, 'training_session_module')
            ->withPivot('order', 'required')
            ->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now())->orderBy('start_date', 'asc');
    }

    public function scopePast($query)
    {
        return $query->where('end_date', '<', now())->orderBy('end_date', 'desc');
    }

    public function scopeCurrent($query)
    {
        return $query->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->orderBy('end_date', 'asc');
    }

    // Accessors & Mutators
    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
            'completed' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
            'cancelled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        };
    }

    public function getDurationAttribute()
    {
        if (!$this->start_date || !$this->end_date) {
            return null;
        }

        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    public function getProgressAttribute()
    {
        if (!$this->start_date || !$this->end_date) {
            return 0;
        }

        if ($this->start_date->isFuture()) {
            return 0;
        }

        if ($this->end_date->isPast()) {
            return 100;
        }

        $totalDays = $this->start_date->diffInDays($this->end_date) + 1;
        $daysElapsed = $this->start_date->diffInDays(now()) + 1;

        return min(100, round(($daysElapsed / $totalDays) * 100));
    }

    public function getEnrollmentCountAttribute()
    {
        return $this->trainees()->count();
    }

    public function getAvailableSpotsAttribute()
    {
        return max(0, $this->capacity - $this->enrollment_count);
    }

    public function getIsFullAttribute()
    {
        return $this->available_spots <= 0;
    }
}
