<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceItem extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'invoice_id',
        'description',
        'quantity',
        'unit_price',
        'tax_rate',
        'tax_amount',
        'discount_amount',
        'total_amount',
    ];
    
    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];
    
    /**
     * Get the invoice that owns the item.
     */
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
    
    /**
     * Calculate the subtotal (quantity * unit_price).
     */
    public function getSubtotalAttribute()
    {
        return $this->quantity * $this->unit_price;
    }
    
    /**
     * Calculate the tax amount based on the subtotal and tax rate.
     */
    public function calculateTaxAmount()
    {
        return $this->subtotal * ($this->tax_rate / 100);
    }
    
    /**
     * Calculate the total amount for this item.
     */
    public function calculateTotalAmount()
    {
        return $this->subtotal + $this->tax_amount - $this->discount_amount;
    }
}
