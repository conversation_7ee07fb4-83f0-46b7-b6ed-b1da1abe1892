<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class KpiTarget extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'kpi_targets';

    protected $fillable = [
        'metric_name',
        'target_type',
        'user_id',
        'campaign_id',
        'start_date',
        'end_date',
        'target_value',
        'min_value',
        'max_value',
        'unit',
        'description',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'target_value' => 'float',
        'min_value' => 'float',
        'max_value' => 'float',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user associated with this target.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the campaign associated with this target.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Scope a query to only include active targets.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include targets for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('start_date', '<=', $date)
                     ->where(function ($q) use ($date) {
                         $q->where('end_date', '>=', $date)
                           ->orWhereNull('end_date');
                     });
    }

    /**
     * Scope a query to only include targets for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('user_id', $userId)
              ->orWhereNull('user_id')
              ->where('target_type', 'team');
        });
    }

    /**
     * Scope a query to only include targets for a specific campaign.
     */
    public function scopeForCampaign($query, $campaignId)
    {
        return $query->where(function ($q) use ($campaignId) {
            $q->where('campaign_id', $campaignId)
              ->orWhereNull('campaign_id');
        });
    }

    /**
     * Scope a query to only include individual targets.
     */
    public function scopeIndividual($query)
    {
        return $query->where('target_type', 'individual');
    }

    /**
     * Scope a query to only include team targets.
     */
    public function scopeTeam($query)
    {
        return $query->where('target_type', 'team');
    }

    /**
     * Scope a query to only include campaign targets.
     */
    public function scopeCampaignTargets($query)
    {
        return $query->where('target_type', 'campaign');
    }

    /**
     * Format the target value with its unit.
     */
    public function getFormattedTargetAttribute()
    {
        switch ($this->unit) {
            case 'percentage':
                return $this->target_value . '%';
            case 'seconds':
                $minutes = floor($this->target_value / 60);
                $seconds = $this->target_value % 60;
                return $minutes . ':' . str_pad($seconds, 2, '0', STR_PAD_LEFT);
            case 'minutes':
                return $this->target_value . ' min';
            case 'hours':
                return $this->target_value . ' hrs';
            case 'count':
            default:
                return $this->target_value;
        }
    }
}
