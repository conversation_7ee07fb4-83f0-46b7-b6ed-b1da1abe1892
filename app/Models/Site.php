<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Site extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'location',
        'description',
        'call_center_id',
        'manager_id',
        'it_manager_id',
        'trainer_id',
        'accountant_id',
        'hr_manager_id',
        'status',
        'capacity',
        'contact_email',
        'contact_phone',
    ];

    public function platforms(): HasMany
    {
        return $this->hasMany(Platform::class);
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function itManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'it_manager_id');
    }

    public function trainer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'trainer_id');
    }

    public function accountant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'accountant_id');
    }

    public function hrManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'hr_manager_id');
    }

    public function callCenter(): BelongsTo
    {
        return $this->belongsTo(CallCenter::class);
    }

    /**
     * Get all reports for this site.
     */
    public function reports()
    {
        return $this->morphMany(Report::class, 'reportable');
    }

    /**
     * Get all personnel assigned to this site.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'site_user')
            ->withPivot('role', 'assigned_at')
            ->withTimestamps();
    }

    /**
     * Get all equipment for this site through platforms.
     */
    public function equipment()
    {
        return $this->hasManyThrough(
            Equipment::class,
            Platform::class,
            'site_id', // Foreign key on platforms table
            'platform_id', // Foreign key on equipment table
            'id', // Local key on sites table
            'id' // Local key on platforms table
        );
    }
}
