<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationCriteria extends Model
{
    use HasFactory;

    // Using default table name 'evaluation_criterias'

    protected $fillable = [
        'name',
        'category',
        'description',
        'weight',
        'is_active',
        'is_required',
        'campaign_id',
    ];

    protected $casts = [
        'weight' => 'integer',
        'is_active' => 'boolean',
        'is_required' => 'boolean',
    ];

    /**
     * Get the campaign associated with this criteria.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get the evaluation scores for this criteria.
     */
    public function scores()
    {
        return $this->hasMany(EvaluationScore::class);
    }

    /**
     * Scope a query to only include active criteria.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include criteria for a specific campaign.
     */
    public function scopeForCampaign($query, $campaignId)
    {
        return $query->where(function ($q) use ($campaignId) {
            $q->where('campaign_id', $campaignId)
              ->orWhereNull('campaign_id'); // Include global criteria
        });
    }

    /**
     * Scope a query to only include global criteria.
     */
    public function scopeGlobal($query)
    {
        return $query->whereNull('campaign_id');
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to only include required criteria.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }
}
