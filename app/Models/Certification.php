<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Certification extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'issuer',
        'description',
        'validity_period',
        'is_active',
    ];

    protected $casts = [
        'validity_period' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the users that have this certification.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_certifications')
            ->withPivot('certificate_number', 'issued_at', 'expires_at', 'status', 'notes', 'document_path', 'verified_by')
            ->withTimestamps();
    }

    /**
     * Get the campaigns that require this certification.
     */
    public function requiredByCampaigns()
    {
        return $this->belongsToMany(Campaign::class, 'campaign_required_certifications')
            ->withPivot('is_mandatory')
            ->withTimestamps();
    }

    /**
     * Get the training modules that prepare for this certification.
     */
    public function trainingModules()
    {
        return $this->belongsToMany(TrainingModule::class, 'training_module_certifications')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active certifications.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by issuer.
     */
    public function scopeIssuer($query, $issuer)
    {
        return $query->where('issuer', $issuer);
    }
}
