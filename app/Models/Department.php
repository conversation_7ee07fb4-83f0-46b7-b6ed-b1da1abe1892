<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'parent_id',
        'manager_id',
        'status',
        'call_center_id',
    ];

    /**
     * Get the parent department.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Department::class, 'parent_id');
    }

    /**
     * Get the child departments.
     */
    public function children(): Has<PERSON>any
    {
        return $this->hasMany(Department::class, 'parent_id');
    }

    /**
     * Get all descendants of the department.
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get the manager of the department.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    /**
     * Get the users in this department.
     */
    public function users(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the call center that owns the department.
     */
    public function callCenter(): BelongsTo
    {
        return $this->belongsTo(CallCenter::class);
    }

    /**
     * Get all users in this department and its descendants.
     */
    public function allUsers()
    {
        $departmentIds = $this->descendants()->pluck('id')->push($this->id);
        return User::whereIn('department_id', $departmentIds);
    }

    /**
     * Scope a query to only include active departments.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
