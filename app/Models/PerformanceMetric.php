<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PerformanceMetric extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'performance_metrics';

    protected $fillable = [
        'user_id',
        'campaign_id',
        'date',
        // Call volume metrics
        'calls_handled',
        'calls_missed',
        'outbound_calls',
        'total_talk_time',
        'total_hold_time',
        'total_wrap_up_time',
        // Efficiency metrics
        'average_handle_time',
        'average_hold_time',
        'average_wrap_up_time',
        'occupancy_rate',
        'first_call_resolution',
        // Quality metrics
        'quality_score',
        'evaluations_count',
        'customer_satisfaction',
        'nps_score',
        // Outcome metrics
        'appointments_set',
        'appointments_kept',
        'conversion_rate',
        'sales_value',
        // Adherence metrics
        'scheduled_time',
        'logged_time',
        'adherence_rate',
        'punctuality_rate',
        // Compliance metrics
        'compliance_score',
        'script_adherence',
        'regulatory_compliance',
        // Custom metrics
        'custom_metrics',
    ];

    protected $casts = [
        'date' => 'date',
        // Call volume metrics
        'calls_handled' => 'integer',
        'calls_missed' => 'integer',
        'outbound_calls' => 'integer',
        'total_talk_time' => 'integer',
        'total_hold_time' => 'integer',
        'total_wrap_up_time' => 'integer',
        // Efficiency metrics
        'average_handle_time' => 'float',
        'average_hold_time' => 'float',
        'average_wrap_up_time' => 'float',
        'occupancy_rate' => 'float',
        'first_call_resolution' => 'float',
        // Quality metrics
        'quality_score' => 'float',
        'evaluations_count' => 'integer',
        'customer_satisfaction' => 'float',
        'nps_score' => 'float',
        // Outcome metrics
        'appointments_set' => 'integer',
        'appointments_kept' => 'integer',
        'conversion_rate' => 'float',
        'sales_value' => 'float',
        // Adherence metrics
        'scheduled_time' => 'integer',
        'logged_time' => 'integer',
        'adherence_rate' => 'float',
        'punctuality_rate' => 'float',
        // Compliance metrics
        'compliance_score' => 'float',
        'script_adherence' => 'float',
        'regulatory_compliance' => 'float',
        // Custom metrics
        'custom_metrics' => 'json',
    ];

    /**
     * Get the agent associated with these metrics.
     */
    public function agent()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the campaign associated with these metrics.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get the KPI targets for this agent and campaign.
     */
    public function kpiTargets()
    {
        return KpiTarget::where(function ($query) {
            $query->where('user_id', $this->user_id)
                  ->orWhereNull('user_id');
        })->where(function ($query) {
            $query->where('campaign_id', $this->campaign_id)
                  ->orWhereNull('campaign_id');
        })->where(function ($query) {
            $query->where('start_date', '<=', $this->date)
                  ->where(function ($q) {
                      $q->where('end_date', '>=', $this->date)
                        ->orWhereNull('end_date');
                  });
        })->where('is_active', true)
          ->get();
    }

    /**
     * Format the total talk time as hours:minutes:seconds.
     */
    public function getFormattedTalkTimeAttribute()
    {
        $hours = floor($this->total_talk_time / 3600);
        $minutes = floor(($this->total_talk_time % 3600) / 60);
        $seconds = $this->total_talk_time % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Format the total hold time as hours:minutes:seconds.
     */
    public function getFormattedHoldTimeAttribute()
    {
        $hours = floor($this->total_hold_time / 3600);
        $minutes = floor(($this->total_hold_time % 3600) / 60);
        $seconds = $this->total_hold_time % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Format the total wrap-up time as hours:minutes:seconds.
     */
    public function getFormattedWrapUpTimeAttribute()
    {
        $hours = floor($this->total_wrap_up_time / 3600);
        $minutes = floor(($this->total_wrap_up_time % 3600) / 60);
        $seconds = $this->total_wrap_up_time % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Get the total calls (handled + outbound).
     */
    public function getTotalCallsAttribute()
    {
        return $this->calls_handled + $this->outbound_calls;
    }

    /**
     * Get the call answer rate.
     */
    public function getAnswerRateAttribute()
    {
        $totalInbound = $this->calls_handled + $this->calls_missed;
        return $totalInbound > 0 ? round(($this->calls_handled / $totalInbound) * 100, 2) : 0;
    }

    /**
     * Get the appointment conversion rate.
     */
    public function getAppointmentConversionRateAttribute()
    {
        return $this->appointments_set > 0
            ? round(($this->appointments_kept / $this->appointments_set) * 100, 2)
            : 0;
    }

    /**
     * Get the total handle time (talk + hold + wrap-up).
     */
    public function getTotalHandleTimeAttribute()
    {
        return $this->total_talk_time + $this->total_hold_time + $this->total_wrap_up_time;
    }

    /**
     * Get the formatted total handle time.
     */
    public function getFormattedTotalHandleTimeAttribute()
    {
        $totalTime = $this->getTotalHandleTimeAttribute();
        $hours = floor($totalTime / 3600);
        $minutes = floor(($totalTime % 3600) / 60);
        $seconds = $totalTime % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Get the overall performance score.
     * This combines quality, efficiency, and outcome metrics into a single score.
     */
    public function getOverallPerformanceScoreAttribute()
    {
        // Define weights for each category
        $weights = [
            'quality' => 0.4,
            'efficiency' => 0.3,
            'outcome' => 0.3
        ];

        // Calculate quality score (average of quality metrics)
        $qualityMetrics = array_filter([
            $this->quality_score,
            $this->customer_satisfaction,
            $this->compliance_score,
            $this->script_adherence
        ]);

        $qualityScore = count($qualityMetrics) > 0
            ? array_sum($qualityMetrics) / count($qualityMetrics)
            : 0;

        // Calculate efficiency score
        $efficiencyScore = 0;
        $efficiencyCount = 0;

        // Normalize AHT (lower is better) - assuming target AHT is 180 seconds
        if ($this->average_handle_time > 0) {
            $targetAHT = 180;
            $ahtScore = min(100, max(0, 100 - (($this->average_handle_time - $targetAHT) / $targetAHT * 100)));
            $efficiencyScore += $ahtScore;
            $efficiencyCount++;
        }

        // Add other efficiency metrics
        if ($this->occupancy_rate > 0) {
            $efficiencyScore += $this->occupancy_rate;
            $efficiencyCount++;
        }

        if ($this->first_call_resolution > 0) {
            $efficiencyScore += $this->first_call_resolution;
            $efficiencyCount++;
        }

        $efficiencyScore = $efficiencyCount > 0
            ? $efficiencyScore / $efficiencyCount
            : 0;

        // Calculate outcome score
        $outcomeMetrics = array_filter([
            $this->conversion_rate,
            $this->getAppointmentConversionRateAttribute(),
            $this->adherence_rate
        ]);

        $outcomeScore = count($outcomeMetrics) > 0
            ? array_sum($outcomeMetrics) / count($outcomeMetrics)
            : 0;

        // Calculate weighted overall score
        $overallScore =
            ($qualityScore * $weights['quality']) +
            ($efficiencyScore * $weights['efficiency']) +
            ($outcomeScore * $weights['outcome']);

        return round($overallScore, 2);
    }

    /**
     * Get the performance rating based on the overall score.
     */
    public function getPerformanceRatingAttribute()
    {
        $score = $this->getOverallPerformanceScoreAttribute();

        if ($score >= 90) {
            return 'Exceptional';
        } elseif ($score >= 80) {
            return 'Exceeds Expectations';
        } elseif ($score >= 70) {
            return 'Meets Expectations';
        } elseif ($score >= 60) {
            return 'Needs Improvement';
        } else {
            return 'Unsatisfactory';
        }
    }

    /**
     * Calculate the performance against targets.
     */
    public function getTargetPerformanceAttribute()
    {
        $targets = $this->kpiTargets();
        $performance = [];

        foreach ($targets as $target) {
            $metricName = $target->metric_name;
            $actualValue = $this->$metricName ?? 0;

            $performance[$metricName] = [
                'target' => $target->target_value,
                'actual' => $actualValue,
                'achievement' => $target->target_value > 0 ? ($actualValue / $target->target_value) * 100 : 0,
                'unit' => $target->unit,
                'description' => $target->description,
            ];
        }

        return $performance;
    }

    /**
     * Scope a query to only include metrics within a date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('date', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include metrics for a specific agent.
     */
    public function scopeForAgent($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include metrics for a specific campaign.
     */
    public function scopeForCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    /**
     * Scope a query to only include metrics for a team of agents.
     */
    public function scopeForTeam($query, $teamId)
    {
        return $query->whereHas('user', function($q) use ($teamId) {
            $q->whereHas('teams', function($q) use ($teamId) {
                $q->where('teams.id', $teamId);
            });
        });
    }

    /**
     * Scope a query to only include metrics for agents with a specific role.
     */
    public function scopeForRole($query, $roleName)
    {
        return $query->whereHas('user', function($q) use ($roleName) {
            $q->role($roleName);
        });
    }

    /**
     * Scope a query to only include metrics for the current day.
     */
    public function scopeToday($query)
    {
        return $query->where('date', now()->format('Y-m-d'));
    }

    /**
     * Scope a query to only include metrics for the current week.
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('date', [
            now()->startOfWeek()->format('Y-m-d'),
            now()->endOfWeek()->format('Y-m-d')
        ]);
    }

    /**
     * Scope a query to only include metrics for the current month.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereBetween('date', [
            now()->startOfMonth()->format('Y-m-d'),
            now()->endOfMonth()->format('Y-m-d')
        ]);
    }

    /**
     * Scope a query to only include metrics for high performers.
     */
    public function scopeHighPerformers($query, $threshold = 80)
    {
        return $query->where(function($q) use ($threshold) {
            $q->where('quality_score', '>=', $threshold)
              ->orWhere('conversion_rate', '>=', $threshold);
        });
    }

    /**
     * Scope a query to only include metrics for agents needing improvement.
     */
    public function scopeNeedsImprovement($query, $threshold = 60)
    {
        return $query->where(function($q) use ($threshold) {
            $q->where('quality_score', '<', $threshold)
              ->orWhere('conversion_rate', '<', $threshold);
        });
    }
}
