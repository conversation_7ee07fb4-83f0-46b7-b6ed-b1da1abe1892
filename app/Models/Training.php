<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Training extends Model
{
    /** @use HasFactory<\Database\Factories\TrainingFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'start_date',
        'progress',
        'notes',
        'validated_at',
        'rating',
        'training_module_id',
    ];

    protected $casts = [
        'start_date' => 'date',
        'validated_at' => 'datetime',
        'rating' => 'float',
    ];

    // Relations
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function module()
    {
        return $this->belongsTo(TrainingModule::class, 'training_module_id');
    }
}
