<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'group',
        'key',
        'value',
        'default_value',
        'type',
        'description',
        'is_public',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get a setting value by group and key
     *
     * @param string $group
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $group, string $key, $default = null)
    {
        try {
            $setting = self::where('group', $group)
                ->where('key', $key)
                ->first();

            if (!$setting) {
                return $default;
            }

            // Cast the value based on the type
            switch ($setting->type) {
                case 'boolean':
                    return (bool) $setting->value;
                case 'integer':
                    return (int) $setting->value;
                case 'float':
                    return (float) $setting->value;
                case 'json':
                    return json_decode($setting->value, true);
                default:
                    return $setting->value;
            }
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return the default
            return $default;
        }
    }

    /**
     * Set a setting value
     *
     * @param string $group
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @param string|null $description
     * @param bool $isPublic
     * @return Setting|null
     */
    public static function setValue(string $group, string $key, $value, string $type = 'string', ?string $description = null, bool $isPublic = false)
    {
        try {
            // Prepare the value based on type
            if ($type === 'json' && !is_string($value)) {
                $value = json_encode($value);
            }

            $setting = self::updateOrCreate(
                ['group' => $group, 'key' => $key],
                [
                    'value' => $value,
                    'type' => $type,
                    'description' => $description,
                    'is_public' => $isPublic,
                ]
            );

            return $setting;
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return null
            return null;
        }
    }

    /**
     * Get all settings for a specific group
     *
     * @param string $group
     * @return array
     */
    public static function getGroup(string $group)
    {
        try {
            $settings = self::where('group', $group)->get();
            $result = [];

            foreach ($settings as $setting) {
                // Cast the value based on the type
                switch ($setting->type) {
                    case 'boolean':
                        $result[$setting->key] = (bool) $setting->value;
                        break;
                    case 'integer':
                        $result[$setting->key] = (int) $setting->value;
                        break;
                    case 'float':
                        $result[$setting->key] = (float) $setting->value;
                        break;
                    case 'json':
                        $result[$setting->key] = json_decode($setting->value, true);
                        break;
                    default:
                        $result[$setting->key] = $setting->value;
                }
            }

            return $result;
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return empty array
            return [];
        }
    }
}
