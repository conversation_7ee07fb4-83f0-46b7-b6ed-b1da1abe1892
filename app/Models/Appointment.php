<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'campaign_id',
        'customer_id',
        'prospect_id',
        'scheduled_at',
        'end_time',
        'duration_minutes',
        'type',
        'category',
        'status',
        'agent_notes',
        'audio_path',
        'cq_notes',
        'validated_by',
        'validated_at',
        'rejected_by',
        'rejected_at',
        'outcome',
        'outcome_notes',
        'follow_up_required',
        'follow_up_date',
        'is_recurring',
        'recurrence_pattern',
        'recurrence_end_date',
        'location',
        'meeting_link',
        'sent_to_customer',
        'sent_to_customer_at',
        'customer_confirmed',
        'customer_confirmed_at',
        'service_id',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'end_time' => 'datetime',
        'validated_at' => 'datetime',
        'rejected_at' => 'datetime',
        'follow_up_date' => 'datetime',
        'recurrence_end_date' => 'datetime',
        'sent_to_customer_at' => 'datetime',
        'customer_confirmed_at' => 'datetime',
        'is_recurring' => 'boolean',
        'follow_up_required' => 'boolean',
        'sent_to_customer' => 'boolean',
        'customer_confirmed' => 'boolean',
        'duration_minutes' => 'integer',
        'service_id' => 'integer',
        'prospect_id' => 'integer',
    ];

    /**
     * L’utilisateur (agent ou manager) qui a créé le RDV.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * La campagne associée.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Le client du RDV.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * The prospect associated with this appointment.
     */
    public function prospect()
    {
        return $this->belongsTo(Prospect::class);
    }

    /**
     * The service being offered in this appointment.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * L’utilisateur CQ qui a validé.
     */
    public function validatedBy()
    {
        return $this->belongsTo(User::class, 'validated_by');
    }

    /**
     * L’utilisateur CQ qui a rejeté.
     */
    public function rejectedBy()
    {
        return $this->belongsTo(User::class, 'rejected_by');
    }

    /**
     * URL publique vers le fichier audio.
     */
    public function getAudioUrlAttribute(): ?string
    {
        if ($this->audio_path) {
            try {
                // Log the audio path for debugging
                Log::info('Getting audio URL for path: ' . $this->audio_path);

                // Check if the file exists
                if (!Storage::disk('public')->exists($this->audio_path)) {
                    Log::warning('Audio file does not exist: ' . $this->audio_path);
                    return null;
                }

                // Generate the URL using the asset helper
                $url = asset('storage/' . $this->audio_path);

                // Log the URL for debugging
                Log::info('Audio URL generated: ' . $url);

                return $url;
            } catch (\Exception $e) {
                Log::error('Error getting audio URL: ' . $e->getMessage());
                return null;
            }
        }

        return null;
    }

    /**
     * Get the MIME type of the audio file.
     */
    public function getAudioMimeTypeAttribute(): ?string
    {
        if ($this->audio_path) {
            try {
                $extension = pathinfo($this->audio_path, PATHINFO_EXTENSION);

                $mimeTypes = [
                    'mp3' => 'audio/mpeg',
                    'wav' => 'audio/wav',
                    'ogg' => 'audio/ogg',
                    'flac' => 'audio/flac',
                    'aac' => 'audio/aac',
                ];

                return $mimeTypes[$extension] ?? 'audio/mpeg';
            } catch (\Exception $e) {
                return 'audio/mpeg';
            }
        }

        return null;
    }

    /**
     * Check if the appointment is upcoming
     */
    public function getIsUpcomingAttribute(): bool
    {
        return $this->scheduled_at > now();
    }

    /**
     * Check if the appointment is in progress
     */
    public function getIsInProgressAttribute(): bool
    {
        $now = now();
        return $this->scheduled_at <= $now &&
               ($this->end_time ? $this->end_time > $now : $this->scheduled_at->addMinutes($this->duration_minutes ?? 60) > $now);
    }

    /**
     * Check if the appointment is completed
     */
    public function getIsCompletedAttribute(): bool
    {
        $now = now();
        return ($this->end_time ? $this->end_time <= $now : $this->scheduled_at->addMinutes($this->duration_minutes ?? 60) <= $now) &&
               in_array($this->status, ['validated', 'rejected', 'completed']);
    }

    /**
     * Get the formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return '1 hour (default)';
        }

        if ($this->duration_minutes < 60) {
            return $this->duration_minutes . ' minutes';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($minutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }

        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '');
    }

    /**
     * Get the appointment status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
            'validated', 'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            'rejected' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
            'in_progress' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
            'cancelled' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        };
    }

    /**
     * Get related appointments (follow-ups or recurring)
     */
    public function relatedAppointments()
    {
        if ($this->is_recurring) {
            return self::where('recurrence_pattern', $this->recurrence_pattern)
                ->where('id', '!=', $this->id)
                ->orderBy('scheduled_at')
                ->get();
        }

        return collect();
    }

    /**
     * Get follow-up appointment
     */
    public function followUpAppointment()
    {
        if ($this->follow_up_required && $this->follow_up_date) {
            return self::where('scheduled_at', $this->follow_up_date)
                ->where('customer_id', $this->customer_id)
                ->first();
        }

        return null;
    }

    /**
     * Send the appointment to the campaign's customer after validation
     */
    public function sendToCustomer()
    {
        if ($this->status !== 'validated') {
            throw new \Exception('Only validated appointments can be sent to customers');
        }

        if ($this->sent_to_customer) {
            throw new \Exception('This appointment has already been sent to the customer');
        }

        // Here you would implement the actual notification logic
        // For example, sending an email or notification to the customer

        $this->sent_to_customer = true;
        $this->sent_to_customer_at = now();
        $this->save();

        return true;
    }

    /**
     * Mark the appointment as confirmed by the customer
     */
    public function confirmByCustomer()
    {
        if (!$this->sent_to_customer) {
            throw new \Exception('This appointment has not been sent to the customer yet');
        }

        if ($this->customer_confirmed) {
            throw new \Exception('This appointment has already been confirmed by the customer');
        }

        $this->customer_confirmed = true;
        $this->customer_confirmed_at = now();
        $this->save();

        return true;
    }

    /**
     * Check if the appointment can be sent to the customer
     */
    public function canBeSentToCustomer()
    {
        return $this->status === 'validated' && !$this->sent_to_customer;
    }

    /**
     * Check if the appointment is awaiting customer confirmation
     */
    public function isAwaitingCustomerConfirmation()
    {
        return $this->sent_to_customer && !$this->customer_confirmed;
    }
}
