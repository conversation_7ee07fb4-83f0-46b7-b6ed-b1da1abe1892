<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OnboardingTemplateTask extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'template_id',
        'name',
        'description',
        'category',
        'due_days',
        'responsible_role',
        'order',
        'is_required',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'due_days' => 'integer',
        'order' => 'integer',
        'is_required' => 'boolean',
    ];

    /**
     * Get the template that owns the task.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(OnboardingTemplate::class, 'template_id');
    }

    /**
     * Get the onboarding tasks created from this template task.
     */
    public function onboardingTasks(): HasMany
    {
        return $this->hasMany(OnboardingTask::class, 'template_task_id');
    }

    /**
     * Get the category name in a human-readable format.
     */
    public function getCategoryNameAttribute(): string
    {
        $categories = [
            'documentation' => 'Documentation',
            'training' => 'Training',
            'equipment' => 'Equipment',
            'system_access' => 'System Access',
            'orientation' => 'Orientation',
            'hr_admin' => 'HR Administration',
            'it_setup' => 'IT Setup',
            'team_introduction' => 'Team Introduction',
            'workspace' => 'Workspace Setup',
            'other' => 'Other',
        ];

        return $categories[$this->category] ?? ucfirst($this->category);
    }

    /**
     * Get the due date for a task based on the onboarding start date.
     */
    public function calculateDueDate($startDate)
    {
        return date('Y-m-d', strtotime($startDate . ' + ' . $this->due_days . ' days'));
    }
}
