<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'invoice_number',
        'client_id',
        'client_type',
        'client_name',
        'client_email',
        'client_address',
        'client_phone',
        'issue_date',
        'due_date',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'notes',
        'status',
        'created_by',
    ];
    
    protected $casts = [
        'issue_date' => 'date',
        'due_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];
    
    /**
     * Get the client that owns the invoice.
     */
    public function client()
    {
        return $this->morphTo();
    }
    
    /**
     * Get the user who created the invoice.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    
    /**
     * Get the items for the invoice.
     */
    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }
    
    /**
     * Get the payments for the invoice.
     */
    public function payments()
    {
        return $this->hasMany(InvoicePayment::class);
    }
    
    /**
     * Calculate the total amount paid for this invoice.
     */
    public function getTotalPaidAttribute()
    {
        return $this->payments->sum('amount');
    }
    
    /**
     * Calculate the remaining balance for this invoice.
     */
    public function getRemainingBalanceAttribute()
    {
        return $this->total_amount - $this->total_paid;
    }
    
    /**
     * Check if the invoice is fully paid.
     */
    public function getIsPaidAttribute()
    {
        return $this->remaining_balance <= 0;
    }
    
    /**
     * Check if the invoice is overdue.
     */
    public function getIsOverdueAttribute()
    {
        return !$this->is_paid && $this->due_date->isPast();
    }
    
    /**
     * Generate a unique invoice number.
     */
    public static function generateInvoiceNumber()
    {
        $prefix = 'INV-';
        $year = date('Y');
        $month = date('m');
        
        $lastInvoice = self::where('invoice_number', 'like', "{$prefix}{$year}{$month}%")
            ->orderBy('invoice_number', 'desc')
            ->first();
        
        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
