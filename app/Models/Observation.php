<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Observation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'agent_id',
        'observer_id',
        'campaign_id',
        'date',
        'content',
        'rating',
        'strengths',
        'areas_for_improvement',
        'action_plan',
        'follow_up_date',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'follow_up_date' => 'date',
        'rating' => 'float',
    ];

    /**
     * Get the agent being observed.
     */
    public function agent()
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    /**
     * Get the user who made the observation.
     */
    public function observer()
    {
        return $this->belongsTo(User::class, 'observer_id');
    }

    /**
     * Get the campaign associated with this observation.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }
}
