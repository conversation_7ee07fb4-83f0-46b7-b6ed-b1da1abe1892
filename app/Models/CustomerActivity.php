<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'activity_type',
        'description',
        'activity_date',
        'status',
        'contact_id',
        'performed_by',
        'outcome',
        'follow_up_notes',
        'follow_up_date',
    ];

    protected $casts = [
        'activity_date' => 'datetime',
        'follow_up_date' => 'datetime',
    ];

    /**
     * Get the customer that owns the activity.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the contact associated with the activity.
     */
    public function contact()
    {
        return $this->belongsTo(CustomerContact::class, 'contact_id');
    }

    /**
     * Get the user who performed the activity.
     */
    public function performer()
    {
        return $this->belongsTo(User::class, 'performed_by');
    }

    /**
     * Check if the activity is completed.
     */
    public function getIsCompletedAttribute()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the activity is scheduled.
     */
    public function getIsScheduledAttribute()
    {
        return $this->status === 'scheduled';
    }

    /**
     * Check if the activity is cancelled.
     */
    public function getIsCancelledAttribute()
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the activity has a follow-up.
     */
    public function getHasFollowUpAttribute()
    {
        return !is_null($this->follow_up_date);
    }
}
