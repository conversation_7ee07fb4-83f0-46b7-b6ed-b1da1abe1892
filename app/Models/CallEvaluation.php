<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CallEvaluation extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'call_evaluations';

    protected $fillable = [
        'call_id',
        'evaluator_id',
        'evaluation_date',
        'greeting_score',
        'communication_score',
        'knowledge_score',
        'problem_solving_score',
        'closing_score',
        'compliance_score',
        'overall_score',
        'strengths',
        'areas_for_improvement',
        'action_items',
        'notes',
        'status',
    ];

    protected $casts = [
        'evaluation_date' => 'date',
        'greeting_score' => 'float',
        'communication_score' => 'float',
        'knowledge_score' => 'float',
        'problem_solving_score' => 'float',
        'closing_score' => 'float',
        'compliance_score' => 'float',
        'overall_score' => 'float',
    ];

    /**
     * Get the call being evaluated.
     */
    public function call()
    {
        return $this->belongsTo(Call::class);
    }

    /**
     * Get the user who performed the evaluation.
     */
    public function evaluator()
    {
        return $this->belongsTo(User::class, 'evaluator_id');
    }

    /**
     * Get the agent who handled the call.
     */
    public function agent()
    {
        return $this->hasOneThrough(User::class, Call::class, 'id', 'id', 'call_id', 'user_id');
    }

    /**
     * Get the campaign associated with the call.
     */
    public function campaign()
    {
        return $this->hasOneThrough(Campaign::class, Call::class, 'id', 'id', 'call_id', 'campaign_id');
    }

    /**
     * Get the individual criteria scores for this evaluation.
     */
    public function scores()
    {
        return $this->hasMany(EvaluationScore::class);
    }

    /**
     * Calculate the overall score based on individual category scores.
     */
    public function calculateOverallScore()
    {
        $scores = [
            $this->greeting_score,
            $this->communication_score,
            $this->knowledge_score,
            $this->problem_solving_score,
            $this->closing_score,
            $this->compliance_score,
        ];

        // Filter out null values
        $scores = array_filter($scores, function ($score) {
            return $score !== null;
        });

        if (count($scores) === 0) {
            return null;
        }

        return round(array_sum($scores) / count($scores), 1);
    }

    /**
     * Calculate the overall score based on weighted criteria scores.
     */
    public function calculateWeightedScore()
    {
        $scores = $this->scores()->with('criteria')->get();

        if ($scores->isEmpty()) {
            return $this->calculateOverallScore();
        }

        $totalWeight = $scores->sum(function ($score) {
            return $score->criteria->weight;
        });

        if ($totalWeight === 0) {
            return null;
        }

        $weightedSum = $scores->sum(function ($score) {
            return $score->score * $score->criteria->weight;
        });

        return round($weightedSum / $totalWeight, 1);
    }

    /**
     * Get the performance rating description based on the overall score.
     */
    public function getRatingDescription()
    {
        if ($this->overall_score === null) {
            return 'Not Rated';
        }

        if ($this->overall_score >= 4.5) {
            return 'Exceptional';
        } elseif ($this->overall_score >= 3.5) {
            return 'Exceeds Expectations';
        } elseif ($this->overall_score >= 2.5) {
            return 'Meets Expectations';
        } elseif ($this->overall_score >= 1.5) {
            return 'Needs Improvement';
        } else {
            return 'Unsatisfactory';
        }
    }

    /**
     * Scope a query to only include evaluations within a date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('evaluation_date', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include evaluations by a specific evaluator.
     */
    public function scopeByEvaluator($query, $evaluatorId)
    {
        return $query->where('evaluator_id', $evaluatorId);
    }

    /**
     * Scope a query to only include evaluations with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
