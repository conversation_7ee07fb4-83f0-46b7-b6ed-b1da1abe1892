<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole
{
    /** @use HasFactory<\Database\Factories\RoleFactory> */
    use HasFactory;

    // Role constants
    public const ADMIN = 'administrator';
    public const DIRECTOR = 'director';
    public const MANAGER = 'platform_manager';
    public const SUPERVISOR = 'supervisor';
    public const QUALITY_CONTROLLER = 'quality_control';
    public const AGENT = 'agent';
    public const IT_MANAGER = 'it_manager';
    public const IT_SUPPORT = 'it_support';
    public const TRAINER = 'trainer';
    public const ACCOUNTANT = 'accountant';
    public const HR_MANAGER = 'hr_manager';
    public const API = 'api';
    public const MOBILE = 'mobile';
    public const ADMIN_API = 'admin_api';
    public const REMOTE_AGENT = 'remote_agent';
    public const REMOTE_SUPERVISOR = 'remote_supervisor';
    public const REMOTE_MANAGER = 'remote_manager';
    public const CAMPAIGN_SUPERVISOR = 'campaign_supervisor';

     /**
     * If you’ve made `id` non-incrementing on your `roles` table .
     */
    protected $primaryKey = 'id';
    public $incrementing = true;

    /**
     * Only these attributes can be mass assigned.
     */
    protected $fillable = [
        'id',
        'name',
        'display_name',
        'guard_name',
    ];
}
