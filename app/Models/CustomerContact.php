<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerContact extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'first_name',
        'last_name',
        'position',
        'department',
        'email',
        'phone',
        'mobile',
        'is_primary',
        'is_decision_maker',
        'is_billing_contact',
        'is_technical_contact',
        'notes',
        'status',
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'is_decision_maker' => 'boolean',
        'is_billing_contact' => 'boolean',
        'is_technical_contact' => 'boolean',
    ];

    /**
     * Get the customer that owns the contact.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the activities associated with this contact.
     */
    public function activities()
    {
        return $this->hasMany(CustomerActivity::class, 'contact_id');
    }

    /**
     * Get the full name of the contact.
     */
    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }
}
