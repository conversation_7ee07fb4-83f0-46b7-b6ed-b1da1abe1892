<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Media extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'mediable_id',
        'mediable_type',
        'file_name',
        'document_title',
        'description',
        'file_path',
        'mime_type',
        'category',
        'tags',
        'department',
        'uploaded_by',
        'expiry_date',
        'verification_status',
        'verified_by',
        'verified_at',
        'rejection_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expiry_date' => 'date',
        'verified_at' => 'datetime',
        'tags' => 'array',
    ];

    /**
     * Get the related model that the media belongs to.
     */
    public function mediable()
    {
        return $this->morphTo();
    }

    /**
     * Get the user who uploaded the document.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the user who verified the document.
     */
    public function verifier()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Check if the document is expired.
     */
    public function isExpired()
    {
        return $this->expiry_date && $this->expiry_date->isPast();
    }

    /**
     * Check if the document is about to expire.
     *
     * @param int $days Days threshold for expiration warning
     * @return bool
     */
    public function isAboutToExpire($days = 30)
    {
        return $this->expiry_date &&
               $this->expiry_date->isFuture() &&
               $this->expiry_date->diffInDays(now()) <= $days;
    }

    /**
     * Get days until expiration.
     */
    public function daysUntilExpiration()
    {
        if (!$this->expiry_date) {
            return null;
        }

        if ($this->expiry_date->isPast()) {
            return -$this->expiry_date->diffInDays(now());
        }

        return $this->expiry_date->diffInDays(now());
    }

    /**
     * Scope a query to only include documents that are expired.
     */
    public function scopeExpired($query)
    {
        return $query->whereNotNull('expiry_date')
                     ->where('expiry_date', '<', Carbon::today());
    }

    /**
     * Scope a query to only include documents that are about to expire.
     */
    public function scopeAboutToExpire($query, $days = 30)
    {
        return $query->whereNotNull('expiry_date')
                     ->where('expiry_date', '>=', Carbon::today())
                     ->where('expiry_date', '<=', Carbon::today()->addDays($days));
    }

    /**
     * Scope a query to only include documents with a specific verification status.
     */
    public function scopeWithVerificationStatus($query, $status)
    {
        return $query->where('verification_status', $status);
    }
}
