<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contract extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'start_date',
        'end_date',
        'status',
        'salary',
        'position',
        'department',
        'terms',
        'notes',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'salary' => 'decimal:2',
    ];

    /**
     * Get the user that owns the contract.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who created the contract.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the documents associated with the contract.
     */
    public function documents()
    {
        return $this->morphMany(Media::class, 'mediable')->where('category', 'contract');
    }

    /**
     * Check if the contract is active.
     */
    public function isActive()
    {
        return $this->status === 'active' && 
               ($this->end_date === null || $this->end_date->isFuture());
    }

    /**
     * Check if the contract is expired.
     */
    public function isExpired()
    {
        return $this->end_date !== null && $this->end_date->isPast();
    }

    /**
     * Check if the contract is about to expire.
     * 
     * @param int $days Days threshold for expiration warning
     * @return bool
     */
    public function isAboutToExpire($days = 30)
    {
        return $this->end_date !== null && 
               $this->end_date->isFuture() && 
               $this->end_date->diffInDays(now()) <= $days;
    }

    /**
     * Get the contract duration in months.
     */
    public function getDurationInMonths()
    {
        if ($this->end_date === null) {
            return null; // Permanent contract
        }
        
        return $this->start_date->diffInMonths($this->end_date);
    }
}
