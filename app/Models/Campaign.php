<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Campaign extends Model
{
    /** @use HasFactory<\Database\Factories\CampaignFactory> */
    use HasFactory;

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::deleting(function ($campaign) {
            // Manually delete related records
            $campaign->evaluationCriteria()->delete();
            $campaign->requiredSkills()->detach();
            $campaign->requiredCertifications()->detach();
            $campaign->kpiTargets()->delete();
            $campaign->performanceMetrics()->delete();

            // Check if there are any calls related to this campaign
            if (class_exists('App\Models\Call')) {
                $campaign->calls()->delete();
            }
        });
    }

    protected $fillable = [
        'name',
        'customer_id',
        'platform_id',
        'manager_id',
        'user_id',
        'start_date',
        'end_date',
        'status',
        'performance',
        'productivity',
        'rating',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'performance' => 'integer',
        'productivity' => 'integer',
        'rating' => 'float',
    ];

    // Relations
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function platform()
    {
        return $this->belongsTo(Platform::class);
    }

    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function agents()
    {
        return $this->hasMany(User::class)->where('role_id', 6);
    }

    public function agent()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function reports()
    {
        return $this->hasMany(Report::class);
    }

    /**
     * Get the skills required for this campaign.
     */
    public function requiredSkills()
    {
        return $this->belongsToMany(Skill::class, 'campaign_required_skills')
            ->withPivot('minimum_proficiency_level', 'is_mandatory')
            ->withTimestamps();
    }

    /**
     * Get the certifications required for this campaign.
     */
    public function requiredCertifications()
    {
        return $this->belongsToMany(Certification::class, 'campaign_required_certifications')
            ->withPivot('is_mandatory')
            ->withTimestamps();
    }

    /**
     * Get eligible agents for this campaign based on skills and certifications.
     */
    public function eligibleAgents()
    {
        return User::where('role_id', 6) // Agent role
            ->where(function($query) {
                $query->where('status', 'validated')
                    ->orWhere('status', 'active');
            })
            ->whereDoesntHave('campaign')
            ->get()
            ->filter(function($user) {
                return $user->isEligibleFor($this);
            });
    }

    /**
     * Check if an agent meets all requirements for this campaign.
     *
     * @param User $agent
     * @return bool
     */
    public function isAgentEligible(User $agent)
    {
        return $agent->isEligibleFor($this);
    }

    /**
     * Get the calls associated with this campaign.
     */
    public function calls()
    {
        return $this->hasMany(Call::class);
    }

    /**
     * Get the call evaluations for this campaign.
     */
    public function callEvaluations()
    {
        return $this->hasManyThrough(CallEvaluation::class, Call::class);
    }

    /**
     * Get the performance metrics for this campaign.
     */
    public function performanceMetrics()
    {
        return $this->hasMany(PerformanceMetric::class);
    }

    /**
     * Get the KPI targets for this campaign.
     */
    public function kpiTargets()
    {
        return $this->hasMany(KpiTarget::class);
    }

    /**
     * Get the evaluation criteria specific to this campaign.
     */
    public function evaluationCriteria()
    {
        return $this->hasMany(EvaluationCriteria::class);
    }

    /**
     * Get all evaluation criteria applicable to this campaign (including global criteria).
     */
    public function allEvaluationCriteria()
    {
        return EvaluationCriteria::where(function($query) {
            $query->where('campaign_id', $this->id)
                  ->orWhereNull('campaign_id');
        })->where('is_active', true);
    }
}
