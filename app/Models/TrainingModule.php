<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrainingModule extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'duration',
        'credits',
        'status',
        'prerequisites',
        'content',
    ];

    protected $casts = [
        'prerequisites' => 'array',
    ];

    // Relationships
    public function sessions()
    {
        return $this->belongsToMany(TrainingSession::class, 'training_session_module')
            ->withPivot('order', 'required')
            ->withTimestamps();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Accessors & Mutators
    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
            'draft' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
            'archived' => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
        };
    }

    /**
     * Get the skills taught in this module.
     */
    public function skillsTaught()
    {
        if (!$this->skills_taught) {
            return collect();
        }

        return Skill::whereIn('id', json_decode($this->skills_taught))->get();
    }

    /**
     * Get the certifications this module prepares for.
     */
    public function certificationsProvided()
    {
        if (!$this->certifications_provided) {
            return collect();
        }

        return Certification::whereIn('id', json_decode($this->certifications_provided))->get();
    }

    /**
     * Add a skill to the module's skills taught.
     */
    public function addSkill($skillId)
    {
        $skills = $this->skills_taught ? json_decode($this->skills_taught) : [];

        if (!in_array($skillId, $skills)) {
            $skills[] = $skillId;
            $this->skills_taught = json_encode($skills);
            $this->save();
        }
    }

    /**
     * Remove a skill from the module's skills taught.
     */
    public function removeSkill($skillId)
    {
        if (!$this->skills_taught) {
            return;
        }

        $skills = json_decode($this->skills_taught);
        $skills = array_filter($skills, function($id) use ($skillId) {
            return $id != $skillId;
        });

        $this->skills_taught = json_encode(array_values($skills));
        $this->save();
    }

    /**
     * Add a certification to the module's certifications provided.
     */
    public function addCertification($certificationId)
    {
        $certifications = $this->certifications_provided ? json_decode($this->certifications_provided) : [];

        if (!in_array($certificationId, $certifications)) {
            $certifications[] = $certificationId;
            $this->certifications_provided = json_encode($certifications);
            $this->save();
        }
    }

    /**
     * Remove a certification from the module's certifications provided.
     */
    public function removeCertification($certificationId)
    {
        if (!$this->certifications_provided) {
            return;
        }

        $certifications = json_decode($this->certifications_provided);
        $certifications = array_filter($certifications, function($id) use ($certificationId) {
            return $id != $certificationId;
        });

        $this->certifications_provided = json_encode(array_values($certifications));
        $this->save();
    }
}
