<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class OnboardingTask extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'onboarding_id',
        'template_task_id',
        'name',
        'description',
        'category',
        'due_date',
        'status',
        'assigned_to',
        'completed_by',
        'completed_at',
        'completion_notes',
        'is_required',
        'order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'due_date' => 'date',
        'completed_at' => 'datetime',
        'is_required' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Get the onboarding that owns the task.
     */
    public function onboarding(): BelongsTo
    {
        return $this->belongsTo(EmployeeOnboarding::class, 'onboarding_id');
    }

    /**
     * Get the template task this was created from.
     */
    public function templateTask(): BelongsTo
    {
        return $this->belongsTo(OnboardingTemplateTask::class, 'template_task_id');
    }

    /**
     * Get the user assigned to complete this task.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who completed this task.
     */
    public function completedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'completed_by');
    }

    /**
     * Get the documents associated with this task.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(OnboardingDocument::class, 'task_id');
    }

    /**
     * Mark the task as completed.
     */
    public function complete($userId, $notes = null)
    {
        $this->status = 'completed';
        $this->completed_by = $userId;
        $this->completed_at = now();
        
        if ($notes) {
            $this->completion_notes = $notes;
        }
        
        $this->save();
        
        // Update the onboarding progress
        $this->onboarding->updateProgress();
        
        return $this;
    }

    /**
     * Check if the task is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date->isPast() && $this->status !== 'completed';
    }

    /**
     * Get the category name in a human-readable format.
     */
    public function getCategoryNameAttribute(): string
    {
        $categories = [
            'documentation' => 'Documentation',
            'training' => 'Training',
            'equipment' => 'Equipment',
            'system_access' => 'System Access',
            'orientation' => 'Orientation',
            'hr_admin' => 'HR Administration',
            'it_setup' => 'IT Setup',
            'team_introduction' => 'Team Introduction',
            'workspace' => 'Workspace Setup',
            'other' => 'Other',
        ];

        return $categories[$this->category] ?? ucfirst($this->category);
    }
}
