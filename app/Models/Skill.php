<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Skill extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the users that have this skill.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_skills')
            ->withPivot('proficiency_level', 'notes', 'acquired_at', 'last_verified_at', 'verified_by')
            ->withTimestamps();
    }

    /**
     * Get the campaigns that require this skill.
     */
    public function requiredByCampaigns()
    {
        return $this->belongsToMany(Campaign::class, 'campaign_required_skills')
            ->withPivot('minimum_proficiency_level', 'is_mandatory')
            ->withTimestamps();
    }

    /**
     * Get the training modules that teach this skill.
     */
    public function trainingModules()
    {
        return $this->belongsToMany(TrainingModule::class, 'training_module_skills')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active skills.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }
}
