<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CallCenter extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'director_id',
        'status',
        'contact_email',
        'contact_phone',
        'address',
    ];

    /**
     * Get the director of the call center
     */
    public function director(): BelongsTo
    {
        return $this->belongsTo(User::class, 'director_id');
    }

    /**
     * Get the sites belonging to this call center
     */
    public function sites(): HasMany
    {
        return $this->hasMany(Site::class);
    }

    /**
     * Get the departments belonging to this call center
     * Note: This relationship will be active once the migration is run
     */
    // public function departments(): HasMany
    // {
    //     return $this->hasMany(Department::class);
    // }
}
