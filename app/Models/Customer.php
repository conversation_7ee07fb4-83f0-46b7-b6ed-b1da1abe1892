<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    /** @use HasFactory<\Database\Factories\CustomerFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'country',
        'postal_code',
        'website',
        'industry',
        'notes',
        'contact_person',
        'contact_position',
        'contact_phone',
        'status',
        'customer_type',
        'size',
        'segment',
        'annual_revenue',
        'employee_count',
        'tax_id',
        'registration_number',
        'established_date',
        'relationship_since',
        'account_manager_id',
        'customer_satisfaction',
        'customer_tier',
        'tags',
        'custom_fields',
        'language_preference',
        'timezone'
    ];

    protected $casts = [
        'established_date' => 'date',
        'relationship_since' => 'date',
        'annual_revenue' => 'decimal:2',
        'employee_count' => 'integer',
        'tags' => 'array',
        'custom_fields' => 'array'
    ];

    /**
     * Get the campaigns associated with the customer.
     */
    public function campaigns()
    {
        return $this->hasMany(Campaign::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get the contacts associated with the customer.
     */
    public function contacts()
    {
        return $this->hasMany(CustomerContact::class);
    }

    /**
     * Get the primary contact for the customer.
     */
    public function primaryContact()
    {
        return $this->hasOne(CustomerContact::class)->where('is_primary', true);
    }

    /**
     * Get the documents associated with the customer.
     */
    public function documents()
    {
        return $this->hasMany(CustomerDocument::class);
    }

    /**
     * Get the notes associated with the customer.
     */
    public function notes()
    {
        return $this->hasMany(CustomerNote::class);
    }

    /**
     * Get the contracts associated with the customer.
     */
    public function contracts()
    {
        return $this->hasMany(CustomerContract::class);
    }

    /**
     * Get the activities associated with the customer.
     */
    public function activities()
    {
        return $this->hasMany(CustomerActivity::class);
    }

    /**
     * Get the account manager for the customer.
     */
    public function accountManager()
    {
        return $this->belongsTo(User::class, 'account_manager_id');
    }

    /**
     * Get the active contracts for the customer.
     */
    public function activeContracts()
    {
        return $this->contracts()->where('status', 'active');
    }

    /**
     * Get the active campaigns for the customer.
     */
    public function activeCampaigns()
    {
        return $this->campaigns()->where('status', 'active');
    }
}
