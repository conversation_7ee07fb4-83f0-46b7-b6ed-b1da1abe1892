<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class Report extends Model
{
    /** @use HasFactory<\Database\Factories\ReportFactory> */
    use HasFactory;

    protected $fillable = [
        'campaign_id',
        'date',
        'content',
        'response',
        'created_by',
        'sent_at',
        'file_path',
        'status',
        'title',
        'description',
        'type',
        'category',
        'tags',
        'priority',
        'metrics',
        'performance_score',
        'quality_score',
        'compliance_score',
        'approved_by',
        'approved_at',
        'approval_status',
        'approval_notes',
        'visibility',
        'is_public',
        'is_template',
        'recurrence',
        'next_scheduled_at',
        'last_generated_at',
        'report_number',
        'reportable_id',
        'reportable_type',
    ];

    protected $casts = [
        'date' => 'date',
        'sent_at' => 'datetime',
        'approved_at' => 'datetime',
        'next_scheduled_at' => 'datetime',
        'last_generated_at' => 'datetime',
        'tags' => 'array',
        'metrics' => 'array',
        'visibility' => 'array',
        'is_public' => 'boolean',
        'is_template' => 'boolean',
        'performance_score' => 'decimal:2',
        'quality_score' => 'decimal:2',
        'compliance_score' => 'decimal:2',
    ];

    // Relations
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function media()
    {
        return $this->hasMany(Media::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function comments()
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Get the parent reportable model (Site, Platform, etc.).
     */
    public function reportable()
    {
        return $this->morphTo();
    }

    /**
     * Get the overall score (average of performance, quality, and compliance)
     */
    public function getOverallScoreAttribute()
    {
        $scores = collect([
            $this->performance_score,
            $this->quality_score,
            $this->compliance_score
        ])->filter()->values();

        return $scores->count() > 0 ? round($scores->avg(), 2) : null;
    }

    /**
     * Get the status color class
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'draft' => 'gray',
            'submitted' => 'blue',
            'approved' => 'green',
            'rejected' => 'red',
            'in_review' => 'yellow',
            default => 'gray'
        };
    }

    /**
     * Get the priority color class
     */
    public function getPriorityColorAttribute()
    {
        return match($this->priority) {
            'low' => 'blue',
            'normal' => 'green',
            'high' => 'yellow',
            'urgent' => 'red',
            default => 'gray'
        };
    }

    /**
     * Check if the report is overdue
     */
    public function getIsOverdueAttribute()
    {
        if (!$this->next_scheduled_at) {
            return false;
        }

        return Carbon::parse($this->next_scheduled_at)->isPast();
    }

    /**
     * Scope a query to only include reports of a specific category
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to only include reports with specific tags
     */
    public function scopeHasTag($query, $tag)
    {
        return $query->where('tags', 'like', "%\"{$tag}\"%");
    }

    /**
     * Scope a query to only include reports with a specific approval status
     */
    public function scopeApprovalStatus($query, $status)
    {
        return $query->where('approval_status', $status);
    }

    /**
     * Scope a query to only include reports with a specific priority
     */
    public function scopePriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to only include reports that are due for generation
     */
    public function scopeDueForGeneration($query)
    {
        return $query->where('next_scheduled_at', '<=', now())
            ->whereNotNull('recurrence');
    }
}
