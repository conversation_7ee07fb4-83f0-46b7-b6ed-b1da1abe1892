<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PerformanceReview extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'reviewer_id',
        'review_date',
        'review_period_start',
        'review_period_end',
        'performance_score',
        'attendance_score',
        'communication_score',
        'teamwork_score',
        'initiative_score',
        'overall_score',
        'strengths',
        'areas_for_improvement',
        'goals',
        'comments',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'review_date' => 'date',
        'review_period_start' => 'date',
        'review_period_end' => 'date',
        'performance_score' => 'float',
        'attendance_score' => 'float',
        'communication_score' => 'float',
        'teamwork_score' => 'float',
        'initiative_score' => 'float',
        'overall_score' => 'float',
    ];

    /**
     * Get the user being reviewed.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who performed the review.
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }

    /**
     * Get the documents associated with the performance review.
     */
    public function documents()
    {
        return $this->morphMany(Media::class, 'mediable')->where('category', 'performance_review');
    }

    /**
     * Calculate the overall score based on individual scores.
     */
    public function calculateOverallScore()
    {
        $scores = [
            $this->performance_score,
            $this->attendance_score,
            $this->communication_score,
            $this->teamwork_score,
            $this->initiative_score,
        ];
        
        // Filter out null values
        $scores = array_filter($scores, function ($score) {
            return $score !== null;
        });
        
        if (count($scores) === 0) {
            return null;
        }
        
        return round(array_sum($scores) / count($scores), 2);
    }

    /**
     * Get the performance rating description based on the overall score.
     */
    public function getRatingDescription()
    {
        if ($this->overall_score === null) {
            return 'Not Rated';
        }
        
        if ($this->overall_score >= 4.5) {
            return 'Exceptional';
        } elseif ($this->overall_score >= 3.5) {
            return 'Exceeds Expectations';
        } elseif ($this->overall_score >= 2.5) {
            return 'Meets Expectations';
        } elseif ($this->overall_score >= 1.5) {
            return 'Needs Improvement';
        } else {
            return 'Unsatisfactory';
        }
    }
}
