<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Call extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'calls';

    protected $fillable = [
        'user_id',
        'campaign_id',
        'call_id',
        'customer_phone',
        'customer_name',
        'call_time',
        'duration',
        'direction',
        'status',
        'recording_url',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'call_time' => 'datetime',
        'duration' => 'integer',
        'metadata' => 'json',
    ];

    /**
     * Get the agent who handled the call.
     */
    public function agent()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the campaign associated with the call.
     */
    public function campaign()
    {
        return $this->belongsTo(Campaign::class);
    }

    /**
     * Get the evaluations for this call.
     */
    public function evaluations()
    {
        return $this->hasMany(CallEvaluation::class);
    }

    /**
     * Get the latest evaluation for this call.
     */
    public function latestEvaluation()
    {
        return $this->hasOne(CallEvaluation::class)->latest();
    }

    /**
     * Format the duration as hours:minutes:seconds.
     */
    public function getFormattedDurationAttribute()
    {
        $hours = floor($this->duration / 3600);
        $minutes = floor(($this->duration % 3600) / 60);
        $seconds = $this->duration % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * Scope a query to only include calls within a date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('call_time', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include calls for a specific agent.
     */
    public function scopeForAgent($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include calls for a specific campaign.
     */
    public function scopeForCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    /**
     * Scope a query to only include inbound calls.
     */
    public function scopeInbound($query)
    {
        return $query->where('direction', 'inbound');
    }

    /**
     * Scope a query to only include outbound calls.
     */
    public function scopeOutbound($query)
    {
        return $query->where('direction', 'outbound');
    }

    /**
     * Scope a query to only include calls with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include calls with evaluations.
     */
    public function scopeEvaluated($query)
    {
        return $query->whereHas('evaluations');
    }

    /**
     * Scope a query to only include calls without evaluations.
     */
    public function scopeNotEvaluated($query)
    {
        return $query->whereDoesntHave('evaluations');
    }
}
