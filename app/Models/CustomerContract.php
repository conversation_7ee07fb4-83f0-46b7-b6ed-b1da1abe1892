<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerContract extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'contract_number',
        'title',
        'description',
        'start_date',
        'end_date',
        'status',
        'value',
        'billing_cycle',
        'renewal_type',
        'notice_period_days',
        'terms_conditions',
        'special_clauses',
        'created_by',
        'approved_by',
        'signed_date',
        'document_path',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'signed_date' => 'date',
        'value' => 'decimal:2',
        'notice_period_days' => 'integer',
    ];

    /**
     * Get the customer that owns the contract.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user who created the contract.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved the contract.
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the campaigns associated with this contract.
     */
    public function campaigns()
    {
        return $this->hasMany(Campaign::class);
    }

    /**
     * Check if the contract is active.
     */
    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    /**
     * Check if the contract is expired.
     */
    public function getIsExpiredAttribute()
    {
        if (!$this->end_date) {
            return false;
        }

        return $this->end_date->isPast() && $this->status !== 'terminated';
    }

    /**
     * Get the remaining days until contract expiration.
     */
    public function getRemainingDaysAttribute()
    {
        if (!$this->end_date) {
            return null;
        }

        if ($this->end_date->isPast()) {
            return 0;
        }

        return now()->diffInDays($this->end_date);
    }
}
