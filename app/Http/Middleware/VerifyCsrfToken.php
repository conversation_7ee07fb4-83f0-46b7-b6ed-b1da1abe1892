<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        '/sites',
        '/sites/*'
    ];

    /**
     * Determine if the request has a valid CSRF token.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function tokensMatch($request)
    {
        // For Livewire requests, we need to handle them specially
        if ($request->header('X-Livewire')) {
            // For Livewire navigation requests, we'll skip CSRF verification
            // but ensure the session has a valid token
            if ($request->has('fingerprint') && !$request->session()->has('_token')) {
                $request->session()->regenerateToken();
            }
            return true;
        }

        // For multipart form data (file uploads), we'll also skip CSRF verification
        if (strpos($request->header('Content-Type', ''), 'multipart/form-data') !== false) {
            return true;
        }

        return parent::tokensMatch($request);
    }
}
