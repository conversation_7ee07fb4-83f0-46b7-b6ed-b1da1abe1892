<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class RefreshCsrfToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Always ensure a CSRF token exists in the session
        if (Session::isStarted() && !Session::has('_token')) {
            Session::regenerateToken();
        }

        // Process the request
        $response = $next($request);

        // Only regenerate the token for non-Livewire requests
        // This prevents token mismatch issues with Livewire
        if (!$request->header('X-Livewire') && !$request->has('fingerprint')) {
            if (Session::isStarted()) {
                // Only regenerate for GET requests to avoid issues with form submissions
                if ($request->isMethod('GET')) {
                    Session::regenerateToken();
                }
            }
        }

        return $response;
    }
}
