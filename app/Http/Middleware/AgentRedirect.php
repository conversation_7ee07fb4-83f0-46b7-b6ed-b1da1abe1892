<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AgentRedirect
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check() && Auth::user()->role_id == 6) {
            // Agent role
            
            // If trying to access admin-only routes, redirect to appropriate agent page
            $currentRoute = $request->route()->getName();
            
            // Define routes that agents should be redirected from and their destinations
            $redirectMap = [
                'users.index' => 'agents.presence',
                'campaigns.index' => 'appointments.index',
                'reports.index' => 'appointments.index',
                'statistics.index' => 'statistics.agent',
            ];
            
            if (array_key_exists($currentRoute, $redirectMap)) {
                return redirect()->route($redirectMap[$currentRoute]);
            }
        }
        
        return $next($request);
    }
}
