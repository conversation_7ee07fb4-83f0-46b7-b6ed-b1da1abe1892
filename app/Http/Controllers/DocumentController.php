<?php

namespace App\Http\Controllers;

use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

class DocumentController extends Controller
{
    /**
     * Display the specified document.
     */
    public function show($id)
    {
        $document = Media::findOrFail($id);
        $user = auth()->user();

        // Check if user has permission to view this document
        // Allow access if:
        // 1. The user owns the document
        // 2. The user is an admin (role_id 1)
        // 3. The user is a manager (role_id 3) and has access to the document's user
        if (!$this->userCanAccessDocument($user, $document)) {
            abort(403, 'Unauthorized access');
        }

        // Try different storage disks to find the file
        $filePath = $document->file_path;

        // Check if file exists in public storage
        if (Storage::disk('public')->exists($filePath)) {
            return response()->file(
                Storage::disk('public')->path($filePath),
                ['Content-Type' => $document->mime_type ?? 'application/octet-stream']
            );
        }

        // Check if file exists with absolute path (without the 'public' prefix)
        $strippedPath = str_replace('public/', '', $filePath);
        if (Storage::disk('public')->exists($strippedPath)) {
            return response()->file(
                Storage::disk('public')->path($strippedPath),
                ['Content-Type' => $document->mime_type ?? 'application/octet-stream']
            );
        }

        // Check if file exists in local storage
        if (Storage::exists($filePath)) {
            return response()->file(
                Storage::path($filePath),
                ['Content-Type' => $document->mime_type ?? 'application/octet-stream']
            );
        }

        // If we get here, the file wasn't found
        abort(404, 'File not found: ' . $filePath);
    }

    /**
     * Check if a user can access a document
     */
    private function userCanAccessDocument($user, $document)
    {
        // Admin can access all documents
        if ($user->hasRole('administrator')) {
            return true;
        }

        // User can access their own documents
        if ($document->mediable_type === 'App\\Models\\User' && $document->mediable_id === $user->id) {
            return true;
        }

        // Manager can access documents of users in their campaign
        if ($user->hasRole('platform_manager') && $document->mediable_type === 'App\\Models\\User') {
            $documentUser = \App\Models\User::find($document->mediable_id);
            if ($documentUser && $documentUser->campaign_id === $user->campaign_id) {
                return true;
            }
        }

        return false;
    }
}
