<?php

namespace App\Http\Controllers;

use App\Models\Media;
use App\Services\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DocumentVerificationController extends Controller
{
    protected $documentService;

    public function __construct(DocumentService $documentService)
    {
        $this->documentService = $documentService;
        $this->middleware('auth');
    }

    /**
     * Verify a document
     *
     * @param Request $request
     * @param Media $document
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verify(Request $request, Media $document)
    {
        $request->validate([
            'status' => 'required|in:verified,rejected',
            'rejection_reason' => 'required_if:status,rejected|nullable|string|max:500',
        ]);

        $this->documentService->verifyDocument(
            $document,
            $request->status,
            $request->rejection_reason
        );

        return redirect()->back()->with('message', 'Document verification status updated successfully.');
    }

    /**
     * Show documents pending verification
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $pendingDocuments = $this->documentService->getDocumentsPendingVerification();
        
        return view('documents.verification.index', [
            'pendingDocuments' => $pendingDocuments,
        ]);
    }

    /**
     * Show document verification details
     *
     * @param Media $document
     * @return \Illuminate\View\View
     */
    public function show(Media $document)
    {
        return view('documents.verification.show', [
            'document' => $document,
        ]);
    }
}
