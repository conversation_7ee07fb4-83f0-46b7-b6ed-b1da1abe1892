<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CsrfController extends Controller
{
    /**
     * Get a fresh CSRF token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refreshToken()
    {
        // Ensure the session is started
        if (!Session::isStarted()) {
            Session::start();
        }

        // Regenerate the CSRF token
        Session::regenerateToken();

        // Return the new token
        return response()->json([
            'token' => csrf_token(),
            'success' => true
        ]);
    }
}
