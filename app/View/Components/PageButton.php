<?php

namespace App\View\Components;

use Illuminate\View\Component;

class PageButton extends Component
{
    /**
     * The button type.
     *
     * @var string
     */
    public $type;

    /**
     * The button label.
     *
     * @var string
     */
    public $label;

    /**
     * The button action.
     *
     * @var string|null
     */
    public $action;

    /**
     * The button href.
     *
     * @var string|null
     */
    public $href;

    /**
     * Create a new component instance.
     *
     * @param  string  $type
     * @param  string  $label
     * @param  string|null  $action
     * @param  string|null  $href
     * @return void
     */
    public function __construct($type, $label, $action = null, $href = null)
    {
        $this->type = $type;
        $this->label = $label;
        $this->action = $action;
        $this->href = $href;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.page-button');
    }
}
