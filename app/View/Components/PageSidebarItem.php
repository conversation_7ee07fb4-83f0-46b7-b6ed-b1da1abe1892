<?php

namespace App\View\Components;

use App\Models\Site;
use Illuminate\View\Component;

class PageSidebarItem extends Component
{
    public $title;
    public $route;
    public $routes;
    public $display;
    public $currentRoute;
    public $bage;
    public $site;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct($title, $route, $routes, $display, $currentRoute, $bage = null, $site = null)
    {
        $this->title = $title;
        $this->route = $route;
        $this->routes = $routes;
        $this->display = $display;
        $this->currentRoute = $currentRoute;
        $this->bage = $bage;
        $this->site = $site;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Closure|string
     */
    public function render()
    {
        return view('components.page-sidebar-item');
    }
}
