<?php

namespace App\Services;

use App\Models\Media;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class DocumentService
{
    /**
     * Upload a document for a user
     *
     * @param User $user The user to upload the document for
     * @param UploadedFile $file The file to upload
     * @param string $category The document category
     * @param array $options Additional options for the document
     * @return Media The created media record
     */
    public function uploadDocument(User $user, UploadedFile $file, string $category, array $options = [])
    {
        $filePath = $file->store('media/documents/' . $category, 'public');
        
        $mediaData = [
            'mediable_id' => $user->id,
            'mediable_type' => User::class,
            'file_name' => $file->getClientOriginalName(),
            'file_path' => $filePath,
            'mime_type' => $file->getMimeType(),
            'category' => $category,
            'uploaded_by' => Auth::id(),
            'verification_status' => 'pending',
        ];
        
        // Add optional fields if provided
        if (isset($options['document_title'])) {
            $mediaData['document_title'] = $options['document_title'];
        }
        
        if (isset($options['description'])) {
            $mediaData['description'] = $options['description'];
        }
        
        if (isset($options['expiry_date'])) {
            $mediaData['expiry_date'] = $options['expiry_date'];
        }
        
        if (isset($options['tags'])) {
            $mediaData['tags'] = $options['tags'];
        }
        
        if (isset($options['department'])) {
            $mediaData['department'] = $options['department'];
        }
        
        return Media::create($mediaData);
    }
    
    /**
     * Delete a document
     *
     * @param Media $document The document to delete
     * @return bool True if the document was deleted, false otherwise
     */
    public function deleteDocument(Media $document)
    {
        // Delete the file from storage
        Storage::disk('public')->delete($document->file_path);
        
        // Delete the media record
        return $document->delete();
    }
    
    /**
     * Verify a document
     *
     * @param Media $document The document to verify
     * @param string $status The verification status (verified or rejected)
     * @param string|null $rejectionReason The reason for rejection (if status is rejected)
     * @return Media The updated media record
     */
    public function verifyDocument(Media $document, string $status, ?string $rejectionReason = null)
    {
        $document->verification_status = $status;
        $document->verified_by = Auth::id();
        $document->verified_at = now();
        
        if ($status === 'rejected' && $rejectionReason) {
            $document->rejection_reason = $rejectionReason;
        }
        
        $document->save();
        
        return $document;
    }
    
    /**
     * Get documents that are about to expire
     *
     * @param int $days The number of days to check for expiration
     * @return \Illuminate\Database\Eloquent\Collection The documents that are about to expire
     */
    public function getDocumentsAboutToExpire(int $days = 30)
    {
        return Media::aboutToExpire($days)->get();
    }
    
    /**
     * Get expired documents
     *
     * @return \Illuminate\Database\Eloquent\Collection The expired documents
     */
    public function getExpiredDocuments()
    {
        return Media::expired()->get();
    }
    
    /**
     * Get documents pending verification
     *
     * @return \Illuminate\Database\Eloquent\Collection The documents pending verification
     */
    public function getDocumentsPendingVerification()
    {
        return Media::withVerificationStatus('pending')->get();
    }
    
    /**
     * Update document metadata
     *
     * @param Media $document The document to update
     * @param array $data The data to update
     * @return Media The updated media record
     */
    public function updateDocumentMetadata(Media $document, array $data)
    {
        $document->fill($data);
        $document->save();
        
        return $document;
    }
}
