<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Cache prefix for settings
     */
    const CACHE_PREFIX = 'settings_';

    /**
     * Cache TTL in seconds (1 hour)
     */
    const CACHE_TTL = 3600;

    /**
     * Get a setting value
     *
     * @param string $group
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get(string $group, string $key, $default = null)
    {
        $cacheKey = self::CACHE_PREFIX . "{$group}.{$key}";

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($group, $key, $default) {
                return Setting::getValue($group, $key, $default);
            });
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return the default
            return $default;
        }
    }

    /**
     * Set a setting value
     *
     * @param string $group
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @param string|null $description
     * @param bool $isPublic
     * @return Setting|null
     */
    public function set(string $group, string $key, $value, string $type = 'string', ?string $description = null, bool $isPublic = false)
    {
        try {
            $setting = Setting::setValue($group, $key, $value, $type, $description, $isPublic);

            // Clear the cache for this setting
            $this->clearCache($group, $key);

            return $setting;
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return null
            return null;
        }
    }

    /**
     * Get all settings for a group
     *
     * @param string $group
     * @return array
     */
    public function getGroup(string $group)
    {
        $cacheKey = self::CACHE_PREFIX . "{$group}";

        try {
            return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($group) {
                return Setting::getGroup($group);
            });
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return empty array
            return [];
        }
    }

    /**
     * Set multiple settings at once
     *
     * @param string $group
     * @param array $settings
     * @return void
     */
    public function setGroup(string $group, array $settings)
    {
        try {
            foreach ($settings as $key => $value) {
                $this->set($group, $key, $value);
            }

            // Clear the group cache
            $this->clearGroupCache($group);
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), just return
            return;
        }
    }

    /**
     * Clear the cache for a specific setting
     *
     * @param string $group
     * @param string $key
     * @return void
     */
    public function clearCache(string $group, string $key)
    {
        Cache::forget(self::CACHE_PREFIX . "{$group}.{$key}");
        $this->clearGroupCache($group); // Also clear the group cache
    }

    /**
     * Clear the cache for an entire group
     *
     * @param string $group
     * @return void
     */
    public function clearGroupCache(string $group)
    {
        Cache::forget(self::CACHE_PREFIX . "{$group}");
    }

    /**
     * Clear all settings cache
     *
     * @return void
     */
    public function clearAllCache()
    {
        // This is a simple approach. In a production environment,
        // you might want a more sophisticated cache clearing mechanism.
        $groups = ['general', 'calls', 'campaigns', 'notifications', 'integrations'];

        foreach ($groups as $group) {
            $this->clearGroupCache($group);
        }
    }
}
