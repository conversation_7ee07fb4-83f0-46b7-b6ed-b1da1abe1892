<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;

class RegistrationNumberService
{
    /**
     * Generate a unique registration number based on the previous registration number
     * Format: XXX-YYYY-NNNN where:
     * - XXX is the role prefix (e.g., ADM for administrator, AGT for agent)
     * - YYYY is the current year
     * - NNN<PERSON> is a sequential number
     *
     * @param int $roleId The role ID of the user
     * @return string The generated registration number
     */
    public static function generate(int $roleId): string
    {
        // Get the role prefix based on the role ID
        $prefix = self::getRolePrefix($roleId);
        
        $year = date('Y');
        $fullPrefix = "{$prefix}-{$year}-";
        
        // Find the highest registration number with the current prefix and year
        $latestUser = User::where('registration_number', 'like', $fullPrefix . '%')
            ->orderBy('registration_number', 'desc')
            ->first();
        
        if ($latestUser && $latestUser->registration_number) {
            // Extract the numeric part and increment it
            $numericPart = substr($latestUser->registration_number, strlen($fullPrefix));
            $nextNumber = (int)$numericPart + 1;
        } else {
            // Start with 1 if no existing registration numbers
            $nextNumber = 1;
        }
        
        // Format with leading zeros (4 digits)
        return $fullPrefix . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * Get the role prefix based on the role ID
     *
     * @param int $roleId The role ID
     * @return string The role prefix
     */
    private static function getRolePrefix(int $roleId): string
    {
        $role = Role::find($roleId);
        
        if (!$role) {
            return 'USR'; // Default prefix for unknown roles
        }
        
        // Map role names to prefixes
        $prefixMap = [
            'administrator' => 'ADM',
            'director' => 'DIR',
            'site_manager' => 'MGR',
            'platform_manager' => 'PLT',
            'supervisor' => 'SUP',
            'quality_control' => 'QCT',
            'agent' => 'AGT',
            'it_manager' => 'ITM',
            'it_support' => 'ITS',
            'trainer' => 'TRN',
        ];
        
        return $prefixMap[$role->name] ?? strtoupper(substr($role->name, 0, 3));
    }
}
