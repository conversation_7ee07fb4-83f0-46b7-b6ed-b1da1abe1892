<?php

namespace App\Providers;

use Illuminate\Notifications\ChannelManager;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Notification;

class NotificationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Add a shouldSend method to all notification channels
        Notification::extend('mail', function ($app) {
            return $app->make(ChannelManager::class)->driver('mail')
                ->shouldSend(function ($notifiable, $notification) {
                    return $notifiable->shouldReceiveNotification('mail') && 
                           $notifiable->isNotificationTypeEnabled(class_basename($notification));
                });
        });

        Notification::extend('database', function ($app) {
            return $app->make(ChannelManager::class)->driver('database')
                ->shouldSend(function ($notifiable, $notification) {
                    return $notifiable->shouldReceiveNotification('database') && 
                           $notifiable->isNotificationTypeEnabled(class_basename($notification));
                });
        });

        Notification::extend('broadcast', function ($app) {
            return $app->make(ChannelManager::class)->driver('broadcast')
                ->shouldSend(function ($notifiable, $notification) {
                    return $notifiable->shouldReceiveNotification('broadcast') && 
                           $notifiable->isNotificationTypeEnabled(class_basename($notification));
                });
        });
    }
}