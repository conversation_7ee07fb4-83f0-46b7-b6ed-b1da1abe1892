<?php

namespace App\Providers;

use App\View\Components\PageSidebarItem;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register components
        Blade::component('page-sidebar-item', PageSidebarItem::class);
        Blade::component('page-button', \App\View\Components\PageButton::class);
        Blade::component('breadcrumb', 'components.breadcrumb');
        Blade::component('breadcrumb-link', 'components.breadcrumb-link');
        Blade::component('password-input', 'components.password-input');

        // Register Livewire components
        \Livewire\Livewire::component('human-resource.hr-index', \App\Livewire\HumanResource\HrIndex::class);
        \Livewire\Livewire::component('human-resource.hr-contracts', \App\Livewire\HumanResource\HrContracts::class);
        \Livewire\Livewire::component('human-resource.hr-performance', \App\Livewire\HumanResource\HrPerformance::class);
        \Livewire\Livewire::component('human-resource.hr-attendance', \App\Livewire\HumanResource\HrAttendance::class);

        // Register our optimized components
        \Livewire\Livewire::component('global.page-resume-optimized', \App\Livewire\Global\PageResumeOptimized::class);
        \Livewire\Livewire::component('global.page-resume-dynamic', \App\Livewire\Global\PageResumeDynamic::class);
        \Livewire\Livewire::component('agents.agent-page-enhanced', \App\Livewire\Agents\AgentPageEnhanced::class);
        \Livewire\Livewire::component('agents.agent-page-dynamic', \App\Livewire\Agents\AgentPageDynamic::class);

        // Register Call Centers components
        \Livewire\Livewire::component('call-centers.sites.site-index', \App\Livewire\CallCenters\Sites\SiteIndex::class);
        \Livewire\Livewire::component('call-centers.sites.site-create', \App\Livewire\CallCenters\Sites\SiteCreate::class);
        \Livewire\Livewire::component('call-centers.sites.site-edit', \App\Livewire\CallCenters\Sites\SiteEdit::class);
        \Livewire\Livewire::component('call-centers.sites.site-show', \App\Livewire\CallCenters\Sites\SiteShow::class);
        \Livewire\Livewire::component('call-centers.sites.site-delete', \App\Livewire\CallCenters\Sites\SiteDelete::class);

        // Register Department components
        \Livewire\Livewire::component('call-centers.departments.department-index', \App\Livewire\CallCenters\Departments\DepartmentIndex::class);


        \Livewire\Livewire::component('call-centers.departments.department-create', \App\Livewire\CallCenters\Departments\DepartmentCreate::class);
        \Livewire\Livewire::component('call-centers.departments.department-edit', \App\Livewire\CallCenters\Departments\DepartmentEdit::class);
        \Livewire\Livewire::component('call-centers.departments.department-show', \App\Livewire\CallCenters\Departments\DepartmentShow::class);
        \Livewire\Livewire::component('call-centers.departments.department-delete', \App\Livewire\CallCenters\Departments\DepartmentDelete::class);

        // Register Accountant components
        \Livewire\Livewire::component('accountant.accountant-index', \App\Livewire\Accountant\AccountantIndex::class);
        \Livewire\Livewire::component('accountant.agent-attendance', \App\Livewire\Accountant\AgentAttendance::class);
        \Livewire\Livewire::component('accountant.payment-management', \App\Livewire\Accountant\PaymentManagement::class);
        \Livewire\Livewire::component('accountant.payment-history', \App\Livewire\Accountant\PaymentHistory::class);
        \Livewire\Livewire::component('accountant.salary-calculation', \App\Livewire\Accountant\SalaryCalculation::class);
        \Livewire\Livewire::component('accountant.financial-reports', \App\Livewire\Accountant\FinancialReports::class);

        // Register Invoice components
        \Livewire\Livewire::component('accountant.invoices-index', \App\Livewire\Accountant\InvoicesIndex::class);
        \Livewire\Livewire::component('accountant.invoices-create', \App\Livewire\Accountant\InvoicesCreate::class);
        \Livewire\Livewire::component('accountant.invoices-edit', \App\Livewire\Accountant\InvoicesEdit::class);
        \Livewire\Livewire::component('accountant.invoices-show', \App\Livewire\Accountant\InvoicesShow::class);
        \Livewire\Livewire::component('accountant.invoices-delete', \App\Livewire\Accountant\InvoicesDelete::class);

        // Register User components
        \Livewire\Livewire::component('users.user-delete', \App\Livewire\Users\UserDelete::class);
        \Livewire\Livewire::component('users.user-delete-button', \App\Livewire\Users\UserDeleteButton::class);

        // Register new Appointment components
        \Livewire\Livewire::component('appointments.appointment-statistics', \App\Livewire\Appointments\AppointmentStatistics::class);
        \Livewire\Livewire::component('appointments.appointment-followups', \App\Livewire\Appointments\AppointmentFollowUps::class);
        \Livewire\Livewire::component('appointments.appointment-customer-interactions', \App\Livewire\Appointments\AppointmentCustomerInteractions::class);
    }
}
