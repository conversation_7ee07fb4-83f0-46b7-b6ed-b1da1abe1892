<?php

namespace App\Livewire\Settings;

use App\Models\Department;
use Livewire\Component;
use App\Traits\HandlePageExpiration;

class DepartmentDelete extends Component
{
    use HandlePageExpiration;
    public Department $department;
    public $hasUsers = false;
    public $hasChildren = false;

    public function mount(Department $department)
    {
        $this->department = $department;
        $this->hasUsers = $department->users()->exists();
        $this->hasChildren = $department->children()->exists();
    }

    public function deleteDepartment()
    {
        // Check if department has users
        if ($this->hasUsers) {
            session()->flash('error', 'Cannot delete department with assigned users. Please reassign users first.');
            return redirect()->route('settings.departments');
        }

        // Check if department has child departments
        if ($this->hasChildren) {
            session()->flash('error', 'Cannot delete department with child departments. Please reassign or delete child departments first.');
            return redirect()->route('settings.departments');
        }

        $this->department->delete();

        session()->flash('message', 'Department deleted successfully.');
        return redirect()->route('settings.departments');
    }

    public function render()
    {
        return view('livewire.settings.department-delete');
    }
}
