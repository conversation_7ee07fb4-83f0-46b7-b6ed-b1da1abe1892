<?php

namespace App\Livewire\Settings;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Livewire\Component;

class Notifications extends Component
{
    public bool $emailNotifications = true;
    public bool $pushNotifications = true;
    public bool $smsNotifications = false;
    
    public array $notificationTypes = [
        'system_updates' => true,
        'security_alerts' => true,
        'task_reminders' => true,
        'mentions' => true,
        'comments' => true,
        'direct_messages' => true,
    ];
    
    public function mount()
    {
        $userId = Auth::id();
        $preferences = Cache::remember("user.{$userId}.notification_preferences", 60 * 24, function () use ($userId) {
            $user = Auth::user();
            
            // Get from database or use defaults
            return [
                'emailNotifications' => $user->email_notifications ?? true,
                'pushNotifications' => $user->push_notifications ?? true,
                'smsNotifications' => $user->sms_notifications ?? false,
                'notificationTypes' => json_decode($user->notification_preferences ?? '{}', true) ?: [
                    'system_updates' => true,
                    'security_alerts' => true,
                    'task_reminders' => true,
                    'mentions' => true,
                    'comments' => true,
                    'direct_messages' => true,
                ],
            ];
        });
        
        $this->emailNotifications = $preferences['emailNotifications'];
        $this->pushNotifications = $preferences['pushNotifications'];
        $this->smsNotifications = $preferences['smsNotifications'];
        $this->notificationTypes = $preferences['notificationTypes'];
    }
    
    public function updatedEmailNotifications()
    {
        $this->updateChannelPreference('email_notifications', $this->emailNotifications);
    }
    
    public function updatedPushNotifications()
    {
        $this->updateChannelPreference('push_notifications', $this->pushNotifications);
    }
    
    public function updatedSmsNotifications()
    {
        $this->updateChannelPreference('sms_notifications', $this->smsNotifications);
    }
    
    public function updatedNotificationTypes($value, $key)
    {
        $this->updateTypePreference($key, $value);
    }
    
    private function updateChannelPreference(string $column, bool $value)
    {
        $user = Auth::user();
        $user->update([$column => $value]);
        
        // Update cache
        $userId = Auth::id();
        $preferences = Cache::get("user.{$userId}.notification_preferences", []);
        
        // Map DB column to property name
        $propertyMap = [
            'email_notifications' => 'emailNotifications',
            'push_notifications' => 'pushNotifications',
            'sms_notifications' => 'smsNotifications',
        ];
        
        $preferences[$propertyMap[$column]] = $value;
        Cache::put("user.{$userId}.notification_preferences", $preferences, 60 * 24);
        
        // Show success message
        $this->dispatch('saved');
    }
    
    private function updateTypePreference(string $type, bool $value)
    {
        $user = Auth::user();
        
        // Get current preferences
        $preferences = json_decode($user->notification_preferences ?? '{}', true) ?: [];
        $preferences[$type] = $value;
        
        // Save to database
        $user->update(['notification_preferences' => json_encode($preferences)]);
        
        // Update cache
        $userId = Auth::id();
        $cachedPreferences = Cache::get("user.{$userId}.notification_preferences", []);
        $cachedPreferences['notificationTypes'] = $preferences;
        Cache::put("user.{$userId}.notification_preferences", $cachedPreferences, 60 * 24);
        
        // Show success message
        $this->dispatch('saved');
    }
    
    public function resetToDefaults()
    {
        $defaults = [
            'emailNotifications' => true,
            'pushNotifications' => true,
            'smsNotifications' => false,
            'notificationTypes' => [
                'system_updates' => true,
                'security_alerts' => true,
                'task_reminders' => true,
                'mentions' => true,
                'comments' => true,
                'direct_messages' => true,
            ],
        ];
        
        $user = Auth::user();
        $user->update([
            'email_notifications' => $defaults['emailNotifications'],
            'push_notifications' => $defaults['pushNotifications'],
            'sms_notifications' => $defaults['smsNotifications'],
            'notification_preferences' => json_encode($defaults['notificationTypes']),
        ]);
        
        // Update properties
        $this->emailNotifications = $defaults['emailNotifications'];
        $this->pushNotifications = $defaults['pushNotifications'];
        $this->smsNotifications = $defaults['smsNotifications'];
        $this->notificationTypes = $defaults['notificationTypes'];
        
        // Update cache
        $userId = Auth::id();
        Cache::put("user.{$userId}.notification_preferences", $defaults, 60 * 24);
        
        // Show success message
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'All notification settings reset to defaults'
        ]);
    }
    
    public function render()
    {
        return view('livewire.settings.notifications');
    }
}
