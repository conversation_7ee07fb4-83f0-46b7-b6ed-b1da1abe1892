<?php

namespace App\Livewire\Settings;

use App\Livewire\Global\Page;

class SettingPage extends Page
{
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Settings';
        $this->resumeDescription = 'System settings and preferences';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Settings',
            'description' => 'System settings and preferences'
        ];

        switch ($routeName) {
            case 'settings.profile':
                $this->resumeTitle = 'Profile Settings';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Profile Settings',
                    'description' => 'Manage your personal profile information',
                    'instructions' => [
                        'Update your name, email, and other personal details',
                        'Upload a profile picture to personalize your account',
                        'All changes will be saved immediately'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Profile Settings';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'settings.password':
                $this->resumeTitle = 'Password Settings';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Password Settings',
                    'description' => 'Update your account password',
                    'instructions' => [
                        'Enter your current password for verification',
                        'Choose a strong new password with at least 8 characters',
                        'Include a mix of letters, numbers, and special characters for better security'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Password Settings';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'settings.appearance':
                $this->resumeTitle = 'Appearance Settings';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Appearance Settings',
                    'description' => 'Customize the look and feel of your interface',
                    'instructions' => [
                        'Choose between light and dark mode',
                        'Adjust color schemes and themes',
                        'Customize layout preferences'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Appearance Settings';
                $this->current_page_resume['type'] = 'infos';
                break;

            default:
                $this->resumeTitle = 'Settings';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Settings',
                    'description' => 'System settings and preferences'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Settings';
                $this->current_page_resume['type'] = 'infos';
                break;
        }
    }
    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'title' => 'Profile',
                'description' => 'Manage your profile information',
                'route' => 'settings.profile',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Password',
                'description' => 'Update your password',
                'route' => 'settings.password',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => [],
                'sections' => []
            ],
            [
                'title' => 'Appearance',
                'description' => 'Customize your interface',
                'route' => 'settings.appearance',
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'section_routes' => [],
                'sections' => []
            ],
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }
    public function render()
    {
        return view('livewire.settings.setting-page');
    }
}
