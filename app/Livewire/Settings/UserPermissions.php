<?php

namespace App\Livewire\Settings;

use Livewire\Component;
use App\Models\User;
use App\Models\Role;
use App\Services\PermissionService;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Cache;
use App\Services\PermissionCacheService;
use Spatie\Permission\Models\Permission;

class UserPermissions extends Component
{
    use WithPagination;

    public $selectedUser = null;
    public $users = [];
    public $permissions = [];
    public $selectedPermissions = [];
    public $searchTerm = '';
    public $groupedPermissions = [];
    public $permissionGroups = [];
    public $rolePermissions = [];
    public $userRole = null;

    protected $permissionService;
    protected $permissionCacheService;

    public function __construct()
    {
        $this->permissionService = app(PermissionService::class);
        $this->permissionCacheService = app(PermissionCacheService::class);
    }

    public function mount()
    {
        $this->users = User::orderBy('first_name')->get();
        if ($this->users->count() > 0) {
            $this->selectedUser = $this->users->first()->id;
            $this->loadPermissions();
        }
    }

    public function loadPermissions()
    {
        if (!$this->selectedUser) {
            return;
        }

        $user = User::find($this->selectedUser);
        if (!$user) {
            return;
        }

        $this->userRole = $user->role;

        // Get all permissions
        $this->permissions = Permission::orderBy('name')->get();
        
        // Get the user's direct permissions using proper relationship
        $directPermissions = $user->permissions()->get();
        
        // Handle admin user (role_id = 1) - admin has all permissions
        if ($user->role_id === 1) {
            $this->selectedPermissions = $this->permissions->pluck('id')->toArray();
        } else {
            $this->selectedPermissions = $this->permissionService->getPermissionIds($directPermissions);
        }

        // Get the user's role permissions for display using proper relationship
        $this->rolePermissions = $this->permissionService->getPermissionIds($user->role);

        // Group permissions by category
        $this->groupPermissions();
        
        // Force refresh the component to ensure UI updates
        $this->dispatch('refresh');
    }

    public function groupPermissions()
    {
        $permissionData = $this->permissionService->getGroupedPermissions();
        
        $this->permissions = $permissionData['permissions'];
        $this->groupedPermissions = $permissionData['groupedPermissions'];
        $this->permissionGroups = $permissionData['permissionGroups'];
    }

    public function updatedSelectedUser()
    {
        $this->loadPermissions();
    }

    public function togglePermission($permissionId)
    {
        $this->selectedPermissions = $this->permissionService->togglePermission($this->selectedPermissions, $permissionId);
    }

    public function toggleAllInGroup($group)
    {
        if (!isset($this->groupedPermissions[$group])) {
            return;
        }

        $this->selectedPermissions = $this->permissionService->toggleGroupPermissions(
            $this->selectedPermissions,
            collect($this->groupedPermissions[$group])
        );
    }

    public function savePermissions()
    {
        if (!$this->selectedUser) {
            session()->flash('error', 'No user selected.');
            return;
        }

        $user = User::find($this->selectedUser);
        if (!$user) {
            session()->flash('error', 'User not found.');
            return;
        }

        try {
            // Clear existing direct permissions first
            $user->permissions()->detach();
            
            // Sync permissions
            $user->permissions()->sync($this->selectedPermissions);
            
            // Clear permission cache for this user
            $this->permissionCacheService->clearUserPermissionsCache($user);
            
            // Clear the user's permission cache
            Cache::forget('user_permissions_' . $user->id);
            
            // Reload permissions
            $user->load('permissions');
            
            session()->flash('success', 'Direct permissions updated successfully for user: ' . $user->name);
            
            // Force refresh the component to ensure UI updates
            $this->dispatch('refresh');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to update permissions: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.settings.user-permissions');
    }
}
