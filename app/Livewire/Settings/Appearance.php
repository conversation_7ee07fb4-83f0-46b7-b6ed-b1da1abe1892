<?php

namespace App\Livewire\Settings;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Cookie;
use Livewire\Component;

class Appearance extends Component
{
    public string $theme = 'system';
    public string $fontSize = 'medium';
    public bool $reducedMotion = false;
    public bool $highContrast = false;
    
    public function mount()
    {
        // Get user preferences from cache or cookie
        $userId = Auth::id();
        $preferences = Cache::remember("user.{$userId}.preferences", 60 * 24, function () {
            return [
                'theme' => Cookie::get('theme', 'system'),
                'fontSize' => Cookie::get('fontSize', 'medium'),
                'reducedMotion' => filter_var(Cookie::get('reducedMotion', 'false'), FILTER_VALIDATE_BOOLEAN),
                'highContrast' => filter_var(Cookie::get('highContrast', 'false'), FILTER_VALIDATE_BOOLEAN),
            ];
        });
        
        $this->theme = $preferences['theme'];
        $this->fontSize = $preferences['fontSize'];
        $this->reducedMotion = $preferences['reducedMotion'];
        $this->highContrast = $preferences['highContrast'];
    }
    
    public function updatedTheme()
    {
        $this->updatePreference('theme', $this->theme);
        $this->dispatch('theme-changed', theme: $this->theme);
    }
    
    public function updatedFontSize()
    {
        $this->updatePreference('fontSize', $this->fontSize);
        $this->dispatch('font-size-changed', fontSize: $this->fontSize);
    }
    
    public function updatedReducedMotion()
    {
        $this->updatePreference('reducedMotion', $this->reducedMotion);
        $this->dispatch('reduced-motion-changed', reducedMotion: $this->reducedMotion);
    }
    
    public function updatedHighContrast()
    {
        $this->updatePreference('highContrast', $this->highContrast);
        $this->dispatch('high-contrast-changed', highContrast: $this->highContrast);
    }
    
    private function updatePreference(string $key, $value)
    {
        // Save to cookie
        Cookie::queue($key, is_bool($value) ? ($value ? 'true' : 'false') : $value, 60 * 24 * 365); // 1 year
        
        // Update cache
        $userId = Auth::id();
        $preferences = Cache::get("user.{$userId}.preferences", []);
        $preferences[$key] = $value;
        Cache::put("user.{$userId}.preferences", $preferences, 60 * 24);
        
        // Show success notification
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => ucfirst($key) . ' preference updated successfully'
        ]);
    }
    
    public function resetToDefaults()
    {
        $this->theme = 'system';
        $this->fontSize = 'medium';
        $this->reducedMotion = false;
        $this->highContrast = false;
        
        // Update all preferences
        $this->updatePreference('theme', $this->theme);
        $this->updatePreference('fontSize', $this->fontSize);
        $this->updatePreference('reducedMotion', $this->reducedMotion);
        $this->updatePreference('highContrast', $this->highContrast);
        
        // Dispatch events
        $this->dispatch('theme-changed', theme: $this->theme);
        $this->dispatch('font-size-changed', fontSize: $this->fontSize);
        $this->dispatch('reduced-motion-changed', reducedMotion: $this->reducedMotion);
        $this->dispatch('high-contrast-changed', highContrast: $this->highContrast);
        
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'All appearance settings reset to defaults'
        ]);
    }
    
    public function render()
    {
        return view('livewire.settings.appearance');
    }
}
