<?php

namespace App\Livewire\Settings;

use App\Models\Department;
use App\Models\User;
use Livewire\Component;
use App\Traits\HandlePageExpiration;

class DepartmentEdit extends Component
{
    use HandlePageExpiration;
    public Department $department;
    public $name = '';
    public $code = '';
    public $description = '';
    public $parent_id = null;
    public $manager_id = null;
    public $status = 'active';

    public function mount(Department $department)
    {
        $this->department = $department;
        $this->name = $department->name;
        $this->code = $department->code;
        $this->description = $department->description;
        $this->parent_id = $department->parent_id;
        $this->manager_id = $department->manager_id;
        $this->status = $department->status;
    }

    public function updateDepartment()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:departments,code,' . $this->department->id,
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:departments,id',
            'manager_id' => 'nullable|exists:users,id',
            'status' => 'required|in:active,inactive',
        ]);

        $this->department->update([
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'parent_id' => $this->parent_id,
            'manager_id' => $this->manager_id,
            'status' => $this->status,
        ]);

        session()->flash('message', 'Department updated successfully.');

        // Redirect back to department management
        return redirect()->route('settings.departments');
    }

    public function render()
    {
        $parentDepartments = Department::where('id', '!=', $this->department->id)
            ->orderBy('name')
            ->get();

        $managers = User::whereIn('role_id', [1, 2, 3, 4, 5, 8, 10, 12])
            ->orderBy('first_name')
            ->get();

        return view('livewire.settings.department-edit', [
            'parentDepartments' => $parentDepartments,
            'managers' => $managers,
        ]);
    }
}
