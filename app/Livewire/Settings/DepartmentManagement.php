<?php

namespace App\Livewire\Settings;

use App\Models\Department;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Str;
use App\Traits\HandlePageExpiration;

class DepartmentManagement extends Component
{
    use WithPagination, HandlePageExpiration;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $selectedDepartments = [];
    public $selectAll = false;

    // Form properties
    public $departmentId = null;
    public $name = '';
    public $code = '';
    public $description = '';
    public $parent_id = null;
    public $manager_id = null;
    public $status = 'active';

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
    ];

    // Modal states
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showDeleteModal = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'code' => 'required|string|max:50',
        'description' => 'nullable|string',
        'parent_id' => 'nullable|exists:departments,id',
        'manager_id' => 'nullable|exists:users,id',
        'status' => 'required|in:active,inactive',
    ];

    public function mount()
    {
        $this->selectedDepartments = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedDepartments = [];
            $this->selectAll = false;
        }
    }

    public function updatedSelectAll(bool $value)
    {
        $departments = $this->getDepartmentsProperty();
        $this->selectedDepartments = $value
            ? $departments->pluck('id')->map(fn($id) => (int)$id)->all()
            : [];
    }

    public function toggleDepartmentSelection(int $departmentId)
    {
        if (in_array($departmentId, $this->selectedDepartments)) {
            $this->selectedDepartments = array_diff($this->selectedDepartments, [$departmentId]);
        } else {
            $this->selectedDepartments[] = $departmentId;
        }

        $departments = $this->getDepartmentsProperty();
        $currentPageIds = $departments->pluck('id')->all();
        $this->selectAll = !empty($this->selectedDepartments) && empty(array_diff($currentPageIds, $this->selectedDepartments));
    }

    public function deleteSelected()
    {
        // Check if any selected departments have users or child departments
        $departmentsWithDependencies = Department::whereIn('id', $this->selectedDepartments)
            ->where(function($query) {
                $query->has('users')->orHas('children');
            })
            ->pluck('name')
            ->toArray();

        if (!empty($departmentsWithDependencies)) {
            session()->flash('error', 'Cannot delete the following departments because they have users or child departments: ' . implode(', ', $departmentsWithDependencies));
            return;
        }

        // Delete departments without dependencies
        Department::whereIn('id', $this->selectedDepartments)->delete();

        session()->flash('message', count($this->selectedDepartments) . ' departments deleted successfully.');
        $this->selectedDepartments = [];
        $this->selectAll = false;
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function createDepartment()
    {
        return redirect()->route('settings.departments.create');
    }

    public function editDepartment($departmentId)
    {
        return redirect()->route('settings.departments.edit', ['department' => $departmentId]);
    }

    public function deleteDepartment($departmentId)
    {
        return redirect()->route('settings.departments.delete', ['department' => $departmentId]);
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showDeleteModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->departmentId = null;
        $this->name = '';
        $this->code = '';
        $this->description = '';
        $this->parent_id = null;
        $this->manager_id = null;
        $this->status = 'active';
        $this->resetErrorBag();
    }

    // This method has been moved to DepartmentCreate component

    // These methods have been moved to DepartmentEdit and DepartmentDelete components

    protected function getDepartmentsQuery()
    {
        return Department::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('code', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function getDepartmentsProperty()
    {
        return $this->getDepartmentsQuery()->paginate($this->perPage);
    }

    public function render()
    {
        $parentDepartments = Department::where('id', '!=', $this->departmentId)
            ->orderBy('name')
            ->get();

        $managers = User::whereIn('role_id', [1, 2, 3, 4, 5, 8, 10, 12])
            ->orderBy('first_name')
            ->get();

        return view('livewire.settings.department-management', [
            'departments' => $this->departments,
            'parentDepartments' => $parentDepartments,
            'managers' => $managers,
        ]);
    }
}
