<?php

namespace App\Livewire\Settings;

use App\Services\SettingsService;
use Livewire\Component;

class Calls extends Component
{
    public $recording;
    public $queue_timeout;
    public $max_hold_time;
    public $voicemail_enabled;

    protected $rules = [
        'recording' => 'required|in:all,outbound,inbound,none',
        'queue_timeout' => 'required|integer|min:10|max:300',
        'max_hold_time' => 'required|integer|min:1|max:60',
        'voicemail_enabled' => 'required|boolean',
    ];

    public function mount()
    {
        $settingsService = app(SettingsService::class);

        try {
            // Load settings from database with defaults
            $this->recording = $settingsService->get('calls', 'recording', 'all');
            $this->queue_timeout = $settingsService->get('calls', 'queue_timeout', 60);
            $this->max_hold_time = $settingsService->get('calls', 'max_hold_time', 10);
            $this->voicemail_enabled = $settingsService->get('calls', 'voicemail_enabled', true);
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), use defaults
            $this->recording = 'all';
            $this->queue_timeout = 60;
            $this->max_hold_time = 10;
            $this->voicemail_enabled = true;
        }
    }

    public function saveSettings()
    {
        $this->validate();

        $settingsService = app(SettingsService::class);

        // Save settings to database
        $settingsService->set('calls', 'recording', $this->recording, 'string', 'Call recording setting', true);
        $settingsService->set('calls', 'queue_timeout', $this->queue_timeout, 'integer', 'Queue timeout in seconds', true);
        $settingsService->set('calls', 'max_hold_time', $this->max_hold_time, 'integer', 'Maximum hold time in minutes', true);
        $settingsService->set('calls', 'voicemail_enabled', $this->voicemail_enabled, 'boolean', 'Whether voicemail is enabled', true);

        $this->dispatch('settings-saved');
        session()->flash('message', 'Call settings saved successfully.');
    }

    public function render()
    {
        return view('livewire.settings.calls');
    }
}