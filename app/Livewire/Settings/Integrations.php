<?php

namespace App\Livewire\Settings;

use App\Services\SettingsService;
use Livewire\Component;

class Integrations extends Component
{
    public $enable_crm_integration;
    public $crm_api_key;
    public $crm_endpoint;
    public $enable_sms_integration;
    public $sms_api_key;
    public $sms_sender_id;

    protected $rules = [
        'enable_crm_integration' => 'required|boolean',
        'crm_api_key' => 'nullable|string|required_if:enable_crm_integration,1',
        'crm_endpoint' => 'nullable|url|required_if:enable_crm_integration,1',
        'enable_sms_integration' => 'required|boolean',
        'sms_api_key' => 'nullable|string|required_if:enable_sms_integration,1',
        'sms_sender_id' => 'nullable|string|required_if:enable_sms_integration,1',
    ];

    public function mount()
    {
        $settingsService = app(SettingsService::class);

        try {
            // Load settings from database with defaults
            $this->enable_crm_integration = $settingsService->get('integrations', 'enable_crm_integration', false);
            $this->crm_api_key = $settingsService->get('integrations', 'crm_api_key', '');
            $this->crm_endpoint = $settingsService->get('integrations', 'crm_endpoint', 'https://api.example.com/crm');
            $this->enable_sms_integration = $settingsService->get('integrations', 'enable_sms_integration', false);
            $this->sms_api_key = $settingsService->get('integrations', 'sms_api_key', '');
            $this->sms_sender_id = $settingsService->get('integrations', 'sms_sender_id', 'CallCenter');
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), use defaults
            $this->enable_crm_integration = false;
            $this->crm_api_key = '';
            $this->crm_endpoint = 'https://api.example.com/crm';
            $this->enable_sms_integration = false;
            $this->sms_api_key = '';
            $this->sms_sender_id = 'CallCenter';
        }
    }

    public function saveSettings()
    {
        $this->validate();

        $settingsService = app(SettingsService::class);

        // Save settings to database
        $settingsService->set('integrations', 'enable_crm_integration', $this->enable_crm_integration, 'boolean', 'Enable CRM integration', true);
        $settingsService->set('integrations', 'crm_api_key', $this->crm_api_key, 'string', 'CRM API key', false); // Not public for security
        $settingsService->set('integrations', 'crm_endpoint', $this->crm_endpoint, 'string', 'CRM API endpoint', true);
        $settingsService->set('integrations', 'enable_sms_integration', $this->enable_sms_integration, 'boolean', 'Enable SMS integration', true);
        $settingsService->set('integrations', 'sms_api_key', $this->sms_api_key, 'string', 'SMS API key', false); // Not public for security
        $settingsService->set('integrations', 'sms_sender_id', $this->sms_sender_id, 'string', 'SMS sender ID', true);

        $this->dispatch('settings-saved');
        session()->flash('message', 'Integration settings saved successfully.');
    }

    public function render()
    {
        return view('livewire.settings.integrations');
    }
}
