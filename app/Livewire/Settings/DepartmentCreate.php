<?php

namespace App\Livewire\Settings;

use App\Models\Department;
use App\Models\User;
use Illuminate\Support\Str;
use Livewire\Component;
use App\Traits\HandlePageExpiration;

class DepartmentCreate extends Component
{
    use HandlePageExpiration;
    public $name = '';
    public $code = '';
    public $description = '';
    public $parent_id = null;
    public $manager_id = null;
    public $status = 'active';

    protected $rules = [
        'name' => 'required|string|max:255',
        'code' => 'required|string|max:50|unique:departments,code',
        'description' => 'nullable|string',
        'parent_id' => 'nullable|exists:departments,id',
        'manager_id' => 'nullable|exists:users,id',
        'status' => 'required|in:active,inactive',
    ];

    public function createDepartment()
    {
        $this->validate();

        // Generate a unique code if not provided or if it's not unique
        if (empty($this->code)) {
            $this->code = Str::slug($this->name);
        }

        // Check if code is unique
        $codeExists = Department::where('code', $this->code)
            ->exists();

        if ($codeExists) {
            $this->code = $this->code . '-' . Str::random(4);
        }

        Department::create([
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'parent_id' => $this->parent_id,
            'manager_id' => $this->manager_id,
            'status' => $this->status,
        ]);

        session()->flash('message', 'Department created successfully.');

        // Redirect back to department management
        return redirect()->route('settings.departments');
    }

    public function render()
    {
        $parentDepartments = Department::where('id', '!=', null)
            ->orderBy('name')
            ->get();

        $managers = User::whereIn('role_id', [1, 2, 3, 4, 5, 8, 10, 12])
            ->orderBy('first_name')
            ->get();

        return view('livewire.settings.department-create', [
            'parentDepartments' => $parentDepartments,
            'managers' => $managers,
        ]);
    }
}
