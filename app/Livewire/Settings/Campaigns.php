<?php

namespace App\Livewire\Settings;

use App\Services\SettingsService;
use Livewire\Component;

class Campaigns extends Component
{
    public $default_campaign_type;
    public $auto_assign_agents;
    public $max_agents_per_campaign;
    public $enable_campaign_analytics;

    protected $rules = [
        'default_campaign_type' => 'required|string|in:outbound,inbound,mixed',
        'auto_assign_agents' => 'required|boolean',
        'max_agents_per_campaign' => 'required|integer|min:1|max:100',
        'enable_campaign_analytics' => 'required|boolean',
    ];

    public function mount()
    {
        $settingsService = app(SettingsService::class);

        try {
            // Load settings from database with defaults
            $this->default_campaign_type = $settingsService->get('campaigns', 'default_campaign_type', 'outbound');
            $this->auto_assign_agents = $settingsService->get('campaigns', 'auto_assign_agents', false);
            $this->max_agents_per_campaign = $settingsService->get('campaigns', 'max_agents_per_campaign', 20);
            $this->enable_campaign_analytics = $settingsService->get('campaigns', 'enable_campaign_analytics', true);
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), use defaults
            $this->default_campaign_type = 'outbound';
            $this->auto_assign_agents = false;
            $this->max_agents_per_campaign = 20;
            $this->enable_campaign_analytics = true;
        }
    }

    public function saveSettings()
    {
        $this->validate();

        $settingsService = app(SettingsService::class);

        // Save settings to database
        $settingsService->set('campaigns', 'default_campaign_type', $this->default_campaign_type, 'string', 'Default campaign type', true);
        $settingsService->set('campaigns', 'auto_assign_agents', $this->auto_assign_agents, 'boolean', 'Auto-assign agents to campaigns', true);
        $settingsService->set('campaigns', 'max_agents_per_campaign', $this->max_agents_per_campaign, 'integer', 'Maximum agents per campaign', true);
        $settingsService->set('campaigns', 'enable_campaign_analytics', $this->enable_campaign_analytics, 'boolean', 'Enable campaign analytics', true);

        $this->dispatch('settings-saved');
        session()->flash('message', 'Campaign settings saved successfully.');
    }

    public function render()
    {
        return view('livewire.settings.campaigns');
    }
}
