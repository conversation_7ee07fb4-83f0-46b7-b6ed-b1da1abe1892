<?php

namespace App\Livewire\Settings;

use App\Services\SettingsService;
use Livewire\Component;

class General extends Component
{
    public $company_name;
    public $timezone;
    public $language;
    public $business_hours;

    protected $rules = [
        'company_name' => 'required|string|max:255',
        'timezone' => 'required|string',
        'language' => 'required|string',
        'business_hours' => 'required|string',
    ];

    public function mount()
    {
        $settingsService = app(SettingsService::class);

        try {
            // Load settings from database with defaults
            $this->company_name = $settingsService->get('general', 'company_name', 'My Call Center');
            $this->timezone = $settingsService->get('general', 'timezone', 'UTC');
            $this->language = $settingsService->get('general', 'language', 'en');
            $this->business_hours = $settingsService->get('general', 'business_hours', '9-5');
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), use defaults
            $this->company_name = 'My Call Center';
            $this->timezone = 'UTC';
            $this->language = 'en';
            $this->business_hours = '9-5';
        }
    }

    public function saveSettings()
    {
        $this->validate();

        $settingsService = app(SettingsService::class);

        // Save settings to database
        $settingsService->set('general', 'company_name', $this->company_name, 'string', 'Company name', true);
        $settingsService->set('general', 'timezone', $this->timezone, 'string', 'Timezone', true);
        $settingsService->set('general', 'language', $this->language, 'string', 'Default language', true);
        $settingsService->set('general', 'business_hours', $this->business_hours, 'string', 'Business hours', true);

        $this->dispatch('settings-saved');
        session()->flash('message', 'General settings saved successfully.');
    }

    public function render()
    {
        return view('livewire.settings.general');
    }
}