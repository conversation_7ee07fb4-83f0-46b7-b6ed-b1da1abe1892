<?php

namespace App\Livewire\Appointments;

use App\Livewire\Forms\AppointmentForm;
use App\Models\Customer;
use App\Models\Campaign;
use App\Models\User;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class AppointmentCreate extends Component
{
    use WithFileUploads;

    public AppointmentForm $form;
    public $uploadError = null;

    #[On('create-appointment')]
    public function storeAppointment()
    {
        try {
            $appointment = $this->form->store();

            if ($appointment) {
                session()->flash('message', 'Appointment created successfully!');
                return $this->redirect(route('appointments.index'), navigate: true);
            }

            // If we get here, there was an error but it was already logged and added to the form errors
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error in storeAppointment: ' . $e->getMessage(), [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            session()->flash('error', 'Failed to create appointment: ' . $e->getMessage());
        }
    }

    public function updatedFormAudio()
    {
        $this->uploadError = null;

        if (!$this->form->audio) {
            return;
        }

        try {
            // Get file information
            $fileSize = $this->form->audio->getSize();
            $maxSize = 2 * 1024 * 1024; // 2MB in bytes
            $fileName = $this->form->audio->getClientOriginalName();
            $fileExtension = $this->form->audio->getClientOriginalExtension();

            // Log file information
            \Illuminate\Support\Facades\Log::info('File upload attempt:', [
                'filename' => $fileName,
                'extension' => $fileExtension,
                'size' => $fileSize,
                'maxSize' => $maxSize
            ]);

            // Check file size first (most common issue)
            if ($fileSize > $maxSize) {
                $this->uploadError = "File size exceeds maximum limit (2MB). Your file is " . round($fileSize / (1024 * 1024), 2) . "MB";
                $this->form->audio = null; // Clear the file
                return;
            }

            // Then validate file type
            $allowedTypes = ['mp3', 'wav', 'aac', 'ogg', 'flac', 'mpga'];
            if (!in_array(strtolower($fileExtension), $allowedTypes)) {
                $this->uploadError = "Invalid file type. Allowed types: MP3, WAV, AAC, OGG, FLAC";
                $this->form->audio = null; // Clear the file
                return;
            }

            // If we get here, the file is valid
            \Illuminate\Support\Facades\Log::info('File passed validation checks', [
                'filename' => $fileName
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in file upload:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $this->uploadError = "Error processing file: " . $e->getMessage();
            $this->form->audio = null; // Clear the file
        }
    }

    public function removeAudio()
    {
        // Clear the audio file
        $this->form->audio = null;
        $this->uploadError = null;

        // Log the removal
        \Illuminate\Support\Facades\Log::info('Audio file removed from create form');
    }

    public function render()
    {
        return view('livewire.appointments.appointment-create', [
            'customers' => Customer::all(),
            'campaigns' => Campaign::where('status', 'active')->get(),
            'users' => User::where('role_id', 6)->get(),
        ]);
    }
}
