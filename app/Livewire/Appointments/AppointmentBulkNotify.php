<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use Livewire\Component;

class AppointmentBulkNotify extends Component
{
    public array $appointmentIds = [];
    public string $notificationMethod = 'email';
    public string $notificationMessage = '';
    public array $appointments = [];
    
    protected $rules = [
        'notificationMethod' => 'required|in:email,sms,both',
        'notificationMessage' => 'required|string|max:1000',
    ];
    
    public function mount()
    {
        // Get appointment IDs from query string
        $this->appointmentIds = explode(',', request()->query('ids', ''));
        
        // Validate that we have appointment IDs
        if (empty($this->appointmentIds)) {
            return redirect()->route('appointments.validate', ['tab' => 'validated'])
                ->with('error', 'No appointments selected for notification.');
        }
        
        // Load appointments
        $this->appointments = Appointment::whereIn('id', $this->appointmentIds)
            ->where('status', 'validated')
            ->with(['customer', 'campaign'])
            ->get()
            ->toArray();
        
        // Redirect if no valid appointments found
        if (empty($this->appointments)) {
            return redirect()->route('appointments.validate', ['tab' => 'validated'])
                ->with('error', 'No valid appointments found for notification.');
        }
        
        // Create a generic template for multiple customers
        $campaignName = $this->appointments[0]['campaign']['name'] ?? 'our company';
        
        $this->notificationMessage = "Dear Customer,\n\n"
            . "We are pleased to confirm your upcoming appointment with {$campaignName}.\n\n"
            . "Please confirm this appointment by replying to this message or clicking the confirmation link.\n\n"
            . "Thank you for choosing our services.\n\n"
            . "Best regards,\n"
            . "The {$campaignName} Team";
    }
    
    public function sendNotifications()
    {
        $this->validate();
        
        $count = 0;
        $errors = 0;
        
        foreach ($this->appointmentIds as $appointmentId) {
            $appointment = Appointment::find($appointmentId);
            
            if ($appointment && $appointment->status === 'validated') {
                try {
                    // In a real application, you would send the actual notification here
                    // based on the selected method (email, SMS, or both)
                    
                    $appointment->update([
                        'sent_to_customer' => true,
                        'sent_to_customer_at' => now(),
                        'notification_method' => $this->notificationMethod,
                        'notification_message' => $this->notificationMessage,
                    ]);
                    
                    $count++;
                } catch (\Exception $e) {
                    $errors++;
                }
            }
        }
        
        $message = "{$count} appointments sent to customers successfully!";
        if ($errors > 0) {
            $message .= " {$errors} appointments could not be sent.";
        }
        
        return redirect()->route('appointments.validate', ['tab' => 'validated'])
            ->with('message', $message);
    }
    
    public function cancel()
    {
        return redirect()->route('appointments.validate', ['tab' => 'validated']);
    }
    
    public function render()
    {
        return view('livewire.appointments.appointment-bulk-notify');
    }
}
