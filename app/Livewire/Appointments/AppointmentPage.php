<?php

namespace App\Livewire\Appointments;

use App\Livewire\Global\Page;
use App\Models\Appointment;
use Livewire\Attributes\On;

class AppointmentPage extends Page
{
    public ?Appointment $appointment = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    #[On('to-appointment-index')]
    public function toAppointmentIndex(Appointment $appointment)
    {
        return $this->redirect(route('appointments.index'), navigate: true);
    }

    #[On('to-appointment-show')]
    public function toAppointmentShow(Appointment $appointment)
    {
        return $this->redirect(route('appointments.show', ['appointment' => $appointment], ), navigate: true);
    }

    #[On('to-appointment-edit')]
    public function toAppointmentEdit(Appointment $appointment)
    {
        return $this->redirect(route('appointments.edit', ['appointment' => $appointment], ), navigate: true);
    }

    #[On('to-appointment-delete')]
    public function toAppointmentDelete(Appointment $appointment)
    {
        return $this->redirect(route('appointments.delete', ['appointment' => $appointment], ), navigate: true);
    }

    #[On('to-appointment-create')]
    public function toAppointmentCreate()
    {
        return $this->redirect(route('appointments.create'), navigate: true);
    }

    #[On('to-appointment-statistics')]
    public function toAppointmentStatistics()
    {
        return $this->redirect(route('appointments.statistics'), navigate: true);
    }

    #[On('to-appointment-followups')]
    public function toAppointmentFollowups()
    {
        return $this->redirect(route('appointments.followups'), navigate: true);
    }

    #[On('to-appointment-customer-interactions')]
    public function toAppointmentCustomerInteractions()
    {
        return $this->redirect(route('appointments.customer-interactions'), navigate: true);
    }

    #[On('to-appointment-review')]
    public function toAppointmentReview(Appointment $appointment)
    {
        return $this->redirect(route('appointments.review', ['appointment' => $appointment]), navigate: true);
    }

    #[On('to-appointment-notify')]
    public function toAppointmentNotify(Appointment $appointment)
    {
        return $this->redirect(route('appointments.notify', ['appointment' => $appointment]), navigate: true);
    }

    #[On('to-appointment-notify-bulk')]
    public function toAppointmentNotifyBulk($ids)
    {
        return $this->redirect(route('appointments.notify-bulk', ['ids' => $ids]), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Appointments';
        $this->resumeDescription = 'Appointment management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Appointments',
            'description' => 'Comprehensive appointment management system for scheduling, tracking, and managing customer appointments. This module allows you to create appointments, validate them through quality control, and track their status throughout the customer journey.'
        ];

        switch ($routeName) {
            case 'appointments.index':
                $totalAppointments = Appointment::count();
                $pendingAppointments = Appointment::where('status', 'pending')->count();
                $validatedAppointments = Appointment::where('status', 'validated')->count();
                $rejectedAppointments = Appointment::where('status', 'rejected')->count();
                $todayAppointments = Appointment::whereDate('date', now()->toDateString())->count();

                $this->resumeTitle = 'All Appointments';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Appointment Management',
                    'description' => 'Comprehensive overview of all appointments in the system. This page provides a complete listing of appointments with filtering and search capabilities. Use this interface to monitor appointment status, track validation progress, and manage customer interactions across all campaigns.',
                    'metrics' => [
                        ['label' => 'Total Appointments', 'value' => $totalAppointments, 'change' => null],
                        ['label' => 'Pending Validation', 'value' => $pendingAppointments, 'change' => $totalAppointments > 0 ? round(($pendingAppointments / $totalAppointments) * 100) : 0],
                        ['label' => 'Validated', 'value' => $validatedAppointments, 'change' => $totalAppointments > 0 ? round(($validatedAppointments / $totalAppointments) * 100) : 0],
                        ['label' => 'Today\'s Appointments', 'value' => $todayAppointments, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'All appointments';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'appointments.create':
                $activeCampaigns = \App\Models\Campaign::where('status', 'active')->count();
                $availableAgents = \App\Models\User::role('agent')->count();

                $this->resumeTitle = 'New Appointment';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'New Appointment',
                    'description' => 'Create a new customer appointment in the system. This form allows you to schedule an appointment with all necessary information including customer details, date and time, campaign association, and agent assignment.',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Provide complete customer contact information',
                        'Select a specific date and time for the appointment',
                        'Choose the appropriate campaign for this appointment',
                        'Assign the agent responsible for this appointment',
                        'Set the initial status (typically "pending" for new appointments)',
                        'Add any relevant notes or special requirements',
                        'New appointments will require validation by quality control'
                    ],
                    'stats' => [
                        'Active Campaigns' => $activeCampaigns,
                        'Available Agents' => $availableAgents,
                        'Today\'s Appointments' => Appointment::whereDate('date', now()->toDateString())->count()
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'New appointments';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'appointments.show':
                if ($this->appointment) {
                    // Get additional appointment information
                    $campaignName = $this->appointment->campaign ? $this->appointment->campaign->name : 'Not assigned';
                    $agentName = $this->appointment->agent ? $this->appointment->agent->getFullNameAttribute() : 'Not assigned';
                    $customerName = $this->appointment->customer_name ?? 'Not specified';
                    $validatedBy = $this->appointment->validated_by ?
                        \App\Models\User::find($this->appointment->validated_by)->getFullNameAttribute() : 'Not validated';
                    $validatedDate = $this->appointment->validated_at ?
                        $this->appointment->validated_at->format('M d, Y H:i') : 'Not validated';

                    $this->resumeTitle = $customerName;
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => $customerName,
                        'subtitle' => 'Appointment Details',
                        'description' => 'Detailed information about the appointment with ' . $customerName . '. This page provides comprehensive details about the appointment\'s schedule, status, associated campaign, and customer information. Use this information to manage the appointment and prepare for customer interaction.',
                        'stats' => [
                            'Status' => ucfirst($this->appointment->status ?? 'Unknown'),
                            'Date' => $this->appointment->date ? $this->appointment->date->format('M d, Y') : 'Not set',
                            'Time' => $this->appointment->time ?? 'Not set',
                            'Campaign' => $campaignName,
                            'Agent' => $agentName,
                            'Customer Phone' => $this->appointment->customer_phone ?? 'Not provided',
                            'Customer Email' => $this->appointment->customer_email ?? 'Not provided',
                            'Validated By' => $validatedBy,
                            'Validated On' => $validatedDate,
                            'Created' => $this->appointment->created_at ? $this->appointment->created_at->format('M d, Y H:i') : 'Unknown',
                            'Last Updated' => $this->appointment->updated_at ? $this->appointment->updated_at->format('M d, Y H:i') : 'Unknown'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $this->appointment->user->getFullNameAttribute();
                    $this->current_page_resume['type'] = 'chart';
                }
                break;

            case 'appointments.edit':
                if ($this->appointment) {
                    $customerName = $this->appointment->customer_name ?? 'this appointment';
                    $campaignName = $this->appointment->campaign ? $this->appointment->campaign->name : 'Not assigned';
                    $agentName = $this->appointment->agent ? $this->appointment->agent->getFullNameAttribute() : 'Not assigned';

                    $this->resumeTitle = 'Edit: ' . $customerName;
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit: ' . $customerName,
                        'description' => 'Update information and settings for the appointment with ' . $customerName . '. This form allows you to modify all aspects of the appointment, including schedule, customer details, campaign association, and status.',
                        'instructions' => [
                            'Update only the fields that need to be changed',
                            'You can modify customer contact information if needed',
                            'Reschedule the appointment by changing date and time',
                            'Reassign to a different campaign or agent if necessary',
                            'Update the appointment status based on current situation',
                            'Add or update notes with relevant information',
                            'Status changes may trigger notifications to relevant parties'
                        ],
                        'stats' => [
                            'Current Status' => ucfirst($this->appointment->status ?? 'Unknown'),
                            'Current Campaign' => $campaignName,
                            'Current Agent' => $agentName,
                            'Scheduled For' => $this->appointment->date ?
                                $this->appointment->date->format('M d, Y') . ' at ' . $this->appointment->time : 'Not set'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $this->appointment->user->getFullNameAttribute();
                    $this->current_page_resume['type'] = 'infos';
                }
                break;

            case 'appointments.delete':
                if ($this->appointment) {
                    $customerName = $this->appointment->customer_name ?? 'this appointment';
                    $campaignName = $this->appointment->campaign ? $this->appointment->campaign->name : 'Not assigned';
                    $agentName = $this->appointment->agent ? $this->appointment->agent->getFullNameAttribute() : 'Not assigned';

                    $this->resumeTitle = 'Delete: ' . $customerName;
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => 'Delete: ' . $customerName,
                        'description' => 'You are about to permanently remove the appointment with ' . $customerName . ' from the system. This action will delete all appointment data and cannot be reversed.',
                        'warning' => 'This action cannot be undone. All appointment data, including customer information and scheduling details, will be permanently deleted. Consider changing the status to "cancelled" instead if you need to maintain a record of this appointment.',
                        'stats' => [
                            'Status' => ucfirst($this->appointment->status ?? 'Unknown'),
                            'Date' => $this->appointment->date ? $this->appointment->date->format('M d, Y') : 'Not set',
                            'Time' => $this->appointment->time ?? 'Not set',
                            'Campaign' => $campaignName,
                            'Agent' => $agentName,
                            'Created' => $this->appointment->created_at ? $this->appointment->created_at->format('M d, Y') : 'Unknown'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume['title'] = $this->appointment->user->getFullNameAttribute();
                    $this->current_page_resume['type'] = 'infos';
                }
                break;

            case 'appointments.validate':
                $pendingCount = Appointment::where('status', 'pending')->count();
                $validatedToday = Appointment::where('status', 'validated')
                    ->whereDate('updated_at', now()->toDateString())->count();
                $rejectedToday = Appointment::where('status', 'rejected')
                    ->whereDate('updated_at', now()->toDateString())->count();
                $totalValidated = Appointment::where('status', 'validated')->count();
                $totalRejected = Appointment::where('status', 'rejected')->count();

                // Customer notification metrics
                $sentToCustomer = Appointment::where('sent_to_customer', true)->count();
                $customerConfirmed = Appointment::where('customer_confirmed', true)->count();
                $awaitingConfirmation = $sentToCustomer - $customerConfirmed;
                $sendRate = $totalValidated > 0 ? round(($sentToCustomer / $totalValidated) * 100, 1) : 0;
                $confirmationRate = $sentToCustomer > 0 ? round(($customerConfirmed / $sentToCustomer) * 100, 1) : 0;

                $this->resumeTitle = 'Validation & Customer Notification';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Validation & Customer Notification',
                    'description' => 'Validate appointments and manage customer notifications in one integrated workflow. This page allows quality control personnel to review appointment details, verify information accuracy, validate appointments, and send notifications to customers. This streamlined process ensures that validated appointments are efficiently communicated to customers.',
                    'metrics' => [
                        ['label' => 'Pending Validation', 'value' => $pendingCount, 'change' => null],
                        ['label' => 'Validation Rate', 'value' => $totalValidated + $totalRejected > 0 ?
                            round(($totalValidated / ($totalValidated + $totalRejected)) * 100) : 0, 'suffix' => '%', 'change' => null],
                        ['label' => 'Sent to Customers', 'value' => $sentToCustomer, 'change' => $sendRate],
                        ['label' => 'Customer Confirmed', 'value' => $customerConfirmed, 'change' => $confirmationRate]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Validation & Notification';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'appointments.calendar':
                $todayAppointments = Appointment::whereDate('date', now()->toDateString())->count();
                $thisWeekAppointments = Appointment::whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()])->count();
                $nextWeekAppointments = Appointment::whereBetween('date', [now()->addWeek()->startOfWeek(), now()->addWeek()->endOfWeek()])->count();
                $thisMonthAppointments = Appointment::whereBetween('date', [now()->startOfMonth(), now()->endOfMonth()])->count();

                $this->resumeTitle = 'Appointment Calendar';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Appointment Calendar',
                    'description' => 'Visual calendar view of all scheduled appointments. This interactive calendar provides a comprehensive timeline of appointments, allowing you to see scheduling patterns, identify busy periods, and manage resources effectively. Use this view to plan ahead and ensure optimal appointment distribution across days and weeks.',
                    'metrics' => [
                        ['label' => 'Today\'s Appointments', 'value' => $todayAppointments, 'change' => null],
                        ['label' => 'This Week', 'value' => $thisWeekAppointments, 'change' => null],
                        ['label' => 'Next Week', 'value' => $nextWeekAppointments, 'change' => $thisWeekAppointments > 0 ?
                            round((($nextWeekAppointments - $thisWeekAppointments) / $thisWeekAppointments) * 100) : null],
                        ['label' => 'This Month', 'value' => $thisMonthAppointments, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Calendar';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'appointments.statistics':
                $totalAppointments = Appointment::count();
                $validatedAppointments = Appointment::where('status', 'validated')->count();
                $rejectedAppointments = Appointment::where('status', 'rejected')->count();
                $pendingAppointments = Appointment::where('status', 'pending')->count();
                $validationRate = $totalAppointments > 0 ? round(($validatedAppointments / $totalAppointments) * 100, 1) : 0;

                $this->resumeTitle = 'Appointment Statistics';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Appointment Statistics & Analytics',
                    'description' => 'Comprehensive analytics and statistics for all appointments in the system. This dashboard provides insights into appointment trends, validation rates, agent performance, and customer engagement metrics. Use these analytics to identify patterns, optimize scheduling, and improve overall appointment quality and outcomes.',
                    'metrics' => [
                        ['label' => 'Total Appointments', 'value' => $totalAppointments, 'change' => null],
                        ['label' => 'Validation Rate', 'value' => $validationRate, 'suffix' => '%', 'change' => null],
                        ['label' => 'Pending Validation', 'value' => $pendingAppointments, 'change' => $totalAppointments > 0 ? round(($pendingAppointments / $totalAppointments) * 100) : 0],
                        ['label' => 'Rejected', 'value' => $rejectedAppointments, 'change' => $totalAppointments > 0 ? round(($rejectedAppointments / $totalAppointments) * 100) : 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Statistics';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'appointments.followups':
                $followUpRequired = Appointment::where('follow_up_required', true)->count();
                $scheduledFollowUps = Appointment::where('follow_up_required', true)
                    ->whereNotNull('follow_up_date')
                    ->count();
                $pendingFollowUps = $followUpRequired - $scheduledFollowUps;
                $upcomingFollowUps = Appointment::where('follow_up_required', true)
                    ->whereNotNull('follow_up_date')
                    ->where('follow_up_date', '>=', now())
                    ->count();

                $this->resumeTitle = 'Appointment Follow-ups';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Appointment Follow-ups',
                    'description' => 'Manage and track follow-up appointments with customers. This interface allows you to identify appointments requiring follow-up, schedule follow-up sessions, and monitor the status of ongoing customer engagement sequences. Effective follow-up management is critical for maintaining customer relationships and maximizing conversion opportunities.',
                    'metrics' => [
                        ['label' => 'Requiring Follow-up', 'value' => $followUpRequired, 'change' => null],
                        ['label' => 'Scheduled Follow-ups', 'value' => $scheduledFollowUps, 'change' => $followUpRequired > 0 ? round(($scheduledFollowUps / $followUpRequired) * 100) : 0],
                        ['label' => 'Pending Scheduling', 'value' => $pendingFollowUps, 'change' => null],
                        ['label' => 'Upcoming Follow-ups', 'value' => $upcomingFollowUps, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Follow-ups';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'appointments.customer-interactions':
                $totalValidated = Appointment::where('status', 'validated')->count();
                $sentToCustomer = Appointment::where('sent_to_customer', true)->count();
                $customerConfirmed = Appointment::where('customer_confirmed', true)->count();
                $awaitingConfirmation = $sentToCustomer - $customerConfirmed;
                $sendRate = $totalValidated > 0 ? round(($sentToCustomer / $totalValidated) * 100, 1) : 0;
                $confirmationRate = $sentToCustomer > 0 ? round(($customerConfirmed / $sentToCustomer) * 100, 1) : 0;

                $this->resumeTitle = 'Customer Interactions';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Customer Interactions',
                    'description' => 'Manage customer interactions with appointments, including notifications, confirmations, and feedback. This interface allows you to track which appointments have been sent to customers, monitor confirmation status, and manage the customer communication workflow. Effective customer interaction management ensures higher attendance rates and better customer satisfaction.',
                    'metrics' => [
                        ['label' => 'Validated Appointments', 'value' => $totalValidated, 'change' => null],
                        ['label' => 'Sent to Customers', 'value' => $sentToCustomer, 'change' => $sendRate],
                        ['label' => 'Customer Confirmed', 'value' => $customerConfirmed, 'change' => $confirmationRate],
                        ['label' => 'Awaiting Confirmation', 'value' => $awaitingConfirmation, 'change' => null]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Customer Interactions';
                $this->current_page_resume['type'] = 'chart';
                break;

            case 'appointments.review':
                $this->resumeTitle = 'Review Appointment';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Review Appointment',
                    'description' => 'Review appointment details and either validate or reject it. This quality control step ensures that only legitimate and properly documented appointments are sent to customers.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Review';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'appointments.notify':
                $this->resumeTitle = 'Send Notification';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Send Customer Notification',
                    'description' => 'Send a notification to the customer about their validated appointment. Choose the notification method and customize the message before sending.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Notify';
                $this->current_page_resume['type'] = 'infos';
                break;

            case 'appointments.notify-bulk':
                $this->resumeTitle = 'Bulk Notifications';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Send Bulk Notifications',
                    'description' => 'Send notifications to multiple customers at once. This page allows you to efficiently notify customers about their validated appointments in a single operation.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Bulk Notify';
                $this->current_page_resume['type'] = 'infos';
                break;

            default:
                $this->resumeTitle = 'Appointments';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Appointments',
                    'description' => 'Comprehensive appointment management system for scheduling, tracking, and managing customer appointments. This module allows you to create appointments, validate them through quality control, and track their status throughout the customer journey. Use the navigation menu to access different sections of the appointment management system.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'All appointments';
                $this->current_page_resume['type'] = 'chart';
                break;
        }
    }
    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'appointment-module',
                'title' => 'Management',
                'description' => 'Appointment management',
                'route' => 'appointments.index',
                'section_routes' => ['appointments.create', 'appointments.edit', 'appointments.delete', 'appointments.show', 'appointments.calendar'],
                'display' => true,
                'authorized_permissions' => ['manage_appointment'],
                'sections' => [
                    [
                        'title' => 'List',
                        'description' => 'View all appointments',
                        'route' => 'appointments.index',
                        'display' => true,
                        'authorized_permissions' => ['show_appointment'],
                    ],
                    [
                        'title' => 'Calendar',
                        'description' => 'View appointments in calendar',
                        'route' => 'appointments.calendar',
                        'display' => true,
                        'authorized_permissions' => ['show_appointment'],
                    ],
                    [
                        'title' => 'Create',
                        'description' => 'Create an appointment',
                        'route' => 'appointments.create',
                        'display' => true,
                        'authorized_permissions' => ['create_appointment'],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit an appointment',
                        'route' => 'appointments.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_appointment'],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete an appointment',
                        'route' => 'appointments.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_appointment'],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show an appointment',
                        'route' => 'appointments.show',
                        'display' => true,
                        'authorized_permissions' => ['show_appointment'],
                    ]
                ]
            ],
            [
                'module_id' => 'appointment-module',
                'title' => 'Validation',
                'description' => 'Quality control and customer notification of appointments',
                'route' => 'appointments.validate',
                'section_routes' => ['appointments.review', 'appointments.notify', 'appointments.notify-bulk'],
                'display' => true,
                'authorized_permissions' => ['validate_appointment'],
                'sections' => [
                    [
                        'title' => 'Review',
                        'description' => 'Review an appointment',
                        'route' => 'appointments.review',
                        'display' => true,
                        'authorized_permissions' => ['validate_appointment'],
                    ],
                    [
                        'title' => 'Notify',
                        'description' => 'Send notification to customer',
                        'route' => 'appointments.notify',
                        'display' => true,
                        'authorized_permissions' => ['notify_appointment'],
                    ],
                    [
                        'title' => 'Bulk Notify',
                        'description' => 'Send notifications to multiple customers',
                        'route' => 'appointments.notify-bulk',
                        'display' => true,
                        'authorized_permissions' => ['notify_bulk_appointment'],
                    ]
                ]
            ],
            [
                'module_id' => 'appointment-module',
                'title' => 'Statistics',
                'description' => 'Appointment statistics and analytics',
                'route' => 'appointments.statistics',
                'section_routes' => [],
                'display' => true,
                'authorized_permissions' => ['show_appointment_statistics'],
                'sections' => []
            ],
            [
                'module_id' => 'appointment-module',
                'title' => 'Follow-ups',
                'description' => 'Manage appointment follow-ups',
                'route' => 'appointments.followups',
                'section_routes' => [],
                'display' => true,
                'authorized_permissions' => ['followups_appointments'],
                'sections' => []
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        if ($this->appointment) {
            return view('livewire.appointments.appointment-page');
        } else {
            return view('livewire.appointments.appointment-page', [
                'appointment' => $this->appointment,
            ]);
        }

    }
}
