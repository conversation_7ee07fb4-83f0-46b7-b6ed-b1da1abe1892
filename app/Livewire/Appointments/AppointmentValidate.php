<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class AppointmentValidate extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'scheduled_at';
    public string $sortDirection = 'desc';
    public int $perPage = 10;
    public array $selectedAppointments = [];
    public bool $selectAll = false;
    public string $activeTab = 'pending';
    // No longer need modal-related properties as we're using pages instead

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'scheduled_at', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
        'activeTab' => ['except' => 'pending', 'as' => 'tab'],
    ];

    // No validation rules needed as form handling is moved to dedicated pages

    public function mount()
    {
        $this->selectedAppointments = [];
        $this->selectAll = false;
        $this->activeTab = 'pending';
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection', 'activeTab'])) {
            $this->resetPage();
            $this->selectedAppointments = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedAppointments = $value
            ? $this->appointments->pluck('id')->map(fn($id) => (int)$id)->all()
            : [];
    }

    public function toggleAppointmentSelection(int $appointmentId)
    {
        if (in_array($appointmentId, $this->selectedAppointments)) {
            $this->selectedAppointments = array_diff($this->selectedAppointments, [$appointmentId]);
        } else {
            $this->selectedAppointments[] = $appointmentId;
        }

        $currentPageIds = $this->appointments->pluck('id')->all();
        $this->selectAll = !empty($this->selectedAppointments) && empty(array_diff($currentPageIds, $this->selectedAppointments));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedAppointments)) {
            Appointment::whereIn('id', $this->selectedAppointments)->delete();
            $this->selectedAppointments = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected appointments deleted successfully!');
            $this->resetPage();
        }
    }

    /**
     * Navigate to the review page for an appointment
     */
    public function reviewAppointment(int $appointmentId)
    {
        return redirect()->route('appointments.review', ['appointment' => $appointmentId]);
    }

    protected function getAppointmentsQuery()
    {
        $query = Appointment::query()
            ->with(['customer', 'campaign', 'user', 'validatedBy', 'rejectedBy'])
            ->orderBy($this->sortField, $this->sortDirection);

        // Filter by status based on active tab
        switch ($this->activeTab) {
            case 'pending':
                $query->where('status', 'pending');
                break;
            case 'validated':
                $query->where('status', 'validated');
                break;
            case 'rejected':
                $query->where('status', 'rejected');
                break;
        }

        // Search functionality
        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->whereHas('user', function ($userQuery) use ($searchTerm) {
                    $userQuery->where('first_name', 'like', $searchTerm)
                        ->orWhere('last_name', 'like', $searchTerm);
                })
                ->orWhereHas('customer', function ($customerQuery) use ($searchTerm) {
                    $customerQuery->where('name', 'like', $searchTerm);
                })
                ->orWhereHas('campaign', function ($campaignQuery) use ($searchTerm) {
                    $campaignQuery->where('name', 'like', $searchTerm);
                });
            });
        }

        return $query;
    }

    public function getAppointmentsProperty()
    {
        return $this->getAppointmentsQuery()->paginate($this->perPage);
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    /**
     * Navigate to the notification page for an appointment
     */
    public function notifyAppointment(int $appointmentId)
    {
        return redirect()->route('appointments.notify', ['appointment' => $appointmentId]);
    }

    /**
     * Navigate to the bulk notification page with selected appointment IDs
     */
    public function notifySelectedAppointments()
    {
        if (empty($this->selectedAppointments)) {
            session()->flash('error', 'No appointments selected.');
            return;
        }

        return redirect()->route('appointments.notify-bulk', ['ids' => implode(',', $this->selectedAppointments)]);
    }

    /**
     * Mark the appointment as confirmed by the customer
     */
    public function markAsConfirmed(int $appointmentId)
    {
        $appointment = Appointment::findOrFail($appointmentId);

        if (!$appointment->sent_to_customer) {
            session()->flash('error', 'Appointment must be sent to customer before it can be confirmed.');
            return;
        }

        try {
            $appointment->update([
                'customer_confirmed' => true,
                'customer_confirmed_at' => now(),
            ]);
            session()->flash('message', 'Appointment marked as confirmed by customer!');
        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.appointments.appointment-validate', [
            'appointments' => $this->appointments,
        ]);
    }
}
