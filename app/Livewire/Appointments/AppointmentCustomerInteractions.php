<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use App\Models\Campaign;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class AppointmentCustomerInteractions extends Component
{
    use WithPagination;
    
    public string $search = '';
    public string $agentSearch = '';
    public ?int $selectedAgentId = null;
    public ?int $selectedCampaignId = null;
    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?string $interactionStatus = null;
    
    public string $sortField = 'scheduled_at';
    public string $sortDirection = 'desc';
    public int $perPage = 10;
    
    public array $selectedAppointments = [];
    public bool $selectAll = false;
    
    // For customer notification
    public ?int $currentAppointmentId = null;
    public ?string $notificationMethod = 'email';
    public ?string $notificationMessage = null;
    
    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'agentSearch' => ['except' => '', 'as' => 'agent'],
        'selectedAgentId' => ['except' => null, 'as' => 'aid'],
        'selectedCampaignId' => ['except' => null, 'as' => 'cid'],
        'startDate' => ['except' => null, 'as' => 'from'],
        'endDate' => ['except' => null, 'as' => 'to'],
        'interactionStatus' => ['except' => null, 'as' => 'status'],
        'sortField' => ['except' => 'scheduled_at', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
    ];
    
    protected $rules = [
        'notificationMethod' => 'required|in:email,sms,both',
        'notificationMessage' => 'required|string|max:500',
    ];
    
    public function updating($field): void
    {
        if (in_array($field, ['search', 'agentSearch', 'selectedAgentId', 'selectedCampaignId', 'startDate', 'endDate', 'interactionStatus', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->resetSelection();
        }
    }
    
    public function resetSelection(): void
    {
        $this->selectedAppointments = [];
        $this->selectAll = false;
    }
    
    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }
    
    public function updatedSelectAll($value): void
    {
        if ($value) {
            $this->selectedAppointments = $this->getAppointmentsQuery()
                ->pluck('id')
                ->map(fn($id) => (string) $id)
                ->toArray();
        } else {
            $this->selectedAppointments = [];
        }
    }
    
    public function getAgentSuggestions($term = '')
    {
        return User::where('role_id', 6)
            ->whereNotNull('campaign_id')
            ->where('campaign_id', '!=', '')
            ->when($term, function ($q) use ($term) {
                $q->where(function ($query) use ($term) {
                    $query->where('first_name', 'like', '%' . $term . '%')
                        ->orWhere('last_name', 'like', '%' . $term . '%');
                });
            })
            ->orderBy('first_name')
            ->limit(10)
            ->get();
    }
    
    public function selectAgent(int $id)
    {
        $this->selectedAgentId = $id;
        $user = User::find($id);
        $this->agentSearch = $user?->getFullNameAttribute() ?? '';
    }
    
    public function updatedAgentSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedAgentId = null;
        }
    }
    
    protected function getAppointmentsQuery()
    {
        $user = auth()->user();
        
        $query = Appointment::with(['customer', 'campaign', 'user'])
            ->where('status', 'validated')
            ->orderBy($this->sortField, $this->sortDirection);
            
        // Filter by interaction status
        if ($this->interactionStatus === 'sent') {
            $query->where('sent_to_customer', true);
        } elseif ($this->interactionStatus === 'not_sent') {
            $query->where('sent_to_customer', false);
        } elseif ($this->interactionStatus === 'confirmed') {
            $query->where('customer_confirmed', true);
        } elseif ($this->interactionStatus === 'awaiting_confirmation') {
            $query->where('sent_to_customer', true)
                ->where('customer_confirmed', false);
        }
        
        // Filter by agent
        if ($this->selectedAgentId) {
            $query->where('user_id', $this->selectedAgentId);
        }
        
        // Filter by campaign
        if ($this->selectedCampaignId) {
            $query->where('campaign_id', $this->selectedCampaignId);
        }
        
        // Role-based filtering
        if (auth()->user()->role_id === 6) {
            $query->where('user_id', $user->id);
        }
        
        if (auth()->user()->role_id === 3) {
            $query->where('campaign_id', $user->campaign_id);
        }
        
        // Date range filtering
        if ($this->startDate) {
            $query->whereDate('scheduled_at', '>=', $this->startDate);
        }
        
        if ($this->endDate) {
            $query->whereDate('scheduled_at', '<=', $this->endDate);
        }
        
        // Search functionality
        if (trim($this->search)) {
            $term = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($term) {
                $q->whereHas('user', function ($userQuery) use ($term) {
                    $userQuery->where('first_name', 'like', $term)
                        ->orWhere('last_name', 'like', $term);
                })
                ->orWhereHas('customer', function ($customerQuery) use ($term) {
                    $customerQuery->where('name', 'like', $term);
                })
                ->orWhereHas('campaign', function ($campaignQuery) use ($term) {
                    $campaignQuery->where('name', 'like', $term);
                });
            });
        }
        
        return $query;
    }
    
    public function getAppointmentsProperty()
    {
        return $this->getAppointmentsQuery()->paginate($this->perPage);
    }
    
    public function openNotificationModal($appointmentId)
    {
        $this->currentAppointmentId = $appointmentId;
        $appointment = Appointment::findOrFail($appointmentId);
        
        // Pre-fill notification message
        $customer = $appointment->customer;
        $scheduledDate = $appointment->scheduled_at->format('l, F j, Y');
        $scheduledTime = $appointment->scheduled_at->format('g:i A');
        
        $this->notificationMessage = "Dear {$customer->name},\n\n"
            . "We are pleased to confirm your appointment scheduled for {$scheduledDate} at {$scheduledTime}.\n\n"
            . "Please confirm this appointment by replying to this message or clicking the confirmation link.\n\n"
            . "Thank you for choosing our services.\n\n"
            . "Best regards,\n"
            . "The {$appointment->campaign->name} Team";
    }
    
    public function closeNotificationModal()
    {
        $this->currentAppointmentId = null;
        $this->notificationMethod = 'email';
        $this->notificationMessage = null;
        $this->resetValidation();
    }
    
    public function sendNotification()
    {
        $this->validate();
        
        $appointment = Appointment::findOrFail($this->currentAppointmentId);
        
        // In a real application, you would send the actual notification here
        // For now, we'll just mark it as sent
        
        $appointment->update([
            'sent_to_customer' => true,
            'sent_to_customer_at' => now(),
        ]);
        
        session()->flash('message', 'Notification sent to customer successfully!');
        $this->closeNotificationModal();
    }
    
    public function markAsConfirmed($appointmentId)
    {
        $appointment = Appointment::findOrFail($appointmentId);
        
        if (!$appointment->sent_to_customer) {
            session()->flash('error', 'Appointment must be sent to customer before it can be confirmed.');
            return;
        }
        
        $appointment->update([
            'customer_confirmed' => true,
            'customer_confirmed_at' => now(),
        ]);
        
        session()->flash('message', 'Appointment marked as confirmed by customer!');
    }
    
    public function bulkSendNotifications()
    {
        if (empty($this->selectedAppointments)) {
            session()->flash('error', 'No appointments selected.');
            return;
        }
        
        $count = 0;
        foreach ($this->selectedAppointments as $appointmentId) {
            $appointment = Appointment::find($appointmentId);
            if ($appointment && $appointment->status === 'validated' && !$appointment->sent_to_customer) {
                $appointment->update([
                    'sent_to_customer' => true,
                    'sent_to_customer_at' => now(),
                ]);
                $count++;
            }
        }
        
        session()->flash('message', "{$count} notifications sent to customers successfully!");
        $this->resetSelection();
    }
    
    public function render()
    {
        return view('livewire.appointments.appointment-customer-interactions', [
            'appointments' => $this->appointments,
            'campaigns' => Campaign::where('status', 'active')->get(),
        ]);
    }
}
