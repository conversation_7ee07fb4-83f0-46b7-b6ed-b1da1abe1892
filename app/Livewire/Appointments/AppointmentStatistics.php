<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use App\Models\Campaign;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class AppointmentStatistics extends Component
{
    public $period = 'month';
    public $startDate;
    public $endDate;
    public $campaignId = null;
    public $agentId = null;
    public $statusFilter = null;

    // Statistics data
    public $totalAppointments = 0;
    public $validatedAppointments = 0;
    public $rejectedAppointments = 0;
    public $pendingAppointments = 0;
    public $validationRate = 0;
    public $appointmentsPerDay = 0;

    // Chart data
    public $appointmentsByStatusChartData;
    public $appointmentsByDayChartData;
    public $appointmentsByCampaignChartData;
    public $appointmentsByAgentChartData;
    public $validationRateChartData;

    protected $queryString = [
        'period' => ['except' => 'month'],
        'campaignId' => ['except' => null, 'as' => 'campaign'],
        'agentId' => ['except' => null, 'as' => 'agent'],
        'statusFilter' => ['except' => null, 'as' => 'status'],
    ];

    public function mount()
    {
        $this->setDateRange($this->period);
        $this->loadStatistics();
    }

    public function setDateRange($period)
    {
        $this->period = $period;

        switch ($period) {
            case 'today':
                $this->startDate = Carbon::today();
                $this->endDate = Carbon::today()->endOfDay();
                break;
            case 'week':
                $this->startDate = Carbon::now()->startOfWeek();
                $this->endDate = Carbon::now()->endOfWeek();
                break;
            case 'month':
                $this->startDate = Carbon::now()->startOfMonth();
                $this->endDate = Carbon::now()->endOfMonth();
                break;
            case 'quarter':
                $this->startDate = Carbon::now()->startOfQuarter();
                $this->endDate = Carbon::now()->endOfQuarter();
                break;
            case 'year':
                $this->startDate = Carbon::now()->startOfYear();
                $this->endDate = Carbon::now()->endOfYear();
                break;
            default:
                $this->startDate = Carbon::now()->startOfMonth();
                $this->endDate = Carbon::now()->endOfMonth();
                break;
        }

        $this->loadStatistics();
    }

    public function updatedCampaignId()
    {
        $this->loadStatistics();
    }

    public function updatedAgentId()
    {
        $this->loadStatistics();
    }

    public function updatedStatusFilter()
    {
        $this->loadStatistics();
    }

    public function loadStatistics()
    {
        // Ensure dates are in the correct format for database queries
        $startDate = $this->startDate instanceof Carbon ? $this->startDate->format('Y-m-d H:i:s') : $this->startDate;
        $endDate = $this->endDate instanceof Carbon ? $this->endDate->format('Y-m-d H:i:s') : $this->endDate;

        // Base query with filters
        $query = Appointment::query()
            ->whereBetween('scheduled_at', [$startDate, $endDate]);

        if ($this->campaignId) {
            $query->where('campaign_id', $this->campaignId);
        }

        if ($this->agentId) {
            $query->where('user_id', $this->agentId);
        }

        if ($this->statusFilter) {
            $query->where('status', $this->statusFilter);
        }

        // Calculate basic statistics
        $this->totalAppointments = $query->count();
        $this->validatedAppointments = (clone $query)->where('status', 'validated')->count();
        $this->rejectedAppointments = (clone $query)->where('status', 'rejected')->count();
        $this->pendingAppointments = (clone $query)->where('status', 'pending')->count();

        // Calculate validation rate
        $this->validationRate = $this->totalAppointments > 0
            ? round(($this->validatedAppointments / $this->totalAppointments) * 100, 1)
            : 0;

        // Calculate appointments per day
        // Convert to Carbon instances if needed for calculations
        $startDateCarbon = $this->startDate instanceof Carbon ? $this->startDate : Carbon::parse($this->startDate);
        $endDateCarbon = $this->endDate instanceof Carbon ? $this->endDate : Carbon::parse($this->endDate);
        $daysDiff = max(1, $startDateCarbon->diffInDays($endDateCarbon) + 1);
        $this->appointmentsPerDay = round($this->totalAppointments / $daysDiff, 1);

        // Load chart data
        $this->loadAppointmentsByStatusChart();
        $this->loadAppointmentsByDayChart();
        $this->loadAppointmentsByCampaignChart();
        $this->loadAppointmentsByAgentChart();
        $this->loadValidationRateChart();
    }

    private function loadAppointmentsByStatusChart()
    {
        // Ensure dates are in the correct format for database queries
        $startDate = $this->startDate instanceof Carbon ? $this->startDate->format('Y-m-d H:i:s') : $this->startDate;
        $endDate = $this->endDate instanceof Carbon ? $this->endDate->format('Y-m-d H:i:s') : $this->endDate;

        // This query should work for both SQLite and MySQL
        $statusData = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
            ->when($this->campaignId, function($query) {
                return $query->where('campaign_id', $this->campaignId);
            })
            ->when($this->agentId, function($query) {
                return $query->where('user_id', $this->agentId);
            })
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();

        $labels = [];
        $data = [];
        $colors = [];

        $statusColors = [
            'pending' => '#3b82f6',    // Blue
            'validated' => '#10b981',  // Green
            'rejected' => '#ef4444',   // Red
            'canceled' => '#f59e0b',   // Amber
            'completed' => '#8b5cf6',  // Purple
        ];

        foreach ($statusData as $status => $count) {
            $labels[] = ucfirst($status);
            $data[] = $count;
            $colors[] = $statusColors[$status] ?? '#6b7280'; // Gray default
        }

        $this->appointmentsByStatusChartData = json_encode([
            'series' => $data,
            'labels' => $labels,
            'colors' => $colors
        ]);
    }

    private function loadAppointmentsByDayChart()
    {
        $format = $this->period == 'year' ? 'Y-m' : 'Y-m-d';
        $labelFormat = $this->period == 'year' ? 'M Y' : 'M d';

        // Ensure dates are in the correct format for database queries
        $startDate = $this->startDate instanceof Carbon ? $this->startDate->format('Y-m-d H:i:s') : $this->startDate;
        $endDate = $this->endDate instanceof Carbon ? $this->endDate->format('Y-m-d H:i:s') : $this->endDate;

        // Check database driver to use appropriate date formatting
        $driver = DB::connection()->getDriverName();

        if ($driver === 'sqlite') {
            // SQLite date formatting
            $sqliteFormat = $this->period == 'year' ? '%Y-%m' : '%Y-%m-%d';

            $appointmentsByDay = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
                ->when($this->campaignId, function($query) {
                    return $query->where('campaign_id', $this->campaignId);
                })
                ->when($this->agentId, function($query) {
                    return $query->where('user_id', $this->agentId);
                })
                ->when($this->statusFilter, function($query) {
                    return $query->where('status', $this->statusFilter);
                })
                ->select(DB::raw("strftime('{$sqliteFormat}', scheduled_at) as date"), DB::raw('count(*) as count'))
                ->groupBy('date')
                ->orderBy('date')
                ->get();
        } else {
            // MySQL date formatting
            $appointmentsByDay = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
                ->when($this->campaignId, function($query) {
                    return $query->where('campaign_id', $this->campaignId);
                })
                ->when($this->agentId, function($query) {
                    return $query->where('user_id', $this->agentId);
                })
                ->when($this->statusFilter, function($query) {
                    return $query->where('status', $this->statusFilter);
                })
                ->select(DB::raw("DATE_FORMAT(scheduled_at, '{$format}') as date"), DB::raw('count(*) as count'))
                ->groupBy('date')
                ->orderBy('date')
                ->get();
        }

        $dates = [];
        $counts = [];

        foreach ($appointmentsByDay as $item) {
            $dates[] = Carbon::parse($item->date)->format($labelFormat);
            $counts[] = $item->count;
        }

        $this->appointmentsByDayChartData = json_encode([
            'categories' => $dates,
            'series' => [
                [
                    'name' => 'Appointments',
                    'data' => $counts
                ]
            ]
        ]);
    }

    private function loadAppointmentsByCampaignChart()
    {
        // Ensure dates are in the correct format for database queries
        $startDate = $this->startDate instanceof Carbon ? $this->startDate->format('Y-m-d H:i:s') : $this->startDate;
        $endDate = $this->endDate instanceof Carbon ? $this->endDate->format('Y-m-d H:i:s') : $this->endDate;

        // This query should work for both SQLite and MySQL
        $campaignData = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
            ->when($this->agentId, function($query) {
                return $query->where('user_id', $this->agentId);
            })
            ->when($this->statusFilter, function($query) {
                return $query->where('status', $this->statusFilter);
            })
            ->select('campaign_id', DB::raw('count(*) as count'))
            ->groupBy('campaign_id')
            ->get();

        $labels = [];
        $data = [];

        foreach ($campaignData as $item) {
            $campaign = Campaign::find($item->campaign_id);
            $labels[] = $campaign ? $campaign->name : 'Unknown';
            $data[] = $item->count;
        }

        $this->appointmentsByCampaignChartData = json_encode([
            'series' => $data,
            'labels' => $labels
        ]);
    }

    private function loadAppointmentsByAgentChart()
    {
        // Ensure dates are in the correct format for database queries
        $startDate = $this->startDate instanceof Carbon ? $this->startDate->format('Y-m-d H:i:s') : $this->startDate;
        $endDate = $this->endDate instanceof Carbon ? $this->endDate->format('Y-m-d H:i:s') : $this->endDate;

        // This query should work for both SQLite and MySQL
        $agentData = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
            ->when($this->campaignId, function($query) {
                return $query->where('campaign_id', $this->campaignId);
            })
            ->when($this->statusFilter, function($query) {
                return $query->where('status', $this->statusFilter);
            })
            ->select('user_id', DB::raw('count(*) as count'))
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        $labels = [];
        $data = [];

        foreach ($agentData as $item) {
            $agent = User::find($item->user_id);
            $labels[] = $agent ? $agent->getFullNameAttribute() : 'Unknown';
            $data[] = $item->count;
        }

        $this->appointmentsByAgentChartData = json_encode([
            'categories' => $labels,
            'series' => [
                [
                    'name' => 'Appointments',
                    'data' => $data
                ]
            ]
        ]);
    }

    private function loadValidationRateChart()
    {
        // Ensure dates are in the correct format for database queries
        $startDate = $this->startDate instanceof Carbon ? $this->startDate->format('Y-m-d H:i:s') : $this->startDate;
        $endDate = $this->endDate instanceof Carbon ? $this->endDate->format('Y-m-d H:i:s') : $this->endDate;

        // SQLite and MySQL both support CASE statements, so this should work for both
        $validationData = Appointment::whereBetween('scheduled_at', [$startDate, $endDate])
            ->when($this->campaignId, function($query) {
                return $query->where('campaign_id', $this->campaignId);
            })
            ->when($this->agentId, function($query) {
                return $query->where('user_id', $this->agentId);
            })
            ->select(
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "validated" THEN 1 ELSE 0 END) as validated'),
                DB::raw('SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected'),
                DB::raw('SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending')
            )
            ->first();

        $total = $validationData->total ?: 1; // Avoid division by zero

        $this->validationRateChartData = json_encode([
            'series' => [
                round(($validationData->validated / $total) * 100, 1),
                round(($validationData->rejected / $total) * 100, 1),
                round(($validationData->pending / $total) * 100, 1)
            ],
            'labels' => ['Validated', 'Rejected', 'Pending']
        ]);
    }

    public function render()
    {
        return view('livewire.appointments.appointment-statistics', [
            'campaigns' => Campaign::where('status', 'active')->get(),
            'agents' => User::where('role_id', 6)->get()
        ]);
    }
}
