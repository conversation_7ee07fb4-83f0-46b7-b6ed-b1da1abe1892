<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use App\Models\Campaign;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AppointmentCalendar extends Component
{
    public $currentMonth;
    public $currentYear;
    public $selectedDate;
    public $appointments = [];
    public $campaigns = [];
    public $agents = [];
    public $selectedCampaignId = null;
    public $selectedAgentId = null;
    public $viewMode = 'month'; // month, week, day
    
    protected $queryString = [
        'currentMonth' => ['except' => null],
        'currentYear' => ['except' => null],
        'selectedDate' => ['except' => null],
        'selectedCampaignId' => ['except' => null, 'as' => 'campaign'],
        'selectedAgentId' => ['except' => null, 'as' => 'agent'],
        'viewMode' => ['except' => 'month', 'as' => 'view'],
    ];
    
    public function mount()
    {
        // Set default values if not provided
        $this->currentMonth = $this->currentMonth ?? date('m');
        $this->currentYear = $this->currentYear ?? date('Y');
        $this->selectedDate = $this->selectedDate ?? date('Y-m-d');
        
        // Load campaigns and agents
        $this->loadCampaigns();
        $this->loadAgents();
        
        // Load appointments
        $this->loadAppointments();
    }
    
    public function loadCampaigns()
    {
        // If user is admin or director, show all campaigns
        if (in_array(Auth::user()->role_id, [1, 2])) {
            $this->campaigns = Campaign::where('status', 'active')->get();
        } 
        // If user is manager, show only their campaign
        elseif (Auth::user()->role_id === 3) {
            $this->campaigns = Campaign::where('id', Auth::user()->campaign_id)->get();
            $this->selectedCampaignId = Auth::user()->campaign_id;
        }
        // If user is agent, show only campaigns they are assigned to
        elseif (Auth::user()->role_id === 6) {
            $this->campaigns = Auth::user()->campaigns;
            if ($this->campaigns->count() === 1) {
                $this->selectedCampaignId = $this->campaigns->first()->id;
            }
        }
    }
    
    public function loadAgents()
    {
        // If user is admin, director, or manager, show all agents
        if (in_array(Auth::user()->role_id, [1, 2, 3, 4])) {
            $query = User::where('role_id', 6);
            
            // Filter by campaign if selected
            if ($this->selectedCampaignId) {
                $query->whereHas('campaigns', function($q) {
                    $q->where('campaigns.id', $this->selectedCampaignId);
                });
            }
            
            $this->agents = $query->get();
        } 
        // If user is agent, only show themselves
        elseif (Auth::user()->role_id === 6) {
            $this->agents = collect([Auth::user()]);
            $this->selectedAgentId = Auth::id();
        }
    }
    
    public function loadAppointments()
    {
        $query = Appointment::query();
        
        // Filter by date range based on view mode
        if ($this->viewMode === 'month') {
            $startDate = "{$this->currentYear}-{$this->currentMonth}-01";
            $endDate = date('Y-m-t', strtotime($startDate));
        } elseif ($this->viewMode === 'week') {
            $date = new \DateTime($this->selectedDate);
            $startDate = clone $date;
            $startDate->modify('this week monday');
            $endDate = clone $startDate;
            $endDate->modify('+6 days');
            $startDate = $startDate->format('Y-m-d');
            $endDate = $endDate->format('Y-m-d');
        } else { // day view
            $startDate = $this->selectedDate;
            $endDate = $this->selectedDate;
        }
        
        $query->whereBetween('scheduled_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);
        
        // Filter by campaign if selected
        if ($this->selectedCampaignId) {
            $query->where('campaign_id', $this->selectedCampaignId);
        }
        
        // Filter by agent if selected
        if ($this->selectedAgentId) {
            $query->where('user_id', $this->selectedAgentId);
        }
        
        // If user is agent, only show their appointments
        if (Auth::user()->role_id === 6) {
            $query->where('user_id', Auth::id());
        }
        
        // If user is manager, only show appointments for their campaign
        if (Auth::user()->role_id === 3) {
            $query->where('campaign_id', Auth::user()->campaign_id);
        }
        
        $this->appointments = $query->with(['user', 'campaign', 'customer'])->get();
    }
    
    public function changeMonth($direction)
    {
        $date = \DateTime::createFromFormat('Y-m-d', "{$this->currentYear}-{$this->currentMonth}-01");
        
        if ($direction === 'next') {
            $date->modify('+1 month');
        } else {
            $date->modify('-1 month');
        }
        
        $this->currentMonth = $date->format('m');
        $this->currentYear = $date->format('Y');
        
        $this->loadAppointments();
    }
    
    public function changeViewMode($mode)
    {
        $this->viewMode = $mode;
        $this->loadAppointments();
    }
    
    public function selectDate($date)
    {
        $this->selectedDate = $date;
        $this->viewMode = 'day';
        $this->loadAppointments();
    }
    
    public function updatedSelectedCampaignId()
    {
        $this->loadAgents();
        $this->loadAppointments();
    }
    
    public function updatedSelectedAgentId()
    {
        $this->loadAppointments();
    }
    
    public function getCalendarDaysProperty()
    {
        $days = [];
        $date = \DateTime::createFromFormat('Y-m-d', "{$this->currentYear}-{$this->currentMonth}-01");
        $daysInMonth = $date->format('t');
        
        // Get the first day of the month
        $firstDayOfMonth = clone $date;
        $firstDayOfWeek = $firstDayOfMonth->format('N');
        
        // Add empty days for the beginning of the month
        for ($i = 1; $i < $firstDayOfWeek; $i++) {
            $days[] = [
                'day' => null,
                'date' => null,
                'isCurrentMonth' => false,
                'appointments' => []
            ];
        }
        
        // Add days of the current month
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $currentDate = "{$this->currentYear}-{$this->currentMonth}-" . str_pad($day, 2, '0', STR_PAD_LEFT);
            $days[] = [
                'day' => $day,
                'date' => $currentDate,
                'isCurrentMonth' => true,
                'isToday' => $currentDate === date('Y-m-d'),
                'isSelected' => $currentDate === $this->selectedDate,
                'appointments' => $this->getAppointmentsForDate($currentDate)
            ];
        }
        
        // Fill the remaining days of the last week
        $lastDayOfWeek = (new \DateTime($this->currentYear . '-' . $this->currentMonth . '-' . $daysInMonth))->format('N');
        for ($i = $lastDayOfWeek + 1; $i <= 7; $i++) {
            $days[] = [
                'day' => null,
                'date' => null,
                'isCurrentMonth' => false,
                'appointments' => []
            ];
        }
        
        return $days;
    }
    
    protected function getAppointmentsForDate($date)
    {
        return $this->appointments->filter(function($appointment) use ($date) {
            return $appointment->scheduled_at->format('Y-m-d') === $date;
        });
    }
    
    public function render()
    {
        return view('livewire.appointments.appointment-calendar', [
            'calendarDays' => $this->getCalendarDaysProperty(),
        ]);
    }
}
