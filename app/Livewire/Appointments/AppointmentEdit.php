<?php

namespace App\Livewire\Appointments;

use App\Livewire\Forms\AppointmentForm;
use App\Models\User;
use App\Models\Campaign;
use App\Models\Customer;
use App\Models\Appointment;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class AppointmentEdit extends Component
{
    use WithFileUploads;
    public Appointment $appointment;
    public AppointmentForm $form;
    public $uploadError = null;

    public function mount(Appointment $appointment)
    {
        $this->appointment = $appointment;
        $this->form->setAppointment($appointment);
    }

    #[On('update-appointment')]
    public function updateAppointment()
    {
        try {
            $result = $this->form->update($this->appointment);

            if ($result) {
                session()->flash('message', 'Appointment updated successfully!');
                $this->redirect(route('appointments.show', ['appointment' => $this->appointment->id]), navigate: true);
            }

            // If we get here, there was an error but it was already logged and added to the form errors
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error in updateAppointment: ' . $e->getMessage(), [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'appointment_id' => $this->appointment->id
            ]);

            session()->flash('error', 'Failed to update appointment: ' . $e->getMessage());
        }
    }

    public function updatedFormAudio()
    {
        $this->uploadError = null;

        if (!$this->form->audio) {
            return;
        }

        try {
            // Get file information
            $fileSize = $this->form->audio->getSize();
            $maxSize = 2 * 1024 * 1024; // 2MB in bytes
            $fileName = $this->form->audio->getClientOriginalName();
            $fileExtension = $this->form->audio->getClientOriginalExtension();

            // Log file information
            \Illuminate\Support\Facades\Log::info('File upload attempt (edit):', [
                'filename' => $fileName,
                'extension' => $fileExtension,
                'size' => $fileSize,
                'maxSize' => $maxSize,
                'appointment_id' => $this->appointment->id
            ]);

            // Check file size first (most common issue)
            if ($fileSize > $maxSize) {
                $this->uploadError = "File size exceeds maximum limit (2MB). Your file is " . round($fileSize / (1024 * 1024), 2) . "MB";
                $this->form->audio = null; // Clear the file
                return;
            }

            // Then validate file type
            $allowedTypes = ['mp3', 'wav', 'aac', 'ogg', 'flac', 'mpga'];
            if (!in_array(strtolower($fileExtension), $allowedTypes)) {
                $this->uploadError = "Invalid file type. Allowed types: MP3, WAV, AAC, OGG, FLAC";
                $this->form->audio = null; // Clear the file
                return;
            }

            // If we get here, the file is valid
            \Illuminate\Support\Facades\Log::info('File passed validation checks (edit)', [
                'filename' => $fileName,
                'appointment_id' => $this->appointment->id
            ]);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error in file upload (edit):', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'appointment_id' => $this->appointment->id
            ]);

            $this->uploadError = "Error processing file: " . $e->getMessage();
            $this->form->audio = null; // Clear the file
        }
    }

    public function removeAudio()
    {
        // Clear the audio file from the form
        $this->form->audio = null;
        $this->uploadError = null;

        // Log the removal
        \Illuminate\Support\Facades\Log::info('Audio file removed from edit form', [
            'appointment_id' => $this->appointment->id
        ]);
    }

    public function removeExistingAudio()
    {
        // This will remove the existing audio file from the database
        if ($this->appointment->audio_path) {
            // Delete the file from storage
            if (\Illuminate\Support\Facades\Storage::disk('public')->exists($this->appointment->audio_path)) {
                \Illuminate\Support\Facades\Storage::disk('public')->delete($this->appointment->audio_path);
            }

            // Update the appointment record
            $this->appointment->update(['audio_path' => null]);

            // Log the removal
            \Illuminate\Support\Facades\Log::info('Existing audio file removed from appointment', [
                'appointment_id' => $this->appointment->id,
                'audio_path' => $this->appointment->audio_path
            ]);

            // Show success message
            session()->flash('message', 'Audio file removed successfully.');
        }
    }

    public function render()
    {
        return view('livewire.appointments.appointment-edit', [
            'customers' => Customer::all(),
            'campaigns' => Campaign::where('status', 'active')->get(),
            'users' => User::where('role_id', 6)->get(),
        ]);
    }
}
