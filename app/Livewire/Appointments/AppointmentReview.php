<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AppointmentReview extends Component
{
    public Appointment $appointment;
    public string $cqNotes = '';
    
    protected $rules = [
        'cqNotes' => 'nullable|string|max:1000',
    ];
    
    public function mount(Appointment $appointment)
    {
        $this->appointment = $appointment;
        $this->cqNotes = $appointment->cq_notes ?? '';
        
        // Redirect if appointment is not pending
        if ($appointment->status !== 'pending') {
            return redirect()->route('appointments.validate', ['tab' => 'pending'])
                ->with('error', 'Only pending appointments can be reviewed.');
        }
    }
    
    public function validateAppointment()
    {
        $this->validate();
        
        $this->appointment->update([
            'status' => 'validated',
            'validated_by' => Auth::id(),
            'validated_at' => now(),
            'cq_notes' => $this->cqNotes,
        ]);
        
        return redirect()->route('appointments.validate', ['tab' => 'validated'])
            ->with('message', 'Appointment validated successfully!');
    }
    
    public function rejectAppointment()
    {
        $this->validate();
        
        $this->appointment->update([
            'status' => 'rejected',
            'rejected_by' => Auth::id(),
            'rejected_at' => now(),
            'cq_notes' => $this->cqNotes,
        ]);
        
        return redirect()->route('appointments.validate', ['tab' => 'rejected'])
            ->with('message', 'Appointment rejected successfully!');
    }
    
    public function cancel()
    {
        return redirect()->route('appointments.validate', ['tab' => 'pending']);
    }
    
    public function render()
    {
        return view('livewire.appointments.appointment-review');
    }
}
