<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use Livewire\Component;

class AppointmentNotify extends Component
{
    public Appointment $appointment;
    public string $notificationMethod = 'email';
    public string $notificationMessage = '';
    
    protected $rules = [
        'notificationMethod' => 'required|in:email,sms,both',
        'notificationMessage' => 'required|string|max:1000',
    ];
    
    public function mount(Appointment $appointment)
    {
        $this->appointment = $appointment;
        
        // Redirect if appointment is not validated
        if ($appointment->status !== 'validated') {
            return redirect()->route('appointments.validate', ['tab' => 'validated'])
                ->with('error', 'Only validated appointments can be sent to customers.');
        }
        
        // Pre-fill notification message with template
        $customer = $appointment->customer;
        $scheduledDate = $appointment->scheduled_at ? $appointment->scheduled_at->format('l, F j, Y') : 'the scheduled date';
        $scheduledTime = $appointment->scheduled_at ? $appointment->scheduled_at->format('g:i A') : 'the scheduled time';
        $campaignName = $appointment->campaign ? $appointment->campaign->name : 'our company';
        
        $this->notificationMessage = "Dear {$customer->name},\n\n"
            . "We are pleased to confirm your appointment scheduled for {$scheduledDate} at {$scheduledTime}.\n\n"
            . "Please confirm this appointment by replying to this message or clicking the confirmation link.\n\n"
            . "Thank you for choosing our services.\n\n"
            . "Best regards,\n"
            . "The {$campaignName} Team";
    }
    
    public function sendNotification()
    {
        $this->validate();
        
        try {
            // In a real application, you would send the actual notification here
            // based on the selected method (email, SMS, or both)
            
            $this->appointment->update([
                'sent_to_customer' => true,
                'sent_to_customer_at' => now(),
                'notification_method' => $this->notificationMethod,
                'notification_message' => $this->notificationMessage,
            ]);
            
            return redirect()->route('appointments.validate', ['tab' => 'validated'])
                ->with('message', 'Appointment sent to customer successfully!');
        } catch (\Exception $e) {
            return redirect()->route('appointments.validate', ['tab' => 'validated'])
                ->with('error', $e->getMessage());
        }
    }
    
    public function cancel()
    {
        return redirect()->route('appointments.validate', ['tab' => 'validated']);
    }
    
    public function render()
    {
        return view('livewire.appointments.appointment-notify');
    }
}
