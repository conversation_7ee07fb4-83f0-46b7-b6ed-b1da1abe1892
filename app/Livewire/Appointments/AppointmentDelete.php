<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;

class AppointmentDelete extends Component
{
    public Appointment $appointment;
    public $name;
    #[On('destroy-appointment')]
    public function destroyAppointment()
    {
        $this->appointment->delete();

        session()->flash('message', 'Selected appointments deleted successfully!');

        return $this->redirect(route('appointments.index'), navigate: true);
    }

    public function mount(Appointment $appointment)
    {
        $this->name = $appointment->user->getFullNameAttribute();
    }
    public function render()
    {
        return view('livewire.appointments.appointment-delete');
    }
}

