<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class AppointmentIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $agentSearch = '';
    public ?int $selectedAgentId = null;
    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?string $statusFilter = null;

    public string $sortField = 'scheduled_at';
    public string $sortDirection = 'desc';
    public int $perPage = 10;

    public array $selectedAppointments = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'agentSearch' => ['except' => '', 'as' => 'agent'],
        'selectedAgentId' => ['except' => null, 'as' => 'aid'],
        'startDate' => ['except' => null, 'as' => 'from'],
        'endDate' => ['except' => null, 'as' => 'to'],
        'statusFilter' => ['except' => null, 'as' => 'status'],
        'sortField' => ['except' => 'scheduled_at', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
    ];

    public function updating($field): void
    {
        if (in_array($field, ['search', 'agentSearch', 'selectedAgentId', 'startDate', 'endDate', 'statusFilter', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->resetSelection();
        }
    }

    public function mount(): void
    {
        $this->resetSelection();
    }

    public function resetSelection(): void
    {
        $this->selectedAppointments = [];
        $this->selectAll = false;
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedAppointments = $value
            ? $this->appointments->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleAppointmentSelection(int $appointmentId)
    {
        if (in_array($appointmentId, $this->selectedAppointments)) {
            $this->selectedAppointments = array_diff($this->selectedAppointments, [$appointmentId]);
        } else {
            $this->selectedAppointments[] = $appointmentId;
        }

        $currentPageIds = $this->appointments->pluck('id')->all();
        $this->selectAll = !empty($this->selectedAppointments) && empty(array_diff($currentPageIds, $this->selectedAppointments));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedAppointments)) {
            Appointment::whereIn('id', $this->selectedAppointments)->delete();
            $this->selectedAppointments = [];
            $this->selectAll = false;
            session()->flash('message', 'Utilisateurs sélectionnés supprimés avec succès !');
            $this->resetPage();
        }
    }

    protected function getAppointmentsQuery()
    {
        $user = auth()->user();

        $query = Appointment::with(['customer', 'campaign', 'user', 'validatedBy', 'rejectedBy'])
            ->orderBy($this->sortField, $this->sortDirection);

        // Filter by agent
        if ($this->selectedAgentId) {
            $query->where('user_id', $this->selectedAgentId);
        }

        // Role-based filtering
        if (auth()->user()->hasRole('agent')) {
            $query->where('user_id', $user->id);
        }

        if (auth()->user()->hasRole('platform_manager')) {
            $query->where('campaign_id', $user->campaign_id);
        }

        // Date range filtering
        if ($this->startDate) {
            $query->whereDate('scheduled_at', '>=', $this->startDate);
        }

        if ($this->endDate) {
            $query->whereDate('scheduled_at', '<=', $this->endDate);
        }

        // Status filtering
        if ($this->statusFilter) {
            $query->where('status', $this->statusFilter);
        }

        // Enhanced search functionality
        if (trim($this->search)) {
            $term = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($term) {
                // Search in user (agent) names
                $q->whereHas('user', function ($userQuery) use ($term) {
                    $userQuery->where('first_name', 'like', $term)
                        ->orWhere('last_name', 'like', $term)
                        ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", [$term])
                        ->orWhereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", [$term]);
                })
                // Search in customer names and details
                ->orWhereHas('customer', function ($customerQuery) use ($term) {
                    $customerQuery->where('name', 'like', $term)
                        ->orWhere('email', 'like', $term)
                        ->orWhere('phone', 'like', $term)
                        ->orWhere('address', 'like', $term);
                })
                // Search in campaign names
                ->orWhereHas('campaign', function ($campaignQuery) use ($term) {
                    $campaignQuery->where('name', 'like', $term)
                        ->orWhere('description', 'like', $term);
                })
                // Search in appointment notes and details
                ->orWhere('notes', 'like', $term)
                ->orWhere('cq_notes', 'like', $term)
                ->orWhere('description', 'like', $term)
                ->orWhere('status', 'like', $term)
                // Search in appointment ID (if the search term is numeric)
                ->orWhere(function($idQuery) use ($term) {
                    $cleanTerm = str_replace('%', '', $term);
                    if (is_numeric($cleanTerm)) {
                        $idQuery->where('id', '=', $cleanTerm);
                    }
                });
            });
        }

        return $query;
    }

    public function getAppointmentsProperty()
    {
        return $this->getAppointmentsQuery()->paginate($this->perPage);
    }


    public function getFilteredAgentsProperty()
    {
        $term = trim($this->agentSearch);

        $user = auth()->user();

        // If the user is an agent, they can only see their own appointments
        if ($user->hasRole('agent')) {
            return User::where('id', $user->id)
                ->get();
        }

        // For other roles, allow searching for agents
        $query = User::role('agent'); // Only get users with agent role

        // If there's a search term, filter by name
        if (!empty($term)) {
            $query->where(function ($q) use ($term) {
                $q->where('first_name', 'like', '%' . $term . '%')
                  ->orWhere('last_name', 'like', '%' . $term . '%')
                  // Also search for full name matches
                  ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ['%' . $term . '%']);
            });
        }

        // Campaign manager can only see agents in their campaign
        if ($user->hasRole('platform_manager') && $user->campaign_id) {
            $query->where('campaign_id', $user->campaign_id);
        }

        return $query->orderBy('first_name')
            ->limit(10)
            ->get();
    }

    public function selectAgent(int $id)
    {
        $user = User::find($id);

        if ($user) {
            $this->selectedAgentId = $id;
            $this->agentSearch = $user->getFullNameAttribute();
        } else {
            $this->selectedAgentId = null;
            $this->agentSearch = '';
        }

        // Reset pagination when selecting an agent
        $this->resetPage();
    }

    public function updatedAgentSearch(string $value)
    {
        // If the search field is cleared, reset the agent selection
        if (trim($value) === '') {
            $this->selectedAgentId = null;
        }

        // If the user is typing, we need to check if they're typing a name that matches an agent
        // This helps with direct typing of agent names without clicking the dropdown
        if (!empty(trim($value)) && !$this->selectedAgentId) {
            $exactMatch = User::role('agent')
                ->where(function($query) use ($value) {
                    $query->whereRaw("CONCAT(first_name, ' ', last_name) = ?", [trim($value)])
                        ->orWhereRaw("CONCAT(last_name, ' ', first_name) = ?", [trim($value)]);
                })
                ->first();

            if ($exactMatch) {
                $this->selectedAgentId = $exactMatch->id;
            }
        }
    }

    // Add a method to clear the agent filter
    public function clearAgentFilter()
    {
        $this->selectedAgentId = null;
        $this->agentSearch = '';
        $this->resetPage();
    }

    // Add a method to clear the search
    public function clearSearch()
    {
        $this->search = '';
        $this->resetPage();
    }

    // Add a method to clear the date filter
    public function clearDateFilter()
    {
        $this->startDate = null;
        $this->endDate = null;
        $this->resetPage();
    }

    // Add a method to clear all filters
    public function clearAllFilters()
    {
        $this->search = '';
        $this->selectedAgentId = null;
        $this->agentSearch = '';
        $this->startDate = null;
        $this->endDate = null;
        $this->statusFilter = '';
        $this->resetPage();
    }

    public function render()
    {
        return view('livewire.appointments.appointment-index', [
            'appointments' => $this->appointments,
            'filteredAgents' => $this->filteredAgents,
        ]);
    }
}

