<?php

namespace App\Livewire\Appointments;

use App\Models\Appointment;
use App\Models\Campaign;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class AppointmentFollowUps extends Component
{
    use WithPagination;
    
    public string $search = '';
    public string $agentSearch = '';
    public ?int $selectedAgentId = null;
    public ?int $selectedCampaignId = null;
    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?string $followUpStatus = null;
    
    public string $sortField = 'scheduled_at';
    public string $sortDirection = 'desc';
    public int $perPage = 10;
    
    public array $selectedAppointments = [];
    public bool $selectAll = false;
    
    // For follow-up scheduling
    public ?int $currentAppointmentId = null;
    public ?string $followUpDate = null;
    public ?string $followUpTime = null;
    public ?string $followUpNotes = null;
    
    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'agentSearch' => ['except' => '', 'as' => 'agent'],
        'selectedAgentId' => ['except' => null, 'as' => 'aid'],
        'selectedCampaignId' => ['except' => null, 'as' => 'cid'],
        'startDate' => ['except' => null, 'as' => 'from'],
        'endDate' => ['except' => null, 'as' => 'to'],
        'followUpStatus' => ['except' => null, 'as' => 'status'],
        'sortField' => ['except' => 'scheduled_at', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
    ];
    
    protected $rules = [
        'followUpDate' => 'required|date|after:today',
        'followUpTime' => 'required',
        'followUpNotes' => 'nullable|string|max:500',
    ];
    
    public function updating($field): void
    {
        if (in_array($field, ['search', 'agentSearch', 'selectedAgentId', 'selectedCampaignId', 'startDate', 'endDate', 'followUpStatus', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->resetSelection();
        }
    }
    
    public function resetSelection(): void
    {
        $this->selectedAppointments = [];
        $this->selectAll = false;
    }
    
    public function sortBy($field): void
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }
    
    public function updatedSelectAll($value): void
    {
        if ($value) {
            $this->selectedAppointments = $this->getAppointmentsQuery()
                ->pluck('id')
                ->map(fn($id) => (string) $id)
                ->toArray();
        } else {
            $this->selectedAppointments = [];
        }
    }
    
    public function getAgentSuggestions($term = '')
    {
        return User::where('role_id', 6)
            ->whereNotNull('campaign_id')
            ->where('campaign_id', '!=', '')
            ->when($term, function ($q) use ($term) {
                $q->where(function ($query) use ($term) {
                    $query->where('first_name', 'like', '%' . $term . '%')
                        ->orWhere('last_name', 'like', '%' . $term . '%');
                });
            })
            ->orderBy('first_name')
            ->limit(10)
            ->get();
    }
    
    public function selectAgent(int $id)
    {
        $this->selectedAgentId = $id;
        $user = User::find($id);
        $this->agentSearch = $user?->getFullNameAttribute() ?? '';
    }
    
    public function updatedAgentSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedAgentId = null;
        }
    }
    
    protected function getAppointmentsQuery()
    {
        $user = auth()->user();
        
        $query = Appointment::with(['customer', 'campaign', 'user'])
            ->where('follow_up_required', true)
            ->orderBy($this->sortField, $this->sortDirection);
            
        // Filter by follow-up status
        if ($this->followUpStatus === 'scheduled') {
            $query->whereNotNull('follow_up_date');
        } elseif ($this->followUpStatus === 'pending') {
            $query->whereNull('follow_up_date');
        } elseif ($this->followUpStatus === 'upcoming') {
            $query->whereNotNull('follow_up_date')
                ->where('follow_up_date', '>=', now());
        } elseif ($this->followUpStatus === 'overdue') {
            $query->whereNotNull('follow_up_date')
                ->where('follow_up_date', '<', now());
        }
        
        // Filter by agent
        if ($this->selectedAgentId) {
            $query->where('user_id', $this->selectedAgentId);
        }
        
        // Filter by campaign
        if ($this->selectedCampaignId) {
            $query->where('campaign_id', $this->selectedCampaignId);
        }
        
        // Role-based filtering
        if (auth()->user()->role_id === 6) {
            $query->where('user_id', $user->id);
        }
        
        if (auth()->user()->role_id === 3) {
            $query->where('campaign_id', $user->campaign_id);
        }
        
        // Date range filtering
        if ($this->startDate) {
            $query->whereDate('scheduled_at', '>=', $this->startDate);
        }
        
        if ($this->endDate) {
            $query->whereDate('scheduled_at', '<=', $this->endDate);
        }
        
        // Search functionality
        if (trim($this->search)) {
            $term = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($term) {
                $q->whereHas('user', function ($userQuery) use ($term) {
                    $userQuery->where('first_name', 'like', $term)
                        ->orWhere('last_name', 'like', $term);
                })
                ->orWhereHas('customer', function ($customerQuery) use ($term) {
                    $customerQuery->where('name', 'like', $term);
                })
                ->orWhereHas('campaign', function ($campaignQuery) use ($term) {
                    $campaignQuery->where('name', 'like', $term);
                });
            });
        }
        
        return $query;
    }
    
    public function getAppointmentsProperty()
    {
        return $this->getAppointmentsQuery()->paginate($this->perPage);
    }
    
    public function openFollowUpModal($appointmentId)
    {
        $this->currentAppointmentId = $appointmentId;
        $appointment = Appointment::findOrFail($appointmentId);
        
        // Pre-fill with existing follow-up date if available
        if ($appointment->follow_up_date) {
            $this->followUpDate = $appointment->follow_up_date->format('Y-m-d');
            $this->followUpTime = $appointment->follow_up_date->format('H:i');
        } else {
            $this->followUpDate = Carbon::now()->addDays(7)->format('Y-m-d');
            $this->followUpTime = '10:00';
        }
        
        $this->followUpNotes = $appointment->outcome_notes ?? '';
    }
    
    public function closeFollowUpModal()
    {
        $this->currentAppointmentId = null;
        $this->followUpDate = null;
        $this->followUpTime = null;
        $this->followUpNotes = null;
        $this->resetValidation();
    }
    
    public function scheduleFollowUp()
    {
        $this->validate();
        
        $appointment = Appointment::findOrFail($this->currentAppointmentId);
        
        // Combine date and time
        $followUpDateTime = Carbon::parse($this->followUpDate . ' ' . $this->followUpTime);
        
        $appointment->update([
            'follow_up_date' => $followUpDateTime,
            'outcome_notes' => $this->followUpNotes
        ]);
        
        // Create a new appointment for the follow-up
        Appointment::create([
            'user_id' => $appointment->user_id,
            'campaign_id' => $appointment->campaign_id,
            'customer_id' => $appointment->customer_id,
            'prospect_id' => $appointment->prospect_id,
            'scheduled_at' => $followUpDateTime,
            'duration_minutes' => $appointment->duration_minutes,
            'type' => 'follow_up',
            'category' => $appointment->category,
            'status' => 'pending',
            'agent_notes' => "Follow-up to appointment #{$appointment->id}. " . $this->followUpNotes,
            'service_id' => $appointment->service_id,
        ]);
        
        session()->flash('message', 'Follow-up scheduled successfully!');
        $this->closeFollowUpModal();
    }
    
    public function markAsCompleted($appointmentId)
    {
        $appointment = Appointment::findOrFail($appointmentId);
        $appointment->update([
            'follow_up_required' => false,
            'outcome' => 'completed',
            'outcome_notes' => 'Follow-up completed and marked as resolved.'
        ]);
        
        session()->flash('message', 'Appointment marked as completed!');
    }
    
    public function bulkScheduleFollowUps()
    {
        if (empty($this->selectedAppointments)) {
            session()->flash('error', 'No appointments selected.');
            return;
        }
        
        // Logic for bulk scheduling would go here
        session()->flash('message', count($this->selectedAppointments) . ' appointments scheduled for follow-up.');
        $this->resetSelection();
    }
    
    public function render()
    {
        return view('livewire.appointments.appointment-followups', [
            'appointments' => $this->appointments,
            'campaigns' => Campaign::where('status', 'active')->get(),
        ]);
    }
}
