<?php

namespace App\Livewire\Auth;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;

#[Layout('components.layouts.auth')]
class Login extends Component
{
    #[Validate('required|string|email')]
    public string $email = '';

    #[Validate('required|string')]
    public string $password = '';

    public bool $remember = false;

    public bool $showPassword = false;

    /**
     * Handle an incoming authentication request.
     */
    public function login()
    {
        $this->validate();

        // Debug login attempt
        $user = \App\Models\User::where('email', $this->email)->first();

        if ($user) {
            // Check if password matches
            if (\Illuminate\Support\Facades\Hash::check($this->password, $user->password)) {
                // Log the user in manually
                Auth::login($user, $this->remember);
                Session::regenerate();

                // Redirect to dashboard with a full page reload
                return redirect()->route('dashboard');
            }
        }

        // If we get here, authentication failed
        throw ValidationException::withMessages([
            'email' => __('auth.failed'),
        ]);
    }


}
