<?php

namespace App\Livewire\Forms;

use App\Models\Site;
use Livewire\Attributes\Validate;
use Livewire\Form;

class SiteForm extends Form
{
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|string|max:255')]
    public $location = '';

    #[Validate('nullable|string')]
    public $description = '';

    #[Validate('nullable|exists:call_centers,id')]
    public $call_center_id = null;

    #[Validate('nullable|exists:users,id')]
    public $manager_id = null;

    #[Validate('nullable|exists:users,id')]
    public $it_manager_id = null;

    #[Validate('nullable|exists:users,id')]
    public $trainer_id = null;

    #[Validate('nullable|exists:users,id')]
    public $accountant_id = null;

    #[Validate('nullable|exists:users,id')]
    public $hr_manager_id = null;

    #[Validate('required|string|in:active,inactive')]
    public $status = 'active';

    #[Validate('nullable|integer|min:0')]
    public $capacity = 0;

    #[Validate('nullable|email')]
    public $contact_email = '';

    #[Validate('nullable|string')]
    public $contact_phone = '';

    /**
     * Get the validation rules
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'location' => 'required|string|max:255',
            'description' => 'nullable|string',
            'call_center_id' => 'nullable|exists:call_centers,id',
            'manager_id' => 'nullable|exists:users,id',
            'it_manager_id' => 'nullable|exists:users,id',
            'trainer_id' => 'nullable|exists:users,id',
            'accountant_id' => 'nullable|exists:users,id',
            'hr_manager_id' => 'nullable|exists:users,id',
            'status' => 'required|string|in:active,inactive',
            'capacity' => 'nullable|integer|min:0',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string',
        ];
    }

    /**
     * Get the validation messages
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'The site name is required',
            'name.max' => 'The site name must not exceed 255 characters',
            'location.required' => 'The location is required',
            'location.max' => 'The location must not exceed 255 characters',
            'call_center_id.exists' => 'The selected call center is invalid',
            'manager_id.exists' => 'The selected manager is invalid',
            'it_manager_id.exists' => 'The selected IT manager is invalid',
            'trainer_id.exists' => 'The selected trainer is invalid',
            'accountant_id.exists' => 'The selected accountant is invalid',
            'hr_manager_id.exists' => 'The selected HR manager is invalid',
            'status.required' => 'The status is required',
            'status.in' => 'The status must be either active or inactive',
            'capacity.integer' => 'The capacity must be an integer',
            'capacity.min' => 'The capacity must be at least 0',
            'contact_email.email' => 'Please enter a valid email address',
        ];
    }

    /**
     * Set the form data from an existing site
     *
     * @param Site $site
     * @return void
     */
    public function setSite(Site $site)
    {
        $this->name = $site->name;
        $this->location = $site->location;
        $this->description = $site->description;
        $this->call_center_id = $site->call_center_id;
        $this->manager_id = $site->manager_id;
        $this->it_manager_id = $site->it_manager_id;
        $this->trainer_id = $site->trainer_id;
        $this->accountant_id = $site->accountant_id;
        $this->hr_manager_id = $site->hr_manager_id;
        $this->status = $site->status;
        $this->capacity = $site->capacity;
        $this->contact_email = $site->contact_email;
        $this->contact_phone = $site->contact_phone;
    }

    /**
     * Store a new site
     *
     * @return Site
     */
    public function store()
    {
        $this->validate();

        $site = Site::create([
            'name' => $this->name,
            'location' => $this->location,
            'description' => $this->description,
            'call_center_id' => $this->call_center_id,
            'manager_id' => $this->manager_id,
            'it_manager_id' => $this->it_manager_id,
            'trainer_id' => $this->trainer_id,
            'accountant_id' => $this->accountant_id,
            'hr_manager_id' => $this->hr_manager_id,
            'status' => $this->status,
            'capacity' => $this->capacity,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
        ]);

        return $site;
    }

    /**
     * Update an existing site
     *
     * @param Site $site
     * @return bool
     */
    public function update(Site $site)
    {
        $this->validate();

        return $site->update([
            'name' => $this->name,
            'location' => $this->location,
            'description' => $this->description,
            'call_center_id' => $this->call_center_id,
            'manager_id' => $this->manager_id,
            'it_manager_id' => $this->it_manager_id,
            'trainer_id' => $this->trainer_id,
            'accountant_id' => $this->accountant_id,
            'hr_manager_id' => $this->hr_manager_id,
            'status' => $this->status,
            'capacity' => $this->capacity,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
        ]);
    }
}
