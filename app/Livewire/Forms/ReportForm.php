<?php

namespace App\Livewire\Forms;

use App\Models\Report;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Form;
use Livewire\Attributes\Validate;

class ReportForm extends Form
{
    #[Validate('required|exists:campaigns,id')]
    public $campaign_id = '';

    #[Validate('required|date')]
    public $date = '';

    #[Validate('required|string')]
    public $title = '';

    #[Validate('nullable|string')]
    public $description = '';

    #[Validate('required|string')]
    public $content = '';

    #[Validate('nullable|string')]
    public $response = '';

    #[Validate('nullable|date|after_or_equal:date')]
    public $sent_at = null;

    #[Validate('nullable|string|max:255')]
    public $file_path = '';

    #[Validate('nullable|string|in:pending,submitted,approved,rejected,in_review')]
    public $status = 'pending';

    #[Validate('nullable|string')]
    public $type = '';

    #[Validate('nullable|string')]
    public $category = '';

    #[Validate('nullable|array')]
    public $tags = [];

    #[Validate('nullable|string|in:low,normal,high,urgent')]
    public $priority = 'normal';

    #[Validate('nullable|decimal:0,2')]
    public $performance_score = null;

    #[Validate('nullable|decimal:0,2')]
    public $quality_score = null;

    #[Validate('nullable|decimal:0,2')]
    public $compliance_score = null;

    #[Validate('nullable|boolean')]
    public $is_public = false;

    #[Validate('nullable|string')]
    public $reportable_type = null;

    #[Validate('nullable|integer')]
    public $reportable_id = null;

    public function setReport(Report $report)
    {
        $this->campaign_id = $report->campaign_id;
        $this->date = $report->date;
        $this->title = $report->title;
        $this->description = $report->description;
        $this->content = $report->content;
        $this->response = $report->response;
        $this->sent_at = $report->sent_at;
        $this->file_path = $report->file_path;
        $this->status = $report->status;
        $this->type = $report->type;
        $this->category = $report->category;
        $this->tags = $report->tags;
        $this->priority = $report->priority;
        $this->performance_score = $report->performance_score;
        $this->quality_score = $report->quality_score;
        $this->compliance_score = $report->compliance_score;
        $this->is_public = $report->is_public;
        $this->reportable_type = $report->reportable_type;
        $this->reportable_id = $report->reportable_id;
    }

    public function store()
    {
        $this->validate();

        $report = Report::create([
            'campaign_id' => $this->campaign_id,
            'date' => $this->date,
            'title' => $this->title,
            'description' => $this->description,
            'content' => $this->content,
            'response' => $this->response,
            'sent_at' => $this->sent_at,
            'file_path' => $this->file_path,
            'status' => $this->status,
            'type' => $this->type,
            'category' => $this->category,
            'tags' => $this->tags,
            'priority' => $this->priority,
            'performance_score' => $this->performance_score,
            'quality_score' => $this->quality_score,
            'compliance_score' => $this->compliance_score,
            'is_public' => $this->is_public,
            'reportable_type' => $this->reportable_type,
            'reportable_id' => $this->reportable_id,
            'created_by' => Auth::user()->id,
        ]);

        $this->reset();

        session()->flash('message', 'Report created successfully!');

        return $report;
    }

    public function update(Report $report)
    {
        $this->validate();

        $report->update([
            'campaign_id' => $this->campaign_id,
            'date' => $this->date,
            'title' => $this->title,
            'description' => $this->description,
            'content' => $this->content,
            'response' => $this->response,
            'sent_at' => $this->sent_at,
            'file_path' => $this->file_path,
            'status' => $this->status,
            'type' => $this->type,
            'category' => $this->category,
            'tags' => $this->tags,
            'priority' => $this->priority,
            'performance_score' => $this->performance_score,
            'quality_score' => $this->quality_score,
            'compliance_score' => $this->compliance_score,
            'is_public' => $this->is_public,
            'reportable_type' => $this->reportable_type,
            'reportable_id' => $this->reportable_id,
        ]);

        session()->flash('message', 'Report updated successfully!');

        return $report;
    }
}

