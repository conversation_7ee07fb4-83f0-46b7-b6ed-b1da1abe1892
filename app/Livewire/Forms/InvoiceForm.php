<?php

namespace App\Livewire\Forms;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Validate;
use Livewire\Form;

class InvoiceForm extends Form
{
    // Client information
    #[Validate('required|string|max:255')]
    public $client_name = '';

    #[Validate('nullable|email|max:255')]
    public $client_email = '';

    #[Validate('nullable|string|max:255')]
    public $client_address = '';

    #[Validate('nullable|string|max:20')]
    public $client_phone = '';

    // Invoice details
    #[Validate('required|date')]
    public $issue_date;

    #[Validate('required|date|after_or_equal:issue_date')]
    public $due_date;

    #[Validate('required|string|in:draft,sent,paid,overdue,cancelled')]
    public $status = 'draft';

    #[Validate('nullable|string')]
    public $notes = '';

    // Calculated fields
    public $subtotal = 0;
    public $tax_rate = 0;
    public $tax_amount = 0;
    public $discount_amount = 0;
    public $total_amount = 0;

    // Invoice items
    public $items = [];

    public function mount()
    {
        $this->issue_date = now()->format('Y-m-d');
        $this->due_date = now()->addDays(30)->format('Y-m-d');
        $this->addItem(); // Add an empty item by default
    }

    public function setInvoice(Invoice $invoice)
    {
        $this->client_name = $invoice->client_name;
        $this->client_email = $invoice->client_email;
        $this->client_address = $invoice->client_address;
        $this->client_phone = $invoice->client_phone;
        $this->issue_date = $invoice->issue_date->format('Y-m-d');
        $this->due_date = $invoice->due_date->format('Y-m-d');
        $this->status = $invoice->status;
        $this->notes = $invoice->notes;
        $this->subtotal = $invoice->subtotal;
        $this->tax_rate = $invoice->tax_rate;
        $this->tax_amount = $invoice->tax_amount;
        $this->discount_amount = $invoice->discount_amount;
        $this->total_amount = $invoice->total_amount;

        // Load invoice items
        $this->items = [];
        foreach ($invoice->items as $item) {
            $this->items[] = [
                'id' => $item->id,
                'description' => $item->description,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'tax_rate' => $item->tax_rate,
                'tax_amount' => $item->tax_amount,
                'discount_amount' => $item->discount_amount,
                'total_amount' => $item->total_amount,
            ];
        }

        if (empty($this->items)) {
            $this->addItem(); // Add an empty item if none exist
        }
    }

    public function addItem()
    {
        $this->items[] = [
            'id' => null,
            'description' => '',
            'quantity' => 1,
            'unit_price' => 0,
            'tax_rate' => $this->tax_rate,
            'tax_amount' => 0,
            'discount_amount' => 0,
            'total_amount' => 0,
        ];

        // Calculate totals after adding a new item
        $this->calculateTotals();
    }

    public function removeItem($index)
    {
        if (count($this->items) > 1) {
            unset($this->items[$index]);
            $this->items = array_values($this->items); // Re-index array
            $this->calculateTotals();
        }
    }

    public function calculateItemTotal($index)
    {
        $item = &$this->items[$index];

        // Calculate subtotal for the item
        $subtotal = $item['quantity'] * $item['unit_price'];

        // Ensure discount amount is not greater than subtotal
        if (isset($item['discount_amount']) && $item['discount_amount'] > $subtotal) {
            $item['discount_amount'] = $subtotal;
        }

        // Calculate tax amount (tax is applied after discount)
        $taxableAmount = $subtotal - ($item['discount_amount'] ?? 0);
        $item['tax_amount'] = round($taxableAmount * ($item['tax_rate'] / 100), 2);

        // Calculate total amount
        $item['total_amount'] = $taxableAmount + $item['tax_amount'];

        $this->calculateTotals();
    }

    public function calculateTotals()
    {
        $this->subtotal = 0;
        $this->tax_amount = 0;
        $this->total_amount = 0;
        $totalItemDiscounts = 0;

        foreach ($this->items as $item) {
            // Calculate raw subtotal (before any discounts)
            $itemSubtotal = $item['quantity'] * $item['unit_price'];
            $this->subtotal += $itemSubtotal;

            // Add up item discounts
            $totalItemDiscounts += isset($item['discount_amount']) ? $item['discount_amount'] : 0;

            // Add up tax amounts
            $this->tax_amount += $item['tax_amount'];
        }

        // Apply global discount
        $this->total_amount = $this->subtotal - $totalItemDiscounts - $this->discount_amount + $this->tax_amount;

        // Ensure total is not negative
        if ($this->total_amount < 0) {
            $this->total_amount = 0;
        }
    }

    public function store()
    {
        $this->validate();

        // Calculate totals before saving
        $this->calculateTotals();

        // Create new invoice
        $invoice = Invoice::create([
            'invoice_number' => Invoice::generateInvoiceNumber(),
            'client_name' => $this->client_name,
            'client_email' => $this->client_email,
            'client_address' => $this->client_address,
            'client_phone' => $this->client_phone,
            'issue_date' => $this->issue_date,
            'due_date' => $this->due_date,
            'subtotal' => $this->subtotal,
            'tax_rate' => $this->tax_rate,
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'total_amount' => $this->total_amount,
            'notes' => $this->notes,
            'status' => $this->status,
            'created_by' => Auth::id(),
        ]);

        // Create invoice items
        foreach ($this->items as $item) {
            if (!empty($item['description'])) {
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'tax_rate' => $item['tax_rate'],
                    'tax_amount' => $item['tax_amount'],
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'total_amount' => $item['total_amount'],
                ]);
            }
        }

        return $invoice;
    }

    public function update(Invoice $invoice)
    {
        $this->validate();

        // Calculate totals before saving
        $this->calculateTotals();

        // Update invoice
        $invoice->update([
            'client_name' => $this->client_name,
            'client_email' => $this->client_email,
            'client_address' => $this->client_address,
            'client_phone' => $this->client_phone,
            'issue_date' => $this->issue_date,
            'due_date' => $this->due_date,
            'subtotal' => $this->subtotal,
            'tax_rate' => $this->tax_rate,
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'total_amount' => $this->total_amount,
            'notes' => $this->notes,
            'status' => $this->status,
        ]);

        // Delete existing items
        $invoice->items()->delete();

        // Create new invoice items
        foreach ($this->items as $item) {
            if (!empty($item['description'])) {
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'tax_rate' => $item['tax_rate'],
                    'tax_amount' => $item['tax_amount'],
                    'discount_amount' => $item['discount_amount'] ?? 0,
                    'total_amount' => $item['total_amount'],
                ]);
            }
        }

        return $invoice;
    }
}
