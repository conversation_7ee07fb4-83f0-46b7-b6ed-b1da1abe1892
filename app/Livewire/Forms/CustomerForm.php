<?php

namespace App\Livewire\Forms;

use App\Models\Customer;
use Livewire\Attributes\Validate;
use Livewire\Form;

class CustomerForm extends Form
{
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|email|max:255')]
    public $email = '';

    #[Validate('nullable|string|max:255')]
    public $phone = '';

    #[Validate('nullable|string|max:255')]
    public $address = '';

    #[Validate('nullable|string|max:255')]
    public $city = '';

    #[Validate('nullable|string|max:255')]
    public $country = '';

    #[Validate('nullable|string|max:255')]
    public $postal_code = '';

    #[Validate('nullable|string|max:255')]
    public $website = '';

    #[Validate('nullable|string|max:255')]
    public $industry = '';

    #[Validate('nullable|string')]
    public $notes = '';

    #[Validate('nullable|string|max:255')]
    public $contact_person = '';

    #[Validate('nullable|string|max:255')]
    public $contact_position = '';

    #[Validate('nullable|string|max:255')]
    public $contact_phone = '';

    #[Validate('required|string|in:active,inactive')]
    public $status = 'active';

    /**
     * Set the customer data
     *
     * @param Customer $customer
     * @return void
     */
    public function setCustomer(Customer $customer)
    {
        $this->name = $customer->name;
        $this->email = $customer->email;
        $this->phone = $customer->phone;
        $this->address = $customer->address;
        $this->city = $customer->city;
        $this->country = $customer->country;
        $this->postal_code = $customer->postal_code;
        $this->website = $customer->website;
        $this->industry = $customer->industry;
        $this->notes = $customer->notes;
        $this->contact_person = $customer->contact_person;
        $this->contact_position = $customer->contact_position;
        $this->contact_phone = $customer->contact_phone;
        $this->status = $customer->status;
    }

    /**
     * Store a new customer
     *
     * @return Customer
     */
    public function store()
    {
        $this->validate();

        $customer = Customer::create([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'city' => $this->city,
            'country' => $this->country,
            'postal_code' => $this->postal_code,
            'website' => $this->website,
            'industry' => $this->industry,
            'notes' => $this->notes,
            'contact_person' => $this->contact_person,
            'contact_position' => $this->contact_position,
            'contact_phone' => $this->contact_phone,
            'status' => $this->status,
        ]);

        return $customer;
    }

    /**
     * Update an existing customer
     *
     * @param Customer $customer
     * @return bool
     */
    public function update(Customer $customer)
    {
        $this->validate();

        return $customer->update([
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'city' => $this->city,
            'country' => $this->country,
            'postal_code' => $this->postal_code,
            'website' => $this->website,
            'industry' => $this->industry,
            'notes' => $this->notes,
            'contact_person' => $this->contact_person,
            'contact_position' => $this->contact_position,
            'contact_phone' => $this->contact_phone,
            'status' => $this->status,
        ]);
    }
}
