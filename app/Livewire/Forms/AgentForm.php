<?php

namespace App\Livewire\Forms;

use App\Models\User;
use App\Models\Media;
use App\Models\Role;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Validate;
use Livewire\Form;
use Livewire\WithFileUploads;

class AgentForm extends Form
{
    use WithFileUploads;
    
    #[Validate('required|string|max:255')]
    public $first_name = '';
    
    #[Validate('required|string|max:255')]
    public $last_name = '';
    
    #[Validate('nullable|date')]
    public $birth_date = null;
    
    #[Validate('required|email|unique:users,email')]
    public $email = '';
    
    #[Validate('nullable|string|max:255')]
    public $phone_number = '';
    
    #[Validate('nullable|string|max:255')]
    public $address = '';
    
    #[Validate('nullable|string|max:255')]
    public $city = '';
    
    #[Validate('nullable|string|max:255')]
    public $country = '';
    
    #[Validate('nullable|string|max:255|unique:users,registration_number')]
    public $registration_number = '';
    
    #[Validate('nullable|date')]
    public $hire_date = null;
    
    #[Validate('required|string|in:in_training,active,inactive,engaged')]
    public $status = 'in_training';
    
    #[Validate('nullable|exists:campaigns,id')]
    public $campaign_id = null;
    
    #[Validate('nullable|image|max:1024')]
    public $profile_picture = null;
    
    public $current_profile_picture = null;
    
    // For password updates
    #[Validate('nullable|string|min:8|confirmed')]
    public $new_password = '';
    
    #[Validate('nullable|string|min:8')]
    public $new_password_confirmation = '';
    
    public function setAgent(User $agent)
    {
        $this->first_name = $agent->first_name;
        $this->last_name = $agent->last_name;
        $this->birth_date = $agent->birth_date;
        $this->email = $agent->email;
        $this->phone_number = $agent->phone_number;
        $this->address = $agent->address;
        $this->city = $agent->city;
        $this->country = $agent->country;
        $this->registration_number = $agent->registration_number;
        $this->hire_date = $agent->hire_date;
        $this->status = $agent->status;
        $this->campaign_id = $agent->campaign_id;
        $this->current_profile_picture = $agent->getProfilePictureUrl();
    }
    
    public function create()
    {
        $this->validate();
        
        // Create the agent with role_id 6 (agent)
        $agent = User::create([
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'birth_date' => $this->birth_date,
            'email' => $this->email,
            'password' => bcrypt('password'), // Default password, should be changed
            'phone_number' => $this->phone_number,
            'address' => $this->address,
            'city' => $this->city,
            'country' => $this->country,
            'registration_number' => $this->registration_number,
            'hire_date' => $this->hire_date,
            'status' => $this->status,
            'role_id' => 6, // Agent role
            'campaign_id' => $this->campaign_id,
        ]);
        
        // Upload profile picture if provided
        if ($this->profile_picture) {
            $filePath = $this->profile_picture->store('media/profile_pictures', 'public');
            Media::create([
                'mediable_id' => $agent->id,
                'mediable_type' => User::class,
                'file_name' => $this->profile_picture->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->profile_picture->getMimeType(),
                'category' => 'profile_picture',
                'uploaded_by' => Auth::id(),
            ]);
        }
        
        return $agent;
    }
    
    public function update(User $agent)
    {
        // Custom validation rules for update
        $rules = [
            'email' => "required|email|unique:users,email,{$agent->id}",
            'registration_number' => "nullable|string|max:255|unique:users,registration_number,{$agent->id}",
            'new_password' => 'nullable|string|min:8|confirmed',
            'new_password_confirmation' => 'nullable|string|min:8',
        ];
        
        $this->validate($rules);
        
        // Prepare update data
        $updateData = [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'birth_date' => $this->birth_date,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'address' => $this->address,
            'city' => $this->city,
            'country' => $this->country,
            'hire_date' => $this->hire_date,
            'status' => $this->status,
            'campaign_id' => $this->campaign_id,
        ];
        
        // Only include registration_number if it's not empty
        if (!empty($this->registration_number)) {
            $updateData['registration_number'] = $this->registration_number;
        }
        
        // Update password if provided
        if ($this->new_password) {
            $updateData['password'] = bcrypt($this->new_password);
        }
        
        // Update agent data
        foreach ($updateData as $key => $value) {
            $agent->$key = $value;
        }
        $agent->save();
        
        // Update profile picture if provided
        if ($this->profile_picture) {
            // Delete old profile picture if exists
            if ($agent->profilePicture()) {
                Storage::disk('public')->delete($agent->profilePicture()->file_path);
                $agent->profilePicture()->delete();
            }
            
            // Upload new profile picture
            $filePath = $this->profile_picture->store('media/profile_pictures', 'public');
            Media::create([
                'mediable_id' => $agent->id,
                'mediable_type' => User::class,
                'file_name' => $this->profile_picture->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->profile_picture->getMimeType(),
                'category' => 'profile_picture',
                'uploaded_by' => Auth::id(),
            ]);
        }
        
        return $agent;
    }
}