<?php

namespace App\Livewire\Forms;

use App\Models\Media;
use App\Models\Appointment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Livewire\Form;
use Livewire\WithFileUploads;

class AppointmentForm extends Form
{
    use WithFileUploads;

    // Basic appointment information
    public $campaign_id;
    public $customer_id;
    public $scheduled_at;
    public $end_time;
    public $duration_minutes = 60; // Default to 1 hour
    public $agent_notes;
    public $audio;

    // Appointment categorization
    public $type = 'call'; // Default type
    public $category;

    // Location information
    public $location;
    public $meeting_link;

    // Follow-up information
    public $follow_up_required = false;
    public $follow_up_date;

    // Recurring appointment information
    public $is_recurring = false;
    public $recurrence_pattern;
    public $recurrence_end_date;

    // Outcome tracking
    public $outcome;
    public $outcome_notes;

    protected $rules = [
        // Basic appointment information
        'campaign_id'  => 'required|exists:campaigns,id',
        'customer_id'  => 'required|exists:customers,id',
        'scheduled_at' => 'required|date',
        'end_time' => 'nullable|date|after:scheduled_at',
        'duration_minutes' => 'nullable|integer|min:5|max:480',
        'agent_notes'  => 'nullable|string',
        'audio' => 'nullable|file|mimes:mp3,wav,aac,ogg,flac,mpga|max:2048',

        // Appointment categorization
        'type' => 'nullable|string|in:call,meeting,video,follow_up,other',
        'category' => 'nullable|string|max:50',

        // Location information
        'location' => 'nullable|string|max:255',
        'meeting_link' => 'nullable|string|max:255',

        // Follow-up information
        'follow_up_required' => 'boolean',
        'follow_up_date' => 'nullable|required_if:follow_up_required,true|date|after:scheduled_at',

        // Recurring appointment information
        'is_recurring' => 'boolean',
        'recurrence_pattern' => 'nullable|required_if:is_recurring,true|string|in:daily,weekly,biweekly,monthly',
        'recurrence_end_date' => 'nullable|required_if:is_recurring,true|date|after:scheduled_at',

        // Outcome tracking
        'outcome' => 'nullable|string|in:successful,unsuccessful,rescheduled,no_show,cancelled',
        'outcome_notes' => 'nullable|string|max:1000',
    ];

    public function setAppointment(Appointment $appointment)
    {
        // Basic appointment information
        $this->campaign_id = $appointment->campaign_id;
        $this->customer_id = $appointment->customer_id;
        $this->scheduled_at = $appointment->scheduled_at;
        $this->end_time = $appointment->end_time;
        $this->duration_minutes = $appointment->duration_minutes ?? 60;
        $this->agent_notes = $appointment->agent_notes;
        $this->audio = $appointment->audio;

        // Appointment categorization
        $this->type = $appointment->type ?? 'call';
        $this->category = $appointment->category;

        // Location information
        $this->location = $appointment->location;
        $this->meeting_link = $appointment->meeting_link;

        // Follow-up information
        $this->follow_up_required = $appointment->follow_up_required ?? false;
        $this->follow_up_date = $appointment->follow_up_date;

        // Recurring appointment information
        $this->is_recurring = $appointment->is_recurring ?? false;
        $this->recurrence_pattern = $appointment->recurrence_pattern;
        $this->recurrence_end_date = $appointment->recurrence_end_date;

        // Outcome tracking
        $this->outcome = $appointment->outcome;
        $this->outcome_notes = $appointment->outcome_notes;
    }

    public function store()
    {
        try {
            // Validate all fields except audio
            $this->validate(array_diff_key($this->rules, ['audio' => '']));

            // Handle audio file separately
            $path = null;
            if ($this->audio && is_object($this->audio)) {
                try {
                    // Check file size before attempting to upload
                    $fileSize = $this->audio->getSize();
                    $maxSize = 2 * 1024 * 1024; // 2MB in bytes

                    if ($fileSize > $maxSize) {
                        throw new \Exception("File size exceeds maximum limit (2MB). Your file is " . round($fileSize / (1024 * 1024), 2) . "MB");
                    }

                    // Log file information for debugging
                    Log::info('Uploading audio file:', [
                        'filename' => $this->audio->getClientOriginalName(),
                        'extension' => $this->audio->getClientOriginalExtension(),
                        'mime' => $this->audio->getMimeType(),
                        'size' => $fileSize
                    ]);

                    // Use Laravel's built-in file storage system
                    $path = $this->audio->store('appointments/audio', 'public');

                    if (!$path) {
                        throw new \Exception("Failed to store the audio file. Please try again with a smaller file.");
                    }

                    // Verify the file exists
                    $fullPath = storage_path('app/public/' . $path);
                    if (!file_exists($fullPath)) {
                        throw new \Exception("File was not saved correctly");
                    }

                    // Log success
                    Log::info('Audio file uploaded successfully:', [
                        'path' => $path,
                        'fullPath' => $fullPath,
                        'file_exists' => file_exists($fullPath),
                        'file_size' => file_exists($fullPath) ? filesize($fullPath) : 0
                    ]);
                } catch (\Exception $e) {
                    // Log error
                    Log::error('Error uploading audio file: ' . $e->getMessage(), [
                        'exception' => get_class($e),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    $this->addError('audio', 'Failed to upload audio file: ' . $e->getMessage());
                    return null;
                }
            }
        } catch (\Throwable $e) {
            Log::error('Form validation error:', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            $this->addError('form', 'Error: ' . $e->getMessage());
            return null;
        }

        // Calculate end time if not provided
        $endTime = $this->end_time;
        if (!$endTime && $this->scheduled_at && $this->duration_minutes) {
            $endTime = (clone $this->scheduled_at)->addMinutes($this->duration_minutes);
        }

        try {
            // Create the appointment
            $appointment = Appointment::create([
                'user_id'      => Auth::id(),
                'campaign_id'  => $this->campaign_id,
                'customer_id'  => $this->customer_id,
                'scheduled_at' => $this->scheduled_at,
                'end_time'     => $endTime,
                'duration_minutes' => $this->duration_minutes,
                'type'         => $this->type,
                'category'     => $this->category,
                'agent_notes'  => $this->agent_notes,
                'audio_path'   => $path,
                'status'       => 'pending',
                'location'     => $this->location,
                'meeting_link' => $this->meeting_link,
                'follow_up_required' => $this->follow_up_required,
                'follow_up_date' => $this->follow_up_date,
                'is_recurring' => $this->is_recurring,
                'recurrence_pattern' => $this->recurrence_pattern,
                'recurrence_end_date' => $this->recurrence_end_date,
            ]);

            // Create recurring appointments if needed
            if ($this->is_recurring && $this->recurrence_pattern && $this->recurrence_end_date) {
                $this->createRecurringAppointments($appointment);
            }

            session()->flash('message', 'Appointment created successfully!');

            // Log success
            Log::info('Appointment created successfully', [
                'appointment_id' => $appointment->id,
                'user_id' => Auth::id(),
                'audio_path' => $path
            ]);

            return $appointment;
        } catch (\Exception $e) {
            // Log error
            Log::error('Error creating appointment: ' . $e->getMessage(), [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // If we uploaded a file but failed to create the appointment, clean up
            if ($path && Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                Log::info('Deleted orphaned audio file: ' . $path);
            }

            $this->addError('form', 'Failed to create appointment: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create recurring appointments based on the recurrence pattern
     */
    protected function createRecurringAppointments(Appointment $appointment)
    {
        $startDate = clone $appointment->scheduled_at;
        $endDate = clone $appointment->recurrence_end_date;
        $currentDate = clone $startDate;

        // Move to the next occurrence based on the pattern
        switch ($appointment->recurrence_pattern) {
            case 'daily':
                $currentDate->addDay();
                break;
            case 'weekly':
                $currentDate->addWeek();
                break;
            case 'biweekly':
                $currentDate->addWeeks(2);
                break;
            case 'monthly':
                $currentDate->addMonth();
                break;
            default:
                return; // Invalid pattern
        }

        // Create recurring appointments until the end date
        while ($currentDate <= $endDate) {
            $endTime = null;
            if ($appointment->end_time) {
                $diff = $appointment->scheduled_at->diffInSeconds($appointment->end_time);
                $endTime = (clone $currentDate)->addSeconds($diff);
            }

            Appointment::create([
                'user_id' => $appointment->user_id,
                'campaign_id' => $appointment->campaign_id,
                'customer_id' => $appointment->customer_id,
                'scheduled_at' => clone $currentDate,
                'end_time' => $endTime,
                'duration_minutes' => $appointment->duration_minutes,
                'type' => $appointment->type,
                'category' => $appointment->category,
                'agent_notes' => $appointment->agent_notes,
                'status' => 'pending',
                'location' => $appointment->location,
                'meeting_link' => $appointment->meeting_link,
                'is_recurring' => true,
                'recurrence_pattern' => $appointment->recurrence_pattern,
                'recurrence_end_date' => $appointment->recurrence_end_date,
            ]);

            // Move to the next occurrence
            switch ($appointment->recurrence_pattern) {
                case 'daily':
                    $currentDate->addDay();
                    break;
                case 'weekly':
                    $currentDate->addWeek();
                    break;
                case 'biweekly':
                    $currentDate->addWeeks(2);
                    break;
                case 'monthly':
                    $currentDate->addMonth();
                    break;
            }
        }
    }

    public function update(Appointment $appointment)
    {
        try {
            // Validate all fields except audio
            $this->validate(array_diff_key($this->rules, ['audio' => '']));

            // Handle audio file separately
            $path = null;
            $oldPath = $appointment->audio_path;

            if ($this->audio && is_object($this->audio)) {
                try {
                    // Check file size before attempting to upload
                    $fileSize = $this->audio->getSize();
                    $maxSize = 2 * 1024 * 1024; // 2MB in bytes

                    if ($fileSize > $maxSize) {
                        throw new \Exception("File size exceeds maximum limit (2MB). Your file is " . round($fileSize / (1024 * 1024), 2) . "MB");
                    }

                    // Log file information for debugging
                    Log::info('Updating audio file:', [
                        'filename' => $this->audio->getClientOriginalName(),
                        'extension' => $this->audio->getClientOriginalExtension(),
                        'mime' => $this->audio->getMimeType(),
                        'size' => $fileSize
                    ]);

                    // Use Laravel's built-in file storage system
                    $path = $this->audio->store('appointments/audio', 'public');

                    if (!$path) {
                        throw new \Exception("Failed to store the audio file. Please try again with a smaller file.");
                    }

                    // Verify the file exists
                    $fullPath = storage_path('app/public/' . $path);
                    if (!file_exists($fullPath)) {
                        throw new \Exception("File was not saved correctly");
                    }

                    // Log success
                    Log::info('Audio file uploaded successfully:', [
                        'path' => $path,
                        'fullPath' => $fullPath,
                        'file_exists' => file_exists($fullPath),
                        'file_size' => file_exists($fullPath) ? filesize($fullPath) : 0
                    ]);
                } catch (\Exception $e) {
                    // Log error
                    Log::error('Error uploading audio file: ' . $e->getMessage(), [
                        'exception' => get_class($e),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'trace' => $e->getTraceAsString()
                    ]);

                    $this->addError('audio', 'Failed to upload audio file: ' . $e->getMessage());
                    return null;
                }
            }

            // Calculate end time if not provided
            $endTime = $this->end_time;
            if (!$endTime && $this->scheduled_at && $this->duration_minutes) {
                $endTime = (clone $this->scheduled_at)->addMinutes($this->duration_minutes);
            }

            $updateData = [
                'campaign_id' => $this->campaign_id,
                'customer_id' => $this->customer_id,
                'scheduled_at' => $this->scheduled_at,
                'end_time' => $endTime,
                'duration_minutes' => $this->duration_minutes,
                'type' => $this->type,
                'category' => $this->category,
                'agent_notes' => $this->agent_notes,
                'location' => $this->location,
                'meeting_link' => $this->meeting_link,
                'follow_up_required' => $this->follow_up_required,
                'follow_up_date' => $this->follow_up_date,
                'is_recurring' => $this->is_recurring,
                'recurrence_pattern' => $this->recurrence_pattern,
                'recurrence_end_date' => $this->recurrence_end_date,
            ];

            // Add audio path if we have a new one
            if ($path) {
                $updateData['audio_path'] = $path;
            }

            $appointment->update($updateData);

            // Delete old audio file if we uploaded a new one
            if ($path && $oldPath && Storage::disk('public')->exists($oldPath)) {
                Storage::disk('public')->delete($oldPath);
                Log::info('Deleted old audio file: ' . $oldPath);
            }

            session()->flash('message', 'Appointment updated successfully!');

            // Log success
            Log::info('Appointment updated successfully', [
                'appointment_id' => $appointment->id,
                'user_id' => Auth::id(),
                'audio_path' => $path ?? $oldPath
            ]);

            return $appointment;
        } catch (\Throwable $e) {
            // Log error
            Log::error('Error updating appointment: ' . $e->getMessage(), [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // If we uploaded a file but failed to update the appointment, clean up
            if (isset($path) && $path && Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                Log::info('Deleted orphaned audio file: ' . $path);
            }

            $this->addError('form', 'Failed to update appointment: ' . $e->getMessage());
            return null;
        }
    }
}

