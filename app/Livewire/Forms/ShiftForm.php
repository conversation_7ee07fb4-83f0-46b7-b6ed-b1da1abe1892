<?php

namespace App\Livewire\Forms;

use App\Models\Shift;
use Livewire\Attributes\Validate;
use Livewire\Form;

class ShiftForm extends Form
{
    // Basic shift information
    public $login_time = '';
    public $logout_time = '';
    public $break_duration = 0;
    public $is_completed = false;
    public $hours_worked = null;

    // Schedule adherence fields
    public $scheduled_start_time = '';
    public $scheduled_end_time = '';
    public $scheduled_duration_minutes = null;
    public $adherence_percentage = null;

    // Break management fields
    public $lunch_duration = 0;
    public $training_duration = 0;
    public $personal_duration = 0;

    // Status and shift type fields
    public $status = 'pending';
    public $shift_type = 'regular';

    // Supervisor fields
    public $approved_by = null;
    public $notes = '';

    protected $rules = [
        // Basic shift information
        'login_time' => 'required|date',
        'logout_time' => 'nullable|date|after:login_time',
        'break_duration' => 'nullable|integer|min:0',
        'is_completed' => 'boolean',
        'hours_worked' => 'nullable|numeric|min:0',

        // Schedule adherence fields
        'scheduled_start_time' => 'nullable|date',
        'scheduled_end_time' => 'nullable|date|after:scheduled_start_time',
        'scheduled_duration_minutes' => 'nullable|integer|min:0',
        'adherence_percentage' => 'nullable|numeric|min:0|max:100',

        // Break management fields
        'lunch_duration' => 'nullable|integer|min:0',
        'training_duration' => 'nullable|integer|min:0',
        'personal_duration' => 'nullable|integer|min:0',

        // Status and shift type fields
        'status' => 'required|in:pending,approved,rejected,exception',
        'shift_type' => 'required|in:regular,overtime,training,meeting',

        // Supervisor fields
        'approved_by' => 'nullable|exists:users,id',
        'notes' => 'nullable|string|max:1000',
    ];

    public function setShift(Shift $shift)
    {
        // Basic shift information
        $this->logout_time = $shift->logout_time ? $shift->logout_time->format('Y-m-d\TH:i') : null;
        $this->login_time = $shift->login_time->format('Y-m-d\TH:i');
        $this->break_duration = $shift->break_duration ?? 0;
        $this->is_completed = $shift->is_completed ?? false;
        $this->hours_worked = $shift->hours_worked;

        // Schedule adherence fields
        $this->scheduled_start_time = $shift->scheduled_start_time ? $shift->scheduled_start_time->format('Y-m-d\TH:i') : null;
        $this->scheduled_end_time = $shift->scheduled_end_time ? $shift->scheduled_end_time->format('Y-m-d\TH:i') : null;
        $this->scheduled_duration_minutes = $shift->scheduled_duration_minutes;
        $this->adherence_percentage = $shift->adherence_percentage;

        // Break management fields
        $this->lunch_duration = $shift->lunch_duration ?? 0;
        $this->training_duration = $shift->training_duration ?? 0;
        $this->personal_duration = $shift->personal_duration ?? 0;

        // Status and shift type fields
        $this->status = $shift->status ?? 'pending';
        $this->shift_type = $shift->shift_type ?? 'regular';

        // Supervisor fields
        $this->approved_by = $shift->approved_by;
        $this->notes = $shift->notes ?? '';
    }

    public function update(Shift $shift)
    {
        $data = $this->validate($this->rules);

        // Calculate adherence if we have scheduled times and actual times
        if ($data['scheduled_start_time'] && $data['scheduled_end_time'] && $data['logout_time']) {
            // Update the shift with the validated data
            $shift->fill($data);

            // Calculate adherence percentage
            $adherencePercentage = $shift->calculateAdherence();

            // Add adherence to the data
            $data['adherence_percentage'] = $adherencePercentage;
        }

        // If the status is changed to approved, set the approver
        if ($data['status'] === 'approved' && $shift->status !== 'approved') {
            $data['approved_by'] = auth()->id();
        }

        // Update the shift
        $shift->update($data);

        session()->flash('message', 'Shift updated successfully!');
    }

    /**
     * Calculate the total break time
     */
    public function getTotalBreakTimeAttribute()
    {
        return $this->break_duration + $this->lunch_duration + $this->training_duration + $this->personal_duration;
    }
}
