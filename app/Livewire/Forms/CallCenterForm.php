<?php

namespace App\Livewire\Forms;

use App\Models\CallCenter;
use Livewire\Attributes\Validate;
use Livewire\Form;

class CallCenterForm extends Form
{
    #[Validate('required|string|max:255')]
    public $name = '';
    
    #[Validate('nullable|string')]
    public $description = '';
    
    #[Validate('nullable|exists:users,id')]
    public $director_id = null;
    
    #[Validate('required|string|in:active,inactive')]
    public $status = 'active';
    
    #[Validate('nullable|email')]
    public $contact_email = '';
    
    #[Validate('nullable|string')]
    public $contact_phone = '';
    
    #[Validate('nullable|string')]
    public $address = '';
    
    /**
     * Store a new call center
     *
     * @return CallCenter
     */
    public function store()
    {
        $this->validate();
        
        $callCenter = CallCenter::create([
            'name' => $this->name,
            'description' => $this->description,
            'director_id' => $this->director_id,
            'status' => $this->status,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
            'address' => $this->address,
        ]);
        
        return $callCenter;
    }
    
    /**
     * Update an existing call center
     *
     * @param CallCenter $callCenter
     * @return bool
     */
    public function update(CallCenter $callCenter)
    {
        $this->validate();
        
        return $callCenter->update([
            'name' => $this->name,
            'description' => $this->description,
            'director_id' => $this->director_id,
            'status' => $this->status,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
            'address' => $this->address,
        ]);
    }
}
