<?php

namespace App\Livewire\Forms;

use Livewire\Attributes\Validate;
use Livewire\Form;

class PlatformForm extends Form
{
    #[Validate('required|string|max:255')]
    public $name = '';

    #[Validate('required|exists:sites,id')]
    public $site_id = null;

    #[Validate('required|string|in:inbound,outbound,hybrid,digital')]
    public $type = 'inbound';

    #[Validate('nullable|exists:users,id')]
    public $manager_id = null;

    #[Validate('nullable|exists:users,id')]
    public $it_support_id = null;

    #[Validate('nullable|string')]
    public $description = '';

    #[Validate('required|string|in:active,inactive')]
    public $status = 'active';

    #[Validate('nullable|integer|min:0')]
    public $capacity = 0;

    #[Validate('nullable|string')]
    public $ip_address = '';

    #[Validate('nullable|string')]
    public $server_location = '';

    #[Validate('nullable|string')]
    public $software_version = '';

    #[Validate('nullable|date')]
    public $last_maintenance = null;

    /**
     * Store a new platform
     *
     * @return \App\Models\Platform
     */
    public function store()
    {
        $this->validate();

        $platform = \App\Models\Platform::create([
            'name' => $this->name,
            'site_id' => $this->site_id,
            'type' => $this->type,
            'manager_id' => $this->manager_id,
            'it_support_id' => $this->it_support_id,
            'description' => $this->description,
            'status' => $this->status,
            'capacity' => $this->capacity,
            'ip_address' => $this->ip_address,
            'server_location' => $this->server_location,
            'software_version' => $this->software_version,
            'last_maintenance' => $this->last_maintenance,
        ]);

        return $platform;
    }

    /**
     * Update an existing platform
     *
     * @param \App\Models\Platform $platform
     * @return bool
     */
    public function update(\App\Models\Platform $platform)
    {
        $this->validate();

        return $platform->update([
            'name' => $this->name,
            'site_id' => $this->site_id,
            'type' => $this->type,
            'manager_id' => $this->manager_id,
            'it_support_id' => $this->it_support_id,
            'description' => $this->description,
            'status' => $this->status,
            'capacity' => $this->capacity,
            'ip_address' => $this->ip_address,
            'server_location' => $this->server_location,
            'software_version' => $this->software_version,
            'last_maintenance' => $this->last_maintenance,
        ]);
    }

    /**
     * Set the form data from an existing platform
     *
     * @param \App\Models\Platform $platform
     * @return void
     */
    public function setPlatform(\App\Models\Platform $platform)
    {
        $this->name = $platform->name;
        $this->site_id = $platform->site_id;
        $this->type = $platform->type;
        $this->manager_id = $platform->manager_id;
        $this->it_support_id = $platform->it_support_id;
        $this->description = $platform->description;
        $this->status = $platform->status;
        $this->capacity = $platform->capacity;
        $this->ip_address = $platform->ip_address;
        $this->server_location = $platform->server_location;
        $this->software_version = $platform->software_version;
        $this->last_maintenance = $platform->last_maintenance;
    }
}