<?php

namespace App\Livewire\Forms;

use App\Models\Campaign;
use Livewire\Attributes\Validate;
use Livewire\Form;

class CampaignForm extends Form
{
    #[Validate('required|string|max:255')]
    public $name = '';
    
    #[Validate('required|exists:customers,id')]
    public $customer_id = null;
    
    #[Validate('required|exists:platforms,id')]
    public $platform_id = null;
    
    #[Validate('nullable|exists:users,id')]
    public $manager_id = null;
    
    #[Validate('required|date')]
    public $start_date = '';
    
    #[Validate('nullable|date|after_or_equal:start_date')]
    public $end_date = null;
    
    #[Validate('required|string|in:active,inactive,completed')]
    public $status = 'active';
    
    /**
     * Set the campaign data to the form
     *
     * @param Campaign $campaign
     * @return void
     */
    public function setCampaign(Campaign $campaign)
    {
        $this->name = $campaign->name;
        $this->customer_id = $campaign->customer_id;
        $this->platform_id = $campaign->platform_id;
        $this->manager_id = $campaign->manager_id;
        $this->start_date = $campaign->start_date;
        $this->end_date = $campaign->end_date;
        $this->status = $campaign->status;
    }
    
    /**
     * Store a new campaign
     *
     * @return Campaign
     */
    public function store()
    {
        $data = $this->validate();
        
        $campaign = Campaign::create([
            'name' => $data['name'],
            'customer_id' => $data['customer_id'],
            'platform_id' => $data['platform_id'],
            'manager_id' => $data['manager_id'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'status' => $data['status'],
        ]);
        
        $this->reset();
        
        session()->flash('message', 'Campaign created successfully!');
        
        return $campaign;
    }
    
    /**
     * Update an existing campaign
     *
     * @param Campaign $campaign
     * @return void
     */
    public function update(Campaign $campaign)
    {
        $data = $this->validate();
        
        $updateData = [
            'name' => $data['name'],
            'customer_id' => $data['customer_id'],
            'platform_id' => $data['platform_id'],
            'manager_id' => $data['manager_id'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'status' => $data['status'],
        ];
        
        $campaign->update($updateData);
        
        session()->flash('message', 'Campaign updated successfully!');
    }
}

