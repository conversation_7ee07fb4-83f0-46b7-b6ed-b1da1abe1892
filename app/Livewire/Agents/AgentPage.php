<?php

namespace App\Livewire\Agents;

use App\Livewire\Global\Page;
use App\Models\Shift;
use App\Models\User;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\DB;

class AgentPage extends Page
{
    public ?User $agent = null;
    public ?Shift $shift = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_section = [];

    // Dynamic page resume properties
    public string $resumeContentType = 'default';
    public ?array $resumeData = null;
    public ?string $resumeTitle = null;
    public ?string $resumeDescription = null;

    // For backward compatibility
    public array $current_page_resume = [];

    #[On('to-agent-index')]
    public function toAgentIndex(?User $agent = null)
    {
        return $this->redirect(route('agents.index'), navigate: true);
    }

    #[On('to-agent-show')]
    public function toAgentShow(User $agent)
    {
        return $this->redirect(route('agents.show', ['agent' => $agent]), navigate: true);
    }

    #[On('to-agent-edit')]
    public function toAgentEdit(User $agent)
    {
        return $this->redirect(route('agents.edit', ['agent' => $agent]), navigate: true);
    }

    #[On('to-agent-delete')]
    public function toAgentDelete(User $agent)
    {
        return $this->redirect(route('agents.delete', ['agent' => $agent]), navigate: true);
    }

    #[On('to-agent-create')]
    public function toAgentCreate()
    {
        return $this->redirect(route('agents.create'), navigate: true);
    }





    #[On('to-agent-presence')]
    public function toAgentPresence()
    {
        return $this->redirect(route('agents.presence'), navigate: true);
    }

    #[On('to-agent-presence-show')]
    public function toAgentPresenceShow(?Shift $shift)
    {
        return $this->redirect(route('agents.presence.show', ['shift' => $shift]), navigate: true);
    }

    #[On('to-agent-presence-edit')]
    public function toAgentPresenceEdit(?Shift $shift)
    {
        return $this->redirect(route('agents.presence.edit', ['shift' => $shift]), navigate: true);
    }

    #[On('to-agent-presence-delete')]
    public function toAgentPresenceDelete(?Shift $shift)
    {
        return $this->redirect(route('agents.presence.delete', ['shift' => $shift]), navigate: true);
    }


    #[On('to-agent-performance')]
    public function toAgentPerformance()
    {
        return $this->redirect(route('agents.performance'), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Initialize with default values
        $this->resumeTitle = 'Agents';
        $this->resumeDescription = 'Agent management system';
        $this->resumeContentType = 'dashboard';
        $this->resumeData = [
            'title' => 'Agents',
            'description' => 'Agent management system'
        ];

        // For backward compatibility
        $this->current_page_resume = [
            'title' => 'Agents',
            'type' => 'chart',
            'description' => 'Agent management system'
        ];

        switch ($routeName) {
            case 'agents.index':
                $totalAgents = \App\Models\User::role('agent')->count();
                $activeAgents = \App\Models\User::role('agent')->where('status', 'actif')->count();
                $inactiveAgents = \App\Models\User::role('agent')->where('status', 'inactif')->count();
                $trainingAgents = \App\Models\User::role('agent')->where('status', 'in_training')->count();
                $productionAgents = \App\Models\User::role('agent')->where('status', 'actif')->count();

                // Calculate recent activity (using agent role only)
                $recentlyActiveAgents = \App\Models\User::role('agent')
                    ->where('updated_at', '>=', now()->subDays(7))
                    ->count();

                // Calculate all agent metrics in a single query
                $agentMetrics = \App\Models\User::role('agent')
                    ->select([
                        DB::raw('COUNT(*) as total'),
                        DB::raw('SUM(CASE WHEN status = "actif" THEN 1 ELSE 0 END) as active'),
                        DB::raw('SUM(CASE WHEN status = "inactif" THEN 1 ELSE 0 END) as inactive'),
                        DB::raw('SUM(CASE WHEN status = "in_training" THEN 1 ELSE 0 END) as training'),
                        DB::raw('SUM(CASE WHEN status = "actif" THEN 1 ELSE 0 END) as production')
                    ])
                    ->first();

                $totalAgents = $agentMetrics->total;
                $activeAgents = $agentMetrics->active;
                $inactiveAgents = $agentMetrics->inactive;
                $trainingAgents = $agentMetrics->training;
                $productionAgents = $agentMetrics->production;

                // Set dynamic page resume properties
                $this->resumeTitle = 'Agent Management';
                $this->resumeDescription = 'Comprehensive overview of all call center agents in the system. This page allows you to monitor agent status, track performance metrics, and manage agent records. Use the filters and search functionality to find specific agents based on status, role, or other criteria.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Agent Management',
                    'description' => 'Comprehensive overview of all call center agents in the system. This page allows you to monitor agent status, track performance metrics, and manage agent records. Use the filters and search functionality to find specific agents based on status, role, or other criteria.',
                    'metrics' => [
                        [
                            'label' => 'Total Agents',
                            'value' => $totalAgents,
                            'change' => null
                        ],
                        [
                            'label' => 'Active Agents',
                            'value' => $activeAgents,
                            'change' => $totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100) : 0
                        ],
                        [
                            'label' => 'Inactive Agents',
                            'value' => $inactiveAgents,
                            'change' => null
                        ],
                        [
                            'label' => 'Training Agents',
                            'value' => $trainingAgents,
                            'change' => $totalAgents > 0 ? round(($trainingAgents / $totalAgents) * 100) : 0
                        ],
                        [
                            'label' => 'Production Agents',
                            'value' => $productionAgents,
                            'change' => $totalAgents > 0 ? round(($productionAgents / $totalAgents) * 100) : 0
                        ],
                        [
                            'label' => 'Recently Active',
                            'value' => $recentlyActiveAgents,
                            'change' => $totalAgents > 0 ? round(($recentlyActiveAgents / $totalAgents) * 100) : 0
                        ]
                    ],
                    'summary' => 'This dashboard provides a real-time overview of your agent workforce. Currently, ' .
                        $activeAgents . ' out of ' . $totalAgents . ' agents are active (' .
                        ($totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100) : 0) . '%). ' .
                        'There are ' . $trainingAgents . ' agents in training and ' . $productionAgents . ' in production. ' .
                        'In the last 7 days, ' . $recentlyActiveAgents . ' agents have been active in the system.',
                    'actions' => [
                        'Create new agent' => 'Use the "Add Agent" button to register a new agent in the system',
                        'Filter agents' => 'Use the filter controls to find agents by status, role, or other criteria',
                        'Export data' => 'Export the current agent list to CSV or Excel for reporting purposes',
                        'Bulk actions' => 'Select multiple agents to perform actions like status updates or campaign assignments'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Agent Management',
                    'type' => 'dashboard',
                    'description' => 'Comprehensive overview of all call center agents in the system. This page allows you to monitor agent status, track performance metrics, and manage agent records. Use the filters and search functionality to find specific agents based on status, role, or other criteria.',
                    'metrics' => [
                        [
                            'label' => 'Total Agents',
                            'value' => $totalAgents,
                            'change' => null
                        ],
                        [
                            'label' => 'Active Agents',
                            'value' => $activeAgents,
                            'change' => $totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100) : 0
                        ],
                        [
                            'label' => 'Inactive Agents',
                            'value' => $inactiveAgents,
                            'change' => null
                        ],
                        [
                            'label' => 'Training Agents',
                            'value' => $trainingAgents,
                            'change' => $totalAgents > 0 ? round(($trainingAgents / $totalAgents) * 100) : 0
                        ],
                        [
                            'label' => 'Production Agents',
                            'value' => $productionAgents,
                            'change' => $totalAgents > 0 ? round(($productionAgents / $totalAgents) * 100) : 0
                        ],
                        [
                            'label' => 'Recently Active',
                            'value' => $recentlyActiveAgents,
                            'change' => $totalAgents > 0 ? round(($recentlyActiveAgents / $totalAgents) * 100) : 0
                        ]
                    ],
                    'summary' => 'This dashboard provides a real-time overview of your agent workforce. Currently, ' .
                        $activeAgents . ' out of ' . $totalAgents . ' agents are active (' .
                        ($totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100) : 0) . '%). ' .
                        'There are ' . $trainingAgents . ' agents in training and ' . $productionAgents . ' in production. ' .
                        'In the last 7 days, ' . $recentlyActiveAgents . ' agents have been active in the system.',
                    'actions' => [
                        'Create new agent' => 'Use the "Add Agent" button to register a new agent in the system',
                        'Filter agents' => 'Use the filter controls to find agents by status, role, or other criteria',
                        'Export data' => 'Export the current agent list to CSV or Excel for reporting purposes',
                        'Bulk actions' => 'Select multiple agents to perform actions like status updates or campaign assignments'
                    ]
                ];
                break;

            case 'agents.create':
                // Set dynamic page resume properties
                $this->resumeTitle = 'Create New Agent';
                $this->resumeDescription = 'This form allows you to register a new agent in the call center management system. Complete all required information to create the agent profile.';
                $this->resumeContentType = 'form';

                $formConfig = [
                    'title' => 'Create New Agent',
                    'description' => 'This form allows you to register a new agent in the call center management system. Complete all required information to create the agent profile. Once created, the agent can be assigned to campaigns, scheduled for shifts, and have their performance tracked.',
                    'steps' => [
                        'Enter the agent\'s personal information (name, email, phone, address)',
                        'Set the agent\'s role (Training or Production) and access permissions',
                        'Upload required documents (ID, resume, certifications if applicable)',
                        'Set initial status (active/inactive) and employment details',
                        'Assign to campaigns if the agent is ready for immediate assignment',
                        'Review all information for accuracy before submission'
                    ],
                    'fields' => [
                        [
                            'name' => 'Full Name',
                            'required' => true,
                            'description' => 'Agent\'s complete name as it appears on official documents'
                        ],
                        [
                            'name' => 'Email Address',
                            'required' => true,
                            'description' => 'Work email address for system access and communications'
                        ],
                        [
                            'name' => 'Phone Number',
                            'required' => true,
                            'description' => 'Primary contact number for the agent'
                        ],
                        [
                            'name' => 'Role',
                            'required' => true,
                            'description' => 'Determines agent permissions and access levels (Training or Production)'
                        ],
                        [
                            'name' => 'Status',
                            'required' => true,
                            'description' => 'Initial status of the agent (Active or Inactive)'
                        ],
                        [
                            'name' => 'Address',
                            'required' => false,
                            'description' => 'Agent\'s residential address for HR records'
                        ],
                        [
                            'name' => 'Emergency Contact',
                            'required' => false,
                            'description' => 'Name and phone number of emergency contact person'
                        ],
                        [
                            'name' => 'Documents',
                            'required' => false,
                            'description' => 'Upload identification, resume, and other required documents'
                        ]
                    ],
                    'validation_rules' => [
                        'Email must be unique in the system',
                        'Phone number must be in valid format',
                        'Password must meet security requirements (min 8 characters, include numbers and special characters)',
                        'Required documents must be in PDF, JPG, or PNG format and under 5MB each'
                    ],
                    'notes' => 'New agents will be automatically set to inactive status until approved by a manager. Initial login credentials will be sent to the provided email address after account creation.'
                ];

                $this->resumeData = $formConfig;
                $this->current_page_resume = [
                    'title' => 'Create New Agent',
                    'type' => 'form',
                    'description' => $this->resumeDescription,
                    'content' => $formConfig
                ];
                break;
                break;

            case 'agents.show':
                if ($this->agent) {
                    // Get additional agent data
                    $shiftsCount = $this->agent->shifts()->count();
                    $presentShifts = $this->agent->shifts()->where('status', 'present')->count();
                    $attendanceRate = $shiftsCount > 0 ? round(($presentShifts / $shiftsCount) * 100) : 0;

                    // Get campaign names
                    $campaignNames = 'None assigned';
                    if (method_exists($this->agent, 'campaigns')) {
                        $campaigns = $this->agent->campaigns()->pluck('name');
                        if ($campaigns->count() > 0) {
                            $campaignNames = $campaigns->join(', ');
                        }
                    }

                    // Get recent observations
                    $recentObservations = collect([]);
                    if (method_exists($this->agent, 'observations')) {
                        $recentObservations = $this->agent->observations()
                            ->orderBy('created_at', 'desc')
                            ->take(3)
                            ->get()
                            ->map(function($observation) {
                                return [
                                    'date' => $observation->created_at->format('M d, Y'),
                                    'rating' => $observation->rating ?? 'Not rated',
                                    'observer' => $observation->observer ? $observation->observer->getFullNameAttribute() : 'System'
                                ];
                            });
                    }

                    // Set dynamic page resume properties
                    $this->resumeTitle = $this->agent->getFullNameAttribute();
                    $this->resumeDescription = 'Detailed profile for ' . $this->agent->getFullNameAttribute() . '. This page provides comprehensive information about the agent\'s status, performance metrics, campaign assignments, and attendance records.';
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => $this->agent->getFullNameAttribute(),
                        'description' => 'Detailed profile for ' . $this->agent->getFullNameAttribute() . '. This page provides comprehensive information about the agent\'s status, performance metrics, campaign assignments, and attendance records. Use this information to evaluate the agent\'s performance and make informed management decisions.',
                        'stats' => [
                            'status' => $this->agent->status === 'actif' ? 'Active' : 'Inactive',
                            'role' => $this->getRoleName($this->agent->role_id),
                            'campaigns' => method_exists($this->agent, 'campaigns') ? $this->agent->campaigns()->count() : 0,
                            'shifts' => $shiftsCount,
                            'attendance_rate' => $attendanceRate . '%',
                            'average_rating' => method_exists($this->agent, 'observations') && $this->agent->observations()->avg('rating') ? number_format($this->agent->observations()->avg('rating'), 1) . '/5' : 'Not rated'
                        ],
                        'metadata' => [
                            'full_name' => $this->agent->getFullNameAttribute(),
                            'employee_id' => $this->agent->employee_id ?? 'Not assigned',
                            'email' => $this->agent->email,
                            'phone' => $this->agent->phone ?? 'Not provided',
                            'joined' => $this->agent->created_at->format('M d, Y'),
                            'last_active' => $this->agent->updated_at->diffForHumans(),
                            'address' => $this->agent->address ?? 'Not provided',
                            'emergency_contact' => $this->agent->emergency_contact ?? 'Not provided'
                        ],
                        'related_items' => [
                            'assigned_campaigns' => $campaignNames,
                            'recent_shifts' => $this->agent->shifts()
                                ->orderBy('date', 'desc')
                                ->take(3)
                                ->get()
                                ->map(function($shift) {
                                    return $shift->date->format('M d, Y') . ' (' . ucfirst($shift->status) . ')';
                                })
                                ->join(', ') ?: 'No recent shifts',
                            'recent_observations' => $recentObservations->count() > 0
                                ? $recentObservations->map(function($obs) {
                                    return $obs['date'] . ' - Rating: ' . $obs['rating'] . ' by ' . $obs['observer'];
                                  })->join('; ')
                                : 'No recent observations'
                        ],
                        'actions' => [
                            'Edit Agent' => 'Update the agent\'s personal information, contact details, and status',
                            'Assign to Campaign' => 'Add or remove the agent from campaign assignments',
                            'Record Observation' => 'Add a new performance observation for this agent',
                            'View Performance History' => 'See detailed performance metrics and historical data',
                            'View Attendance Records' => 'Review the agent\'s attendance and shift history'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => $this->agent->getFullNameAttribute(),
                        'type' => 'entity',
                        'description' => 'Detailed profile for ' . $this->agent->getFullNameAttribute() . '. This page provides comprehensive information about the agent\'s status, performance metrics, campaign assignments, and attendance records. Use this information to evaluate the agent\'s performance and make informed management decisions.',
                        'stats' => [
                            'status' => $this->agent->status === 'actif' ? 'Active' : 'Inactive',
                            'role' => $this->getRoleName($this->agent->role_id),
                            'campaigns' => method_exists($this->agent, 'campaigns') ? $this->agent->campaigns()->count() : 0,
                            'shifts' => $shiftsCount,
                            'attendance_rate' => $attendanceRate . '%',
                            'average_rating' => method_exists($this->agent, 'observations') && $this->agent->observations()->avg('rating') ? number_format($this->agent->observations()->avg('rating'), 1) . '/5' : 'Not rated'
                        ],
                        'metadata' => [
                            'full_name' => $this->agent->getFullNameAttribute(),
                            'employee_id' => $this->agent->employee_id ?? 'Not assigned',
                            'email' => $this->agent->email,
                            'phone' => $this->agent->phone ?? 'Not provided',
                            'joined' => $this->agent->created_at->format('M d, Y'),
                            'last_active' => $this->agent->updated_at->diffForHumans(),
                            'address' => $this->agent->address ?? 'Not provided',
                            'emergency_contact' => $this->agent->emergency_contact ?? 'Not provided'
                        ],
                        'related_items' => [
                            'assigned_campaigns' => $campaignNames,
                            'recent_shifts' => $this->agent->shifts()
                                ->orderBy('date', 'desc')
                                ->take(3)
                                ->get()
                                ->map(function($shift) {
                                    return $shift->date->format('M d, Y') . ' (' . ucfirst($shift->status) . ')';
                                })
                                ->join(', ') ?: 'No recent shifts',
                            'recent_observations' => $recentObservations->count() > 0
                                ? $recentObservations->map(function($obs) {
                                    return $obs['date'] . ' - Rating: ' . $obs['rating'] . ' by ' . $obs['observer'];
                                  })->join('; ')
                                : 'No recent observations'
                        ],
                        'actions' => [
                            'Edit Agent' => 'Update the agent\'s personal information, contact details, and status',
                            'Assign to Campaign' => 'Add or remove the agent from campaign assignments',
                            'Record Observation' => 'Add a new performance observation for this agent',
                            'View Performance History' => 'See detailed performance metrics and historical data',
                            'View Attendance Records' => 'Review the agent\'s attendance and shift history'
                        ]
                    ];
                }
                break;

            case 'agents.edit':
                if ($this->agent) {
                    // Set dynamic page resume properties
                    $this->resumeTitle = 'Edit Agent: ' . $this->agent->getFullNameAttribute();
                    $this->resumeDescription = 'Update the agent\'s information and settings.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit Agent: ' . $this->agent->getFullNameAttribute(),
                        'description' => 'Update the agent\'s information and settings.',
                        'steps' => [
                            'Update the agent\'s personal information',
                            'Modify role and permissions if needed',
                            'Update campaign assignments',
                            'Save your changes'
                        ],
                        'notes' => 'Changing an agent\'s status will affect their access to the system.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Edit Agent: ' . $this->agent->getFullNameAttribute(),
                        'type' => 'form',
                        'description' => 'Update the agent\'s information and settings.',
                        'steps' => [
                            'Update the agent\'s personal information',
                            'Modify role and permissions if needed',
                            'Update campaign assignments',
                            'Save your changes'
                        ],
                        'notes' => 'Changing an agent\'s status will affect their access to the system.'
                    ];
                }
                break;

            case 'agents.delete':
                if ($this->agent) {
                    // Set dynamic page resume properties
                    $this->resumeTitle = 'Delete Agent: ' . $this->agent->getFullNameAttribute();
                    $this->resumeDescription = 'You are about to permanently remove this agent from the system.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Delete Agent: ' . $this->agent->getFullNameAttribute(),
                        'description' => 'You are about to permanently remove this agent from the system.',
                        'steps' => [
                            'Review the agent\'s information',
                            'Confirm deletion',
                            'Provide a reason for deletion (optional)'
                        ],
                        'notes' => 'This action cannot be undone. All data associated with this agent will be permanently removed.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Delete Agent: ' . $this->agent->getFullNameAttribute(),
                        'type' => 'form',
                        'description' => 'You are about to permanently remove this agent from the system.',
                        'steps' => [
                            'Review the agent\'s information',
                            'Confirm deletion',
                            'Provide a reason for deletion (optional)'
                        ],
                        'notes' => 'This action cannot be undone. All data associated with this agent will be permanently removed.'
                    ];
                }
                break;

            case 'agents.presence':
                $totalShifts = \App\Models\Shift::count();
                $todayShifts = \App\Models\Shift::whereDate('date', now())->count();
                $attendanceRate = \App\Models\Shift::where('status', 'present')->count() / ($totalShifts ?: 1) * 100;
                $absentAgents = \App\Models\Shift::whereDate('date', now())->where('status', 'absent')->count();

                // Set dynamic page resume properties
                $this->resumeTitle = 'Agent Presence';
                $this->resumeDescription = 'Track agent attendance and shift management across the organization.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Agent Presence',
                    'description' => 'Track agent attendance and shift management across the organization.',
                    'metrics' => [
                        [
                            'label' => 'Today\'s Shifts',
                            'value' => $todayShifts,
                            'change' => null
                        ],
                        [
                            'label' => 'Total Shifts',
                            'value' => $totalShifts,
                            'change' => null
                        ],
                        [
                            'label' => 'Attendance Rate',
                            'value' => round($attendanceRate) . '%',
                            'change' => 1.2
                        ],
                        [
                            'label' => 'Absent Agents',
                            'value' => $absentAgents,
                            'change' => -0.5
                        ]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Agent Presence',
                    'type' => 'dashboard',
                    'description' => 'Track agent attendance and shift management across the organization.',
                    'metrics' => [
                        [
                            'label' => 'Today\'s Shifts',
                            'value' => $todayShifts,
                            'change' => null
                        ],
                        [
                            'label' => 'Total Shifts',
                            'value' => $totalShifts,
                            'change' => null
                        ],
                        [
                            'label' => 'Attendance Rate',
                            'value' => round($attendanceRate) . '%',
                            'change' => 1.2
                        ],
                        [
                            'label' => 'Absent Agents',
                            'value' => $absentAgents,
                            'change' => -0.5
                        ]
                    ]
                ];
                break;

            case 'agents.presence.show':
                if ($this->shift) {
                    // Set dynamic page resume properties
                    $this->resumeTitle = 'Shift Details: ' . $this->shift->user->getFullNameAttribute();
                    $this->resumeDescription = 'Detailed information about this shift record.';
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => 'Shift Details: ' . $this->shift->user->getFullNameAttribute(),
                        'description' => 'Detailed information about this shift record.',
                        'stats' => [
                            'status' => ucfirst($this->shift->status),
                            'date' => $this->shift->date->format('M d, Y'),
                            'duration' => $this->shift->duration . ' hours',
                            'campaign' => $this->shift->campaign->name ?? 'N/A'
                        ],
                        'metadata' => [
                            'check_in' => $this->shift->check_in ? $this->shift->check_in->format('h:i A') : 'N/A',
                            'check_out' => $this->shift->check_out ? $this->shift->check_out->format('h:i A') : 'N/A',
                            'recorded_by' => $this->shift->recorded_by ? \App\Models\User::find($this->shift->recorded_by)->name : 'System',
                            'notes' => $this->shift->notes ?? 'No notes'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Shift Details: ' . $this->shift->user->getFullNameAttribute(),
                        'type' => 'entity',
                        'description' => 'Detailed information about this shift record.',
                        'stats' => [
                            'status' => ucfirst($this->shift->status),
                            'date' => $this->shift->date->format('M d, Y'),
                            'duration' => $this->shift->duration . ' hours',
                            'campaign' => $this->shift->campaign->name ?? 'N/A'
                        ],
                        'metadata' => [
                            'check_in' => $this->shift->check_in ? $this->shift->check_in->format('h:i A') : 'N/A',
                            'check_out' => $this->shift->check_out ? $this->shift->check_out->format('h:i A') : 'N/A',
                            'recorded_by' => $this->shift->recorded_by ? \App\Models\User::find($this->shift->recorded_by)->name : 'System',
                            'notes' => $this->shift->notes ?? 'No notes'
                        ]
                    ];
                }
                break;

            case 'agents.presence.edit':
                if ($this->shift) {
                    // Set dynamic page resume properties
                    $this->resumeTitle = 'Edit Shift: ' . $this->shift->user->getFullNameAttribute();
                    $this->resumeDescription = 'Update the shift information for this agent.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit Shift: ' . $this->shift->user->getFullNameAttribute(),
                        'description' => 'Update the shift information for this agent.',
                        'steps' => [
                            'Verify the shift date and agent',
                            'Update the shift status if needed',
                            'Adjust check-in and check-out times',
                            'Add any relevant notes',
                            'Save your changes'
                        ],
                        'notes' => 'Changes to shift records may affect payroll calculations.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Edit Shift: ' . $this->shift->user->getFullNameAttribute(),
                        'type' => 'form',
                        'description' => 'Update the shift information for this agent.',
                        'steps' => [
                            'Verify the shift date and agent',
                            'Update the shift status if needed',
                            'Adjust check-in and check-out times',
                            'Add any relevant notes',
                            'Save your changes'
                        ],
                        'notes' => 'Changes to shift records may affect payroll calculations.'
                    ];
                }
                break;

            case 'agents.presence.delete':
                if ($this->shift) {
                    // Set dynamic page resume properties
                    $this->resumeTitle = 'Delete Shift: ' . $this->shift->user->getFullNameAttribute();
                    $this->resumeDescription = 'You are about to permanently remove this shift record.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Delete Shift: ' . $this->shift->user->getFullNameAttribute(),
                        'description' => 'You are about to permanently remove this shift record.',
                        'steps' => [
                            'Review the shift information',
                            'Confirm deletion',
                            'Provide a reason for deletion (optional)'
                        ],
                        'notes' => 'This action cannot be undone. The shift record will be permanently removed from the system.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Delete Shift: ' . $this->shift->user->getFullNameAttribute(),
                        'type' => 'form',
                        'description' => 'You are about to permanently remove this shift record.',
                        'steps' => [
                            'Review the shift information',
                            'Confirm deletion',
                            'Provide a reason for deletion (optional)'
                        ],
                        'notes' => 'This action cannot be undone. The shift record will be permanently removed from the system.'
                    ];
                }
                break;

            case 'agents.performance':
                // Set dynamic page resume properties
                $this->resumeTitle = 'Agent Performance';
                $this->resumeDescription = 'Monitor and analyze agent performance metrics across all campaigns.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Agent Performance',
                    'description' => 'Monitor and analyze agent performance metrics across all campaigns.',
                    'metrics' => [
                        [
                            'label' => 'Average Rating',
                            'value' => '4.2/5',
                            'change' => 0.3
                        ],
                        [
                            'label' => 'Completion Rate',
                            'value' => '87%',
                            'change' => 2.1
                        ],
                        [
                            'label' => 'Response Time',
                            'value' => '1.8 min',
                            'change' => -0.2
                        ],
                        [
                            'label' => 'Quality Score',
                            'value' => '92%',
                            'change' => 1.5
                        ]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Agent Performance',
                    'type' => 'dashboard',
                    'description' => 'Monitor and analyze agent performance metrics across all campaigns.',
                    'metrics' => [
                        [
                            'label' => 'Average Rating',
                            'value' => '4.2/5',
                            'change' => 0.3
                        ],
                        [
                            'label' => 'Completion Rate',
                            'value' => '87%',
                            'change' => 2.1
                        ],
                        [
                            'label' => 'Response Time',
                            'value' => '1.8 min',
                            'change' => -0.2
                        ],
                        [
                            'label' => 'Quality Score',
                            'value' => '92%',
                            'change' => 1.5
                        ]
                    ]
                ];
                break;

            default:
                // Set dynamic page resume properties
                $this->resumeTitle = 'All Agents';
                $this->resumeDescription = 'Overview of all agents in the system.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'All Agents',
                    'description' => 'Overview of all agents in the system.'
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'All Agents',
                    'type' => 'dashboard',
                    'description' => 'Overview of all agents in the system.'
                ];
                break;
        }
    }

    /**
     * Get role name from role ID
     */
    private function getRoleName($roleId)
    {
        $roles = [
            1 => 'Admin',
            2 => 'Director',
            3 => 'Manager',
            4 => 'Supervisor',
            5 => 'Agent (Training)',
            6 => 'Agent (Production)'
        ];

        return $roles[$roleId] ?? 'Unknown';
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'agent-module',
                'title' => 'Management',
                'description' => 'Agent management',
                'route' => 'agents.index',
                'display' => true,
                'authorized_permissions' => ['manage_agent'],
                'section_routes' => ['agents.create', 'agents.edit', 'agents.delete', 'agents.show'],
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create an agent',
                        'route' => 'agents.create',
                        'display' => true,
                        'authorized_permissions' => ['create_agent'],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit an agent',
                        'route' => 'agents.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_agent'],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete an agent',
                        'route' => 'agents.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_agent'],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show an agent',
                        'route' => 'agents.show',
                        'display' => true,
                        'authorized_permissions' => ['show_agent']
                    ]
                ]
            ],

            [
                'module_id' => 'agent-module',
                'title' => 'Presence',
                'description' => 'Agent presence management',
                'route' => 'agents.presence.index',
                'display' => true,
                'authorized_permissions' => ['manage_agent_presence'],
                'section_routes' => ['agents.presence.edit', 'agents.presence.delete', 'agents.presence.show'],
                'sections' => [
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a shifts',
                        'route' => 'agents.presence.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_agent_presence'],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a shifts',
                        'route' => 'agents.presence.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_agent_presence'],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a shifts',
                        'route' => 'agents.presence.show',
                        'display' => true,
                        'authorized_permissions' => ['show_agent_presence']
                    ]
                ]
            ],
            [
                'module_id' => 'agent-module',
                'title' => 'Performance',
                'description' => 'Agent performance management',
                'route' => 'agents.performance',
                'display' => true,
                'authorized_permissions' => ['show_agent_performance'],
                'section_routes' => [],
                'sections' => []
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        return view('livewire.agents.agent-page', [
            'agent' => $this->agent,
            'shift' => $this->shift,
        ]);
    }
}
