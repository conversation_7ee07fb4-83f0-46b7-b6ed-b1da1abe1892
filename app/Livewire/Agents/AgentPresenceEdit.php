<?php

namespace App\Livewire\Agents;

use App\Livewire\Forms\ShiftForm;
use App\Models\Shift;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class AgentPresenceEdit extends Component
{
    use WithFileUploads;
    public Shift $shift;
    public ShiftForm $form;

    public function mount(Shift $shift)
    {
        $this->shift = $shift;
        $this->form->setShift($shift);
    }

    #[On('update-shift')]
    public function updateShift()
    {
        $this->form->update($this->shift);
        session()->flash('message', 'Shift updated successfully!');
        $this->redirect(route('agents.presence.show', ['shift' => $this->shift->id]), navigate: true);
    }
    public function render()
    {
        return view('livewire.agents.agent-presence-edit');
    }
}
