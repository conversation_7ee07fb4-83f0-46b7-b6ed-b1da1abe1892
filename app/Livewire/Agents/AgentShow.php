<?php

namespace App\Livewire\Agents;

use App\Models\User;
use App\Models\Shift;
use App\Models\Appointment;
use App\Models\Document;
use App\Models\Media;
use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class AgentShow extends Component
{
    public User $agent;
    public array $performanceMetrics = [];
    public array $recentShifts = [];
    public array $recentAppointments = [];
    public array $documents = [];
    public array $evolutionHistory = [];

    public function mount(User $agent)
    {
        $this->agent = $agent;
        $this->loadPerformanceMetrics();
        $this->loadRecentShifts();
        $this->loadRecentAppointments();
        $this->loadDocuments();
        $this->loadEvolutionHistory();
    }

    /**
     * Load agent documents
     */
    protected function loadDocuments()
    {
        $documents = $this->agent->media()
            ->whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])
            ->get();

        // Process documents to include full URLs
        $processedDocuments = $documents->map(function($document) {
            $data = $document->toArray();
            // Add the full URL to each document
            $data['file_url'] = Storage::disk('public')->url($document->file_path);
            return $data;
        });

        $this->documents = $processedDocuments->groupBy('category')->toArray();
    }

    protected function loadPerformanceMetrics()
    {
        // Calculate performance metrics for the agent
        $totalShifts = Shift::where('user_id', $this->agent->id)->count();

        // Calculate total hours in a database-agnostic way
        $totalHours = 0;
        $shifts = Shift::where('user_id', $this->agent->id)
            ->whereNotNull('logout_time')
            ->get();

        foreach ($shifts as $shift) {
            if ($shift->login_time && $shift->logout_time) {
                $totalHours += $shift->login_time->diffInMinutes($shift->logout_time) / 60;
            }
        }

        $this->performanceMetrics = [
            'total_shifts' => $totalShifts,
            'total_hours' => round($totalHours, 2),
            'total_appointments' => Appointment::where('user_id', $this->agent->id)->count(),
            'validated_appointments' => Appointment::where('user_id', $this->agent->id)
                ->whereNotNull('validated_at')
                ->count(),
        ];
    }

    protected function loadRecentShifts()
    {
        $shifts = Shift::where('user_id', $this->agent->id)
            ->orderBy('date', 'desc')
            ->limit(5)
            ->get();

        // Convert to array after ensuring dates are properly formatted
        $this->recentShifts = $shifts->map(function($shift) {
            $data = $shift->toArray();
            // Ensure login_time and logout_time are strings
            if (isset($data['login_time']) && $data['login_time'] instanceof \Carbon\Carbon) {
                $data['login_time'] = $data['login_time']->toDateTimeString();
            }
            if (isset($data['logout_time']) && $data['logout_time'] instanceof \Carbon\Carbon) {
                $data['logout_time'] = $data['logout_time']->toDateTimeString();
            }
            return $data;
        })->toArray();
    }

    protected function loadRecentAppointments()
    {
        $appointments = Appointment::where('user_id', $this->agent->id)
            ->with('customer')
            ->orderBy('scheduled_at', 'desc')
            ->limit(5)
            ->get();

        // Convert to array after ensuring dates are properly formatted
        $this->recentAppointments = $appointments->map(function($appointment) {
            $data = $appointment->toArray();
            // Ensure scheduled_at is a string
            if (isset($data['scheduled_at']) && $data['scheduled_at'] instanceof \Carbon\Carbon) {
                $data['scheduled_at'] = $data['scheduled_at']->toDateTimeString();
            }
            return $data;
        })->toArray();
    }

    /**
     * Load agent's evolution history
     */
    protected function loadEvolutionHistory()
    {
        $history = [];

        // Registration date
        if ($this->agent->created_at) {
            try {
                $registrationDate = $this->agent->created_at instanceof \Carbon\Carbon
                    ? $this->agent->created_at
                    : \Carbon\Carbon::parse($this->agent->created_at);

                $history[] = [
                    'date' => $registrationDate,
                    'event' => 'Registration',
                    'description' => 'Agent registered in the system',
                    'icon' => 'user-plus',
                    'color' => 'blue'
                ];
            } catch (\Exception $e) {
                // Skip this event if date parsing fails
            }
        }

        // Hire date
        if ($this->agent->hire_date) {
            try {
                $hireDate = $this->agent->hire_date instanceof \Carbon\Carbon
                    ? $this->agent->hire_date
                    : \Carbon\Carbon::parse($this->agent->hire_date);

                $history[] = [
                    'date' => $hireDate,
                    'event' => 'Hiring',
                    'description' => 'Agent was hired',
                    'icon' => 'briefcase',
                    'color' => 'green'
                ];
            } catch (\Exception $e) {
                // Skip this event if date parsing fails
            }
        }

        // Training information
        $training = $this->agent->training;
        if ($training) {
            // Training start date
            if ($training->start_date) {
                try {
                    $trainingStartDate = $training->start_date instanceof \Carbon\Carbon
                        ? $training->start_date
                        : \Carbon\Carbon::parse($training->start_date);

                    $history[] = [
                        'date' => $trainingStartDate,
                        'event' => 'Training Started',
                        'description' => $training->module ? 'Started training on ' . $training->module->name : 'Started training',
                        'icon' => 'academic-cap',
                        'color' => 'purple',
                        'details' => [
                            'Module' => $training->module ? $training->module->name : 'Not assigned',
                            'Progress' => $training->progress . '%',
                            'Rating' => $training->rating ? number_format($training->rating, 1) . '/3' : 'Not rated'
                        ]
                    ];
                } catch (\Exception $e) {
                    // Skip this event if date parsing fails
                }
            }

            // Training validation date
            if ($training->validated_at) {
                try {
                    $trainingDuration = null;
                    $validatedAt = $training->validated_at instanceof \Carbon\Carbon
                        ? $training->validated_at
                        : \Carbon\Carbon::parse($training->validated_at);

                    if ($training->start_date) {
                        try {
                            $startDate = $training->start_date instanceof \Carbon\Carbon
                                ? $training->start_date
                                : \Carbon\Carbon::parse($training->start_date);
                            $trainingDuration = $startDate->diffInDays($validatedAt);
                        } catch (\Exception $e) {
                            // If start_date parsing fails, continue without duration
                        }
                    }

                    $history[] = [
                        'date' => $validatedAt,
                        'event' => 'Training Completed',
                        'description' => 'Successfully completed training' . ($trainingDuration ? " in $trainingDuration days" : ''),
                        'icon' => 'check-badge',
                        'color' => 'green',
                        'details' => [
                            'Final Progress' => $training->progress . '%',
                            'Final Rating' => $training->rating ? number_format($training->rating, 1) . '/3' : 'Not rated',
                            'Duration' => $trainingDuration ? "$trainingDuration days" : 'Unknown'
                        ]
                    ];
                } catch (\Exception $e) {
                    // Skip this event if date parsing fails
                }
            }
        }

        // Campaign assignment
        if ($this->agent->campaign) {
            // Find the first shift in this campaign as the production start date
            $firstShift = Shift::where('user_id', $this->agent->id)
                ->orderBy('date', 'asc')
                ->first();

            $productionStartDate = $firstShift ? $firstShift->date : null;

            if ($productionStartDate) {
                try {
                    $startDate = $productionStartDate instanceof \Carbon\Carbon
                        ? $productionStartDate
                        : \Carbon\Carbon::parse($productionStartDate);

                    $history[] = [
                        'date' => $startDate,
                        'event' => 'Production Started',
                        'description' => 'Started working on ' . $this->agent->campaign->name . ' campaign',
                        'icon' => 'play',
                        'color' => 'blue',
                        'details' => [
                            'Campaign' => $this->agent->campaign->name,
                            'Performance' => $this->agent->campaign->performance ? $this->agent->campaign->performance . '%' : 'Not measured',
                            'Productivity' => $this->agent->campaign->productivity ? $this->agent->campaign->productivity . '%' : 'Not measured',
                            'Rating' => $this->agent->campaign->rating ? number_format($this->agent->campaign->rating, 1) . '/3' : 'Not rated'
                        ]
                    ];
                } catch (\Exception $e) {
                    // Skip this event if date parsing fails
                }
            }
        }

        // Format dates for display and store timestamp for sorting
        foreach ($history as &$item) {
            if ($item['date'] instanceof \DateTime || $item['date'] instanceof \Carbon\Carbon) {
                $item['timestamp'] = $item['date']->getTimestamp();
                $item['formatted_date'] = $item['date']->format('M d, Y');
                $item['date'] = $item['date']->toDateTimeString();
            } else {
                // If date is already a string, try to parse it
                try {
                    $carbonDate = \Carbon\Carbon::parse($item['date']);
                    $item['timestamp'] = $carbonDate->getTimestamp();
                    $item['formatted_date'] = $carbonDate->format('M d, Y');
                    $item['date'] = $carbonDate->toDateTimeString();
                } catch (\Exception $e) {
                    // If parsing fails, use current time as fallback
                    $item['timestamp'] = time();
                    $item['formatted_date'] = 'Unknown date';
                    // Keep the original date string
                }
            }
        }

        // Sort history by timestamp
        usort($history, function($a, $b) {
            return $a['timestamp'] - $b['timestamp'];
        });

        $this->evolutionHistory = $history;
    }

    public function render()
    {
        return view('livewire.agents.agent-show');
    }
}
