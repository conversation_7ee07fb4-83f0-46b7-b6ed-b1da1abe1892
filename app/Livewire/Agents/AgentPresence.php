<?php

namespace App\Livewire\Agents;

use App\Models\Shift;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;


class AgentPresence extends Component
{
    use WithPagination;

    public string $sortField = 'date';
    public string $sortDirection = 'asc';
    public int $perPage = 5;
    public array $selectedShifts = [];
    public bool $selectAll = false;
    public ?int $currentShiftId = null;
    public ?Carbon $breakStartedAt = null;
    public bool $onBreak = false;
    public string $agentSearch = '';
    public array $agents = [];
    public ?int $selectedAgentId = null;
    public ?string $startDate = null;
    public ?string $endDate = null;
    public string $dateRangeFilter = 'today'; // 'today', 'yesterday', 'this_week', 'last_week', 'this_month', 'custom'
    public string $validationFilter = '';

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'date', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 5, 'as' => 'pp'],
        'agentSearch' => ['except' => '', 'as' => 'as'],
        'selectedAgentId' => ['except' => '', 'as' => 'agent'],
        'dateRangeFilter' => ['except' => 'today', 'as' => 'date'],
        'validationFilter' => ['except' => '', 'as' => 'validation'],
    ];

    public function mount()
    {
        // 1) Charger la liste des agents selon rôle
        $user = auth()->user();

        if ($user->role_id === 6) {
            // agent : uniquement lui-même
            $this->agents = [$user];
            $this->selectedAgentId = $user->id;

        } else if ($user->role_id === 3) {
            // manager : tous les agents de sa campagne
            $this->agents = User::where('role_id', '=', 6)
                ->where('campaign_id', $user->campaign_id)
                ->get()
                ->all();
        } else if (in_array($user->role_id, [1, 2])) {
            // directeur : tous les agents
            $this->agents = User::where('role_id', '=', 6)->get()->all();
        }

        // réinit sélections
        $this->selectedShifts = [];
        $this->selectAll = false;

        // Vérifier si on a un shift en cours
        $this->currentShiftId = session('current_shift_id');
        // Verifier si le shift a une pause en cours
        $this->onBreak = optional($this->currentShift)->on_break && is_null(optional($this->currentShift)->logout_time);
        $this->breakStartedAt = session('current_shift_break_started_at') ?? null;

        // Set default date range
        $this->updateDateRange('today');
    }

    public function updatedDateRangeFilter($value)
    {
        $this->updateDateRange($value);
    }

    private function updateDateRange($range)
    {
        $this->dateRangeFilter = $range;
        
        switch ($range) {
            case 'today':
                $this->startDate = Carbon::today()->format('Y-m-d');
                $this->endDate = Carbon::today()->format('Y-m-d');
                break;
            case 'yesterday':
                $this->startDate = Carbon::yesterday()->format('Y-m-d');
                $this->endDate = Carbon::yesterday()->format('Y-m-d');
                break;
            case 'this_week':
                $this->startDate = Carbon::now()->startOfWeek()->format('Y-m-d');
                $this->endDate = Carbon::now()->endOfWeek()->format('Y-m-d');
                break;
            case 'last_week':
                $this->startDate = Carbon::now()->subWeek()->startOfWeek()->format('Y-m-d');
                $this->endDate = Carbon::now()->subWeek()->endOfWeek()->format('Y-m-d');
                break;
            case 'this_month':
                $this->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                $this->endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
                break;
            case 'custom':
                $this->startDate = null;
                $this->endDate = null;
                break;
        }
    }

    /**
     * Si on vide le champ de recherche, on remet la sélection à null
     */
    public function updatedAgentSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedAgentId = null;
        }
    }

    /**
     * Select an agent from the dropdown
     */
    public function selectAgent(int $id)
    {
        $this->selectedAgentId = $id;
        $agent = User::find($id);
        if ($agent) {
            $this->agentSearch = $agent->first_name . ' ' . $agent->last_name;
        }
    }

    public function updating(string $name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection', 'dateFilter', 'validationFilter'], true)) {
            $this->resetPage();
            $this->selectedShifts = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        // bascule asc/desc si même champ
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedShifts = $value
            ? $this->shifts->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleShiftSelection(int $shiftId)
    {
        if (in_array($shiftId, $this->selectedShifts, true)) {
            $this->selectedShifts = array_diff($this->selectedShifts, [$shiftId]);
        } else {
            $this->selectedShifts[] = $shiftId;
        }

        $pageIds = $this->shifts->pluck('id')->all();
        $this->selectAll = !empty($this->selectedShifts)
            && empty(array_diff($pageIds, $this->selectedShifts));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedShifts)) {
            Shift::whereIn('id', $this->selectedShifts)->delete();
            session()->flash('message', 'Shifts supprimés avec succès !');
            $this->selectedShifts = [];
            $this->selectAll = false;
            $this->resetPage();
        }
    }

    public function validateShift(int $id)
    {
        Shift::where('id', $id)->update(['is_validated' => 'active']);
    }

    public function unvalidateShift(int $id)
    {
        Shift::where('id', $id)->update(['is_validated' => 'inactive']);
    }

    protected function getShiftsQuery(): Builder
    {
        $user = auth()->user();

        // Base query avec jointure user pour tri sur user_name
        $query = Shift::query()->with('user');

        // Filtrer par agent sélectionné
        if ($user->role_id === 6) {
            // agent : ne voit que son propre shift
            $query->where('user_id', '=', $user->id);
        } else if ($user->role_id === 3) {
            // manager : ne voit que les de ses agents
            $query->whereHas('user', function ($q) use ($user) {
                $q->where('campaign_id', $user->campaign_id);
            });
        } else {
            // manager/directeur : filtre sur $this->selectedAgentId
            if ($this->selectedAgentId) {
                $query->where('user_id', '=', $this->selectedAgentId);
            }
        }

        // Recherche sur nom de l’agent
        if (trim($this->agentSearch) !== '') {
            $term = '%' . trim($this->agentSearch) . '%';
            $query->whereHas(
                'user',
                fn($q) =>
                $q->where('first_name', 'like', $term)
                    ->orWhere('last_name', 'like', $term)
            );
        }

        // Apply date filter
        $this->applyDateFilter($query);

        // Apply validation filter
        if ($this->validationFilter === 'validated') {
            $query->where('is_validated', true);
        } elseif ($this->validationFilter === 'not_validated') {
            $query->where('is_validated', false);
        }

        // Tri
        if ($this->sortField === 'user_name') {
            $query->join('users', 'shifts.user_id', '=', 'users.id')
                ->orderBy('users.first_name', $this->sortDirection)
                ->select('shifts.*');
        } elseif ($this->sortField === 'start_day') {
            $query->orderBy('login_time', $this->sortDirection);
        } elseif ($this->sortField === 'end_day') {
            $query->orderBy('logout_time', $this->sortDirection);
        } elseif ($this->sortField === 'break_duration') {
            $query->orderBy('break_duration', $this->sortDirection);
        } elseif ($this->sortField === 'hours_worked') {
            $query->orderBy('hours_worked', $this->sortDirection);
        } else {
            $query->orderBy($this->sortField, $this->sortDirection);
        }

        return $query;
    }

    // computed property : accessible via $this->shifts :contentReference[oaicite:1]{index=1}
    public function getShiftsProperty()
    {
        return $this->getShiftsQuery()->paginate($this->perPage);
    }

    /**
     * Get filtered agents based on search term
     */
    public function getFilteredAgentsProperty()
    {
        $user = auth()->user();
        $term = trim($this->agentSearch);

        $query = User::query()->where('role_id', 6);

        if ($user->role_id === 3) {
            $query->where('campaign_id', $user->campaign_id);
        } elseif ($user->role_id === 6) {
            $query->where('id', $user->id);
        }

        if ($term !== '') {
            $like = "%{$term}%";
            $query->where(function (Builder $q) use ($like) {
                $q->where('first_name', 'like', $like)
                    ->orWhere('last_name', 'like', $like)
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", [$like]);
            });
        }

        return $query->orderBy('first_name')->limit(10)->get();
    }

    /**
     * Start the day: create a shift with scheduled information if available
     */
    public function startDay(): void
    {
        // Get the user's scheduled shift for today if available
        $userId = auth()->id();
        $today = now()->toDateString();

        // Create the shift
        $shiftData = [
            'user_id' => $userId,
            'login_time' => now(),
            'date' => $today,
            'break_duration' => 0,
            'lunch_duration' => 0,
            'training_duration' => 0,
            'personal_duration' => 0,
            'status' => 'pending',
            'shift_type' => 'regular',
        ];

        // TODO: In a real implementation, we would fetch the scheduled shift from a schedule table
        // For now, we'll just set some default scheduled times
        $workdayStart = now()->setTime(9, 0);
        $workdayEnd = now()->setTime(17, 0);

        // Only set scheduled times if within reasonable hours (8am-6pm)
        $currentHour = now()->hour;
        if ($currentHour >= 8 && $currentHour <= 18) {
            $shiftData['scheduled_start_time'] = $workdayStart;
            $shiftData['scheduled_end_time'] = $workdayEnd;
            $shiftData['scheduled_duration_minutes'] = $workdayStart->diffInMinutes($workdayEnd);
        }

        $shift = Shift::create($shiftData);

        $this->currentShiftId = $shift->id;
        session(['current_shift_id' => $shift->id]);
    }

    /**
     * Start a break of a specific type
     */
    public function startBreak($breakType = 'standard')
    {
        $this->breakStartedAt = now();
        session(['current_shift_break_started_at' => $this->breakStartedAt]);
        session(['current_break_type' => $breakType]);

        $shift = Shift::find($this->currentShiftId);
        if (!$shift) {
            return;
        }

        $this->onBreak = true;
        $shift->update(['on_break' => $this->onBreak]);

        // Store break start in the breaks JSON field
        $breaks = json_decode($shift->breaks ?? '[]', true);
        $breaks[] = [
            'type' => $breakType,
            'start_time' => now()->toDateTimeString(),
            'end_time' => null,
            'duration' => null
        ];

        $shift->update(['breaks' => json_encode($breaks)]);
    }

    /**
     * Start a lunch break
     */
    public function startLunchBreak()
    {
        $this->startBreak('lunch');
    }

    /**
     * Start a training break
     */
    public function startTrainingBreak()
    {
        $this->startBreak('training');
    }

    /**
     * Start a personal break
     */
    public function startPersonalBreak()
    {
        $this->startBreak('personal');
    }

    /**
     * End the current break and calculate duration
     */
    public function endBreak()
    {
        if (!$this->currentShiftId || !$this->breakStartedAt) {
            return;
        }

        $shift = Shift::find($this->currentShiftId);
        if (!$shift) {
            return;
        }

        // Calculate duration in minutes (absolute value)
        $minutes = $this->breakStartedAt->diffInMinutes(now(), true);

        // Get the break type
        $breakType = session('current_break_type', 'standard');

        // Update the appropriate break duration field
        if ($minutes > 0) {
            switch ($breakType) {
                case 'lunch':
                    $shift->increment('lunch_duration', (int) $minutes);
                    break;
                case 'training':
                    $shift->increment('training_duration', (int) $minutes);
                    break;
                case 'personal':
                    $shift->increment('personal_duration', (int) $minutes);
                    break;
                default:
                    $shift->increment('break_duration', (int) $minutes);
            }
        }

        // Update the breaks JSON field
        $breaks = json_decode($shift->breaks ?? '[]', true);
        if (!empty($breaks)) {
            $lastBreakIndex = count($breaks) - 1;
            $breaks[$lastBreakIndex]['end_time'] = now()->toDateTimeString();
            $breaks[$lastBreakIndex]['duration'] = $minutes;
            $shift->update(['breaks' => json_encode($breaks)]);
        }

        // Reset break state
        $this->onBreak = false;
        $shift->update(['on_break' => $this->onBreak]);

        // Clear break session data
        $this->breakStartedAt = null;
        session(['current_shift_break_started_at' => null]);
        session(['current_break_type' => null]);
    }

    /**
     * Fin de journée : on ferme le shift, calcule hours_worked
     */
    public function endDay()
    {
        if (!$this->currentShiftId) {
            return;
        }

        $shift = Shift::find($this->currentShiftId);
        $logout = now();

        $shift->logout_time = $logout;

        // Total minutes entre login et logout (positif)
        $totalMinutes = Carbon::parse($shift->login_time)
            ->diffInMinutes($logout, true)
            - $shift->break_duration;

        // Convertir en heures décimales, 2 chiffres après la virgule
        $shift->hours_worked = round($totalMinutes / 60, 2);

        $shift->save();

        // Réinitialiser l’état
        $this->currentShiftId = null;
        session(['current_shift_id' => $this->currentShiftId]);
        $this->breakStartedAt = null;
        session(['current_shift_break_started_at' => $this->breakStartedAt]);
    }

    /**
     * Renvoie le shift du jour en cours (ou null si aucun)
     */
    public function getCurrentShiftProperty()
    {
        return Shift::where('user_id', auth()->id())
            ->whereDate('date', today())
            ->latest('login_time')
            ->first();
    }

    /**
     * Apply date filter to the query
     */
    protected function applyDateFilter(Builder $query): void
    {
        switch ($this->dateRangeFilter) {
            case 'today':
                $query->whereDate('date', today());
                break;
            case 'yesterday':
                $query->whereDate('date', today()->subDay());
                break;
            case 'this_week':
                $query->whereBetween('date', [
                    now()->startOfWeek()->toDateString(),
                    now()->endOfWeek()->toDateString()
                ]);
                break;
            case 'last_week':
                $query->whereBetween('date', [
                    now()->subWeek()->startOfWeek()->toDateString(),
                    now()->subWeek()->endOfWeek()->toDateString()
                ]);
                break;
            case 'this_month':
                $query->whereBetween('date', [
                    now()->startOfMonth()->toDateString(),
                    now()->endOfMonth()->toDateString()
                ]);
                break;
            case 'custom':
                if ($this->startDate && $this->endDate) {
                    $query->whereBetween('date', [
                        $this->startDate,
                        $this->endDate
                    ]);
                }
                break;
            // Custom date range would be handled with additional properties
        }
    }

    /**
     * Approve a shift (for supervisors/managers)
     */
    public function approveShift($shiftId)
    {
        // Check if user has permission to approve shifts
        if (auth()->user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to approve shifts.');
            return;
        }

        $shift = Shift::find($shiftId);
        if (!$shift) {
            session()->flash('error', 'Shift not found.');
            return;
        }

        $shift->update([
            'status' => 'approved',
            'approved_by' => auth()->id()
        ]);

        session()->flash('message', 'Shift approved successfully.');
    }

    /**
     * Reject a shift (for supervisors/managers)
     */
    public function rejectShift($shiftId, $reason = '')
    {
        // Check if user has permission to reject shifts
        if (auth()->user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to reject shifts.');
            return;
        }

        $shift = Shift::find($shiftId);
        if (!$shift) {
            session()->flash('error', 'Shift not found.');
            return;
        }

        $shift->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'notes' => $reason
        ]);

        session()->flash('message', 'Shift rejected.');
    }

    /**
     * Mark a shift as an exception (for supervisors/managers)
     */
    public function markShiftAsException($shiftId, $notes = '')
    {
        // Check if user has permission
        if (auth()->user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to mark shifts as exceptions.');
            return;
        }

        $shift = Shift::find($shiftId);
        if (!$shift) {
            session()->flash('error', 'Shift not found.');
            return;
        }

        $shift->update([
            'status' => 'exception',
            'notes' => $notes
        ]);

        session()->flash('message', 'Shift marked as exception.');
    }

    public function render()
    {
        return view('livewire.agents.agent-presence', [
            'shifts' => $this->shifts,
            'filteredAgents' => $this->filteredAgents,
        ]);
    }
}
