<?php

namespace App\Livewire\Agents;

use App\Models\Shift;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class EnhancedAgentPresence extends Component
{
    use WithPagination;

    // Basic properties
    public string $sortField = 'date';
    public string $sortDirection = 'asc';
    public int $perPage = 10;
    public array $selectedShifts = [];
    public bool $selectAll = false;

    // Current shift tracking
    public ?int $currentShiftId = null;
    public ?Carbon $breakStartedAt = null;
    public bool $onBreak = false;
    public string $currentBreakType = 'standard';

    // Filters
    public string $agentSearch = '';
    public array $agents = [];
    public ?int $selectedAgentId = null;
    public string $dateFilter = 'today';
    public string $statusFilter = '';
    public string $shiftTypeFilter = '';
    public string $adherenceFilter = '';

    // Dashboard metrics
    public array $metrics = [
        'total_shifts' => 0,
        'on_time_logins' => 0,
        'late_logins' => 0,
        'early_logouts' => 0,
        'adherence_percentage' => 0,
        'average_break_time' => 0,
    ];

    protected $queryString = [
        'sortField' => ['except' => 'date', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
        'agentSearch' => ['except' => '', 'as' => 'as'],
        'selectedAgentId' => ['except' => '', 'as' => 'agent'],
        'dateFilter' => ['except' => 'today', 'as' => 'date'],
        'statusFilter' => ['except' => '', 'as' => 'status'],
        'shiftTypeFilter' => ['except' => '', 'as' => 'type'],
        'adherenceFilter' => ['except' => '', 'as' => 'adherence'],
    ];

    public function mount()
    {
        // Load agents based on user role
        $user = auth()->user();

        if ($user->hasRole('agent')) {
            // Agent: only themselves
            $this->agents = [$user];
            $this->selectedAgentId = $user->id;
        } else if ($user->hasRole('platform_manager')) {
            // Manager: all agents in their campaign
            $this->agents = User::role('agent')
                ->where('campaign_id', $user->campaign_id)
                ->get()
                ->all();
        } else if ($user->hasAnyRole(['administrator', 'director'])) {
            // Director: all agents
            $this->agents = User::role('agent')->get()->all();
        }

        // Reset selections
        $this->selectedShifts = [];
        $this->selectAll = false;

        // Check if there's an active shift
        $this->currentShiftId = session('current_shift_id');

        // Check if the shift has an active break
        $this->onBreak = optional($this->currentShift)->on_break && is_null(optional($this->currentShift)->logout_time);
        $this->breakStartedAt = session('current_shift_break_started_at') ?? null;
        $this->currentBreakType = session('current_break_type') ?? 'standard';

        // Load dashboard metrics
        $this->loadMetrics();
    }

    /**
     * Load dashboard metrics for the current view
     */
    protected function loadMetrics()
    {
        $query = $this->getShiftsQuery();

        // Total shifts
        $this->metrics['total_shifts'] = $query->count();

        // On-time logins (within 5 minutes of scheduled start)
        $this->metrics['on_time_logins'] = $query->clone()
            ->whereNotNull('scheduled_start_time')
            ->whereRaw('ABS(TIMESTAMPDIFF(MINUTE, login_time, scheduled_start_time)) <= 5')
            ->count();

        // Late logins (more than 5 minutes after scheduled start)
        $this->metrics['late_logins'] = $query->clone()
            ->whereNotNull('scheduled_start_time')
            ->whereRaw('TIMESTAMPDIFF(MINUTE, scheduled_start_time, login_time) > 5')
            ->count();

        // Early logouts (more than 5 minutes before scheduled end)
        $this->metrics['early_logouts'] = $query->clone()
            ->whereNotNull('scheduled_end_time')
            ->whereNotNull('logout_time')
            ->whereRaw('TIMESTAMPDIFF(MINUTE, logout_time, scheduled_end_time) > 5')
            ->count();

        // Average adherence percentage
        $this->metrics['adherence_percentage'] = $query->clone()
            ->whereNotNull('adherence_percentage')
            ->avg('adherence_percentage') ?? 0;

        // Average break time
        $totalBreakMinutes = $query->clone()->sum('break_duration') +
            $query->clone()->sum('lunch_duration') +
            $query->clone()->sum('training_duration') +
            $query->clone()->sum('personal_duration');

        $shiftsWithBreaks = $query->clone()
            ->where(function($q) {
                $q->where('break_duration', '>', 0)
                  ->orWhere('lunch_duration', '>', 0)
                  ->orWhere('training_duration', '>', 0)
                  ->orWhere('personal_duration', '>', 0);
            })
            ->count();

        $this->metrics['average_break_time'] = $shiftsWithBreaks > 0
            ? round($totalBreakMinutes / $shiftsWithBreaks)
            : 0;
    }

    /**
     * Reset search when emptied
     */
    public function updatedAgentSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedAgentId = null;
        }
    }

    /**
     * Reset page when filters change
     */
    public function updating(string $name)
    {
        if (in_array($name, [
            'sortField', 'sortDirection', 'perPage', 'dateFilter',
            'statusFilter', 'shiftTypeFilter', 'adherenceFilter'
        ], true)) {
            $this->resetPage();
            $this->selectedShifts = [];
            $this->selectAll = false;
        }
    }

    /**
     * Sort by field
     */
    public function sortBy(string $field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    /**
     * Toggle select all shifts
     */
    public function updatedSelectAll(bool $value)
    {
        $this->selectedShifts = $value
            ? $this->shifts->pluck('id')->map('intval')->all()
            : [];
    }

    /**
     * Toggle selection of a single shift
     */
    public function toggleShiftSelection(int $shiftId)
    {
        if (in_array($shiftId, $this->selectedShifts, true)) {
            $this->selectedShifts = array_diff($this->selectedShifts, [$shiftId]);
        } else {
            $this->selectedShifts[] = $shiftId;
        }

        $pageIds = $this->shifts->pluck('id')->all();
        $this->selectAll = !empty($this->selectedShifts)
            && empty(array_diff($pageIds, $this->selectedShifts));
    }

    /**
     * Delete selected shifts
     */
    public function deleteSelected()
    {
        if (!empty($this->selectedShifts)) {
            Shift::whereIn('id', $this->selectedShifts)->delete();
            session()->flash('message', 'Shifts deleted successfully!');
            $this->selectedShifts = [];
            $this->selectAll = false;
            $this->resetPage();
            $this->loadMetrics();
        }
    }

    /**
     * Get shifts query with filters
     */
    protected function getShiftsQuery(): Builder
    {
        $user = auth()->user();

        // Base query with user relation
        $query = Shift::query()->with(['user', 'approver']);

        // Filter by agent based on role
        if ($user->hasRole('agent')) {
            // Agent: only see their own shifts
            $query->where('user_id', '=', $user->id);
        } else if ($user->hasRole('platform_manager')) {
            // Manager: only see shifts from their campaign agents
            $query->whereHas('user', function ($q) use ($user) {
                $q->where('campaign_id', $user->campaign_id);
            });
        } else {
            // Director: filter by selected agent if any
            if ($this->selectedAgentId) {
                $query->where('user_id', '=', $this->selectedAgentId);
            }
        }

        // Search by agent name
        if (trim($this->agentSearch) !== '') {
            $term = '%' . trim($this->agentSearch) . '%';
            $query->whereHas(
                'user',
                fn($q) =>
                $q->where('first_name', 'like', $term)
                    ->orWhere('last_name', 'like', $term)
            );
        }

        // Apply date filter
        $this->applyDateFilter($query);

        // Apply status filter
        if ($this->statusFilter) {
            $query->where('status', $this->statusFilter);
        }

        // Apply shift type filter
        if ($this->shiftTypeFilter) {
            $query->where('shift_type', $this->shiftTypeFilter);
        }

        // Apply adherence filter
        if ($this->adherenceFilter) {
            switch ($this->adherenceFilter) {
                case 'excellent':
                    $query->where('adherence_percentage', '>=', 95);
                    break;
                case 'good':
                    $query->whereBetween('adherence_percentage', [90, 94.99]);
                    break;
                case 'average':
                    $query->whereBetween('adherence_percentage', [85, 89.99]);
                    break;
                case 'poor':
                    $query->where('adherence_percentage', '<', 85);
                    break;
            }
        }

        // Apply sorting
        if ($this->sortField === 'user_name') {
            $query->join('users', 'shifts.user_id', '=', 'users.id')
                ->orderBy('users.first_name', $this->sortDirection)
                ->select('shifts.*');
        } else {
            $query->orderBy($this->sortField, $this->sortDirection);
        }

        return $query;
    }

    // Computed property for shifts
    public function getShiftsProperty()
    {
        return $this->getShiftsQuery()->paginate($this->perPage);
    }

    /**
     * Get the current active shift for the authenticated user
     */
    public function getCurrentShiftProperty()
    {
        return Shift::where('user_id', auth()->id())
            ->whereDate('date', today())
            ->whereNull('logout_time')
            ->latest('login_time')
            ->first();
    }

    /**
     * Apply date filter to the query
     */
    protected function applyDateFilter(Builder $query): void
    {
        switch ($this->dateFilter) {
            case 'today':
                $query->whereDate('date', today());
                break;
            case 'yesterday':
                $query->whereDate('date', today()->subDay());
                break;
            case 'this_week':
                $query->whereBetween('date', [
                    now()->startOfWeek()->toDateString(),
                    now()->endOfWeek()->toDateString()
                ]);
                break;
            case 'last_week':
                $query->whereBetween('date', [
                    now()->subWeek()->startOfWeek()->toDateString(),
                    now()->subWeek()->endOfWeek()->toDateString()
                ]);
                break;
            case 'this_month':
                $query->whereMonth('date', now()->month)
                    ->whereYear('date', now()->year);
                break;
            case 'last_month':
                $query->whereMonth('date', now()->subMonth()->month)
                    ->whereYear('date', now()->subMonth()->year);
                break;
            case 'custom':
                // Custom date range would be handled with additional properties
                break;
        }
    }

    /**
     * Start the day: create a shift with scheduled information if available
     */
    public function startDay(): void
    {
        // Get the user's scheduled shift for today if available
        $userId = auth()->id();
        $today = now()->toDateString();

        // Create the shift
        $shiftData = [
            'user_id' => $userId,
            'login_time' => now(),
            'date' => $today,
            'break_duration' => 0,
            'lunch_duration' => 0,
            'training_duration' => 0,
            'personal_duration' => 0,
            'status' => 'pending',
            'shift_type' => 'regular',
        ];

        // TODO: In a real implementation, we would fetch the scheduled shift from a schedule table
        // For now, we'll just set some default scheduled times
        $workdayStart = now()->setTime(9, 0);
        $workdayEnd = now()->setTime(17, 0);

        // Only set scheduled times if within reasonable hours (8am-6pm)
        $currentHour = now()->hour;
        if ($currentHour >= 8 && $currentHour <= 18) {
            $shiftData['scheduled_start_time'] = $workdayStart;
            $shiftData['scheduled_end_time'] = $workdayEnd;
            $shiftData['scheduled_duration_minutes'] = $workdayStart->diffInMinutes($workdayEnd);
        }

        $shift = Shift::create($shiftData);

        $this->currentShiftId = $shift->id;
        session(['current_shift_id' => $shift->id]);

        $this->loadMetrics();
        session()->flash('message', 'Shift started successfully.');
    }

    /**
     * Start a break of a specific type
     */
    public function startBreak($breakType = 'standard')
    {
        $this->breakStartedAt = now();
        $this->currentBreakType = $breakType;

        session(['current_shift_break_started_at' => $this->breakStartedAt]);
        session(['current_break_type' => $breakType]);

        $shift = Shift::find($this->currentShiftId);
        if (!$shift) {
            return;
        }

        $this->onBreak = true;
        $shift->update(['on_break' => $this->onBreak]);

        // Store break start in the breaks JSON field
        $breaks = json_decode($shift->breaks ?? '[]', true);
        $breaks[] = [
            'type' => $breakType,
            'start_time' => now()->toDateTimeString(),
            'end_time' => null,
            'duration' => null
        ];

        $shift->update(['breaks' => json_encode($breaks)]);

        session()->flash('message', ucfirst($breakType) . ' break started.');
    }

    /**
     * Start a lunch break
     */
    public function startLunchBreak()
    {
        $this->startBreak('lunch');
    }

    /**
     * Start a training break
     */
    public function startTrainingBreak()
    {
        $this->startBreak('training');
    }

    /**
     * Start a personal break
     */
    public function startPersonalBreak()
    {
        $this->startBreak('personal');
    }

    /**
     * End the current break and calculate duration
     */
    public function endBreak()
    {
        if (!$this->currentShiftId || !$this->breakStartedAt) {
            return;
        }

        $shift = Shift::find($this->currentShiftId);
        if (!$shift) {
            return;
        }

        // Calculate duration in minutes (absolute value)
        $minutes = $this->breakStartedAt->diffInMinutes(now(), true);

        // Get the break type
        $breakType = $this->currentBreakType;

        // Update the appropriate break duration field
        if ($minutes > 0) {
            switch ($breakType) {
                case 'lunch':
                    $shift->increment('lunch_duration', (int) $minutes);
                    break;
                case 'training':
                    $shift->increment('training_duration', (int) $minutes);
                    break;
                case 'personal':
                    $shift->increment('personal_duration', (int) $minutes);
                    break;
                default:
                    $shift->increment('break_duration', (int) $minutes);
            }
        }

        // Update the breaks JSON field
        $breaks = json_decode($shift->breaks ?? '[]', true);
        if (!empty($breaks)) {
            $lastBreakIndex = count($breaks) - 1;
            $breaks[$lastBreakIndex]['end_time'] = now()->toDateTimeString();
            $breaks[$lastBreakIndex]['duration'] = $minutes;
            $shift->update(['breaks' => json_encode($breaks)]);
        }

        // Reset break state
        $this->onBreak = false;
        $shift->update(['on_break' => $this->onBreak]);

        // Clear break session data
        $this->breakStartedAt = null;
        $this->currentBreakType = 'standard';
        session(['current_shift_break_started_at' => null]);
        session(['current_break_type' => null]);

        $this->loadMetrics();
        session()->flash('message', 'Break ended successfully.');
    }

    /**
     * End the day: close the shift, calculate hours worked and adherence
     */
    public function endDay()
    {
        if (!$this->currentShiftId) {
            return;
        }

        $shift = Shift::find($this->currentShiftId);
        if (!$shift) {
            return;
        }

        $logout = now();
        $shift->logout_time = $logout;

        // Calculate total break time
        $totalBreakTime = $shift->total_break_time;

        // Calculate total minutes between login and logout (minus breaks)
        $totalMinutes = Carbon::parse($shift->login_time)
            ->diffInMinutes($logout, true) - $totalBreakTime;

        // Convert to decimal hours, rounded to 2 decimal places
        $shift->hours_worked = round($totalMinutes / 60, 2);

        // Calculate adherence if scheduled times are available
        if ($shift->scheduled_start_time && $shift->scheduled_end_time) {
            $shift->adherence_percentage = $shift->calculateAdherence();
        }

        // Mark the shift as completed
        $shift->is_completed = true;

        $shift->save();

        // Reset session state
        $this->currentShiftId = null;
        $this->breakStartedAt = null;
        $this->currentBreakType = 'standard';
        session(['current_shift_id' => null]);
        session(['current_shift_break_started_at' => null]);
        session(['current_break_type' => null]);

        $this->loadMetrics();
        session()->flash('message', 'Shift ended successfully.');
    }

    /**
     * Approve a shift (for supervisors/managers)
     */
    public function approveShift($shiftId)
    {
        // Check if user has permission to approve shifts
        if (auth()->user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to approve shifts.');
            return;
        }

        $shift = Shift::find($shiftId);
        if (!$shift) {
            session()->flash('error', 'Shift not found.');
            return;
        }

        $shift->update([
            'status' => 'approved',
            'approved_by' => auth()->id()
        ]);

        $this->loadMetrics();
        session()->flash('message', 'Shift approved successfully.');
    }

    /**
     * Reject a shift (for supervisors/managers)
     */
    public function rejectShift($shiftId, $reason = '')
    {
        // Check if user has permission to reject shifts
        if (auth()->user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to reject shifts.');
            return;
        }

        $shift = Shift::find($shiftId);
        if (!$shift) {
            session()->flash('error', 'Shift not found.');
            return;
        }

        $shift->update([
            'status' => 'rejected',
            'approved_by' => auth()->id(),
            'notes' => $reason
        ]);

        $this->loadMetrics();
        session()->flash('message', 'Shift rejected.');
    }

    /**
     * Mark a shift as an exception (for supervisors/managers)
     */
    public function markShiftAsException($shiftId, $notes = '')
    {
        // Check if user has permission
        if (auth()->user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to mark shifts as exceptions.');
            return;
        }

        $shift = Shift::find($shiftId);
        if (!$shift) {
            session()->flash('error', 'Shift not found.');
            return;
        }

        $shift->update([
            'status' => 'exception',
            'notes' => $notes
        ]);

        $this->loadMetrics();
        session()->flash('message', 'Shift marked as exception.');
    }

    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.agents.enhanced-agent-presence', [
            'shifts' => $this->shifts,
            'metrics' => $this->metrics,
        ]);
    }
}
