<?php

namespace App\Livewire\Agents;

use App\Models\Appointment;
use App\Models\PerformanceMetric;
use App\Models\Shift;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;

class AgentPerformance extends Component
{
    use WithPagination;

    public string $agentSearch = '';
    public array $agents = [];
    public ?string $startDate = null;
    public ?string $endDate = null;
    public ?int $selectedAgentId = null;
    public string $period = 'week'; // 'day', 'week', 'month', 'year'
    public array $performanceData = [];
    public array $chartData = [];

    protected $queryString = [
        'agentSearch' => ['except' => '', 'as' => 'as'],
        'selectedAgentId' => ['except' => null, 'as' => 'agent'],
        'period' => ['except' => 'week'],
        'startDate' => ['except' => null],
        'endDate' => ['except' => null],
    ];

    public function mount()
    {
        $user = auth()->user();

        // Set default date range if not provided
        if (!$this->startDate) {
            $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
        }

        if (!$this->endDate) {
            $this->endDate = Carbon::now()->format('Y-m-d');
        }

        // Load agents based on user role
        $query = User::query()->where('role_id', 6);

        if ($user->role_id === 6) {
            $this->agents = [$user];
            $this->selectedAgentId = $user->id;
        } elseif ($user->role_id === 3) {
            $this->agents = $query->where('campaign_id', $user->campaign_id)->get()->all();
        } elseif (in_array($user->role_id, [1, 2])) {
            $this->agents = $query->get()->all();
        }

        // If agent is already selected, load performance data
        if ($this->selectedAgentId) {
            $this->loadPerformanceData();
        }
    }

    public function getSelectedAgentProperty()
    {
        return User::with(['campaign', 'appointments' => function($query) {
                if ($this->startDate && $this->endDate) {
                    $query->whereBetween('scheduled_at', [
                        Carbon::parse($this->startDate)->startOfDay(),
                        Carbon::parse($this->endDate)->endOfDay()
                    ]);
                }
            }])
            ->when($this->selectedAgentId, fn($q) => $q->where('id', $this->selectedAgentId))
            ->first();
    }

    public function updatedAgentSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedAgentId = null;
            $this->performanceData = [];
            $this->chartData = [];
        }
    }

    public function updatedStartDate()
    {
        if ($this->selectedAgentId) {
            $this->loadPerformanceData();
        }
    }

    public function updatedEndDate()
    {
        if ($this->selectedAgentId) {
            $this->loadPerformanceData();
        }
    }

    public function updatedPeriod()
    {
        if ($this->selectedAgentId) {
            $this->loadPerformanceData();
        }
    }

    public function getFilteredAgentsProperty()
    {
        $user = auth()->user();
        $term = trim($this->agentSearch);

        $query = User::query()->where('role_id', 6);

        if ($user->role_id === 3) {
            $query->where('campaign_id', $user->campaign_id);
        } elseif ($user->role_id === 6) {
            $query->where('id', $user->id);
        }

        if ($term !== '') {
            $like = "%{$term}%";
            $query->where(function (Builder $q) use ($like) {
                $q->where('first_name', 'like', $like)
                    ->orWhere('last_name', 'like', $like)
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", [$like]);
            });
        }

        return $query->orderBy('first_name')->limit(10)->get();
    }

    public function selectAgent(int $id)
    {
        $this->selectedAgentId = $id;

        $agent = collect($this->agents)->firstWhere('id', $id);

        $this->agentSearch = $agent
            ? ($agent->first_name . ' ' . $agent->last_name)
            : '';

        $this->loadPerformanceData();
    }

    /**
     * Load performance data for the selected agent
     */
    public function loadPerformanceData(): void
    {
        if (!$this->selectedAgentId) {
            return;
        }

        $startDate = Carbon::parse($this->startDate)->startOfDay();
        $endDate = Carbon::parse($this->endDate)->endOfDay();

        // Get appointments for the selected date range
        $appointments = Appointment::where('user_id', $this->selectedAgentId)
            ->whereBetween('scheduled_at', [$startDate, $endDate])
            ->get();

        // Get performance metrics for the selected date range
        $performanceMetrics = PerformanceMetric::where('user_id', $this->selectedAgentId)
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->get();

        // Calculate appointment metrics
        $totalAppointments = $appointments->count();
        $validatedAppointments = $appointments->where('status', 'validated')->count();
        $rejectedAppointments = $appointments->where('status', 'rejected')->count();
        $pendingAppointments = $appointments->where('status', 'pending')->count();

        // Calculate validation rate
        $validationRate = $totalAppointments > 0
            ? round(($validatedAppointments / $totalAppointments) * 100, 2)
            : 0;

        // Calculate daily average
        $daysDiff = max(1, $startDate->diffInDays($endDate) + 1);
        $dailyAverage = round($totalAppointments / $daysDiff, 2);

        // Calculate call metrics
        $totalCalls = $performanceMetrics->sum('calls_handled') + $performanceMetrics->sum('outbound_calls');
        $totalTalkTime = $performanceMetrics->sum('total_talk_time');
        $avgHandleTime = $performanceMetrics->avg('average_handle_time') ?? 0;
        $qualityScore = $performanceMetrics->avg('quality_score') ?? 0;
        $customerSatisfaction = $performanceMetrics->avg('customer_satisfaction') ?? 0;
        $firstCallResolution = $performanceMetrics->avg('first_call_resolution') ?? 0;
        $adherenceRate = $performanceMetrics->avg('adherence_rate') ?? 0;
        $complianceScore = $performanceMetrics->avg('compliance_score') ?? 0;

        // Calculate overall performance score
        $overallScore = 0;
        $scoreCount = 0;

        if ($performanceMetrics->isNotEmpty()) {
            foreach ($performanceMetrics as $metric) {
                $overallScore += $metric->getOverallPerformanceScoreAttribute();
                $scoreCount++;
            }

            $overallScore = $scoreCount > 0 ? round($overallScore / $scoreCount, 2) : 0;
        }

        // Get performance rating
        $performanceRating = '';
        if ($overallScore >= 90) {
            $performanceRating = 'Exceptional';
        } elseif ($overallScore >= 80) {
            $performanceRating = 'Exceeds Expectations';
        } elseif ($overallScore >= 70) {
            $performanceRating = 'Meets Expectations';
        } elseif ($overallScore >= 60) {
            $performanceRating = 'Needs Improvement';
        } else {
            $performanceRating = 'Unsatisfactory';
        }

        // Store performance data
        $this->performanceData = [
            // Appointment metrics
            'total_appointments' => $totalAppointments,
            'validated_appointments' => $validatedAppointments,
            'rejected_appointments' => $rejectedAppointments,
            'pending_appointments' => $pendingAppointments,
            'validation_rate' => $validationRate,
            'daily_average' => $dailyAverage,

            // Call metrics
            'total_calls' => $totalCalls,
            'total_talk_time' => $this->formatTime($totalTalkTime),
            'avg_handle_time' => $this->formatTime($avgHandleTime),
            'quality_score' => round($qualityScore, 2),
            'customer_satisfaction' => round($customerSatisfaction, 2),
            'first_call_resolution' => round($firstCallResolution, 2),

            // Adherence metrics
            'adherence_rate' => round($adherenceRate, 2),
            'compliance_score' => round($complianceScore, 2),

            // Overall performance
            'overall_score' => $overallScore,
            'performance_rating' => $performanceRating,

            // Date range
            'date_range' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'days' => $daysDiff,
            ],
        ];

        // Prepare chart data based on period
        $this->prepareChartData($startDate, $endDate, $appointments, $performanceMetrics);
    }

    /**
     * Format time in seconds to a readable format
     */
    private function formatTime(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    }

    /**
     * Prepare chart data based on the selected period
     */
    private function prepareChartData(Carbon $startDate, Carbon $endDate, $appointments, $performanceMetrics = null): void
    {
        $format = 'Y-m-d';
        $groupByFormat = 'Y-m-d';
        $labelFormat = 'd M';

        switch ($this->period) {
            case 'day':
                $groupByFormat = 'Y-m-d H';
                $labelFormat = 'H:i';
                break;
            case 'week':
                $groupByFormat = 'Y-m-d';
                $labelFormat = 'd M';
                break;
            case 'month':
                $groupByFormat = 'Y-m-d';
                $labelFormat = 'd M';
                break;
            case 'year':
                $groupByFormat = 'Y-m';
                $labelFormat = 'M Y';
                break;
        }

        // Group appointments by date
        $groupedAppointments = $appointments->groupBy(function ($appointment) use ($groupByFormat) {
            return Carbon::parse($appointment->scheduled_at)->format($groupByFormat);
        });

        // Group performance metrics by date if available
        $groupedMetrics = collect([]);
        if ($performanceMetrics && $performanceMetrics->isNotEmpty()) {
            $groupedMetrics = $performanceMetrics->groupBy(function ($metric) use ($groupByFormat) {
                return Carbon::parse($metric->date)->format($groupByFormat);
            });
        }

        // Generate date range
        $dateRange = [];
        $currentDate = clone $startDate;

        while ($currentDate <= $endDate) {
            $dateKey = $currentDate->format($groupByFormat);
            $dateRange[$dateKey] = [
                'label' => $currentDate->format($labelFormat),
                'total' => 0,
                'validated' => 0,
                'rejected' => 0,
                'pending' => 0,
                'quality_score' => 0,
                'call_volume' => 0,
                'adherence_rate' => 0,
                'customer_satisfaction' => 0,
                'first_call_resolution' => 0,
            ];

            if ($this->period === 'day') {
                $currentDate->addHour();
            } else if ($this->period === 'week' || $this->period === 'month') {
                $currentDate->addDay();
            } else {
                $currentDate->addMonth();
            }
        }

        // Fill in appointment counts
        foreach ($groupedAppointments as $date => $dateAppointments) {
            if (isset($dateRange[$date])) {
                $dateRange[$date]['total'] = $dateAppointments->count();
                $dateRange[$date]['validated'] = $dateAppointments->where('status', 'validated')->count();
                $dateRange[$date]['rejected'] = $dateAppointments->where('status', 'rejected')->count();
                $dateRange[$date]['pending'] = $dateAppointments->where('status', 'pending')->count();
            }
        }

        // Fill in performance metrics
        foreach ($groupedMetrics as $date => $dateMetrics) {
            if (isset($dateRange[$date])) {
                $dateRange[$date]['quality_score'] = round($dateMetrics->avg('quality_score') ?? 0, 2);
                $dateRange[$date]['call_volume'] = $dateMetrics->sum('calls_handled') + $dateMetrics->sum('outbound_calls');
                $dateRange[$date]['adherence_rate'] = round($dateMetrics->avg('adherence_rate') ?? 0, 2);
                $dateRange[$date]['customer_satisfaction'] = round($dateMetrics->avg('customer_satisfaction') ?? 0, 2);
                $dateRange[$date]['first_call_resolution'] = round($dateMetrics->avg('first_call_resolution') ?? 0, 2);
            }
        }

        // Format chart data for ApexCharts
        $labels = [];
        $totalSeries = [];
        $validatedSeries = [];
        $rejectedSeries = [];
        $pendingSeries = [];
        $qualityScoreSeries = [];
        $callVolumeSeries = [];
        $adherenceRateSeries = [];
        $customerSatisfactionSeries = [];
        $firstCallResolutionSeries = [];

        foreach ($dateRange as $date => $data) {
            $labels[] = $data['label'];
            $totalSeries[] = $data['total'];
            $validatedSeries[] = $data['validated'];
            $rejectedSeries[] = $data['rejected'];
            $pendingSeries[] = $data['pending'];
            $qualityScoreSeries[] = $data['quality_score'];
            $callVolumeSeries[] = $data['call_volume'];
            $adherenceRateSeries[] = $data['adherence_rate'];
            $customerSatisfactionSeries[] = $data['customer_satisfaction'];
            $firstCallResolutionSeries[] = $data['first_call_resolution'];
        }

        $this->chartData = [
            'labels' => $labels,
            'series' => [
                [
                    'name' => 'Total Appointments',
                    'data' => $totalSeries,
                    'type' => 'column',
                ],
                [
                    'name' => 'Validated Appointments',
                    'data' => $validatedSeries,
                    'type' => 'column',
                ],
                [
                    'name' => 'Call Volume',
                    'data' => $callVolumeSeries,
                    'type' => 'line',
                ],
                [
                    'name' => 'Quality Score',
                    'data' => $qualityScoreSeries,
                    'type' => 'line',
                ],
                [
                    'name' => 'Adherence Rate',
                    'data' => $adherenceRateSeries,
                    'type' => 'line',
                ],
            ],
            'additionalSeries' => [
                [
                    'name' => 'Customer Satisfaction',
                    'data' => $customerSatisfactionSeries,
                ],
                [
                    'name' => 'First Call Resolution',
                    'data' => $firstCallResolutionSeries,
                ],
                [
                    'name' => 'Rejected Appointments',
                    'data' => $rejectedSeries,
                ],
                [
                    'name' => 'Pending Appointments',
                    'data' => $pendingSeries,
                ],
            ],
        ];
    }

    /**
     * Get team average performance for comparison
     */
    public function getTeamAverageProperty()
    {
        $user = auth()->user();
        $startDate = Carbon::parse($this->startDate)->startOfDay();
        $endDate = Carbon::parse($this->endDate)->endOfDay();

        // Get campaign ID for filtering
        $campaignId = null;
        if ($this->selectedAgent) {
            $campaignId = $this->selectedAgent->campaign_id;
        } else if ($user->campaign_id) {
            $campaignId = $user->campaign_id;
        }

        if (!$campaignId) {
            return [
                'daily_average' => 0,
                'validation_rate' => 0,
            ];
        }

        // Get all agents in the same campaign
        $agentIds = User::where('role_id', 6)
            ->where('campaign_id', $campaignId)
            ->pluck('id')
            ->toArray();

        if (empty($agentIds)) {
            return [
                'daily_average' => 0,
                'validation_rate' => 0,
            ];
        }

        // Get appointments for all agents in the campaign
        $appointments = Appointment::whereIn('user_id', $agentIds)
            ->whereBetween('scheduled_at', [$startDate, $endDate])
            ->get();

        $totalAppointments = $appointments->count();
        $validatedAppointments = $appointments->where('status', 'validated')->count();
        $agentCount = count($agentIds);
        $daysDiff = max(1, $startDate->diffInDays($endDate) + 1);

        // Calculate team averages
        $teamDailyAverage = $agentCount > 0 ? round($totalAppointments / ($daysDiff * $agentCount), 2) : 0;
        $teamValidationRate = $totalAppointments > 0 ? round(($validatedAppointments / $totalAppointments) * 100, 2) : 0;

        return [
            'daily_average' => $teamDailyAverage,
            'validation_rate' => $teamValidationRate,
        ];
    }

    /**
     * Export performance data to CSV
     */
    public function exportPerformanceData()
    {
        // Implementation for exporting data will go here
        // This would typically generate a CSV file and trigger a download
        $this->dispatch('showNotification', [
            'message' => 'Export functionality will be implemented soon.',
            'type' => 'info',
        ]);
    }

    public function render()
    {
        return view('livewire.agents.agent-performance', [
            'filteredAgents' => $this->filteredAgents,
            'selectedAgent' => $this->selectedAgent,
            'teamAverage' => $this->teamAverage,
        ]);
    }
}
