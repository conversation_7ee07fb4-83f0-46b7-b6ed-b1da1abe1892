<?php

namespace App\Livewire\Agents;

use App\Models\Campaign;
use App\Models\Media;
use App\Models\Training;
use App\Models\TrainingModule;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\On;

class AgentEdit extends Component
{
    use WithFileUploads;

    public User $agent;
    public $campaigns;
    public $trainers;
    public $trainingModules;
    public $form;

    // Document management
    public $resume;
    public $id_card;
    public $certificates = [];
    public $other_documents = [];
    public $current_documents = [];

    public $documentTypes = [
        'resume' => 'Resume/CV',
        'id_card' => 'ID Card',
        'certificate' => 'Certificates',
        'other_document' => 'Other Documents'
    ];

    public function mount(User $agent)
    {
        $this->agent = $agent;
        $this->campaigns = Campaign::all();
        $this->trainers = User::where('role_id', 3)->get(); // Assuming role_id 3 is for trainers
        $this->trainingModules = TrainingModule::all();

        // Get the profile picture URL using the User model method
        $profilePictureUrl = $agent->getProfilePictureUrl();

        // Load existing documents
        $this->loadExistingDocuments();

        // Get training data
        $training = $agent->training;

        // Initialize form with empty password fields
        $this->form = [
            'first_name' => $agent->first_name,
            'last_name' => $agent->last_name,
            'email' => $agent->email,
            'phone_number' => $agent->phone_number,
            'birth_date' => $agent->birth_date,
            'address' => $agent->address,
            'city' => $agent->city,
            'country' => $agent->country,
            'status' => $agent->status,
            'campaign_id' => $agent->campaign_id,
            'registration_number' => $agent->registration_number,
            'hire_date' => $agent->hire_date,
            'training_module_id' => $training ? $training->training_module_id : null,
            'training_start_date' => $training ? $training->start_date : null,
            'training_completion_date' => $training ? $training->validated_at : null,
            'training_progress' => $training ? $training->progress : 0,
            'training_rating' => $training ? $training->rating : null,
            'training_validated' => $training ? ($training->validated_at ? true : false) : false,
            'trainer_id' => $agent->trainer_id,
            'daily_appointment_target' => $agent->daily_appointment_target,
            'weekly_hours_target' => $agent->weekly_hours_target,
            'new_password' => '',
            'new_password_confirmation' => '',
            'current_profile_picture' => $profilePictureUrl,
            'profile_picture' => null,
        ];
    }

    /**
     * Load existing documents for the agent
     */
    protected function loadExistingDocuments()
    {
        $this->current_documents = $this->agent->media()
            ->whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])
            ->get()
            ->groupBy('category')
            ->toArray();
    }

    /**
     * Remove profile picture
     */
    public function removeProfilePicture()
    {
        // Get the profile picture media model
        $profilePicture = $this->agent->profilePicture()->first();

        // Check if a profile picture exists
        if ($profilePicture) {
            Storage::disk('public')->delete($profilePicture->file_path);
            $profilePicture->delete();
        }

        $this->form['profile_picture'] = null;
        $this->form['current_profile_picture'] = '/images/users/default.png';

        // Force refresh the component to update the UI
        $this->dispatch('profile-picture-removed');
    }

    public function save()
    {
        // Create a validation rules array
        $validationRules = [
            'form.first_name' => 'required|string|max:255',
            'form.last_name' => 'required|string|max:255',
            'form.email' => 'required|email|max:255|unique:users,email,' . $this->agent->id,
            'form.phone_number' => 'nullable|string|max:20',
            'form.birth_date' => 'nullable|date',
            'form.address' => 'nullable|string|max:255',
            'form.city' => 'nullable|string|max:100',
            'form.country' => 'nullable|string|max:100',
            'form.status' => 'required|string|in:in_training,validated,active,inactive,engaged',
            'form.campaign_id' => 'nullable|exists:campaigns,id',
            'form.registration_number' => 'nullable|string|max:50',
            'form.hire_date' => 'nullable|date',
            'form.training_module_id' => 'nullable|exists:training_modules,id',
            'form.training_start_date' => 'nullable|date',
            'form.training_completion_date' => 'nullable|date',
            'form.training_progress' => 'nullable|numeric|min:0|max:100',
            'form.training_rating' => 'nullable|numeric|min:0|max:3',
            'form.training_validated' => 'nullable|boolean',
            'form.trainer_id' => 'nullable|exists:users,id',
            'form.daily_appointment_target' => 'nullable|integer|min:0',
            'form.weekly_hours_target' => 'nullable|numeric|min:0',
            'form.profile_picture' => 'nullable|image|max:1024', // 1MB max
            'form.new_password' => 'nullable|min:8',
            'form.new_password_confirmation' => 'nullable',
            'resume' => 'nullable|file|mimes:pdf,doc,docx|max:5120', // 5MB max
            'id_card' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048', // 2MB max
            'certificates.*' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
            'other_documents.*' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png,xls,xlsx|max:10240', // 10MB max
        ];

        // If status is in_training or validated, require training_start_date
        if (in_array($this->form['status'], ['in_training', 'validated'])) {
            // If training_start_date is not set, set it to today
            if (empty($this->form['training_start_date'])) {
                $this->form['training_start_date'] = now()->format('Y-m-d');
            }
            // Ensure training_start_date is properly formatted
            else if (is_string($this->form['training_start_date'])) {
                try {
                    // Try to parse the date and reformat it
                    $date = \Carbon\Carbon::parse($this->form['training_start_date']);
                    $this->form['training_start_date'] = $date->format('Y-m-d');
                } catch (\Exception $e) {
                    // If parsing fails, set to today
                    $this->form['training_start_date'] = now()->format('Y-m-d');
                    Log::warning('Invalid training_start_date format, using today instead: ' . $e->getMessage());
                }
            }
        }

        // Only validate password confirmation if a new password is provided
        if (!empty(trim($this->form['new_password']))) {
            // Manually check if passwords match
            if ($this->form['new_password'] !== $this->form['new_password_confirmation']) {
                $this->addError('form.new_password', 'The password confirmation does not match.');
                return;
            }
        }

        $this->validate($validationRules);

        // Update user data
        $this->agent->update([
            'first_name' => $this->form['first_name'],
            'last_name' => $this->form['last_name'],
            'email' => $this->form['email'],
            'phone_number' => $this->form['phone_number'],
            'birth_date' => $this->form['birth_date'],
            'address' => $this->form['address'],
            'city' => $this->form['city'],
            'country' => $this->form['country'],
            'status' => $this->form['status'],
            'campaign_id' => $this->form['campaign_id'],
            'registration_number' => $this->form['registration_number'],
            'hire_date' => $this->form['hire_date'],
            'trainer_id' => $this->form['trainer_id'],
            'daily_appointment_target' => $this->form['daily_appointment_target'],
            'weekly_hours_target' => $this->form['weekly_hours_target'],
        ]);

        // Update or create training record
        if ($this->form['status'] === 'in_training' || $this->form['status'] === 'validated') {
            $trainingData = [
                'training_module_id' => $this->form['training_module_id'] ?: null,
                'progress' => $this->form['training_progress'] ?? 0,
                'rating' => $this->form['training_rating'] ?: null,
                'start_date' => $this->form['training_start_date'] ?? now()->format('Y-m-d'),
            ];

            // Set validated_at if training is validated
            if ($this->form['training_validated']) {
                $trainingData['validated_at'] = $this->form['training_completion_date'] ?? now();
            } else {
                $trainingData['validated_at'] = null;
            }

            // If agent status is 'validated', ensure training is marked as validated
            if ($this->form['status'] === 'validated' && !$this->form['training_validated']) {
                $trainingData['validated_at'] = now();
            }

            // Update or create training record
            $training = $this->agent->training;

            try {
                if ($training) {
                    $training->update($trainingData);
                } else {
                    // Ensure start_date is set
                    if (empty($trainingData['start_date'])) {
                        $trainingData['start_date'] = now()->format('Y-m-d');
                    }

                    // Log the training data for debugging
                    Log::info('Creating training record with data:', $trainingData);

                    $this->agent->training()->create($trainingData);
                }
            } catch (\Exception $e) {
                // Log the error
                Log::error('Error updating/creating training record: ' . $e->getMessage());
                session()->flash('error', 'Error updating training information: ' . $e->getMessage());
                return;
            }
        }

        // Update password if provided
        if (!empty(trim($this->form['new_password']))) {
            $this->agent->update([
                'password' => bcrypt($this->form['new_password']),
            ]);
        }

        // Handle profile picture upload
        if ($this->form['profile_picture']) {
            // Delete old profile picture if exists
            $profilePicture = $this->agent->profilePicture()->first();
            if ($profilePicture) {
                Storage::disk('public')->delete($profilePicture->file_path);
                $profilePicture->delete();
            }

            // Store new profile picture
            $path = $this->form['profile_picture']->store('profile-pictures', 'public');
            $this->agent->media()->create([
                'file_path' => $path,
                'file_name' => $this->form['profile_picture']->getClientOriginalName(),
                'mime_type' => $this->form['profile_picture']->getMimeType(),
                'category' => 'profile_picture',
                'uploaded_by' => auth()->id(),
            ]);
        }

        // Handle resume upload
        if ($this->resume) {
            // Delete existing resume if any
            $existingResume = $this->agent->media()->where('category', 'resume')->first();
            if ($existingResume) {
                Storage::disk('public')->delete($existingResume->file_path);
                $existingResume->delete();
            }

            $filePath = $this->resume->store('media/documents/resumes', 'public');
            $this->agent->media()->create([
                'file_name' => $this->resume->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->resume->getMimeType(),
                'category' => 'resume',
                'uploaded_by' => auth()->id(),
            ]);
        }

        // Handle ID card upload
        if ($this->id_card) {
            // Delete existing ID card if any
            $existingIdCard = $this->agent->media()->where('category', 'id_card')->first();
            if ($existingIdCard) {
                Storage::disk('public')->delete($existingIdCard->file_path);
                $existingIdCard->delete();
            }

            $filePath = $this->id_card->store('media/documents/id_cards', 'public');
            $this->agent->media()->create([
                'file_name' => $this->id_card->getClientOriginalName(),
                'file_path' => $filePath,
                'mime_type' => $this->id_card->getMimeType(),
                'category' => 'id_card',
                'uploaded_by' => auth()->id(),
            ]);
        }

        // Handle certificates upload
        if (!empty($this->certificates)) {
            foreach ($this->certificates as $certificate) {
                $filePath = $certificate->store('media/documents/certificates', 'public');
                $this->agent->media()->create([
                    'file_name' => $certificate->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $certificate->getMimeType(),
                    'category' => 'certificate',
                    'uploaded_by' => auth()->id(),
                ]);
            }
        }

        // Handle other documents upload
        if (!empty($this->other_documents)) {
            foreach ($this->other_documents as $document) {
                $filePath = $document->store('media/documents/other', 'public');
                $this->agent->media()->create([
                    'file_name' => $document->getClientOriginalName(),
                    'file_path' => $filePath,
                    'mime_type' => $document->getMimeType(),
                    'category' => 'other_document',
                    'uploaded_by' => auth()->id(),
                ]);
            }
        }

        session()->flash('message', 'Agent updated successfully!');

        return $this->redirect(route('agents.show', $this->agent), navigate: true);
    }

    /**
     * Remove a certificate from the array
     */
    public function removeCertificate($index)
    {
        if (isset($this->certificates[$index])) {
            unset($this->certificates[$index]);
            $this->certificates = array_values($this->certificates);
        }
    }

    /**
     * Remove an other document from the array
     */
    public function removeOtherDocument($index)
    {
        if (isset($this->other_documents[$index])) {
            unset($this->other_documents[$index]);
            $this->other_documents = array_values($this->other_documents);
        }
    }

    /**
     * Remove an existing document
     */
    public function removeExistingDocument($documentId, $category)
    {
        $document = Media::find($documentId);

        if ($document && $document->mediable_id == $this->agent->id && $document->mediable_type == User::class) {
            // Delete the file from storage
            if (Storage::disk('public')->exists($document->file_path)) {
                Storage::disk('public')->delete($document->file_path);
            }

            // Delete the database record
            $document->delete();

            // Reload documents
            $this->loadExistingDocuments();

            session()->flash('message', 'Document removed successfully.');
        }
    }

    public function render()
    {
        return view('livewire.agents.agent-edit', [
            'documentTypes' => $this->documentTypes
        ]);
    }

    // Reset password fields after validation errors
    protected function onValidationError($errors)
    {
        // If there are validation errors but not for the password fields,
        // reset the password fields to empty
        if (!isset($errors['form.new_password'])) {
            $this->form['new_password'] = '';
            $this->form['new_password_confirmation'] = '';
        }
    }
}
