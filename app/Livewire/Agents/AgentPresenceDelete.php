<?php

namespace App\Livewire\Agents;

use App\Models\Shift;
use Livewire\Attributes\On;
use Livewire\Component;

class AgentPresenceDelete extends Component
{
    public Shift $shift;
    public $name;
    #[On('destroy-shift')]
    public function destroyUser()
    {

        $this->shift->delete();

        session()->flash('message', 'Selected shifts deleted successfully!');

        return $this->redirect(route('shifts.index'), navigate: true);
    }

    public function mount(Shift $shift)
    {
        $this->name = $shift->user->getFullNameAttribute();
    }
    public function render()
    {
        return view('livewire.agents.agent-presence-delete');
    }
}
