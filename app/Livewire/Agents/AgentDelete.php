<?php

namespace App\Livewire\Agents;

use Livewire\Component;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;

class AgentDelete extends Component
{
    public User $agent;
    public $name;
    public bool $forceDelete = false;

    public function toggleForceDelete()
    {
        $this->forceDelete = !$this->forceDelete;
    }

    #[On('destroy-agent')]
    public function destroyUser()
    {
        try {
            // Log the agent we're trying to delete
            Log::info('Attempting to delete agent: ' . $this->agent->id);

            // Get all media files associated with the agent
            $mediaFiles = $this->agent->media()->get();
            Log::info('Found ' . count($mediaFiles) . ' media files to delete');

            // Delete all media files from storage and database
            foreach ($mediaFiles as $media) {
                if (Storage::disk('public')->exists($media->file_path)) {
                    Log::info('Deleting file: ' . $media->file_path);
                    Storage::disk('public')->delete($media->file_path);
                }
                $media->delete();
            }

            // Detach relationships that might prevent deletion if force delete is enabled
            if ($this->forceDelete) {
                Log::info('Force delete enabled, detaching relationships');
                $this->detachRelationships();
            }

            // Try to delete the agent directly
            try {
                Log::info('Attempting to delete agent directly: ' . $this->agent->id);
                $this->agent->delete();
            } catch (\Exception $innerException) {
                // If direct deletion fails, try using raw SQL to bypass foreign key constraints
                Log::warning('Direct deletion failed, trying raw SQL: ' . $innerException->getMessage());

                if (strpos($innerException->getMessage(), 'no such table: main.evaluation_criterias') !== false) {
                    Log::info('Detected missing evaluation_criterias table, using raw SQL to delete agent');
                    DB::statement('PRAGMA foreign_keys = OFF;');
                    DB::delete('DELETE FROM users WHERE id = ?', [$this->agent->id]);
                    DB::statement('PRAGMA foreign_keys = ON;');
                } else {
                    // If it's a different error, rethrow it
                    throw $innerException;
                }
            }

            Log::info('Agent deleted successfully: ' . $this->agent->id);
            session()->flash('message', 'Agent deleted successfully!');

            // Redirect to the agents index page
            return $this->redirect(route('agents.index'), navigate: true);
        } catch (\Exception $e) {
            Log::error('Error deleting agent: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            session()->flash('error', 'Error deleting agent: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Detach relationships that might prevent deletion
     */
    private function detachRelationships()
    {
        // Detach team memberships
        if (method_exists($this->agent, 'teams')) {
            Log::info('Detaching team memberships');
            $this->agent->teams()->detach();
        }

        // Detach campaign memberships
        if (method_exists($this->agent, 'campaigns')) {
            Log::info('Detaching campaign memberships');
            $this->agent->campaigns()->detach();
        }

        // Detach site memberships
        if (method_exists($this->agent, 'sites')) {
            Log::info('Detaching site memberships');
            $this->agent->sites()->detach();
        }

        // Detach skills
        if (method_exists($this->agent, 'skills')) {
            Log::info('Detaching skills');
            $this->agent->skills()->detach();
        }

        // Detach certifications
        if (method_exists($this->agent, 'certifications')) {
            Log::info('Detaching certifications');
            $this->agent->certifications()->detach();
        }

        // Clear notifications
        if (method_exists($this->agent, 'notifications')) {
            Log::info('Clearing notifications');
            $this->agent->notifications()->delete();
        }

        // Set manager_id to null for direct reports
        if (method_exists($this->agent, 'directReports') && $this->agent->directReports()->count() > 0) {
            Log::info('Setting manager_id to null for direct reports');
            $this->agent->directReports()->update(['manager_id' => null]);
        }

        // Delete observations
        if (method_exists($this->agent, 'observations')) {
            Log::info('Deleting observations');
            $this->agent->observations()->delete();
        }
    }

    public function mount(User $agent)
    {
        $this->agent = $agent;
        $this->name = $agent->getFullNameAttribute();
    }

    public function render()
    {
        return view('livewire.agents.agent-delete');
    }
}
