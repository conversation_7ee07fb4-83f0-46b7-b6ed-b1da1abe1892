<?php

namespace App\Livewire\Hr;

use App\Models\Contract;
use App\Models\User;
use App\Models\Media;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class HrContracts extends Component
{
    use WithPagination, WithFileUploads;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'start_date';
    public $sortDirection = 'desc';
    public $selectedStatus = '';
    public $selectedType = '';
    public $selectedUser = '';

    // Contract form properties
    public $showContractModal = false;
    public $editMode = false;
    public $contractId = null;
    public $userId;
    public $contractType = 'fixed-term';
    public $startDate;
    public $endDate;
    public $status = 'active';
    public $salary;
    public $position;
    public $department;
    public $terms;
    public $notes;
    public $contractDocument;

    // Contract types and statuses
    public $contractTypes = [
        'fixed-term' => 'Fixed Term',
        'permanent' => 'Permanent',
        'probation' => 'Probation',
        'internship' => 'Internship',
        'part-time' => 'Part-Time',
    ];

    public $contractStatuses = [
        'active' => 'Active',
        'expired' => 'Expired',
        'terminated' => 'Terminated',
    ];

    protected $rules = [
        'userId' => 'required|exists:users,id',
        'contractType' => 'required|string',
        'startDate' => 'required|date',
        'endDate' => 'nullable|date|after_or_equal:startDate',
        'status' => 'required|string',
        'salary' => 'nullable|numeric|min:0',
        'position' => 'nullable|string|max:255',
        'department' => 'nullable|string|max:255',
        'terms' => 'nullable|string',
        'notes' => 'nullable|string',
        'contractDocument' => 'nullable|file|max:10240', // 10MB max
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingSelectedType()
    {
        $this->resetPage();
    }

    public function updatingSelectedUser()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function openContractModal($userId = null)
    {
        $this->resetValidation();
        $this->resetContractForm();

        $this->editMode = false;
        $this->contractId = null;
        $this->userId = $userId;
        $this->showContractModal = true;
    }

    public function editContract(Contract $contract)
    {
        $this->resetValidation();
        $this->resetContractForm();

        $this->editMode = true;
        $this->contractId = $contract->id;
        $this->userId = $contract->user_id;
        $this->contractType = $contract->type;
        $this->startDate = $contract->start_date->format('Y-m-d');
        $this->endDate = $contract->end_date ? $contract->end_date->format('Y-m-d') : null;
        $this->status = $contract->status;
        $this->salary = $contract->salary;
        $this->position = $contract->position;
        $this->department = $contract->department;
        $this->terms = $contract->terms;
        $this->notes = $contract->notes;

        $this->showContractModal = true;
    }

    public function closeContractModal()
    {
        $this->showContractModal = false;
    }

    public function resetContractForm()
    {
        $this->userId = null;
        $this->contractType = 'fixed-term';
        $this->startDate = now()->format('Y-m-d');
        $this->endDate = null;
        $this->status = 'active';
        $this->salary = null;
        $this->position = null;
        $this->department = null;
        $this->terms = null;
        $this->notes = null;
        $this->contractDocument = null;
    }

    public function saveContract()
    {
        $this->validate();

        if ($this->editMode && $this->contractId) {
            $contract = Contract::findOrFail($this->contractId);
            $contract->update([
                'user_id' => $this->userId,
                'type' => $this->contractType,
                'start_date' => $this->startDate,
                'end_date' => $this->endDate,
                'status' => $this->status,
                'salary' => $this->salary,
                'position' => $this->position,
                'department' => $this->department,
                'terms' => $this->terms,
                'notes' => $this->notes,
            ]);

            $message = 'Contract updated successfully.';
        } else {
            $contract = Contract::create([
                'user_id' => $this->userId,
                'type' => $this->contractType,
                'start_date' => $this->startDate,
                'end_date' => $this->endDate,
                'status' => $this->status,
                'salary' => $this->salary,
                'position' => $this->position,
                'department' => $this->department,
                'terms' => $this->terms,
                'notes' => $this->notes,
                'created_by' => Auth::id(),
            ]);

            $message = 'Contract created successfully.';
        }

        // Handle contract document upload if provided
        if ($this->contractDocument) {
            $fileName = 'Contract - ' . ($contract->user->first_name ?? '') . ' ' . ($contract->user->last_name ?? '') . ' - ' . now()->format('Y-m-d');
            $filePath = $this->contractDocument->store('media/documents/contract', 'public');

            Media::create([
                'mediable_id' => $contract->id,
                'mediable_type' => Contract::class,
                'file_name' => $fileName,
                'file_path' => $filePath,
                'mime_type' => $this->contractDocument->getMimeType(),
                'category' => 'contract',
                'uploaded_by' => Auth::id(),
            ]);
        }

        session()->flash('message', $message);
        $this->closeContractModal();
    }

    public function deleteContract($contractId)
    {
        return $this->redirect(route('hr.contracts.delete', ['contract' => $contractId]), navigate: true);
    }

    public function render()
    {
        $contracts = Contract::query()
            ->with(['user', 'creator'])
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('registration_number', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->selectedType, function ($query) {
                $query->where('type', $this->selectedType);
            })
            ->when($this->selectedUser, function ($query) {
                $query->where('user_id', $this->selectedUser);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        $users = User::orderBy('first_name')->get();

        return view('livewire.hr.hr-contracts', [
            'contracts' => $contracts,
            'users' => $users,
        ]);
    }
}
