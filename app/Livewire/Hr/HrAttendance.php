<?php

namespace App\Livewire\Hr;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class HrAttendance extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'date';
    public $sortDirection = 'desc';
    public $selectedStatus = '';
    public $selectedUser = '';
    public $dateRange = '';

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingSelectedUser()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        // This is a placeholder component for now
        // In the future, it will handle actual attendance records

        $users = User::orderBy('first_name')->get();

        return view('livewire.hr.hr-attendance', [
            'users' => $users,
        ]);
    }
}
