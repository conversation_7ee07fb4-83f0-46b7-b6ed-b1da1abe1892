<?php

namespace App\Livewire\Hr;

use App\Models\EmployeeOnboarding;
use App\Models\OnboardingTemplate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class HrOnboardingCreate extends Component
{
    public $userId;
    public $templateId;
    public $startDate;
    public $targetCompletionDate;
    public $assignedTo;
    public $notes;
    
    public $searchQuery = '';
    public $selectedUser = null;
    
    protected $rules = [
        'userId' => 'required|exists:users,id',
        'templateId' => 'required|exists:onboarding_templates,id',
        'startDate' => 'required|date|after_or_equal:today',
        'targetCompletionDate' => 'nullable|date|after_or_equal:startDate',
        'assignedTo' => 'nullable|exists:users,id',
        'notes' => 'nullable|string',
    ];
    
    public function mount()
    {
        $this->startDate = now()->format('Y-m-d');
        $this->assignedTo = Auth::id();
    }
    
    public function searchUsers()
    {
        return User::where(function ($query) {
                $query->where('first_name', 'like', '%' . $this->searchQuery . '%')
                    ->orWhere('last_name', 'like', '%' . $this->searchQuery . '%')
                    ->orWhere('email', 'like', '%' . $this->searchQuery . '%');
            })
            ->where('status', '!=', 'inactive')
            ->orderBy('first_name')
            ->limit(10)
            ->get();
    }
    
    public function selectUser($userId)
    {
        $this->userId = $userId;
        $this->selectedUser = User::find($userId);
        $this->searchQuery = '';
        
        // If user has a role, try to find a matching template
        if ($this->selectedUser && $this->selectedUser->role_id) {
            $template = OnboardingTemplate::where('role_id', $this->selectedUser->role_id)
                ->where('is_active', true)
                ->first();
                
            if ($template) {
                $this->templateId = $template->id;
            }
        }
    }
    
    public function calculateTargetDate()
    {
        if (!$this->startDate || !$this->templateId) {
            return;
        }
        
        $template = OnboardingTemplate::find($this->templateId);
        if (!$template) {
            return;
        }
        
        // Get the maximum due_days from template tasks
        $maxDueDays = $template->tasks()->max('due_days') ?? 14;
        
        // Set target completion date to start date + max due days
        $this->targetCompletionDate = date('Y-m-d', strtotime($this->startDate . ' + ' . $maxDueDays . ' days'));
    }
    
    public function updated($propertyName)
    {
        $this->validateOnly($propertyName);
        
        if ($propertyName === 'templateId' || $propertyName === 'startDate') {
            $this->calculateTargetDate();
        }
    }
    
    public function save()
    {
        $this->validate();
        
        // Create the onboarding process
        $onboarding = EmployeeOnboarding::create([
            'user_id' => $this->userId,
            'template_id' => $this->templateId,
            'start_date' => $this->startDate,
            'target_completion_date' => $this->targetCompletionDate,
            'status' => 'pending',
            'progress_percentage' => 0,
            'assigned_to' => $this->assignedTo,
            'notes' => $this->notes,
        ]);
        
        // Create tasks from the template
        $onboarding->createTasksFromTemplate();
        
        session()->flash('message', 'Onboarding process created successfully.');
        
        return redirect()->route('hr.onboarding');
    }
    
    public function render()
    {
        $users = $this->searchQuery ? $this->searchUsers() : collect();
        $templates = OnboardingTemplate::where('is_active', true)->get();
        $hrStaff = User::whereIn('role_id', [1, 2, 3, 4]) // Admin, HR roles
            ->where('status', 'active')
            ->orderBy('first_name')
            ->get();
            
        return view('livewire.hr.hr-onboarding-create', [
            'users' => $users,
            'templates' => $templates,
            'hrStaff' => $hrStaff,
        ]);
    }
}
