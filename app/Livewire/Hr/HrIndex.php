<?php

namespace App\Livewire\Hr;

use App\Models\User;
use App\Models\Media;
use App\Models\Contract;
use App\Models\PerformanceReview;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class HrIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        // Get users for the table
        $users = User::query()
            ->where(function ($query) {
                $query->where('first_name', 'like', '%' . $this->search . '%')
                    ->orWhere('last_name', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->orWhere('registration_number', 'like', '%' . $this->search . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        // Get total employees
        $totalEmployees = User::count();

        // Get recent employees
        $recentEmployees = User::orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get active contracts and handle exceptions
        try {
            // Check if the contracts table exists
            if (Schema::hasTable('contracts')) {
                $activeContracts = Contract::where('status', 'active')->count();

                // Get expiring contracts
                $expiringContracts = Contract::where('status', 'active')
                    ->whereNotNull('end_date')
                    ->where('end_date', '>=', now())
                    ->where('end_date', '<=', now()->addMonths(3))
                    ->with('user')
                    ->orderBy('end_date')
                    ->take(5)
                    ->get();
            } else {
                $activeContracts = 0;
                $expiringContracts = collect();
            }
        } catch (\Exception $e) {
            $activeContracts = 0;
            $expiringContracts = collect();
        }

        // Get recent performance reviews and handle exceptions
        try {
            // Check if the performance_reviews table exists
            if (Schema::hasTable('performance_reviews')) {
                $recentReviews = PerformanceReview::where('review_date', '>=', now()->subMonths(3))->count();
            } else {
                $recentReviews = 0;
            }
        } catch (\Exception $e) {
            $recentReviews = 0;
        }

        // Get total documents
        $totalDocuments = Media::count();

        // Department distribution data
        $departmentData = $this->getDepartmentDistribution();

        // Contract types data
        $contractData = $this->getContractTypeDistribution();

        return view('livewire.hr.hr-index', [
            'users' => $users,
            'totalEmployees' => $totalEmployees,
            'recentEmployees' => $recentEmployees,
            'activeContracts' => $activeContracts,
            'expiringContracts' => $expiringContracts,
            'recentReviews' => $recentReviews,
            'totalDocuments' => $totalDocuments,
            'departmentData' => $departmentData,
            'contractData' => $contractData,
        ]);
    }

    protected function getDepartmentDistribution()
    {
        try {
            $departments = Contract::select('department', DB::raw('count(*) as total'))
                ->whereNotNull('department')
                ->groupBy('department')
                ->orderBy('total', 'desc')
                ->get();

            $labels = $departments->pluck('department')->toArray();
            $values = $departments->pluck('total')->toArray();
        } catch (\Exception $e) {
            $labels = [];
            $values = [];
        }

        // If no data, provide default
        if (empty($labels)) {
            $labels = ['No Department Data'];
            $values = [1];
        }

        return [
            'labels' => $labels,
            'values' => $values,
        ];
    }

    protected function getContractTypeDistribution()
    {
        try {
            $types = Contract::select('type', DB::raw('count(*) as total'))
                ->groupBy('type')
                ->orderBy('total', 'desc')
                ->get();

            $labels = $types->pluck('type')->map(function($type) {
                return ucfirst($type);
            })->toArray();

            $values = $types->pluck('total')->toArray();
        } catch (\Exception $e) {
            $labels = [];
            $values = [];
        }

        // If no data, provide default
        if (empty($labels)) {
            $labels = ['No Contract Data'];
            $values = [1];
        }

        return [
            'labels' => $labels,
            'values' => $values,
        ];
    }
}
