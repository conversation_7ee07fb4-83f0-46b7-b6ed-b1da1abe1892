<?php

namespace App\Livewire\Hr;

use App\Models\EmployeeOnboarding;
use App\Models\OnboardingDocument;
use App\Models\OnboardingTask;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;

class HrOnboardingShow extends Component
{
    use WithFileUploads;
    
    public EmployeeOnboarding $onboarding;
    
    // Task management
    public $taskId;
    public $taskStatus;
    public $taskNotes;
    public $taskAssignedTo;
    
    // Document upload
    public $documentName;
    public $documentFile;
    public $documentTaskId;
    
    // Modals
    public $showTaskModal = false;
    public $showDocumentModal = false;
    
    protected $rules = [
        'onboarding.status' => 'required|in:pending,in_progress,completed,cancelled',
        'onboarding.assigned_to' => 'nullable|exists:users,id',
        'onboarding.notes' => 'nullable|string',
        'taskStatus' => 'required|in:pending,in_progress,completed,skipped',
        'taskNotes' => 'nullable|string',
        'taskAssignedTo' => 'nullable|exists:users,id',
        'documentName' => 'required|string|max:255',
        'documentFile' => 'required|file|max:10240', // 10MB max
    ];
    
    public function mount(EmployeeOnboarding $onboarding)
    {
        $this->onboarding = $onboarding;
    }
    
    public function updateOnboarding()
    {
        $this->validate([
            'onboarding.status' => 'required|in:pending,in_progress,completed,cancelled',
            'onboarding.assigned_to' => 'nullable|exists:users,id',
            'onboarding.notes' => 'nullable|string',
        ]);
        
        $this->onboarding->save();
        
        session()->flash('message', 'Onboarding information updated successfully.');
    }
    
    public function openTaskModal($taskId)
    {
        $this->taskId = $taskId;
        $task = OnboardingTask::find($taskId);
        
        if ($task) {
            $this->taskStatus = $task->status;
            $this->taskNotes = $task->completion_notes;
            $this->taskAssignedTo = $task->assigned_to;
            $this->showTaskModal = true;
        }
    }
    
    public function closeTaskModal()
    {
        $this->showTaskModal = false;
        $this->reset(['taskId', 'taskStatus', 'taskNotes', 'taskAssignedTo']);
    }
    
    public function updateTask()
    {
        $this->validate([
            'taskStatus' => 'required|in:pending,in_progress,completed,skipped',
            'taskNotes' => 'nullable|string',
            'taskAssignedTo' => 'nullable|exists:users,id',
        ]);
        
        $task = OnboardingTask::find($this->taskId);
        
        if (!$task) {
            session()->flash('error', 'Task not found.');
            return;
        }
        
        $oldStatus = $task->status;
        $task->status = $this->taskStatus;
        $task->assigned_to = $this->taskAssignedTo;
        
        if ($this->taskStatus === 'completed' && $oldStatus !== 'completed') {
            $task->completed_by = Auth::id();
            $task->completed_at = now();
            $task->completion_notes = $this->taskNotes;
        } elseif ($this->taskStatus !== 'completed' && $oldStatus === 'completed') {
            $task->completed_by = null;
            $task->completed_at = null;
            $task->completion_notes = $this->taskNotes;
        } else {
            $task->completion_notes = $this->taskNotes;
        }
        
        $task->save();
        
        // Update onboarding progress
        $this->onboarding->updateProgress();
        
        session()->flash('message', 'Task updated successfully.');
        $this->closeTaskModal();
    }
    
    public function openDocumentModal($taskId = null)
    {
        $this->documentTaskId = $taskId;
        $this->showDocumentModal = true;
    }
    
    public function closeDocumentModal()
    {
        $this->showDocumentModal = false;
        $this->reset(['documentName', 'documentFile', 'documentTaskId']);
    }
    
    public function uploadDocument()
    {
        $this->validate([
            'documentName' => 'required|string|max:255',
            'documentFile' => 'required|file|max:10240', // 10MB max
        ]);
        
        $path = $this->documentFile->store('onboarding/' . $this->onboarding->id, 'public');
        
        OnboardingDocument::create([
            'onboarding_id' => $this->onboarding->id,
            'task_id' => $this->documentTaskId,
            'name' => $this->documentName,
            'file_path' => $path,
            'file_type' => $this->documentFile->getClientOriginalExtension(),
            'uploaded_by' => Auth::id(),
        ]);
        
        session()->flash('message', 'Document uploaded successfully.');
        $this->closeDocumentModal();
    }
    
    public function deleteDocument($documentId)
    {
        $document = OnboardingDocument::find($documentId);
        
        if (!$document) {
            session()->flash('error', 'Document not found.');
            return;
        }
        
        // Delete the file from storage
        if (Storage::exists('public/' . $document->file_path)) {
            Storage::delete('public/' . $document->file_path);
        }
        
        // Delete the record
        $document->delete();
        
        session()->flash('message', 'Document deleted successfully.');
    }
    
    public function render()
    {
        // Get tasks grouped by category
        $tasksByCategory = $this->onboarding->tasks()
            ->orderBy('order')
            ->get()
            ->groupBy('category');
            
        // Get documents
        $documents = $this->onboarding->documents()
            ->with('uploader')
            ->orderBy('created_at', 'desc')
            ->get();
            
        // Get HR staff for assignment
        $hrStaff = User::whereIn('role_id', [1, 2, 3, 4, 5]) // Admin, HR, Supervisor roles
            ->where('status', 'active')
            ->orderBy('first_name')
            ->get();
            
        return view('livewire.hr.hr-onboarding-show', [
            'tasksByCategory' => $tasksByCategory,
            'documents' => $documents,
            'hrStaff' => $hrStaff,
        ]);
    }
}
