<?php

namespace App\Livewire\Hr;

use App\Models\PerformanceReview;
use App\Models\User;
use App\Models\Media;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class HrPerformance extends Component
{
    use WithPagination, WithFileUploads;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'review_date';
    public $sortDirection = 'desc';
    public $selectedStatus = '';
    public $selectedUser = '';
    public $dateRange = '';

    // Review form properties
    public $showReviewModal = false;
    public $editMode = false;
    public $reviewId = null;
    public $userId;
    public $reviewDate;
    public $reviewPeriodStart;
    public $reviewPeriodEnd;
    public $performanceScore;
    public $attendanceScore;
    public $communicationScore;
    public $teamworkScore;
    public $initiativeScore;
    public $overallScore;
    public $strengths;
    public $areasForImprovement;
    public $goals;
    public $comments;
    public $status = 'draft';
    public $reviewDocument;

    // Review statuses
    public $reviewStatuses = [
        'draft' => 'Draft',
        'completed' => 'Completed',
        'acknowledged' => 'Acknowledged',
    ];

    protected $rules = [
        'userId' => 'required|exists:users,id',
        'reviewDate' => 'required|date',
        'reviewPeriodStart' => 'required|date',
        'reviewPeriodEnd' => 'required|date|after_or_equal:reviewPeriodStart',
        'performanceScore' => 'nullable|numeric|min:1|max:5',
        'attendanceScore' => 'nullable|numeric|min:1|max:5',
        'communicationScore' => 'nullable|numeric|min:1|max:5',
        'teamworkScore' => 'nullable|numeric|min:1|max:5',
        'initiativeScore' => 'nullable|numeric|min:1|max:5',
        'strengths' => 'nullable|string',
        'areasForImprovement' => 'nullable|string',
        'goals' => 'nullable|string',
        'comments' => 'nullable|string',
        'status' => 'required|string',
        'reviewDocument' => 'nullable|file|max:10240', // 10MB max
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingSelectedUser()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function openReviewModal($userId = null)
    {
        $this->resetValidation();
        $this->resetReviewForm();

        $this->editMode = false;
        $this->reviewId = null;
        $this->userId = $userId;
        $this->reviewDate = now()->format('Y-m-d');
        $this->reviewPeriodStart = now()->subMonths(6)->format('Y-m-d');
        $this->reviewPeriodEnd = now()->format('Y-m-d');

        $this->showReviewModal = true;
    }

    public function editReview(PerformanceReview $review)
    {
        $this->resetValidation();
        $this->resetReviewForm();

        $this->editMode = true;
        $this->reviewId = $review->id;
        $this->userId = $review->user_id;
        $this->reviewDate = $review->review_date->format('Y-m-d');
        $this->reviewPeriodStart = $review->review_period_start->format('Y-m-d');
        $this->reviewPeriodEnd = $review->review_period_end->format('Y-m-d');
        $this->performanceScore = $review->performance_score;
        $this->attendanceScore = $review->attendance_score;
        $this->communicationScore = $review->communication_score;
        $this->teamworkScore = $review->teamwork_score;
        $this->initiativeScore = $review->initiative_score;
        $this->overallScore = $review->overall_score;
        $this->strengths = $review->strengths;
        $this->areasForImprovement = $review->areas_for_improvement;
        $this->goals = $review->goals;
        $this->comments = $review->comments;
        $this->status = $review->status;

        $this->showReviewModal = true;
    }

    public function closeReviewModal()
    {
        $this->showReviewModal = false;
    }

    public function resetReviewForm()
    {
        $this->userId = null;
        $this->reviewDate = now()->format('Y-m-d');
        $this->reviewPeriodStart = now()->subMonths(6)->format('Y-m-d');
        $this->reviewPeriodEnd = now()->format('Y-m-d');
        $this->performanceScore = null;
        $this->attendanceScore = null;
        $this->communicationScore = null;
        $this->teamworkScore = null;
        $this->initiativeScore = null;
        $this->overallScore = null;
        $this->strengths = null;
        $this->areasForImprovement = null;
        $this->goals = null;
        $this->comments = null;
        $this->status = 'draft';
        $this->reviewDocument = null;
    }

    public function calculateOverallScore()
    {
        $scores = [
            $this->performanceScore,
            $this->attendanceScore,
            $this->communicationScore,
            $this->teamworkScore,
            $this->initiativeScore,
        ];

        // Filter out null values
        $scores = array_filter($scores, function ($score) {
            return $score !== null && $score !== '';
        });

        if (count($scores) === 0) {
            $this->overallScore = null;
            return;
        }

        $this->overallScore = round(array_sum($scores) / count($scores), 2);
    }

    public function saveReview()
    {
        $this->validate();

        // Calculate overall score
        $this->calculateOverallScore();

        if ($this->editMode && $this->reviewId) {
            $review = PerformanceReview::findOrFail($this->reviewId);
            $review->update([
                'user_id' => $this->userId,
                'reviewer_id' => Auth::id(),
                'review_date' => $this->reviewDate,
                'review_period_start' => $this->reviewPeriodStart,
                'review_period_end' => $this->reviewPeriodEnd,
                'performance_score' => $this->performanceScore,
                'attendance_score' => $this->attendanceScore,
                'communication_score' => $this->communicationScore,
                'teamwork_score' => $this->teamworkScore,
                'initiative_score' => $this->initiativeScore,
                'overall_score' => $this->overallScore,
                'strengths' => $this->strengths,
                'areas_for_improvement' => $this->areasForImprovement,
                'goals' => $this->goals,
                'comments' => $this->comments,
                'status' => $this->status,
            ]);

            $message = 'Performance review updated successfully.';
        } else {
            $review = PerformanceReview::create([
                'user_id' => $this->userId,
                'reviewer_id' => Auth::id(),
                'review_date' => $this->reviewDate,
                'review_period_start' => $this->reviewPeriodStart,
                'review_period_end' => $this->reviewPeriodEnd,
                'performance_score' => $this->performanceScore,
                'attendance_score' => $this->attendanceScore,
                'communication_score' => $this->communicationScore,
                'teamwork_score' => $this->teamworkScore,
                'initiative_score' => $this->initiativeScore,
                'overall_score' => $this->overallScore,
                'strengths' => $this->strengths,
                'areas_for_improvement' => $this->areasForImprovement,
                'goals' => $this->goals,
                'comments' => $this->comments,
                'status' => $this->status,
            ]);

            $message = 'Performance review created successfully.';
        }

        // Handle review document upload if provided
        if ($this->reviewDocument) {
            $fileName = 'Performance Review - ' . ($review->user->first_name ?? '') . ' ' . ($review->user->last_name ?? '') . ' - ' . now()->format('Y-m-d');
            $filePath = $this->reviewDocument->store('media/documents/performance_review', 'public');

            Media::create([
                'mediable_id' => $review->id,
                'mediable_type' => PerformanceReview::class,
                'file_name' => $fileName,
                'file_path' => $filePath,
                'mime_type' => $this->reviewDocument->getMimeType(),
                'category' => 'performance_review',
                'uploaded_by' => Auth::id(),
            ]);
        }

        session()->flash('message', $message);
        $this->closeReviewModal();
    }

    public function deleteReview($reviewId)
    {
        $review = PerformanceReview::findOrFail($reviewId);

        // Delete associated documents
        foreach ($review->documents as $document) {
            Storage::disk('public')->delete($document->file_path);
            $document->delete();
        }

        // Delete the review
        $review->delete();

        session()->flash('message', 'Performance review deleted successfully.');
    }

    public function render()
    {
        $reviews = PerformanceReview::query()
            ->with(['user', 'reviewer'])
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%')
                      ->orWhere('registration_number', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->selectedUser, function ($query) {
                $query->where('user_id', $this->selectedUser);
            })
            ->when($this->dateRange, function ($query) {
                if ($this->dateRange === 'last_month') {
                    $query->whereMonth('review_date', now()->subMonth()->month)
                          ->whereYear('review_date', now()->subMonth()->year);
                } elseif ($this->dateRange === 'last_quarter') {
                    $query->where('review_date', '>=', now()->subMonths(3))
                          ->where('review_date', '<=', now());
                } elseif ($this->dateRange === 'last_year') {
                    $query->whereYear('review_date', now()->subYear()->year);
                } elseif ($this->dateRange === 'current_month') {
                    $query->whereMonth('review_date', now()->month)
                          ->whereYear('review_date', now()->year);
                } elseif ($this->dateRange === 'current_year') {
                    $query->whereYear('review_date', now()->year);
                }
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        $users = User::orderBy('first_name')->get();

        return view('livewire.hr.hr-performance', [
            'reviews' => $reviews,
            'users' => $users,
        ]);
    }
}
