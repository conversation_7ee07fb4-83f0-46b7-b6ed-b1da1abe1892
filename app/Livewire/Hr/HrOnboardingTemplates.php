<?php

namespace App\Livewire\Hr;

use App\Models\Department;
use App\Models\OnboardingTemplate;
use App\Models\OnboardingTemplateTask;
use App\Models\Role;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class HrOnboardingTemplates extends Component
{
    use WithPagination;
    
    public $search = '';
    public $perPage = 10;
    
    // Template form
    public $templateId;
    public $name;
    public $description;
    public $roleId;
    public $departmentId;
    public $isActive = true;
    
    // Task form
    public $taskId;
    public $taskName;
    public $taskDescription;
    public $taskCategory = 'documentation';
    public $taskDueDays = 1;
    public $taskResponsibleRole;
    public $taskOrder = 0;
    public $taskIsRequired = true;
    
    // Modals
    public $showTemplateModal = false;
    public $showTaskModal = false;
    public $editingTemplate = null;
    
    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'roleId' => 'nullable|exists:roles,id',
        'departmentId' => 'nullable|exists:departments,id',
        'isActive' => 'boolean',
        'taskName' => 'required|string|max:255',
        'taskDescription' => 'nullable|string',
        'taskCategory' => 'required|string',
        'taskDueDays' => 'required|integer|min:1',
        'taskResponsibleRole' => 'nullable|string',
        'taskOrder' => 'required|integer|min:0',
        'taskIsRequired' => 'boolean',
    ];
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function openTemplateModal($templateId = null)
    {
        $this->reset(['name', 'description', 'roleId', 'departmentId', 'isActive']);
        
        if ($templateId) {
            $this->templateId = $templateId;
            $template = OnboardingTemplate::find($templateId);
            
            if ($template) {
                $this->name = $template->name;
                $this->description = $template->description;
                $this->roleId = $template->role_id;
                $this->departmentId = $template->department_id;
                $this->isActive = $template->is_active;
                $this->editingTemplate = $template;
            }
        } else {
            $this->templateId = null;
            $this->editingTemplate = null;
        }
        
        $this->showTemplateModal = true;
    }
    
    public function closeTemplateModal()
    {
        $this->showTemplateModal = false;
        $this->reset(['templateId', 'name', 'description', 'roleId', 'departmentId', 'isActive', 'editingTemplate']);
    }
    
    public function saveTemplate()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'roleId' => 'nullable|exists:roles,id',
            'departmentId' => 'nullable|exists:departments,id',
            'isActive' => 'boolean',
        ]);
        
        if ($this->templateId) {
            // Update existing template
            $template = OnboardingTemplate::find($this->templateId);
            
            if (!$template) {
                session()->flash('error', 'Template not found.');
                return;
            }
            
            $template->update([
                'name' => $this->name,
                'description' => $this->description,
                'role_id' => $this->roleId,
                'department_id' => $this->departmentId,
                'is_active' => $this->isActive,
            ]);
            
            session()->flash('message', 'Template updated successfully.');
        } else {
            // Create new template
            OnboardingTemplate::create([
                'name' => $this->name,
                'description' => $this->description,
                'role_id' => $this->roleId,
                'department_id' => $this->departmentId,
                'is_active' => $this->isActive,
                'created_by' => Auth::id(),
            ]);
            
            session()->flash('message', 'Template created successfully.');
        }
        
        $this->closeTemplateModal();
    }
    
    public function cloneTemplate($templateId)
    {
        $template = OnboardingTemplate::find($templateId);
        
        if (!$template) {
            session()->flash('error', 'Template not found.');
            return;
        }
        
        $newName = $template->name . ' (Copy)';
        $template->cloneTemplate($newName);
        
        session()->flash('message', 'Template cloned successfully.');
    }
    
    public function openTaskModal($templateId, $taskId = null)
    {
        $this->reset(['taskName', 'taskDescription', 'taskCategory', 'taskDueDays', 'taskResponsibleRole', 'taskOrder', 'taskIsRequired']);
        
        $this->templateId = $templateId;
        
        if ($taskId) {
            $this->taskId = $taskId;
            $task = OnboardingTemplateTask::find($taskId);
            
            if ($task) {
                $this->taskName = $task->name;
                $this->taskDescription = $task->description;
                $this->taskCategory = $task->category;
                $this->taskDueDays = $task->due_days;
                $this->taskResponsibleRole = $task->responsible_role;
                $this->taskOrder = $task->order;
                $this->taskIsRequired = $task->is_required;
            }
        } else {
            $this->taskId = null;
            
            // Set next order number
            $maxOrder = OnboardingTemplateTask::where('template_id', $templateId)->max('order') ?? 0;
            $this->taskOrder = $maxOrder + 1;
        }
        
        $this->showTaskModal = true;
    }
    
    public function closeTaskModal()
    {
        $this->showTaskModal = false;
        $this->reset(['taskId', 'taskName', 'taskDescription', 'taskCategory', 'taskDueDays', 'taskResponsibleRole', 'taskOrder', 'taskIsRequired']);
    }
    
    public function saveTask()
    {
        $this->validate([
            'taskName' => 'required|string|max:255',
            'taskDescription' => 'nullable|string',
            'taskCategory' => 'required|string',
            'taskDueDays' => 'required|integer|min:1',
            'taskResponsibleRole' => 'nullable|string',
            'taskOrder' => 'required|integer|min:0',
            'taskIsRequired' => 'boolean',
        ]);
        
        if ($this->taskId) {
            // Update existing task
            $task = OnboardingTemplateTask::find($this->taskId);
            
            if (!$task) {
                session()->flash('error', 'Task not found.');
                return;
            }
            
            $task->update([
                'name' => $this->taskName,
                'description' => $this->taskDescription,
                'category' => $this->taskCategory,
                'due_days' => $this->taskDueDays,
                'responsible_role' => $this->taskResponsibleRole,
                'order' => $this->taskOrder,
                'is_required' => $this->taskIsRequired,
            ]);
            
            session()->flash('message', 'Task updated successfully.');
        } else {
            // Create new task
            OnboardingTemplateTask::create([
                'template_id' => $this->templateId,
                'name' => $this->taskName,
                'description' => $this->taskDescription,
                'category' => $this->taskCategory,
                'due_days' => $this->taskDueDays,
                'responsible_role' => $this->taskResponsibleRole,
                'order' => $this->taskOrder,
                'is_required' => $this->taskIsRequired,
            ]);
            
            session()->flash('message', 'Task created successfully.');
        }
        
        $this->closeTaskModal();
    }
    
    public function deleteTask($taskId)
    {
        $task = OnboardingTemplateTask::find($taskId);
        
        if (!$task) {
            session()->flash('error', 'Task not found.');
            return;
        }
        
        $task->delete();
        
        session()->flash('message', 'Task deleted successfully.');
    }
    
    public function render()
    {
        $templates = OnboardingTemplate::with(['role', 'department', 'creator'])
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->orderBy('name')
            ->paginate($this->perPage);
            
        $roles = Role::orderBy('name')->get();
        $departments = Department::orderBy('name')->get();
        
        $categories = [
            'documentation' => 'Documentation',
            'training' => 'Training',
            'equipment' => 'Equipment',
            'system_access' => 'System Access',
            'orientation' => 'Orientation',
            'hr_admin' => 'HR Administration',
            'it_setup' => 'IT Setup',
            'team_introduction' => 'Team Introduction',
            'workspace' => 'Workspace Setup',
            'other' => 'Other',
        ];
        
        $responsibleRoles = [
            'hr_manager' => 'HR Manager',
            'it_manager' => 'IT Manager',
            'supervisor' => 'Supervisor',
            'trainer' => 'Trainer',
            'manager' => 'Manager',
            'admin' => 'Administrator',
        ];
        
        return view('livewire.hr.hr-onboarding-templates', [
            'templates' => $templates,
            'roles' => $roles,
            'departments' => $departments,
            'categories' => $categories,
            'responsibleRoles' => $responsibleRoles,
        ]);
    }
}
