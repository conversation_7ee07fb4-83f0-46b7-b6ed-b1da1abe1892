<?php

namespace App\Livewire\Hr;

use App\Models\Media;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class HrDocuments extends Component
{
    use WithPagination, WithFileUploads;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedCategory = '';
    public $selectedUser = '';

    // Document upload properties
    public $showUploadModal = false;
    public $documentFile;
    public $documentCategory = 'resume';
    public $documentUser;
    public $documentName = '';

    // Document categories
    public $documentCategories = [
        'resume' => 'Resume/CV',
        'id_card' => 'ID Card',
        'certificate' => 'Certificate',
        'contract' => 'Contract',
        'other_document' => 'Other Document'
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedCategory()
    {
        $this->resetPage();
    }

    public function updatingSelectedUser()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function openUploadModal($userId = null)
    {
        $this->showUploadModal = true;
        $this->documentUser = $userId;
        $this->reset(['documentFile', 'documentName']);
        $this->documentCategory = 'resume';
    }

    public function closeUploadModal()
    {
        $this->showUploadModal = false;
    }

    public function uploadDocument()
    {
        $this->validate([
            'documentFile' => 'required|file|max:10240', // 10MB max
            'documentCategory' => 'required|string',
            'documentUser' => 'required|exists:users,id',
            'documentName' => 'nullable|string|max:255',
        ]);

        $user = User::find($this->documentUser);

        if (!$user) {
            session()->flash('error', 'User not found.');
            return;
        }

        $fileName = $this->documentName ?: $this->documentFile->getClientOriginalName();
        $filePath = $this->documentFile->store('media/documents/' . $this->documentCategory, 'public');

        Media::create([
            'mediable_id' => $user->id,
            'mediable_type' => User::class,
            'file_name' => $fileName,
            'file_path' => $filePath,
            'mime_type' => $this->documentFile->getMimeType(),
            'category' => $this->documentCategory,
            'uploaded_by' => Auth::id(),
        ]);

        session()->flash('message', 'Document uploaded successfully.');
        $this->closeUploadModal();
    }

    public function deleteDocument($documentId)
    {
        $document = Media::find($documentId);

        if (!$document) {
            session()->flash('error', 'Document not found.');
            return;
        }

        // Delete the file from storage
        Storage::disk('public')->delete($document->file_path);

        // Delete the database record
        $document->delete();

        session()->flash('message', 'Document deleted successfully.');
    }

    public function render()
    {
        $documents = Media::query()
            ->whereIn('category', array_keys($this->documentCategories))
            ->when($this->search, function ($query) {
                $query->where('file_name', 'like', '%' . $this->search . '%');
            })
            ->when($this->selectedCategory, function ($query) {
                $query->where('category', $this->selectedCategory);
            })
            ->when($this->selectedUser, function ($query) {
                $query->where('mediable_id', $this->selectedUser)
                    ->where('mediable_type', User::class);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        $users = User::orderBy('first_name')->get();

        return view('livewire.hr.hr-documents', [
            'documents' => $documents,
            'users' => $users,
            'documentCategories' => $this->documentCategories,
        ]);
    }
}
