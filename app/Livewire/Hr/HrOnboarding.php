<?php

namespace App\Livewire\Hr;

use App\Models\EmployeeOnboarding;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class HrOnboarding extends Component
{
    use WithPagination;
    
    public $search = '';
    public $perPage = 10;
    public $sortField = 'start_date';
    public $sortDirection = 'desc';
    public $statusFilter = '';
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingStatusFilter()
    {
        $this->resetPage();
    }
    
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }
    
    public function render()
    {
        // Get onboarding processes with related user data
        $onboardings = EmployeeOnboarding::with(['user', 'assignedTo'])
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter, function ($query) {
                $query->where('status', $this->statusFilter);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
        
        // Get counts for dashboard
        $pendingCount = EmployeeOnboarding::where('status', 'pending')->count();
        $inProgressCount = EmployeeOnboarding::where('status', 'in_progress')->count();
        $completedCount = EmployeeOnboarding::where('status', 'completed')->count();
        $cancelledCount = EmployeeOnboarding::where('status', 'cancelled')->count();
        
        // Get recent onboardings for dashboard
        $recentOnboardings = EmployeeOnboarding::with(['user', 'assignedTo'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
        
        // Get upcoming onboardings (starting in the next 7 days)
        $upcomingOnboardings = EmployeeOnboarding::with(['user', 'assignedTo'])
            ->where('start_date', '>=', now())
            ->where('start_date', '<=', now()->addDays(7))
            ->orderBy('start_date')
            ->take(5)
            ->get();
        
        return view('livewire.hr.hr-onboarding', [
            'onboardings' => $onboardings,
            'pendingCount' => $pendingCount,
            'inProgressCount' => $inProgressCount,
            'completedCount' => $completedCount,
            'cancelledCount' => $cancelledCount,
            'recentOnboardings' => $recentOnboardings,
            'upcomingOnboardings' => $upcomingOnboardings,
        ]);
    }
}
