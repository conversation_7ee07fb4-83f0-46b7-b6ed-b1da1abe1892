<?php

namespace App\Livewire\Hr;

use App\Models\Contract;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;

class HrContractDelete extends Component
{
    public $contract;
    public $confirmationText = '';
    public $confirmationValid = false;

    public function mount(Contract $contract)
    {
        $this->contract = $contract->load(['user', 'documents']);
    }

    public function updatedConfirmationText()
    {
        // Use the user's name and contract type as confirmation
        $confirmationString = $this->contract->user->full_name . ' - ' . $this->contract->type;
        $this->confirmationValid = ($this->confirmationText === $confirmationString);
    }

    public function delete()
    {
        // Use the user's name and contract type as confirmation
        $confirmationString = $this->contract->user->full_name . ' - ' . $this->contract->type;

        // Validate that the confirmation text matches
        if ($this->confirmationText !== $confirmationString) {
            $this->addError('confirmationText', 'Please type the exact confirmation text to confirm deletion.');
            return;
        }

        try {
            // Store information for success message
            $userName = $this->contract->user->full_name;
            $contractType = $this->contract->type;

            // Delete associated documents
            foreach ($this->contract->documents as $document) {
                if ($document->file_path && Storage::disk('public')->exists($document->file_path)) {
                    Storage::disk('public')->delete($document->file_path);
                }
                $document->delete();
            }

            // Delete the contract
            $this->contract->delete();

            // Flash success message
            session()->flash('message', "Contract for '{$userName}' ({$contractType}) has been deleted successfully.");

            // Redirect to the contracts page
            return $this->redirect(route('hr.contracts'), navigate: true);
        } catch (\Exception $e) {
            // Handle any exceptions that might occur during deletion
            session()->flash('error', 'An error occurred while deleting the contract: ' . $e->getMessage());
            return $this->redirect(route('hr.contracts'), navigate: true);
        }
    }

    #[On('to-hr-contracts')]
    public function navigateToContracts()
    {
        return redirect()->route('hr.contracts');
    }

    public function render()
    {
        return view('livewire.hr.hr-contract-delete');
    }
}
