<?php

namespace App\Livewire\CallQuality;

use App\Models\Call;
use App\Models\CallEvaluation;
use Livewire\Component;

class CallShow extends Component
{
    public Call $call;
    public $evaluations = [];
    public $showEvaluationModal = false;
    
    public function mount(Call $call)
    {
        $this->call = $call;
        $this->loadEvaluations();
    }
    
    public function loadEvaluations()
    {
        $this->evaluations = $this->call->evaluations()->with('evaluator')->get();
    }
    
    public function openEvaluationModal()
    {
        $this->showEvaluationModal = true;
        $this->dispatch('open-evaluation-modal', callId: $this->call->id);
    }
    
    public function closeEvaluationModal()
    {
        $this->showEvaluationModal = false;
    }
    
    public function evaluationCompleted()
    {
        $this->closeEvaluationModal();
        $this->loadEvaluations();
        $this->dispatch('refresh-call-data');
    }
    
    public function render()
    {
        return view('livewire.call-quality.call-show');
    }
}
