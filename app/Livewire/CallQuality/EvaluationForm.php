<?php

namespace App\Livewire\CallQuality;

use App\Models\Call;
use App\Models\CallEvaluation;
use App\Models\EvaluationCriteria;
use App\Models\EvaluationScore;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class EvaluationForm extends Component
{
    public $callId;
    public $call;
    public $evaluationId = null;
    public $isEditMode = false;
    
    // Form fields
    public $evaluationDate;
    public $greetingScore;
    public $communicationScore;
    public $knowledgeScore;
    public $problemSolvingScore;
    public $closingScore;
    public $complianceScore;
    public $overallScore;
    public $strengths;
    public $areasForImprovement;
    public $actionItems;
    public $notes;
    public $status = 'completed';
    
    // Criteria scores
    public $criteriaScores = [];
    public $criteriaComments = [];
    
    protected $listeners = ['open-evaluation-modal' => 'loadCall'];
    
    protected $rules = [
        'evaluationDate' => 'required|date',
        'greetingScore' => 'nullable|numeric|min:1|max:5',
        'communicationScore' => 'nullable|numeric|min:1|max:5',
        'knowledgeScore' => 'nullable|numeric|min:1|max:5',
        'problemSolvingScore' => 'nullable|numeric|min:1|max:5',
        'closingScore' => 'nullable|numeric|min:1|max:5',
        'complianceScore' => 'nullable|numeric|min:1|max:5',
        'strengths' => 'nullable|string',
        'areasForImprovement' => 'nullable|string',
        'actionItems' => 'nullable|string',
        'notes' => 'nullable|string',
        'status' => 'required|string|in:draft,completed,reviewed',
        'criteriaScores.*' => 'nullable|numeric|min:1|max:5',
        'criteriaComments.*' => 'nullable|string',
    ];
    
    public function mount($evaluationId = null)
    {
        $this->evaluationDate = now()->format('Y-m-d');
        
        if ($evaluationId) {
            $this->evaluationId = $evaluationId;
            $this->isEditMode = true;
            $this->loadEvaluation();
        }
    }
    
    public function loadCall($callId)
    {
        $this->callId = $callId;
        $this->call = Call::with(['agent', 'campaign'])->findOrFail($callId);
        $this->loadCriteria();
    }
    
    public function loadEvaluation()
    {
        $evaluation = CallEvaluation::with(['scores.criteria'])->findOrFail($this->evaluationId);
        $this->callId = $evaluation->call_id;
        $this->call = Call::with(['agent', 'campaign'])->findOrFail($this->callId);
        
        $this->evaluationDate = $evaluation->evaluation_date->format('Y-m-d');
        $this->greetingScore = $evaluation->greeting_score;
        $this->communicationScore = $evaluation->communication_score;
        $this->knowledgeScore = $evaluation->knowledge_score;
        $this->problemSolvingScore = $evaluation->problem_solving_score;
        $this->closingScore = $evaluation->closing_score;
        $this->complianceScore = $evaluation->compliance_score;
        $this->overallScore = $evaluation->overall_score;
        $this->strengths = $evaluation->strengths;
        $this->areasForImprovement = $evaluation->areas_for_improvement;
        $this->actionItems = $evaluation->action_items;
        $this->notes = $evaluation->notes;
        $this->status = $evaluation->status;
        
        $this->loadCriteria();
        
        // Load criteria scores
        foreach ($evaluation->scores as $score) {
            $this->criteriaScores[$score->evaluation_criteria_id] = $score->score;
            $this->criteriaComments[$score->evaluation_criteria_id] = $score->comment;
        }
    }
    
    public function loadCriteria()
    {
        // Get criteria for this campaign (or global criteria)
        $criteria = EvaluationCriteria::where(function ($query) {
            $query->where('campaign_id', $this->call->campaign_id)
                  ->orWhereNull('campaign_id');
        })->where('is_active', true)
          ->orderBy('category')
          ->orderBy('name')
          ->get();
        
        // Initialize scores for criteria that don't have scores yet
        foreach ($criteria as $criterion) {
            if (!isset($this->criteriaScores[$criterion->id])) {
                $this->criteriaScores[$criterion->id] = null;
            }
            if (!isset($this->criteriaComments[$criterion->id])) {
                $this->criteriaComments[$criterion->id] = null;
            }
        }
    }
    
    public function calculateOverallScore()
    {
        $scores = [
            $this->greetingScore,
            $this->communicationScore,
            $this->knowledgeScore,
            $this->problemSolvingScore,
            $this->closingScore,
            $this->complianceScore,
        ];
        
        // Filter out null values
        $scores = array_filter($scores, function ($score) {
            return $score !== null && $score !== '';
        });
        
        if (count($scores) === 0) {
            $this->overallScore = null;
            return;
        }
        
        $this->overallScore = round(array_sum($scores) / count($scores), 1);
    }
    
    public function calculateWeightedScore()
    {
        $totalWeight = 0;
        $weightedSum = 0;
        $criteriaCount = 0;
        
        // Get all criteria
        $criteria = EvaluationCriteria::whereIn('id', array_keys($this->criteriaScores))->get();
        
        foreach ($criteria as $criterion) {
            $score = $this->criteriaScores[$criterion->id];
            
            if ($score !== null && $score !== '') {
                $weight = $criterion->weight;
                $totalWeight += $weight;
                $weightedSum += $score * $weight;
                $criteriaCount++;
            }
        }
        
        if ($criteriaCount === 0) {
            $this->calculateOverallScore();
            return;
        }
        
        $this->overallScore = $totalWeight > 0 ? round($weightedSum / $totalWeight, 1) : null;
    }
    
    public function updatedCriteriaScores()
    {
        $this->calculateWeightedScore();
    }
    
    public function updatedGreetingScore()
    {
        $this->calculateOverallScore();
    }
    
    public function updatedCommunicationScore()
    {
        $this->calculateOverallScore();
    }
    
    public function updatedKnowledgeScore()
    {
        $this->calculateOverallScore();
    }
    
    public function updatedProblemSolvingScore()
    {
        $this->calculateOverallScore();
    }
    
    public function updatedClosingScore()
    {
        $this->calculateOverallScore();
    }
    
    public function updatedComplianceScore()
    {
        $this->calculateOverallScore();
    }
    
    public function saveEvaluation()
    {
        $this->validate();
        
        // Calculate overall score if not already done
        if ($this->overallScore === null) {
            $this->calculateWeightedScore();
        }
        
        if ($this->isEditMode) {
            $evaluation = CallEvaluation::findOrFail($this->evaluationId);
        } else {
            $evaluation = new CallEvaluation();
            $evaluation->call_id = $this->callId;
            $evaluation->evaluator_id = Auth::id();
        }
        
        $evaluation->evaluation_date = $this->evaluationDate;
        $evaluation->greeting_score = $this->greetingScore;
        $evaluation->communication_score = $this->communicationScore;
        $evaluation->knowledge_score = $this->knowledgeScore;
        $evaluation->problem_solving_score = $this->problemSolvingScore;
        $evaluation->closing_score = $this->closingScore;
        $evaluation->compliance_score = $this->complianceScore;
        $evaluation->overall_score = $this->overallScore;
        $evaluation->strengths = $this->strengths;
        $evaluation->areas_for_improvement = $this->areasForImprovement;
        $evaluation->action_items = $this->actionItems;
        $evaluation->notes = $this->notes;
        $evaluation->status = $this->status;
        
        $evaluation->save();
        
        // Save criteria scores
        foreach ($this->criteriaScores as $criteriaId => $score) {
            if ($score !== null && $score !== '') {
                $evaluationScore = EvaluationScore::updateOrCreate(
                    [
                        'call_evaluation_id' => $evaluation->id,
                        'evaluation_criteria_id' => $criteriaId,
                    ],
                    [
                        'score' => $score,
                        'comment' => $this->criteriaComments[$criteriaId] ?? null,
                    ]
                );
            }
        }
        
        $message = $this->isEditMode ? 'Evaluation updated successfully.' : 'Evaluation saved successfully.';
        session()->flash('message', $message);
        
        $this->dispatch('evaluation-completed');
    }
    
    public function render()
    {
        $criteria = null;
        
        if ($this->call) {
            $criteria = EvaluationCriteria::where(function ($query) {
                $query->where('campaign_id', $this->call->campaign_id)
                      ->orWhereNull('campaign_id');
            })->where('is_active', true)
              ->orderBy('category')
              ->orderBy('name')
              ->get()
              ->groupBy('category');
        }
        
        return view('livewire.call-quality.evaluation-form', [
            'criteria' => $criteria,
        ]);
    }
}
