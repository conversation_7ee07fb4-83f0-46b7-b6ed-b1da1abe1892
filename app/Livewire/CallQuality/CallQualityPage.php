<?php

namespace App\Livewire\CallQuality;

use App\Livewire\Global\Page;

class CallQualityPage extends Page
{
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    public function mount($component = '')
    {
        parent::mount($component);

        // Get call from route if available
        $call = request()->route('call');
        $this->pages = [
            [
                'module_id' => 'call-quality-dashboard-module',
                'title' => 'Dashboard',
                'description' => 'Call Quality Dashboard',
                'route' => 'call-quality.dashboard',
                'display' => true,
                'authorized_permissions' => ['manage_call_quality'],
                'section_routes' => [],
                'sections' => [],
                'component' => 'call-quality.dashboard',
            ],
            [
                'module_id' => 'call-quality-calls-module',
                'title' => 'Calls',
                'description' => 'Call Management',
                'route' => 'call-quality.calls',
                'display' => true,
                'authorized_permissions' => ['manage_call_quality_calls'],
                'section_routes' => [],
                'sections' => [],
                'component' => 'call-quality.call-index',
            ],
            [
                'module_id' => 'call-quality-evaluations-module',
                'title' => 'Evaluations',
                'description' => 'Call Evaluations',
                'route' => 'call-quality.evaluations',
                'display' => true,
                'authorized_permissions' => ['manage_call_quality_evaluations'],
                'section_routes' => [],
                'sections' => [],
                'component' => 'call-quality.evaluation-index',
            ],
            [
                'module_id' => 'call-quality-criteria-module',
                'title' => 'Criteria',
                'description' => 'Quality Criteria',
                'route' => 'call-quality.criteria',
                'display' => true,
                'authorized_permissions' => ['manage_call_quality_criteria'],
                'section_routes' => [],
                'sections' => [],
                'component' => 'call-quality.criteria-index',
            ],
            [
                'module_id' => 'call-quality-reports-module',
                'title' => 'Reports',
                'description' => 'Quality Reports',
                'route' => 'call-quality.reports',
                'display' => true,
                'authorized_permissions' => ['show_call_quality_reports'],
                'section_routes' => [],
                'sections' => [],
                'component' => 'call-quality.quality-reports',
            ],
            [
                'module_id' => 'call-quality-settings-module',
                'title' => 'Settings',
                'description' => 'Quality Settings',
                'route' => 'call-quality.settings',
                'display' => true,
                'authorized_permissions' => ['manage_call_quality_settings'],
                'section_routes' => [],
                'sections' => [],
                'component' => 'call-quality.quality-settings',
            ],
        ];

        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function setPageResume($route)
    {
        switch ($route) {
            case 'call-quality.dashboard':
                $this->resumeTitle = 'Call Quality Dashboard';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Call Quality Dashboard',
                    'description' => 'Monitor and manage call quality metrics and evaluations',
                    'metrics' => [
                        ['label' => 'Total Calls', 'value' => 0],
                        ['label' => 'Evaluated Calls', 'value' => 0],
                        ['label' => 'Average Score', 'value' => 0, 'suffix' => '%'],
                        ['label' => 'Active Criteria', 'value' => 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Call Quality Dashboard',
                    'type' => 'dashboard',
                    'description' => 'Monitor and manage call quality metrics and evaluations'
                ];
                break;

            case 'call-quality.calls':
                $this->resumeTitle = 'Call Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Call Management',
                    'description' => 'Manage and review call recordings and data',
                    'metrics' => [
                        ['label' => 'Total Calls', 'value' => 0],
                        ['label' => 'Pending Review', 'value' => 0],
                        ['label' => 'Completed', 'value' => 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Call Management',
                    'type' => 'dashboard',
                    'description' => 'Manage and review call recordings and data'
                ];
                break;

            case 'call-quality.evaluations':
                $this->resumeTitle = 'Call Evaluations';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Call Evaluations',
                    'description' => 'Review and manage call quality evaluations',
                    'metrics' => [
                        ['label' => 'Total Evaluations', 'value' => 0],
                        ['label' => 'Pending', 'value' => 0],
                        ['label' => 'Completed', 'value' => 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Call Evaluations',
                    'type' => 'dashboard',
                    'description' => 'Review and manage call quality evaluations'
                ];
                break;

            case 'call-quality.criteria':
                $this->resumeTitle = 'Quality Criteria';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Quality Criteria',
                    'description' => 'Manage evaluation criteria and scoring rules',
                    'metrics' => [
                        ['label' => 'Active Criteria', 'value' => 0],
                        ['label' => 'Categories', 'value' => 0],
                        ['label' => 'Total Points', 'value' => 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Quality Criteria',
                    'type' => 'dashboard',
                    'description' => 'Manage evaluation criteria and scoring rules'
                ];
                break;

            case 'call-quality.reports':
                $this->resumeTitle = 'Quality Reports';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Quality Reports',
                    'description' => 'View and analyze call quality reports and trends',
                    'metrics' => [
                        ['label' => 'Reports Generated', 'value' => 0],
                        ['label' => 'Average Score', 'value' => 0, 'suffix' => '%'],
                        ['label' => 'Trend', 'value' => 0, 'suffix' => '%']
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Quality Reports',
                    'type' => 'dashboard',
                    'description' => 'View and analyze call quality reports and trends'
                ];
                break;

            case 'call-quality.settings':
                $this->resumeTitle = 'Quality Settings';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Quality Settings',
                    'description' => 'Configure call quality monitoring settings and preferences',
                    'steps' => [
                        'Configure evaluation parameters',
                        'Set scoring thresholds',
                        'Define notification rules',
                        'Save settings'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Quality Settings',
                    'type' => 'form',
                    'description' => 'Configure call quality monitoring settings and preferences'
                ];
                break;

            default:
                $this->resumeTitle = 'Call Quality';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Call Quality',
                    'description' => 'Call quality monitoring and evaluation system'
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Call Quality',
                    'type' => 'default',
                    'description' => 'Call quality monitoring and evaluation system'
                ];
                break;
        }
    }

    public function render()
    {
        return view('livewire.call-quality.call-quality-page');
    }
}
