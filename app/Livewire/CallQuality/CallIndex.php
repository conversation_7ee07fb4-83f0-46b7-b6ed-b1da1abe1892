<?php

namespace App\Livewire\CallQuality;

use App\Models\Call;
use App\Models\Campaign;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class CallIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'call_time';
    public $sortDirection = 'desc';
    public $selectedCampaign = '';
    public $selectedAgent = '';
    public $selectedStatus = '';
    public $selectedDirection = '';
    public $dateRange = '';
    public $startDate = '';
    public $endDate = '';
    public $showFilters = false;
    public $showImportModal = false;
    public $selectedCalls = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'selectedCampaign' => ['except' => ''],
        'selectedAgent' => ['except' => ''],
        'selectedStatus' => ['except' => ''],
        'selectedDirection' => ['except' => ''],
        'dateRange' => ['except' => ''],
        'sortField' => ['except' => 'call_time'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount()
    {
        $this->setDefaultDateRange();
    }

    public function setDefaultDateRange()
    {
        $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function updatingSelectedCampaign()
    {
        $this->resetPage();
    }

    public function updatingSelectedAgent()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingSelectedDirection()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();
        
        switch ($this->dateRange) {
            case 'today':
                $this->startDate = Carbon::today()->format('Y-m-d');
                $this->endDate = Carbon::today()->format('Y-m-d');
                break;
            case 'yesterday':
                $this->startDate = Carbon::yesterday()->format('Y-m-d');
                $this->endDate = Carbon::yesterday()->format('Y-m-d');
                break;
            case 'this_week':
                $this->startDate = Carbon::now()->startOfWeek()->format('Y-m-d');
                $this->endDate = Carbon::now()->endOfWeek()->format('Y-m-d');
                break;
            case 'last_week':
                $this->startDate = Carbon::now()->subWeek()->startOfWeek()->format('Y-m-d');
                $this->endDate = Carbon::now()->subWeek()->endOfWeek()->format('Y-m-d');
                break;
            case 'this_month':
                $this->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                $this->endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_month':
                $this->startDate = Carbon::now()->subMonth()->startOfMonth()->format('Y-m-d');
                $this->endDate = Carbon::now()->subMonth()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_30_days':
                $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
                $this->endDate = Carbon::now()->format('Y-m-d');
                break;
            case 'last_90_days':
                $this->startDate = Carbon::now()->subDays(90)->format('Y-m-d');
                $this->endDate = Carbon::now()->format('Y-m-d');
                break;
            case 'custom':
                // Do nothing, let the user select custom dates
                break;
            default:
                $this->setDefaultDateRange();
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->selectedCampaign = '';
        $this->selectedAgent = '';
        $this->selectedStatus = '';
        $this->selectedDirection = '';
        $this->dateRange = '';
        $this->setDefaultDateRange();
        $this->resetPage();
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedCalls = $this->getCalls()->pluck('id')->map(function ($id) {
                return (string) $id;
            })->toArray();
        } else {
            $this->selectedCalls = [];
        }
    }

    public function updatedSelectedCalls()
    {
        $this->selectAll = false;
    }

    public function getCalls()
    {
        return Call::query()
            ->with(['agent', 'campaign'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('call_id', 'like', '%' . $this->search . '%')
                      ->orWhere('customer_phone', 'like', '%' . $this->search . '%')
                      ->orWhere('customer_name', 'like', '%' . $this->search . '%')
                      ->orWhereHas('agent', function ($q) {
                          $q->where('first_name', 'like', '%' . $this->search . '%')
                            ->orWhere('last_name', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->selectedCampaign, function ($query) {
                $query->where('campaign_id', $this->selectedCampaign);
            })
            ->when($this->selectedAgent, function ($query) {
                $query->where('user_id', $this->selectedAgent);
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->selectedDirection, function ($query) {
                $query->where('direction', $this->selectedDirection);
            })
            ->when($this->startDate && $this->endDate, function ($query) {
                $query->whereBetween('call_time', [
                    Carbon::parse($this->startDate)->startOfDay(),
                    Carbon::parse($this->endDate)->endOfDay()
                ]);
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function openImportModal()
    {
        $this->showImportModal = true;
    }

    public function closeImportModal()
    {
        $this->showImportModal = false;
    }

    public function bulkEvaluate()
    {
        if (empty($this->selectedCalls)) {
            session()->flash('error', 'No calls selected for evaluation.');
            return;
        }
        
        return redirect()->route('call-quality.bulk-evaluate', [
            'calls' => implode(',', $this->selectedCalls)
        ]);
    }

    public function render()
    {
        $calls = $this->getCalls()->paginate($this->perPage);
        $campaigns = Campaign::orderBy('name')->get();
        $agents = User::where('role_id', 6)->orderBy('first_name')->get();
        
        $callStatuses = [
            'completed' => 'Completed',
            'abandoned' => 'Abandoned',
            'transferred' => 'Transferred',
            'missed' => 'Missed',
        ];
        
        $callDirections = [
            'inbound' => 'Inbound',
            'outbound' => 'Outbound',
        ];
        
        $dateRanges = [
            'today' => 'Today',
            'yesterday' => 'Yesterday',
            'this_week' => 'This Week',
            'last_week' => 'Last Week',
            'this_month' => 'This Month',
            'last_month' => 'Last Month',
            'last_30_days' => 'Last 30 Days',
            'last_90_days' => 'Last 90 Days',
            'custom' => 'Custom Range',
        ];
        
        return view('livewire.call-quality.call-index', [
            'calls' => $calls,
            'campaigns' => $campaigns,
            'agents' => $agents,
            'callStatuses' => $callStatuses,
            'callDirections' => $callDirections,
            'dateRanges' => $dateRanges,
        ]);
    }
}
