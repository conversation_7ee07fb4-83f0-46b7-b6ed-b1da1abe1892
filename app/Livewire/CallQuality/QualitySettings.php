<?php

namespace App\Livewire\CallQuality;

use App\Models\Campaign;
use App\Models\KpiTarget;
use Livewire\Component;
use Livewire\WithPagination;

class QualitySettings extends Component
{
    use WithPagination;

    public $activeTab = 'kpi_targets';
    public $perPage = 10;
    public $showCreateModal = false;
    public $showEditModal = false;
    
    // KPI Target form fields
    public $targetId;
    public $metricName;
    public $targetType = 'individual';
    public $userId;
    public $campaignId;
    public $startDate;
    public $endDate;
    public $targetValue;
    public $minValue;
    public $maxValue;
    public $unit = 'count';
    public $description;
    public $isActive = true;

    protected $rules = [
        'metricName' => 'required|string|max:255',
        'targetType' => 'required|string|in:individual,team,campaign',
        'userId' => 'nullable|exists:users,id',
        'campaignId' => 'nullable|exists:campaigns,id',
        'startDate' => 'required|date',
        'endDate' => 'nullable|date|after_or_equal:startDate',
        'targetValue' => 'required|numeric',
        'minValue' => 'nullable|numeric|lte:targetValue',
        'maxValue' => 'nullable|numeric|gte:targetValue',
        'unit' => 'required|string|in:count,percentage,seconds,minutes,hours',
        'description' => 'nullable|string',
        'isActive' => 'boolean',
    ];

    public function mount()
    {
        $this->startDate = now()->format('Y-m-d');
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function closeCreateModal()
    {
        $this->showCreateModal = false;
        $this->resetForm();
    }

    public function openEditModal($targetId)
    {
        $this->resetForm();
        $this->targetId = $targetId;
        $target = KpiTarget::findOrFail($targetId);
        
        $this->metricName = $target->metric_name;
        $this->targetType = $target->target_type;
        $this->userId = $target->user_id;
        $this->campaignId = $target->campaign_id;
        $this->startDate = $target->start_date->format('Y-m-d');
        $this->endDate = $target->end_date ? $target->end_date->format('Y-m-d') : null;
        $this->targetValue = $target->target_value;
        $this->minValue = $target->min_value;
        $this->maxValue = $target->max_value;
        $this->unit = $target->unit;
        $this->description = $target->description;
        $this->isActive = $target->is_active;
        
        $this->showEditModal = true;
    }

    public function closeEditModal()
    {
        $this->showEditModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->targetId = null;
        $this->metricName = '';
        $this->targetType = 'individual';
        $this->userId = null;
        $this->campaignId = null;
        $this->startDate = now()->format('Y-m-d');
        $this->endDate = null;
        $this->targetValue = null;
        $this->minValue = null;
        $this->maxValue = null;
        $this->unit = 'count';
        $this->description = '';
        $this->isActive = true;
    }

    public function createTarget()
    {
        $this->validate();
        
        KpiTarget::create([
            'metric_name' => $this->metricName,
            'target_type' => $this->targetType,
            'user_id' => $this->userId,
            'campaign_id' => $this->campaignId,
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
            'target_value' => $this->targetValue,
            'min_value' => $this->minValue,
            'max_value' => $this->maxValue,
            'unit' => $this->unit,
            'description' => $this->description,
            'is_active' => $this->isActive,
        ]);
        
        $this->closeCreateModal();
        session()->flash('message', 'KPI target created successfully.');
    }

    public function updateTarget()
    {
        $this->validate();
        
        $target = KpiTarget::findOrFail($this->targetId);
        $target->update([
            'metric_name' => $this->metricName,
            'target_type' => $this->targetType,
            'user_id' => $this->userId,
            'campaign_id' => $this->campaignId,
            'start_date' => $this->startDate,
            'end_date' => $this->endDate,
            'target_value' => $this->targetValue,
            'min_value' => $this->minValue,
            'max_value' => $this->maxValue,
            'unit' => $this->unit,
            'description' => $this->description,
            'is_active' => $this->isActive,
        ]);
        
        $this->closeEditModal();
        session()->flash('message', 'KPI target updated successfully.');
    }

    public function toggleActive($targetId)
    {
        $target = KpiTarget::findOrFail($targetId);
        $target->is_active = !$target->is_active;
        $target->save();
        
        session()->flash('message', 'Target status updated successfully.');
    }

    public function render()
    {
        $kpiTargets = KpiTarget::with(['user', 'campaign'])
            ->orderBy('start_date', 'desc')
            ->paginate($this->perPage);
        
        $campaigns = Campaign::orderBy('name')->get();
        $metricOptions = [
            'quality_score' => 'Quality Score',
            'average_handle_time' => 'Average Handle Time',
            'first_call_resolution' => 'First Call Resolution',
            'customer_satisfaction' => 'Customer Satisfaction',
            'adherence_rate' => 'Schedule Adherence',
            'occupancy_rate' => 'Occupancy Rate',
            'conversion_rate' => 'Conversion Rate',
            'calls_handled' => 'Calls Handled',
            'evaluations_count' => 'Evaluations Count',
        ];
        
        $unitOptions = [
            'count' => 'Count',
            'percentage' => 'Percentage (%)',
            'seconds' => 'Seconds',
            'minutes' => 'Minutes',
            'hours' => 'Hours',
        ];
        
        return view('livewire.call-quality.quality-settings', [
            'kpiTargets' => $kpiTargets,
            'campaigns' => $campaigns,
            'metricOptions' => $metricOptions,
            'unitOptions' => $unitOptions,
        ]);
    }
}
