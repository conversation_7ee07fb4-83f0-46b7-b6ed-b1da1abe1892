<?php

namespace App\Livewire\CallQuality;

use App\Models\Call;
use App\Models\CallEvaluation;
use App\Models\Campaign;
use App\Models\EvaluationCriteria;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;

class QualityReports extends Component
{
    public $reportType = 'agent_performance';
    public $startDate;
    public $endDate;
    public $selectedCampaign = '';
    public $selectedAgent = '';
    public $selectedCriteria = '';
    public $groupBy = 'day';
    public $reportData = [];
    public $chartData = [];
    
    public function mount()
    {
        $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
        $this->generateReport();
    }
    
    public function updatedReportType()
    {
        $this->generateReport();
    }
    
    public function updatedStartDate()
    {
        $this->generateReport();
    }
    
    public function updatedEndDate()
    {
        $this->generateReport();
    }
    
    public function updatedSelectedCampaign()
    {
        $this->generateReport();
    }
    
    public function updatedSelectedAgent()
    {
        $this->generateReport();
    }
    
    public function updatedSelectedCriteria()
    {
        $this->generateReport();
    }
    
    public function updatedGroupBy()
    {
        $this->generateReport();
    }
    
    public function generateReport()
    {
        switch ($this->reportType) {
            case 'agent_performance':
                $this->generateAgentPerformanceReport();
                break;
            case 'campaign_performance':
                $this->generateCampaignPerformanceReport();
                break;
            case 'evaluation_criteria':
                $this->generateEvaluationCriteriaReport();
                break;
            case 'quality_trend':
                $this->generateQualityTrendReport();
                break;
            case 'evaluation_volume':
                $this->generateEvaluationVolumeReport();
                break;
        }
    }
    
    protected function generateAgentPerformanceReport()
    {
        $query = User::where('role_id', 6) // Agent role
            ->whereHas('calls', function ($query) {
                $query->whereBetween('call_time', [
                    Carbon::parse($this->startDate)->startOfDay(),
                    Carbon::parse($this->endDate)->endOfDay()
                ]);
                
                if ($this->selectedCampaign) {
                    $query->where('campaign_id', $this->selectedCampaign);
                }
                
                $query->whereHas('evaluations');
            })
            ->with(['calls' => function ($query) {
                $query->whereBetween('call_time', [
                    Carbon::parse($this->startDate)->startOfDay(),
                    Carbon::parse($this->endDate)->endOfDay()
                ]);
                
                if ($this->selectedCampaign) {
                    $query->where('campaign_id', $this->selectedCampaign);
                }
                
                $query->with(['evaluations']);
            }]);
        
        if ($this->selectedAgent) {
            $query->where('id', $this->selectedAgent);
        }
        
        $agents = $query->get();
        
        $this->reportData = $agents->map(function ($agent) {
            $evaluations = collect();
            
            foreach ($agent->calls as $call) {
                $evaluations = $evaluations->merge($call->evaluations);
            }
            
            $totalEvaluations = $evaluations->count();
            $averageScore = $evaluations->avg('overall_score') ?? 0;
            
            // Calculate score distribution
            $exceptional = $evaluations->filter(function ($eval) {
                return $eval->overall_score >= 4.5;
            })->count();
            
            $exceeds = $evaluations->filter(function ($eval) {
                return $eval->overall_score >= 3.5 && $eval->overall_score < 4.5;
            })->count();
            
            $meets = $evaluations->filter(function ($eval) {
                return $eval->overall_score >= 2.5 && $eval->overall_score < 3.5;
            })->count();
            
            $needsImprovement = $evaluations->filter(function ($eval) {
                return $eval->overall_score >= 1.5 && $eval->overall_score < 2.5;
            })->count();
            
            $unsatisfactory = $evaluations->filter(function ($eval) {
                return $eval->overall_score < 1.5;
            })->count();
            
            return [
                'id' => $agent->id,
                'name' => $agent->first_name . ' ' . $agent->last_name,
                'calls_count' => $agent->calls->count(),
                'evaluations_count' => $totalEvaluations,
                'average_score' => round($averageScore, 2),
                'score_distribution' => [
                    'exceptional' => $totalEvaluations > 0 ? round(($exceptional / $totalEvaluations) * 100, 1) : 0,
                    'exceeds' => $totalEvaluations > 0 ? round(($exceeds / $totalEvaluations) * 100, 1) : 0,
                    'meets' => $totalEvaluations > 0 ? round(($meets / $totalEvaluations) * 100, 1) : 0,
                    'needs_improvement' => $totalEvaluations > 0 ? round(($needsImprovement / $totalEvaluations) * 100, 1) : 0,
                    'unsatisfactory' => $totalEvaluations > 0 ? round(($unsatisfactory / $totalEvaluations) * 100, 1) : 0,
                ],
            ];
        })->sortByDesc('average_score')->values()->toArray();
        
        // Prepare chart data
        $this->chartData = [
            'labels' => collect($this->reportData)->pluck('name')->toArray(),
            'scores' => collect($this->reportData)->pluck('average_score')->toArray(),
        ];
    }
    
    protected function generateCampaignPerformanceReport()
    {
        $query = Campaign::whereHas('calls', function ($query) {
            $query->whereBetween('call_time', [
                Carbon::parse($this->startDate)->startOfDay(),
                Carbon::parse($this->endDate)->endOfDay()
            ]);
            
            $query->whereHas('evaluations');
        })
        ->with(['calls' => function ($query) {
            $query->whereBetween('call_time', [
                Carbon::parse($this->startDate)->startOfDay(),
                Carbon::parse($this->endDate)->endOfDay()
            ]);
            
            $query->with(['evaluations']);
        }]);
        
        if ($this->selectedCampaign) {
            $query->where('id', $this->selectedCampaign);
        }
        
        $campaigns = $query->get();
        
        $this->reportData = $campaigns->map(function ($campaign) {
            $evaluations = collect();
            
            foreach ($campaign->calls as $call) {
                $evaluations = $evaluations->merge($call->evaluations);
            }
            
            $totalEvaluations = $evaluations->count();
            $averageScore = $evaluations->avg('overall_score') ?? 0;
            
            return [
                'id' => $campaign->id,
                'name' => $campaign->name,
                'calls_count' => $campaign->calls->count(),
                'evaluations_count' => $totalEvaluations,
                'average_score' => round($averageScore, 2),
                'evaluation_rate' => $campaign->calls->count() > 0 ? round(($totalEvaluations / $campaign->calls->count()) * 100, 1) : 0,
            ];
        })->sortByDesc('average_score')->values()->toArray();
        
        // Prepare chart data
        $this->chartData = [
            'labels' => collect($this->reportData)->pluck('name')->toArray(),
            'scores' => collect($this->reportData)->pluck('average_score')->toArray(),
            'rates' => collect($this->reportData)->pluck('evaluation_rate')->toArray(),
        ];
    }
    
    protected function generateEvaluationCriteriaReport()
    {
        $query = EvaluationCriteria::whereHas('scores', function ($query) {
            $query->whereHas('evaluation', function ($query) {
                $query->whereBetween('evaluation_date', [
                    Carbon::parse($this->startDate)->startOfDay(),
                    Carbon::parse($this->endDate)->endOfDay()
                ]);
                
                if ($this->selectedCampaign) {
                    $query->whereHas('call', function ($query) {
                        $query->where('campaign_id', $this->selectedCampaign);
                    });
                }
            });
        })
        ->with(['scores' => function ($query) {
            $query->whereHas('evaluation', function ($query) {
                $query->whereBetween('evaluation_date', [
                    Carbon::parse($this->startDate)->startOfDay(),
                    Carbon::parse($this->endDate)->endOfDay()
                ]);
                
                if ($this->selectedCampaign) {
                    $query->whereHas('call', function ($query) {
                        $query->where('campaign_id', $this->selectedCampaign);
                    });
                }
            });
        }]);
        
        if ($this->selectedCriteria) {
            $query->where('id', $this->selectedCriteria);
        }
        
        $criteria = $query->get();
        
        $this->reportData = $criteria->map(function ($criterion) {
            $scores = $criterion->scores;
            $totalScores = $scores->count();
            $averageScore = $scores->avg('score') ?? 0;
            
            return [
                'id' => $criterion->id,
                'name' => $criterion->name,
                'category' => $criterion->category,
                'scores_count' => $totalScores,
                'average_score' => round($averageScore, 2),
            ];
        })->sortBy('average_score')->values()->toArray();
        
        // Prepare chart data
        $this->chartData = [
            'labels' => collect($this->reportData)->pluck('name')->toArray(),
            'scores' => collect($this->reportData)->pluck('average_score')->toArray(),
            'categories' => collect($this->reportData)->pluck('category')->toArray(),
        ];
    }
    
    protected function generateQualityTrendReport()
    {
        $dateFormat = 'Y-m-d';
        $interval = 'P1D'; // 1 day interval
        
        switch ($this->groupBy) {
            case 'day':
                $dateFormat = 'Y-m-d';
                $interval = 'P1D';
                break;
            case 'week':
                $dateFormat = 'Y-W';
                $interval = 'P1W';
                break;
            case 'month':
                $dateFormat = 'Y-m';
                $interval = 'P1M';
                break;
        }
        
        $period = new \DatePeriod(
            Carbon::parse($this->startDate),
            new \DateInterval($interval),
            Carbon::parse($this->endDate)->addDay()
        );
        
        $dateLabels = [];
        foreach ($period as $date) {
            $dateLabels[] = $date->format($dateFormat);
        }
        
        $query = CallEvaluation::whereBetween('evaluation_date', [
            Carbon::parse($this->startDate)->startOfDay(),
            Carbon::parse($this->endDate)->endOfDay()
        ]);
        
        if ($this->selectedCampaign) {
            $query->whereHas('call', function ($query) {
                $query->where('campaign_id', $this->selectedCampaign);
            });
        }
        
        if ($this->selectedAgent) {
            $query->whereHas('call', function ($query) {
                $query->where('user_id', $this->selectedAgent);
            });
        }
        
        $evaluations = $query->get();
        
        $groupedEvaluations = $evaluations->groupBy(function ($evaluation) use ($dateFormat) {
            return Carbon::parse($evaluation->evaluation_date)->format($dateFormat);
        });
        
        $scores = array_fill(0, count($dateLabels), 0);
        $counts = array_fill(0, count($dateLabels), 0);
        
        foreach ($groupedEvaluations as $date => $dateEvaluations) {
            $index = array_search($date, $dateLabels);
            
            if ($index !== false) {
                $scores[$index] = $dateEvaluations->avg('overall_score') ?? 0;
                $counts[$index] = $dateEvaluations->count();
            }
        }
        
        $this->reportData = collect($dateLabels)->map(function ($date, $index) use ($scores, $counts) {
            return [
                'date' => $date,
                'average_score' => round($scores[$index], 2),
                'evaluations_count' => $counts[$index],
            ];
        })->toArray();
        
        // Prepare chart data
        $this->chartData = [
            'labels' => $dateLabels,
            'scores' => array_map(function ($score) {
                return round($score, 2);
            }, $scores),
            'counts' => $counts,
        ];
    }
    
    protected function generateEvaluationVolumeReport()
    {
        $dateFormat = 'Y-m-d';
        $interval = 'P1D'; // 1 day interval
        
        switch ($this->groupBy) {
            case 'day':
                $dateFormat = 'Y-m-d';
                $interval = 'P1D';
                break;
            case 'week':
                $dateFormat = 'Y-W';
                $interval = 'P1W';
                break;
            case 'month':
                $dateFormat = 'Y-m';
                $interval = 'P1M';
                break;
        }
        
        $period = new \DatePeriod(
            Carbon::parse($this->startDate),
            new \DateInterval($interval),
            Carbon::parse($this->endDate)->addDay()
        );
        
        $dateLabels = [];
        foreach ($period as $date) {
            $dateLabels[] = $date->format($dateFormat);
        }
        
        // Get calls
        $callsQuery = Call::whereBetween('call_time', [
            Carbon::parse($this->startDate)->startOfDay(),
            Carbon::parse($this->endDate)->endOfDay()
        ]);
        
        if ($this->selectedCampaign) {
            $callsQuery->where('campaign_id', $this->selectedCampaign);
        }
        
        if ($this->selectedAgent) {
            $callsQuery->where('user_id', $this->selectedAgent);
        }
        
        $calls = $callsQuery->get();
        
        // Get evaluations
        $evaluationsQuery = CallEvaluation::whereBetween('evaluation_date', [
            Carbon::parse($this->startDate)->startOfDay(),
            Carbon::parse($this->endDate)->endOfDay()
        ]);
        
        if ($this->selectedCampaign) {
            $evaluationsQuery->whereHas('call', function ($query) {
                $query->where('campaign_id', $this->selectedCampaign);
            });
        }
        
        if ($this->selectedAgent) {
            $evaluationsQuery->whereHas('call', function ($query) {
                $query->where('user_id', $this->selectedAgent);
            });
        }
        
        $evaluations = $evaluationsQuery->get();
        
        // Group by date
        $groupedCalls = $calls->groupBy(function ($call) use ($dateFormat) {
            return Carbon::parse($call->call_time)->format($dateFormat);
        });
        
        $groupedEvaluations = $evaluations->groupBy(function ($evaluation) use ($dateFormat) {
            return Carbon::parse($evaluation->evaluation_date)->format($dateFormat);
        });
        
        $callCounts = array_fill(0, count($dateLabels), 0);
        $evaluationCounts = array_fill(0, count($dateLabels), 0);
        
        foreach ($groupedCalls as $date => $dateCalls) {
            $index = array_search($date, $dateLabels);
            
            if ($index !== false) {
                $callCounts[$index] = $dateCalls->count();
            }
        }
        
        foreach ($groupedEvaluations as $date => $dateEvaluations) {
            $index = array_search($date, $dateLabels);
            
            if ($index !== false) {
                $evaluationCounts[$index] = $dateEvaluations->count();
            }
        }
        
        $this->reportData = collect($dateLabels)->map(function ($date, $index) use ($callCounts, $evaluationCounts) {
            $callCount = $callCounts[$index];
            $evaluationCount = $evaluationCounts[$index];
            
            return [
                'date' => $date,
                'calls_count' => $callCount,
                'evaluations_count' => $evaluationCount,
                'evaluation_rate' => $callCount > 0 ? round(($evaluationCount / $callCount) * 100, 1) : 0,
            ];
        })->toArray();
        
        // Prepare chart data
        $this->chartData = [
            'labels' => $dateLabels,
            'calls' => $callCounts,
            'evaluations' => $evaluationCounts,
        ];
    }
    
    public function render()
    {
        $campaigns = Campaign::orderBy('name')->get();
        $agents = User::where('role_id', 6)->orderBy('first_name')->get();
        $criteria = EvaluationCriteria::orderBy('name')->get();
        
        return view('livewire.call-quality.quality-reports', [
            'campaigns' => $campaigns,
            'agents' => $agents,
            'criteria' => $criteria,
        ]);
    }
}
