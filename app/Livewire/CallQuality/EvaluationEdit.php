<?php

namespace App\Livewire\CallQuality;

use App\Models\CallEvaluation;
use Livewire\Component;

class EvaluationEdit extends Component
{
    public CallEvaluation $evaluation;
    
    public function mount(CallEvaluation $evaluation)
    {
        $this->evaluation = $evaluation;
    }
    
    public function render()
    {
        return view('livewire.call-quality.evaluation-edit', [
            'evaluation' => $this->evaluation,
        ]);
    }
}
