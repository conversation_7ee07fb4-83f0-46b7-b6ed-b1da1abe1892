<?php

namespace App\Livewire\CallQuality;

use App\Models\CallEvaluation;
use App\Models\Campaign;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class EvaluationIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'evaluation_date';
    public $sortDirection = 'desc';
    public $selectedCampaign = '';
    public $selectedAgent = '';
    public $selectedEvaluator = '';
    public $selectedStatus = '';
    public $dateRange = '';
    public $startDate = '';
    public $endDate = '';
    public $showFilters = false;
    public $selectedEvaluations = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'selectedCampaign' => ['except' => ''],
        'selectedAgent' => ['except' => ''],
        'selectedEvaluator' => ['except' => ''],
        'selectedStatus' => ['except' => ''],
        'dateRange' => ['except' => ''],
        'sortField' => ['except' => 'evaluation_date'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount()
    {
        $this->setDefaultDateRange();
    }

    public function setDefaultDateRange()
    {
        $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
        $this->endDate = Carbon::now()->format('Y-m-d');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function updatingSelectedCampaign()
    {
        $this->resetPage();
    }

    public function updatingSelectedAgent()
    {
        $this->resetPage();
    }

    public function updatingSelectedEvaluator()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();
        
        switch ($this->dateRange) {
            case 'today':
                $this->startDate = Carbon::today()->format('Y-m-d');
                $this->endDate = Carbon::today()->format('Y-m-d');
                break;
            case 'yesterday':
                $this->startDate = Carbon::yesterday()->format('Y-m-d');
                $this->endDate = Carbon::yesterday()->format('Y-m-d');
                break;
            case 'this_week':
                $this->startDate = Carbon::now()->startOfWeek()->format('Y-m-d');
                $this->endDate = Carbon::now()->endOfWeek()->format('Y-m-d');
                break;
            case 'last_week':
                $this->startDate = Carbon::now()->subWeek()->startOfWeek()->format('Y-m-d');
                $this->endDate = Carbon::now()->subWeek()->endOfWeek()->format('Y-m-d');
                break;
            case 'this_month':
                $this->startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                $this->endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_month':
                $this->startDate = Carbon::now()->subMonth()->startOfMonth()->format('Y-m-d');
                $this->endDate = Carbon::now()->subMonth()->endOfMonth()->format('Y-m-d');
                break;
            case 'last_30_days':
                $this->startDate = Carbon::now()->subDays(30)->format('Y-m-d');
                $this->endDate = Carbon::now()->format('Y-m-d');
                break;
            case 'last_90_days':
                $this->startDate = Carbon::now()->subDays(90)->format('Y-m-d');
                $this->endDate = Carbon::now()->format('Y-m-d');
                break;
            case 'custom':
                // Do nothing, let the user select custom dates
                break;
            default:
                $this->setDefaultDateRange();
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->selectedCampaign = '';
        $this->selectedAgent = '';
        $this->selectedEvaluator = '';
        $this->selectedStatus = '';
        $this->dateRange = '';
        $this->setDefaultDateRange();
        $this->resetPage();
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedEvaluations = $this->getEvaluations()->pluck('id')->map(function ($id) {
                return (string) $id;
            })->toArray();
        } else {
            $this->selectedEvaluations = [];
        }
    }

    public function updatedSelectedEvaluations()
    {
        $this->selectAll = false;
    }

    public function getEvaluations()
    {
        return CallEvaluation::with(['call.agent', 'call.campaign', 'evaluator'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->whereHas('call.agent', function ($q) {
                        $q->where('first_name', 'like', '%' . $this->search . '%')
                          ->orWhere('last_name', 'like', '%' . $this->search . '%');
                    })
                    ->orWhereHas('evaluator', function ($q) {
                        $q->where('first_name', 'like', '%' . $this->search . '%')
                          ->orWhere('last_name', 'like', '%' . $this->search . '%');
                    })
                    ->orWhereHas('call.campaign', function ($q) {
                        $q->where('name', 'like', '%' . $this->search . '%');
                    });
                });
            })
            ->when($this->selectedCampaign, function ($query) {
                $query->whereHas('call', function ($q) {
                    $q->where('campaign_id', $this->selectedCampaign);
                });
            })
            ->when($this->selectedAgent, function ($query) {
                $query->whereHas('call', function ($q) {
                    $q->where('user_id', $this->selectedAgent);
                });
            })
            ->when($this->selectedEvaluator, function ($query) {
                $query->where('evaluator_id', $this->selectedEvaluator);
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->startDate && $this->endDate, function ($query) {
                $query->whereBetween('evaluation_date', [
                    Carbon::parse($this->startDate)->startOfDay(),
                    Carbon::parse($this->endDate)->endOfDay()
                ]);
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function render()
    {
        $evaluations = $this->getEvaluations()->paginate($this->perPage);
        $campaigns = Campaign::orderBy('name')->get();
        $agents = User::where('role_id', 6)->orderBy('first_name')->get();
        $evaluators = User::whereIn('role_id', [1, 2, 3, 4])->orderBy('first_name')->get();
        
        $evaluationStatuses = [
            'draft' => 'Draft',
            'completed' => 'Completed',
            'reviewed' => 'Reviewed',
        ];
        
        $dateRanges = [
            'today' => 'Today',
            'yesterday' => 'Yesterday',
            'this_week' => 'This Week',
            'last_week' => 'Last Week',
            'this_month' => 'This Month',
            'last_month' => 'Last Month',
            'last_30_days' => 'Last 30 Days',
            'last_90_days' => 'Last 90 Days',
            'custom' => 'Custom Range',
        ];
        
        return view('livewire.call-quality.evaluation-index', [
            'evaluations' => $evaluations,
            'campaigns' => $campaigns,
            'agents' => $agents,
            'evaluators' => $evaluators,
            'evaluationStatuses' => $evaluationStatuses,
            'dateRanges' => $dateRanges,
        ]);
    }
}
