<?php

namespace App\Livewire\CallQuality;

use App\Models\Call;
use App\Models\CallEvaluation;
use App\Models\Campaign;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;

class Dashboard extends Component
{
    public $period = 'week';
    public $startDate;
    public $endDate;
    public $selectedCampaignId = null;
    public $callStats = [];
    public $evaluationStats = [];
    public $topPerformers = [];
    public $improvementAreas = [];
    public $recentEvaluations = [];
    public $campaignPerformance = [];
    public $qualityTrend = [];

    protected $queryString = [
        'period' => ['except' => 'week'],
        'selectedCampaignId' => ['except' => null],
    ];

    public function mount()
    {
        $this->setDateRange();
        $this->loadDashboardData();
    }

    public function setDateRange()
    {
        $now = Carbon::now();

        switch ($this->period) {
            case 'day':
                $this->startDate = $now->copy()->startOfDay();
                $this->endDate = $now->copy()->endOfDay();
                break;
            case 'week':
                $this->startDate = $now->copy()->startOfWeek();
                $this->endDate = $now->copy()->endOfWeek();
                break;
            case 'month':
                $this->startDate = $now->copy()->startOfMonth();
                $this->endDate = $now->copy()->endOfMonth();
                break;
            case 'quarter':
                $this->startDate = $now->copy()->startOfQuarter();
                $this->endDate = $now->copy()->endOfQuarter();
                break;
            case 'year':
                $this->startDate = $now->copy()->startOfYear();
                $this->endDate = $now->copy()->endOfYear();
                break;
            default:
                $this->startDate = $now->copy()->startOfWeek();
                $this->endDate = $now->copy()->endOfWeek();
        }
    }

    public function updatedPeriod()
    {
        $this->setDateRange();
        $this->loadDashboardData();
    }

    public function updatedSelectedCampaignId()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $this->loadCallStats();
        $this->loadEvaluationStats();
        $this->loadTopPerformers();
        $this->loadImprovementAreas();
        $this->loadRecentEvaluations();
        $this->loadCampaignPerformance();
        $this->loadQualityTrend();
    }

    protected function loadCallStats()
    {
        $callsQuery = Call::whereBetween('call_time', [$this->startDate, $this->endDate]);
        
        if ($this->selectedCampaignId) {
            $callsQuery->where('campaign_id', $this->selectedCampaignId);
        }
        
        $totalCalls = $callsQuery->count();
        $inboundCalls = $callsQuery->clone()->where('direction', 'inbound')->count();
        $outboundCalls = $callsQuery->clone()->where('direction', 'outbound')->count();
        $evaluatedCalls = $callsQuery->clone()->whereHas('evaluations')->count();
        $evaluationRate = $totalCalls > 0 ? round(($evaluatedCalls / $totalCalls) * 100, 1) : 0;
        
        $this->callStats = [
            'total' => $totalCalls,
            'inbound' => $inboundCalls,
            'outbound' => $outboundCalls,
            'evaluated' => $evaluatedCalls,
            'evaluation_rate' => $evaluationRate,
        ];
    }

    protected function loadEvaluationStats()
    {
        $evaluationsQuery = CallEvaluation::whereBetween('evaluation_date', [$this->startDate, $this->endDate]);
        
        if ($this->selectedCampaignId) {
            $evaluationsQuery->whereHas('call', function ($query) {
                $query->where('campaign_id', $this->selectedCampaignId);
            });
        }
        
        $evaluations = $evaluationsQuery->get();
        
        $totalEvaluations = $evaluations->count();
        $averageScore = $evaluations->avg('overall_score') ?? 0;
        
        // Calculate score distribution
        $scoreDistribution = [
            'exceptional' => 0,
            'exceeds' => 0,
            'meets' => 0,
            'needs_improvement' => 0,
            'unsatisfactory' => 0,
        ];
        
        foreach ($evaluations as $evaluation) {
            if ($evaluation->overall_score >= 4.5) {
                $scoreDistribution['exceptional']++;
            } elseif ($evaluation->overall_score >= 3.5) {
                $scoreDistribution['exceeds']++;
            } elseif ($evaluation->overall_score >= 2.5) {
                $scoreDistribution['meets']++;
            } elseif ($evaluation->overall_score >= 1.5) {
                $scoreDistribution['needs_improvement']++;
            } else {
                $scoreDistribution['unsatisfactory']++;
            }
        }
        
        // Convert to percentages
        if ($totalEvaluations > 0) {
            foreach ($scoreDistribution as $key => $value) {
                $scoreDistribution[$key] = round(($value / $totalEvaluations) * 100, 1);
            }
        }
        
        $this->evaluationStats = [
            'total' => $totalEvaluations,
            'average_score' => round($averageScore, 2),
            'score_distribution' => $scoreDistribution,
        ];
    }

    protected function loadTopPerformers()
    {
        $query = User::where('role_id', 6) // Agent role
            ->whereHas('calls', function ($query) {
                $query->whereBetween('call_time', [$this->startDate, $this->endDate]);
                if ($this->selectedCampaignId) {
                    $query->where('campaign_id', $this->selectedCampaignId);
                }
                $query->whereHas('evaluations');
            })
            ->withCount(['calls' => function ($query) {
                $query->whereBetween('call_time', [$this->startDate, $this->endDate]);
                if ($this->selectedCampaignId) {
                    $query->where('campaign_id', $this->selectedCampaignId);
                }
            }])
            ->with(['callEvaluations' => function ($query) {
                $query->whereBetween('evaluation_date', [$this->startDate, $this->endDate]);
                if ($this->selectedCampaignId) {
                    $query->whereHas('call', function ($q) {
                        $q->where('campaign_id', $this->selectedCampaignId);
                    });
                }
            }]);
        
        $agents = $query->get();
        
        $this->topPerformers = $agents->map(function ($agent) {
            $evaluations = $agent->callEvaluations;
            $averageScore = $evaluations->avg('overall_score') ?? 0;
            
            return [
                'id' => $agent->id,
                'name' => $agent->first_name . ' ' . $agent->last_name,
                'calls_count' => $agent->calls_count,
                'evaluations_count' => $evaluations->count(),
                'average_score' => round($averageScore, 2),
            ];
        })->sortByDesc('average_score')->take(5)->values()->toArray();
    }

    protected function loadImprovementAreas()
    {
        // This would analyze evaluation scores to identify common areas for improvement
        // For now, we'll use a simplified approach
        $scoresQuery = \App\Models\EvaluationScore::whereHas('evaluation', function ($query) {
            $query->whereBetween('evaluation_date', [$this->startDate, $this->endDate]);
            
            if ($this->selectedCampaignId) {
                $query->whereHas('call', function ($q) {
                    $q->where('campaign_id', $this->selectedCampaignId);
                });
            }
        })->with('criteria');
        
        $scores = $scoresQuery->get();
        
        // Group by criteria and calculate average score
        $criteriaScores = [];
        foreach ($scores as $score) {
            $criteriaId = $score->criteria->id;
            $criteriaName = $score->criteria->name;
            $criteriaCategory = $score->criteria->category;
            
            if (!isset($criteriaScores[$criteriaId])) {
                $criteriaScores[$criteriaId] = [
                    'id' => $criteriaId,
                    'name' => $criteriaName,
                    'category' => $criteriaCategory,
                    'scores' => [],
                ];
            }
            
            $criteriaScores[$criteriaId]['scores'][] = $score->score;
        }
        
        // Calculate average scores and find lowest scoring criteria
        foreach ($criteriaScores as &$criteria) {
            $criteria['average_score'] = !empty($criteria['scores']) ? 
                round(array_sum($criteria['scores']) / count($criteria['scores']), 2) : 0;
            unset($criteria['scores']);
        }
        
        // Sort by average score (ascending) and take the 5 lowest
        usort($criteriaScores, function ($a, $b) {
            return $a['average_score'] <=> $b['average_score'];
        });
        
        $this->improvementAreas = array_slice($criteriaScores, 0, 5);
    }

    protected function loadRecentEvaluations()
    {
        $query = CallEvaluation::with(['call.agent', 'evaluator'])
            ->whereBetween('evaluation_date', [$this->startDate, $this->endDate])
            ->latest('evaluation_date');
        
        if ($this->selectedCampaignId) {
            $query->whereHas('call', function ($q) {
                $q->where('campaign_id', $this->selectedCampaignId);
            });
        }
        
        $this->recentEvaluations = $query->take(5)->get()->map(function ($evaluation) {
            return [
                'id' => $evaluation->id,
                'call_id' => $evaluation->call_id,
                'agent_name' => $evaluation->call->agent->first_name . ' ' . $evaluation->call->agent->last_name,
                'evaluator_name' => $evaluation->evaluator->first_name . ' ' . $evaluation->evaluator->last_name,
                'date' => $evaluation->evaluation_date->format('M d, Y'),
                'score' => $evaluation->overall_score,
                'rating' => $evaluation->getRatingDescription(),
            ];
        })->toArray();
    }

    protected function loadCampaignPerformance()
    {
        $campaigns = Campaign::whereHas('calls', function ($query) {
            $query->whereBetween('call_time', [$this->startDate, $this->endDate]);
            $query->whereHas('evaluations');
        })->get();
        
        $this->campaignPerformance = $campaigns->map(function ($campaign) {
            $evaluations = CallEvaluation::whereHas('call', function ($query) use ($campaign) {
                $query->where('campaign_id', $campaign->id);
                $query->whereBetween('call_time', [$this->startDate, $this->endDate]);
            })->get();
            
            $averageScore = $evaluations->avg('overall_score') ?? 0;
            $callsCount = $campaign->calls()
                ->whereBetween('call_time', [$this->startDate, $this->endDate])
                ->count();
            $evaluatedCallsCount = $campaign->calls()
                ->whereBetween('call_time', [$this->startDate, $this->endDate])
                ->whereHas('evaluations')
                ->count();
            
            return [
                'id' => $campaign->id,
                'name' => $campaign->name,
                'calls_count' => $callsCount,
                'evaluations_count' => $evaluatedCallsCount,
                'average_score' => round($averageScore, 2),
                'evaluation_rate' => $callsCount > 0 ? round(($evaluatedCallsCount / $callsCount) * 100, 1) : 0,
            ];
        })->sortByDesc('average_score')->values()->toArray();
    }

    protected function loadQualityTrend()
    {
        // Generate date range based on period
        $dateFormat = 'Y-m-d';
        $interval = 'day';
        $dateLabels = [];
        
        switch ($this->period) {
            case 'day':
                $interval = 'hour';
                $dateFormat = 'H:i';
                $period = new \DatePeriod(
                    $this->startDate,
                    new \DateInterval('PT1H'),
                    $this->endDate
                );
                break;
            case 'week':
                $period = new \DatePeriod(
                    $this->startDate,
                    new \DateInterval('P1D'),
                    $this->endDate
                );
                break;
            case 'month':
                $period = new \DatePeriod(
                    $this->startDate,
                    new \DateInterval('P1D'),
                    $this->endDate
                );
                break;
            case 'quarter':
                $interval = 'week';
                $period = new \DatePeriod(
                    $this->startDate,
                    new \DateInterval('P1W'),
                    $this->endDate
                );
                break;
            case 'year':
                $interval = 'month';
                $dateFormat = 'M Y';
                $period = new \DatePeriod(
                    $this->startDate,
                    new \DateInterval('P1M'),
                    $this->endDate
                );
                break;
            default:
                $period = new \DatePeriod(
                    $this->startDate,
                    new \DateInterval('P1D'),
                    $this->endDate
                );
        }
        
        foreach ($period as $date) {
            $dateLabels[] = $date->format($dateFormat);
        }
        
        // Get evaluations grouped by date
        $evaluations = CallEvaluation::whereBetween('evaluation_date', [$this->startDate, $this->endDate]);
        
        if ($this->selectedCampaignId) {
            $evaluations->whereHas('call', function ($query) {
                $query->where('campaign_id', $this->selectedCampaignId);
            });
        }
        
        $evaluations = $evaluations->get();
        
        // Initialize scores array with zeros
        $scores = array_fill(0, count($dateLabels), 0);
        $counts = array_fill(0, count($dateLabels), 0);
        
        // Group evaluations by date and calculate average scores
        foreach ($evaluations as $evaluation) {
            $dateKey = $evaluation->evaluation_date->format($dateFormat);
            $index = array_search($dateKey, $dateLabels);
            
            if ($index !== false) {
                $scores[$index] += $evaluation->overall_score;
                $counts[$index]++;
            }
        }
        
        // Calculate average scores
        for ($i = 0; $i < count($scores); $i++) {
            if ($counts[$i] > 0) {
                $scores[$i] = round($scores[$i] / $counts[$i], 2);
            }
        }
        
        $this->qualityTrend = [
            'labels' => $dateLabels,
            'scores' => $scores,
        ];
    }

    public function render()
    {
        $campaigns = Campaign::where('status', 'active')->get();
        
        return view('livewire.call-quality.dashboard', [
            'campaigns' => $campaigns,
        ]);
    }
}
