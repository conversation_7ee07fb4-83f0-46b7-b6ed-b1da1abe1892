<?php

namespace App\Livewire\CallQuality;

use App\Models\Campaign;
use App\Models\EvaluationCriteria;
use Livewire\Component;
use Livewire\WithPagination;

class CriteriaIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $selectedCampaign = '';
    public $selectedCategory = '';
    public $showInactive = false;
    public $showFilters = false;
    public $showCreateModal = false;
    public $showEditModal = false;
    public $selectedCriteria = null;
    
    // Form fields
    public $criteriaId;
    public $name;
    public $category;
    public $description;
    public $weight = 1;
    public $isActive = true;
    public $isRequired = false;
    public $campaignId;

    protected $queryString = [
        'search' => ['except' => ''],
        'selectedCampaign' => ['except' => ''],
        'selectedCategory' => ['except' => ''],
        'showInactive' => ['except' => false],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    protected $rules = [
        'name' => 'required|string|max:255',
        'category' => 'required|string|max:255',
        'description' => 'nullable|string',
        'weight' => 'required|integer|min:1|max:10',
        'isActive' => 'boolean',
        'isRequired' => 'boolean',
        'campaignId' => 'nullable|exists:campaigns,id',
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function updatingSelectedCampaign()
    {
        $this->resetPage();
    }

    public function updatingSelectedCategory()
    {
        $this->resetPage();
    }

    public function updatingShowInactive()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function resetFilters()
    {
        $this->search = '';
        $this->selectedCampaign = '';
        $this->selectedCategory = '';
        $this->showInactive = false;
        $this->resetPage();
    }

    public function openCreateModal()
    {
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function closeCreateModal()
    {
        $this->showCreateModal = false;
        $this->resetForm();
    }

    public function openEditModal($criteriaId)
    {
        $this->resetForm();
        $this->criteriaId = $criteriaId;
        $this->selectedCriteria = EvaluationCriteria::findOrFail($criteriaId);
        
        $this->name = $this->selectedCriteria->name;
        $this->category = $this->selectedCriteria->category;
        $this->description = $this->selectedCriteria->description;
        $this->weight = $this->selectedCriteria->weight;
        $this->isActive = $this->selectedCriteria->is_active;
        $this->isRequired = $this->selectedCriteria->is_required;
        $this->campaignId = $this->selectedCriteria->campaign_id;
        
        $this->showEditModal = true;
    }

    public function closeEditModal()
    {
        $this->showEditModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->criteriaId = null;
        $this->name = '';
        $this->category = '';
        $this->description = '';
        $this->weight = 1;
        $this->isActive = true;
        $this->isRequired = false;
        $this->campaignId = null;
        $this->selectedCriteria = null;
    }

    public function createCriteria()
    {
        $this->validate();
        
        EvaluationCriteria::create([
            'name' => $this->name,
            'category' => $this->category,
            'description' => $this->description,
            'weight' => $this->weight,
            'is_active' => $this->isActive,
            'is_required' => $this->isRequired,
            'campaign_id' => $this->campaignId,
        ]);
        
        $this->closeCreateModal();
        session()->flash('message', 'Evaluation criteria created successfully.');
    }

    public function updateCriteria()
    {
        $this->validate();
        
        $criteria = EvaluationCriteria::findOrFail($this->criteriaId);
        $criteria->update([
            'name' => $this->name,
            'category' => $this->category,
            'description' => $this->description,
            'weight' => $this->weight,
            'is_active' => $this->isActive,
            'is_required' => $this->isRequired,
            'campaign_id' => $this->campaignId,
        ]);
        
        $this->closeEditModal();
        session()->flash('message', 'Evaluation criteria updated successfully.');
    }

    public function toggleActive($criteriaId)
    {
        $criteria = EvaluationCriteria::findOrFail($criteriaId);
        $criteria->is_active = !$criteria->is_active;
        $criteria->save();
        
        session()->flash('message', 'Criteria status updated successfully.');
    }

    public function render()
    {
        $criteria = EvaluationCriteria::when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('category', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->when($this->selectedCampaign, function ($query) {
                if ($this->selectedCampaign === 'global') {
                    $query->whereNull('campaign_id');
                } else {
                    $query->where('campaign_id', $this->selectedCampaign);
                }
            })
            ->when($this->selectedCategory, function ($query) {
                $query->where('category', $this->selectedCategory);
            })
            ->when(!$this->showInactive, function ($query) {
                $query->where('is_active', true);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
        
        $campaigns = Campaign::orderBy('name')->get();
        $categories = EvaluationCriteria::select('category')->distinct()->orderBy('category')->pluck('category');
        
        return view('livewire.call-quality.criteria-index', [
            'criteria' => $criteria,
            'campaigns' => $campaigns,
            'categories' => $categories,
        ]);
    }
}
