<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\WithPagination;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class UserRolePermission extends Component
{
    use WithPagination;

    public $tab = 'roles';
    public $selectedTab = 'roles';
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $selectAll = false;
    public $selectedItems = [];

    protected $listeners = ['role-created' => 'refresh', 'role-updated' => 'refresh', 'permission-created' => 'refresh', 'permission-updated' => 'refresh'];

    public function mount()
    {
        $this->fetchData();
    }

    public function sortBy($field)
    {
        $this->sortField = $field;
        $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->fetchData();
    }

    public function fetchData()
    {
        $query = $this->selectedTab === 'roles' 
            ? Role::query()
            : Permission::query();

        $query = $query
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection);

        return $query->paginate($this->perPage);
    }

    public function toggleSelection($id)
    {
        if (in_array($id, $this->selectedItems)) {
            $this->selectedItems = array_diff($this->selectedItems, [$id]);
        } else {
            $this->selectedItems[] = $id;
        }

        // Update selectAll state based on visible items
        $visibleItems = $this->items->items();
        $visibleIds = array_column($visibleItems, 'id');
        $this->selectAll = count(array_intersect($this->selectedItems, $visibleIds)) === count($visibleIds);
    }

    public function selectTab($tab)
    {
        $this->selectedTab = $tab;
        $this->selectedItems = [];
        $this->selectAll = false;
        $this->fetchData();
    }

    public function selectAll($selectAll)
    {
        $this->selectAll = $selectAll;
        
        if ($selectAll) {
            $visibleItems = $this->items->items();
            $this->selectedItems = array_column($visibleItems, 'id');
        } else {
            $this->selectedItems = [];
        }
    }

    public function deleteSelected()
    {
        $this->confirm('Delete Selected', [
            'text' => 'Are you sure you want to delete the selected ' . ($this->tab === 'roles' ? 'roles' : 'permissions') . '?',
            'title' => 'Delete Selected',
            'icon' => 'warning',
            'confirmButtonText' => 'Yes, delete them',
            'showCancelButton' => true,
        ])->then(function ($result) {
            if ($result) {
                $model = $this->tab === 'roles' ? Role::class : Permission::class;
                $model::whereIn('id', $this->selectedItems)->delete();
                
                $this->selectedItems = [];
                $this->selectAll = false;
                
                $this->dispatch('alert', type: 'success', message: 'Selected items deleted successfully.');
            }
        });
    }

    public function navigateTo($url)
    {
        return redirect($url);
    }

    public function render()
    {
        return view('livewire.users.user-role-permission', [
            'tab' => $this->tab,
            'items' => $this->fetchData(),
            'selectedTab' => $this->selectedTab,
        ]);
    }
}
