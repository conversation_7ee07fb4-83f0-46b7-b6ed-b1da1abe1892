<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Log;

class UserPermissionDelete extends Component
{
    public Permission $permission;
    public bool $forceDelete = false;
    public $name;

    public function mount(Permission $permission)
    {
        $this->permission = $permission;
        $this->name = $permission->name;
    }

    public function toggleForceDelete()
    {
        $this->forceDelete = !$this->forceDelete;
    }

    #[On('destroy-permission')]
    public function destroyPermission()
    {
        try {
            Log::info('Attempting to delete permission: ' . $this->permission->id);
            
            // Check if permission is protected
            if ($this->permission->is_protected ?? false) {
                throw new \Exception('This is a protected permission and cannot be deleted.');
            }

            // Check if permission is assigned to roles
            $rolesCount = $this->permission->roles()->count();
            if ($rolesCount > 0 && !$this->forceDelete) {
                throw new \Exception('This permission is assigned to roles. Enable force delete to remove it anyway.');
            }

            // If force delete, detach from all roles first
            if ($this->forceDelete) {
                $this->permission->roles()->detach();
            }

            // Delete the permission
            $this->permission->delete();

            Log::info('Permission deleted successfully: ' . $this->permission->id);
            session()->flash('message', [
                'type' => 'success',
                'text' => 'Permission deleted successfully!'
            ]);

            // Redirect to permissions index
            return $this->redirect(route('users.permissions.index'), navigate: true);
        } catch (\Exception $e) {
            Log::error('Error deleting permission: ' . $e->getMessage());
            $this->addError('delete_error', 'Error deleting permission: ' . $e->getMessage());
            return null;
        }
    }

    public function render()
    {
        return view('livewire.users.user-permission-delete', [
            'rolesCount' => $this->permission->roles()->count(),
            'usersCount' => $this->permission->users()->count()
        ]);
    }
}
