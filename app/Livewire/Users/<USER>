<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class UserPermissionEdit extends Component
{
    use WithPagination;

    public Permission $permission;
    public $roles;
    public $selectedRoles = [];
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';

    protected $queryString = [
        'search' => ['except' => ''],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    protected $listeners = ['refreshComponent' => '$refresh'];

    public function mount(Permission $permission)
    {
        $this->authorize('update', $permission);
        $this->permission = $permission;
        $this->selectedRoles = $permission->roles->pluck('id')->toArray();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    public function toggleRole($roleId, $checked)
    {
        if ($checked && !in_array($roleId, $this->selectedRoles)) {
            $this->selectedRoles[] = $roleId;
        } else {
            $this->selectedRoles = array_diff($this->selectedRoles, [$roleId]);
        }
    }

    public function save()
    {
        $this->validate([
            'selectedRoles' => 'required|array|min:1',
            'selectedRoles.*' => 'exists:roles,id',
        ], [
            'selectedRoles.required' => 'Please select at least one role.',
            'selectedRoles.*.exists' => 'One or more selected roles are invalid.',
        ]);

        try {
            $this->permission->syncRoles($this->selectedRoles);
            
            session()->flash('message', [
                'type' => 'success',
                'text' => 'Permission roles updated successfully!'
            ]);
            
            $this->redirect(route('users.permissions.show', $this->permission), navigate: true);
        } catch (\Exception $e) {
            $this->addError('error', 'Error updating permission roles: ' . $e->getMessage());
        }
    }

    public function render()
    {
        $query = Role::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy($this->sortField, $this->sortDirection);

        $roles = $query->paginate($this->perPage);

        return view('livewire.users.user-permission-edit', [
            'roles' => $roles,
            'assignedRoles' => $this->permission->roles->pluck('id')->toArray(),
        ]);
    }
}
