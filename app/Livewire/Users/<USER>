<?php

namespace App\Livewire\Users;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;

class UserDelete extends Component
{
    public User $user;
    public $name;
    public bool $forceDelete = false;

    public function toggleForceDelete()
    {
        $this->forceDelete = !$this->forceDelete;
    }

    #[On('destroy-user')]
    public function destroyUser()
    {
        try {
            // Log the user we're trying to delete
            Log::info('Attempting to delete user: ' . $this->user->id);

            // Get the profile picture media model
            $profilePicture = $this->user->profilePicture()->first();

            // Check if a profile picture exists
            if ($profilePicture) {
                Log::info('Deleting profile picture');
                Storage::disk('public')->delete($profilePicture->file_path);
                $profilePicture->delete();
            }

            // Delete any other related media
            Log::info('Deleting media');
            $this->user->media()->delete();

            // Detach relationships that might prevent deletion if force delete is enabled
            if ($this->forceDelete) {
                Log::info('Force delete enabled, detaching relationships');
                $this->detachRelationships();
            }

            // Try to delete the user directly
            try {
                Log::info('Attempting to delete user directly: ' . $this->user->id);
                $this->user->delete();
            } catch (\Exception $innerException) {
                // If direct deletion fails, try using raw SQL to bypass foreign key constraints
                Log::warning('Direct deletion failed, trying raw SQL: ' . $innerException->getMessage());

                if (strpos($innerException->getMessage(), 'no such table: main.evaluation_criterias') !== false) {
                    Log::info('Detected missing evaluation_criterias table, using raw SQL to delete user');
                    DB::statement('PRAGMA foreign_keys = OFF;');
                    DB::delete('DELETE FROM users WHERE id = ?', [$this->user->id]);
                    DB::statement('PRAGMA foreign_keys = ON;');
                } else {
                    // If it's a different error, rethrow it
                    throw $innerException;
                }
            }

            Log::info('User deleted successfully: ' . $this->user->id);
            session()->flash('message', 'User deleted successfully!');

            // Redirect to the users index page
            return $this->redirect(route('users.index'), navigate: true);
        } catch (\Exception $e) {
            Log::error('Error deleting user: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            session()->flash('error', 'Error deleting user: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Detach relationships that might prevent deletion
     */
    private function detachRelationships()
    {
        // Detach team memberships
        if (method_exists($this->user, 'teams')) {
            Log::info('Detaching team memberships');
            $this->user->teams()->detach();
        }

        // Detach campaign memberships
        if (method_exists($this->user, 'campaigns')) {
            Log::info('Detaching campaign memberships');
            $this->user->campaigns()->detach();
        }

        // Detach site memberships
        if (method_exists($this->user, 'sites')) {
            Log::info('Detaching site memberships');
            $this->user->sites()->detach();
        }

        // Detach skills
        if (method_exists($this->user, 'skills')) {
            Log::info('Detaching skills');
            $this->user->skills()->detach();
        }

        // Detach certifications
        if (method_exists($this->user, 'certifications')) {
            Log::info('Detaching certifications');
            $this->user->certifications()->detach();
        }

        // Clear notifications
        if (method_exists($this->user, 'notifications')) {
            Log::info('Clearing notifications');
            $this->user->notifications()->delete();
        }

        // Set manager_id to null for direct reports
        if (method_exists($this->user, 'directReports') && $this->user->directReports()->count() > 0) {
            Log::info('Setting manager_id to null for direct reports');
            $this->user->directReports()->update(['manager_id' => null]);
        }
    }

    public function delete()
    {
        // Just call destroyUser for consistency
        return $this->destroyUser();
    }

    public function mount(User $user)
    {
        $this->name = $user->name;
    }

    public function render()
    {
        return view('livewire.users.user-delete');
    }
}
