<?php

namespace App\Livewire\Users;

use App\Models\Certification;
use App\Models\User;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;

class UserCertifications extends Component
{
    use WithPagination, WithFileUploads;
    
    public User $user;
    public $search = '';
    public $status = '';
    public $perPage = 10;
    
    // For adding new certification
    public $selectedCertification = null;
    public $certificateNumber = '';
    public $issuedAt = null;
    public $expiresAt = null;
    public $certificationNotes = '';
    public $documentFile = null;
    
    // For editing certification
    public $editingCertificationId = null;
    public $editCertificateNumber = '';
    public $editIssuedAt = null;
    public $editExpiresAt = null;
    public $editCertificationNotes = '';
    public $editDocumentFile = null;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'perPage' => ['except' => 10],
    ];
    
    protected $rules = [
        'selectedCertification' => 'required|exists:certifications,id',
        'certificateNumber' => 'nullable|string|max:255',
        'issuedAt' => 'required|date',
        'expiresAt' => 'nullable|date|after:issuedAt',
        'certificationNotes' => 'nullable|string',
        'documentFile' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png',
        
        'editCertificateNumber' => 'nullable|string|max:255',
        'editIssuedAt' => 'required|date',
        'editExpiresAt' => 'nullable|date|after:editIssuedAt',
        'editCertificationNotes' => 'nullable|string',
        'editDocumentFile' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png',
    ];
    
    public function mount(User $user)
    {
        $this->user = $user;
        $this->issuedAt = now()->format('Y-m-d');
    }
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingStatus()
    {
        $this->resetPage();
    }
    
    public function addCertification()
    {
        $this->validate([
            'selectedCertification' => 'required|exists:certifications,id',
            'certificateNumber' => 'nullable|string|max:255',
            'issuedAt' => 'required|date',
            'expiresAt' => 'nullable|date|after:issuedAt',
            'certificationNotes' => 'nullable|string',
            'documentFile' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png',
        ]);
        
        // Check if user already has this certification
        if ($this->user->certifications()->where('certification_id', $this->selectedCertification)->exists()) {
            session()->flash('error', 'User already has this certification.');
            return;
        }
        
        $documentPath = null;
        if ($this->documentFile) {
            $documentPath = $this->documentFile->store('certifications', 'public');
        }
        
        $certification = Certification::find($this->selectedCertification);
        
        $this->user->certifications()->attach($this->selectedCertification, [
            'certificate_number' => $this->certificateNumber,
            'issued_at' => $this->issuedAt,
            'expires_at' => $this->expiresAt ?: ($certification->validity_period ? now()->addMonths($certification->validity_period)->format('Y-m-d') : null),
            'status' => 'active',
            'notes' => $this->certificationNotes,
            'document_path' => $documentPath,
            'verified_by' => auth()->id(),
        ]);
        
        session()->flash('message', 'Certification added successfully.');
        
        // Reset form
        $this->selectedCertification = null;
        $this->certificateNumber = '';
        $this->issuedAt = now()->format('Y-m-d');
        $this->expiresAt = null;
        $this->certificationNotes = '';
        $this->documentFile = null;
    }
    
    public function startEditing($certificationId)
    {
        $this->editingCertificationId = $certificationId;
        
        $userCertification = $this->user->certifications()->where('certification_id', $certificationId)->first()->pivot;
        
        $this->editCertificateNumber = $userCertification->certificate_number;
        $this->editIssuedAt = $userCertification->issued_at ? $userCertification->issued_at->format('Y-m-d') : null;
        $this->editExpiresAt = $userCertification->expires_at ? $userCertification->expires_at->format('Y-m-d') : null;
        $this->editCertificationNotes = $userCertification->notes;
    }
    
    public function cancelEditing()
    {
        $this->editingCertificationId = null;
        $this->editCertificateNumber = '';
        $this->editIssuedAt = null;
        $this->editExpiresAt = null;
        $this->editCertificationNotes = '';
        $this->editDocumentFile = null;
    }
    
    public function updateCertification()
    {
        $this->validate([
            'editCertificateNumber' => 'nullable|string|max:255',
            'editIssuedAt' => 'required|date',
            'editExpiresAt' => 'nullable|date|after:editIssuedAt',
            'editCertificationNotes' => 'nullable|string',
            'editDocumentFile' => 'nullable|file|max:10240|mimes:pdf,jpg,jpeg,png',
        ]);
        
        $userCertification = $this->user->certifications()->where('certification_id', $this->editingCertificationId)->first()->pivot;
        
        $documentPath = $userCertification->document_path;
        if ($this->editDocumentFile) {
            // Delete old file if exists
            if ($documentPath && Storage::disk('public')->exists($documentPath)) {
                Storage::disk('public')->delete($documentPath);
            }
            
            $documentPath = $this->editDocumentFile->store('certifications', 'public');
        }
        
        // Determine status based on expiration date
        $status = 'active';
        if ($this->editExpiresAt && now()->greaterThan($this->editExpiresAt)) {
            $status = 'expired';
        }
        
        $this->user->certifications()->updateExistingPivot($this->editingCertificationId, [
            'certificate_number' => $this->editCertificateNumber,
            'issued_at' => $this->editIssuedAt,
            'expires_at' => $this->editExpiresAt,
            'status' => $status,
            'notes' => $this->editCertificationNotes,
            'document_path' => $documentPath,
            'verified_by' => auth()->id(),
        ]);
        
        session()->flash('message', 'Certification updated successfully.');
        
        $this->cancelEditing();
    }
    
    public function removeCertification($certificationId)
    {
        $userCertification = $this->user->certifications()->where('certification_id', $certificationId)->first()->pivot;
        
        // Delete document file if exists
        if ($userCertification->document_path && Storage::disk('public')->exists($userCertification->document_path)) {
            Storage::disk('public')->delete($userCertification->document_path);
        }
        
        $this->user->certifications()->detach($certificationId);
        
        session()->flash('message', 'Certification removed successfully.');
    }
    
    public function render()
    {
        $userCertifications = $this->user->certifications()
            ->when($this->search, function ($query) {
                return $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%')
                    ->orWhere('issuer', 'like', '%' . $this->search . '%');
            })
            ->when($this->status, function ($query) {
                return $query->wherePivot('status', $this->status);
            })
            ->paginate($this->perPage);
            
        $availableCertifications = Certification::where('is_active', true)
            ->whereNotIn('id', $this->user->certifications->pluck('id'))
            ->get();
            
        return view('livewire.users.user-certifications', [
            'userCertifications' => $userCertifications,
            'availableCertifications' => $availableCertifications,
        ]);
    }
}
