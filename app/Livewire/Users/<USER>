<?php

namespace App\Livewire\Users;

use App\Models\Team;
use App\Models\User;
use Livewire\Component;

class TeamMemberManagement extends Component
{
    public $teamId;
    public $team;
    public $teamRole = 'member';
    public $teamMemberIds = [];

    public function mount($teamId)
    {
        $this->teamId = $teamId;
        $this->team = Team::with('members')->findOrFail($teamId);
        $this->teamMemberIds = $this->team->members->pluck('id')->toArray();
    }

    public function saveTeamMembers()
    {
        // Get current members
        $currentMembers = $this->team->members->pluck('id')->toArray();

        // Members to add
        $membersToAdd = array_diff($this->teamMemberIds, $currentMembers);

        // Members to remove
        $membersToRemove = array_diff($currentMembers, $this->teamMemberIds);

        // Add new members
        foreach ($membersToAdd as $memberId) {
            $this->team->members()->attach($memberId, [
                'role' => $this->teamRole ?: 'member',
                'joined_at' => now(),
            ]);
        }

        // Remove members
        foreach ($membersToRemove as $memberId) {
            $this->team->members()->updateExistingPivot($memberId, [
                'left_at' => now(),
            ]);
        }

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Team members updated successfully.',
        ]);

        $this->redirect(route('users.hierarchy', ['viewMode' => 'team', 'selectedTeamId' => $this->teamId]), navigate: true);
    }

    public function cancel()
    {
        $this->redirect(route('users.hierarchy', ['viewMode' => 'team', 'selectedTeamId' => $this->teamId]), navigate: true);
    }

    public function render()
    {
        return view('livewire.users.team-member-management', [
            'potentialTeamMembers' => User::orderBy('last_name')->get(),
        ]);
    }
}
