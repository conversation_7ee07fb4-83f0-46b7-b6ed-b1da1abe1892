<?php

namespace App\Livewire\Users;

use App\Models\Department;
use App\Models\User;
use Livewire\Component;

class OrgChart extends Component
{
    public $selectedDepartmentId = null;
    public $searchQuery = '';
    public $departments = [];

    protected $queryString = [
        'selectedDepartmentId' => ['except' => null],
        'searchQuery' => ['except' => ''],
    ];

    public function mount()
    {
        $this->departments = Department::orderBy('name')->get();
    }

    public function selectDepartment($departmentId)
    {
        $this->selectedDepartmentId = $departmentId;
    }

    public function render()
    {
        $users = User::query()
            ->when($this->searchQuery, function ($query) {
                $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->searchQuery . '%')
                      ->orWhere('last_name', 'like', '%' . $this->searchQuery . '%')
                      ->orWhere('email', 'like', '%' . $this->searchQuery . '%')
                      ->orWhere('job_title', 'like', '%' . $this->searchQuery . '%');
                });
            })
            ->when($this->selectedDepartmentId, function ($query) {
                $query->where('department_id', $this->selectedDepartmentId);
            })
            ->with(['department', 'manager', 'directReports'])
            ->orderBy('hierarchy_level')
            ->orderBy('last_name')
            ->get();

        // Prepare user data for JavaScript
        $usersForJs = $users->map(function($user) {
            return [
                "id" => $user->id,
                "name" => $user->first_name . ' ' . $user->last_name,
                "title" => $user->job_title ?: 'Not set',
                "department" => $user->department ? $user->department->name : 'Not assigned',
                "email" => $user->email
            ];
        })->values()->toArray();

        // Ensure we have users
        if ($users->isEmpty()) {
            // If no users found with current filters, try getting all users
            if ($this->searchQuery || $this->selectedDepartmentId) {
                $orgData = [];
            } else {
                // If no filters and still no users, create empty array
                $orgData = [];
            }
        } else {
            // Organize users into a hierarchical structure
            $orgData = $this->organizeHierarchy($users);
        }

        // If we still have no org data but have users, create a flat structure
        if (empty($orgData) && $users->isNotEmpty()) {
            $orgData = $this->createFlatHierarchy($users);
        }

        return view('livewire.users.org-chart', [
            'users' => $users,
            'usersForJs' => $usersForJs,
            'departments' => $this->departments,
            'selectedDepartment' => $this->selectedDepartmentId ? Department::find($this->selectedDepartmentId) : null,
            'orgData' => $orgData,
        ]);
    }

    /**
     * Organize users into a hierarchical structure
     *
     * @param \Illuminate\Database\Eloquent\Collection $users
     * @return array
     */
    protected function organizeHierarchy($users)
    {
        // If department filter is active, we need to handle the hierarchy differently
        if ($this->selectedDepartmentId) {
            return $this->organizeDepartmentHierarchy($users);
        }

        // Find top-level users (those without managers)
        $topLevelUsers = $users->filter(function ($user) {
            return $user->manager_id === null;
        });

        // If no top-level users found (which can happen with filtering),
        // create a flat structure with all users
        if ($topLevelUsers->isEmpty() && $users->isNotEmpty()) {
            return $this->createFlatHierarchy($users);
        }

        // Build the hierarchy
        $hierarchy = [];
        foreach ($topLevelUsers as $user) {
            $hierarchy[] = $this->buildUserNode($user, $users);
        }

        return $hierarchy;
    }

    /**
     * Organize hierarchy for a specific department
     *
     * @param \Illuminate\Database\Eloquent\Collection $users
     * @return array
     */
    protected function organizeDepartmentHierarchy($users)
    {
        // Find the department manager if exists
        $departmentManager = $users->first(function ($user) {
            return $user->department && $user->department->manager_id === $user->id;
        });

        // If department has a manager who is in the filtered users, start with them
        if ($departmentManager) {
            return [$this->buildUserNode($departmentManager, $users)];
        }

        // Otherwise, find users in this department without managers or with managers outside the department
        $topDepartmentUsers = $users->filter(function ($user) use ($users) {
            return $user->manager_id === null || !$users->contains('id', $user->manager_id);
        });

        // If we found top department users, build hierarchy from them
        if ($topDepartmentUsers->isNotEmpty()) {
            $hierarchy = [];
            foreach ($topDepartmentUsers as $user) {
                $hierarchy[] = $this->buildUserNode($user, $users);
            }
            return $hierarchy;
        }

        // If all else fails, create a flat structure
        return $this->createFlatHierarchy($users);
    }

    /**
     * Create a flat hierarchy with all users
     *
     * @param \Illuminate\Database\Eloquent\Collection $users
     * @return array
     */
    protected function createFlatHierarchy($users)
    {
        // Sort users by name for better display
        $sortedUsers = $users->sortBy(function ($user) {
            return $user->last_name . ', ' . $user->first_name;
        });

        $hierarchy = [];
        foreach ($sortedUsers as $user) {
            $hierarchy[] = [
                'id' => $user->id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'title' => $user->job_title ?: 'Not set',
                'department' => $user->department ? $user->department->name : 'Not assigned',
                'email' => $user->email,
                'image' => $user->profile_photo_url,
                'children' => [],
            ];
        }

        return $hierarchy;
    }

    /**
     * Build a hierarchical node for a user
     *
     * @param \App\Models\User $user
     * @param \Illuminate\Database\Eloquent\Collection $allUsers
     * @return array
     */
    protected function buildUserNode($user, $allUsers)
    {
        // Find direct reports
        $directReports = $allUsers->filter(function ($u) use ($user) {
            return $u->manager_id === $user->id;
        });

        $children = [];
        foreach ($directReports as $report) {
            $children[] = $this->buildUserNode($report, $allUsers);
        }

        return [
            'id' => $user->id,
            'name' => $user->first_name . ' ' . $user->last_name,
            'title' => $user->job_title ?: 'Not set',
            'department' => $user->department ? $user->department->name : 'Not assigned',
            'email' => $user->email,
            'image' => $user->profile_photo_url,
            'children' => $children,
        ];
    }
}
