<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Validator;

class RoleCreate extends Component
{
    public $name;
    public $guard_name = 'web';
    public $permissions = [];

    protected $rules = [
        'name' => 'required|string|max:255|unique:roles',
        'guard_name' => 'required|string',
    ];

    public function mount()
    {
        $this->permissions = Permission::pluck('name', 'id')->toArray();
    }

    public function createRole()
    {
        $validatedData = $this->validate();

        try {
            $role = Role::create($validatedData);
            
            if (!empty($this->selectedPermissions)) {
                $role->givePermissionTo($this->selectedPermissions);
            }

            $this->reset(['name', 'selectedPermissions']);
            
            $this->dispatch('role-created', $role->id);
            
            session()->flash('success', 'Role created successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Error creating role: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.users.role-create', [
            'permissions' => $this->permissions,
        ]);
    }
}
