<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;

class RolePermissionManagement extends Component
{
    use WithPagination;

    public $tab = 'roles';
    public $searchTerm = '';
    public $roleName = '';
    public $permissionName = '';
    public $roleId;
    public $permissionId;
    public $selectedPermissions = [];
    public $showRoleModal = false;
    public $showPermissionModal = false;
    public $editMode = false;

    protected $listeners = ['refreshComponent' => '$refresh'];

    public function mount($activeTab = null)
    {
        // Set active tab from route parameter if provided
        if ($activeTab) {
            $this->tab = $activeTab;
        }
    }

    public function render()
    {
        $roles = Role::when($this->searchTerm, function ($query) {
            return $query->where('name', 'like', '%' . $this->searchTerm . '%');
        })->paginate(10);

        $permissions = Permission::when($this->searchTerm, function ($query) {
            return $query->where('name', 'like', '%' . $this->searchTerm . '%');
        })->paginate(10);

        return view('livewire.users.role-permission-management', [
            'roles' => $roles,
            'permissions' => $permissions,
        ]);
    }

    public function openRoleModal($roleId = null)
    {
        $this->resetValidation();
        $this->roleId = $roleId;
        $this->editMode = $roleId ? true : false;
        
        if ($this->editMode) {
            $role = Role::findOrFail($roleId);
            $this->roleName = $role->name;
            $this->selectedPermissions = $role->permissions->pluck('id')->toArray();
        } else {
            $this->roleName = '';
            $this->selectedPermissions = [];
        }
        
        $this->showRoleModal = true;
    }

    public function openPermissionModal($permissionId = null)
    {
        $this->resetValidation();
        $this->permissionId = $permissionId;
        $this->editMode = $permissionId ? true : false;
        
        if ($this->editMode) {
            $permission = Permission::findOrFail($permissionId);
            $this->permissionName = $permission->name;
        } else {
            $this->permissionName = '';
        }
        
        $this->showPermissionModal = true;
    }

    public function closeRoleModal()
    {
        $this->showRoleModal = false;
        $this->resetValidation();
    }

    public function closePermissionModal()
    {
        $this->showPermissionModal = false;
        $this->resetValidation();
    }

    public function saveRole()
    {
        $this->validate([
            'roleName' => 'required|min:3|max:100',
        ]);

        DB::beginTransaction();
        try {
            if ($this->editMode) {
                $role = Role::findOrFail($this->roleId);
                $role->name = $this->roleName;
                $role->save();
            } else {
                $role = Role::create(['name' => $this->roleName]);
            }
            
            // Sync permissions
            $role->syncPermissions($this->selectedPermissions);
            
            DB::commit();
            
            $this->showRoleModal = false;
            $this->dispatchBrowserEvent('notify', ['message' => 'Role saved successfully!']);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->dispatchBrowserEvent('notify', ['message' => 'Error: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    public function savePermission()
    {
        $this->validate([
            'permissionName' => 'required|min:3|max:100',
        ]);

        DB::beginTransaction();
        try {
            if ($this->editMode) {
                $permission = Permission::findOrFail($this->permissionId);
                $permission->name = $this->permissionName;
                $permission->save();
            } else {
                Permission::create(['name' => $this->permissionName]);
            }
            
            DB::commit();
            
            $this->showPermissionModal = false;
            $this->dispatchBrowserEvent('notify', ['message' => 'Permission saved successfully!']);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->dispatchBrowserEvent('notify', ['message' => 'Error: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    public function deleteRole($roleId)
    {
        try {
            $role = Role::findOrFail($roleId);
            $role->delete();
            $this->dispatchBrowserEvent('notify', ['message' => 'Role deleted successfully!']);
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('notify', ['message' => 'Error: ' . $e->getMessage(), 'type' => 'error']);
        }
    }

    public function deletePermission($permissionId)
    {
        try {
            $permission = Permission::findOrFail($permissionId);
            $permission->delete();
            $this->dispatchBrowserEvent('notify', ['message' => 'Permission deleted successfully!']);
        } catch (\Exception $e) {
            $this->dispatchBrowserEvent('notify', ['message' => 'Error: ' . $e->getMessage(), 'type' => 'error']);
        }
    }
}
