<?php

namespace App\Livewire\Users;

use App\Models\Department;
use App\Models\User;
use Livewire\Component;

class UserHierarchyEdit extends Component
{
    public $userId;
    public $user;
    public $departments = [];
    public $managers = [];
    public $jobTitle = '';
    public $employmentType = '';
    public $hierarchyLevel = null;
    public $departmentId = null;
    public $managerId = null;

    public function mount($userId)
    {
        $this->userId = $userId;
        $this->user = User::findOrFail($userId);
        $this->departments = Department::orderBy('name')->get();
        $this->managers = User::whereHas('directReports')
            ->orWhereHas('managedDepartment')
            ->where('id', '!=', $userId) // Exclude the current user
            ->get();

        // Load user data
        $this->jobTitle = $this->user->job_title;
        $this->employmentType = $this->user->employment_type;
        $this->hierarchyLevel = $this->user->hierarchy_level;
        $this->departmentId = $this->user->department_id;
        $this->managerId = $this->user->manager_id;
    }

    public function saveUserHierarchy()
    {
        $this->validate([
            'jobTitle' => 'nullable|string|max:100',
            'employmentType' => 'nullable|string|max:50',
            'hierarchyLevel' => 'nullable|integer|min:1|max:10',
            'departmentId' => 'nullable|exists:departments,id',
            'managerId' => 'nullable|exists:users,id',
        ]);

        $this->user->update([
            'job_title' => $this->jobTitle,
            'employment_type' => $this->employmentType,
            'hierarchy_level' => $this->hierarchyLevel,
            'department_id' => $this->departmentId,
            'manager_id' => $this->managerId,
        ]);

        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'User hierarchy information updated successfully.',
        ]);

        $this->redirect(route('users.hierarchy'), navigate: true);
    }

    public function cancel()
    {
        $this->redirect(route('users.hierarchy'), navigate: true);
    }

    public function render()
    {
        return view('livewire.users.user-hierarchy-edit');
    }
}
