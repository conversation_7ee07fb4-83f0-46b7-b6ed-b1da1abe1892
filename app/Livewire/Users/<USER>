<?php

namespace App\Livewire\Users;

use Livewire\Component;

use Spatie\Permission\Models\Permission;

class PermissionIndex extends Component
{
    public $permissions = [];
    public $selectAll = false;
    public $selectedPermissions = [];

    public function mount()
    {
        $this->permissions = Permission::all();
    }

    public function deleteSelected()
    {
        if (empty($this->selectedPermissions)) {
            return;
        }

        Permission::whereIn('id', $this->selectedPermissions)->delete();
        $this->selectedPermissions = [];
        $this->selectAll = false;
        $this->permissions = Permission::all();
    }

    public function render()
    {
        return view('livewire.users.permission-index', [
            'permissions' => $this->permissions,
            'selectAll' => $this->selectAll,
            'selectedPermissions' => $this->selectedPermissions,
        ]);
    }
}
