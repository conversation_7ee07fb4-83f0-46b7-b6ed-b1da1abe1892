<?php

namespace App\Livewire\Users;

use App\Models\User;
use App\Models\Call;
use App\Models\CallEvaluation;
use App\Models\EmployeeOnboarding;
use App\Models\PerformanceMetric;
use App\Models\PerformanceReview;
use App\Models\KpiTarget;
use Carbon\Carbon;
use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class UserShow extends Component
{
    public User $user;
    public $userDocuments = [];
    public $sessions = [];
    public $profilePictureUrl;

    // Performance data
    public $performanceMetrics = [];
    public $callEvaluations = [];
    public $kpiTargets = [];
    public $performanceReviews = [];
    public $period = 'month'; // 'week', 'month', 'quarter', 'year'
    public $startDate;
    public $endDate;
    public $qualityScores = [];
    public $callStats = [];
    public $adherenceRate = 0;
    public $avgQualityScore = 0;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadUserDocuments();
        $this->loadUserSessions();
        $this->setProfilePictureUrl();

        // Set date range based on period
        $this->setDateRange();

        // Load performance data if user is an agent
        if ($this->user->role_id === 3) { // Assuming role_id 3 is for agents
            $this->loadPerformanceData();
        }

        // Eager load onboarding information
        $this->user->load(['onboardings' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }]);
    }

    /**
     * Set the date range based on the selected period
     */
    protected function setDateRange()
    {
        $now = Carbon::now();

        switch ($this->period) {
            case 'week':
                $this->startDate = $now->copy()->startOfWeek()->format('Y-m-d');
                $this->endDate = $now->copy()->endOfWeek()->format('Y-m-d');
                break;
            case 'month':
                $this->startDate = $now->copy()->startOfMonth()->format('Y-m-d');
                $this->endDate = $now->copy()->endOfMonth()->format('Y-m-d');
                break;
            case 'quarter':
                $this->startDate = $now->copy()->startOfQuarter()->format('Y-m-d');
                $this->endDate = $now->copy()->endOfQuarter()->format('Y-m-d');
                break;
            case 'year':
                $this->startDate = $now->copy()->startOfYear()->format('Y-m-d');
                $this->endDate = $now->copy()->endOfYear()->format('Y-m-d');
                break;
            default:
                $this->startDate = $now->copy()->startOfMonth()->format('Y-m-d');
                $this->endDate = $now->copy()->endOfMonth()->format('Y-m-d');
        }
    }

    /**
     * Load all performance-related data
     */
    protected function loadPerformanceData()
    {
        $this->loadPerformanceMetrics();
        $this->loadCallEvaluations();
        $this->loadKpiTargets();
        $this->loadPerformanceReviews();
        $this->calculatePerformanceStats();
    }

    /**
     * Load performance metrics for the user
     */
    protected function loadPerformanceMetrics()
    {
        $this->performanceMetrics = PerformanceMetric::where('user_id', $this->user->id)
            ->whereBetween('date', [$this->startDate, $this->endDate])
            ->orderBy('date')
            ->get();
    }

    /**
     * Load call evaluations for the user
     */
    protected function loadCallEvaluations()
    {
        $this->callEvaluations = CallEvaluation::whereHas('call', function ($query) {
                $query->where('user_id', $this->user->id);
            })
            ->whereBetween('evaluation_date', [$this->startDate, $this->endDate])
            ->with(['call', 'evaluator'])
            ->orderBy('evaluation_date', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Load KPI targets for the user
     */
    protected function loadKpiTargets()
    {
        $this->kpiTargets = KpiTarget::where(function ($query) {
                $query->where('user_id', $this->user->id)
                    ->orWhere(function ($q) {
                        $q->whereNull('user_id')
                          ->where('campaign_id', $this->user->campaign_id);
                    });
            })
            ->where(function ($query) {
                $query->where(function ($q) {
                    $q->where('start_date', '<=', $this->endDate)
                      ->where('end_date', '>=', $this->startDate);
                })
                ->orWhere(function ($q) {
                    $q->where('start_date', '<=', $this->endDate)
                      ->whereNull('end_date');
                });
            })
            ->where('is_active', true)
            ->get();
    }

    /**
     * Load performance reviews for the user
     */
    protected function loadPerformanceReviews()
    {
        $this->performanceReviews = PerformanceReview::where('user_id', $this->user->id)
            ->whereBetween('review_date', [$this->startDate, $this->endDate])
            ->with('reviewer')
            ->orderBy('review_date', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * Set the profile picture URL
     */
    protected function setProfilePictureUrl()
    {
        // Use the User model's getProfilePictureUrl method for consistency
        $this->profilePictureUrl = $this->user->getProfilePictureUrl();
    }

    /**
     * Load user documents grouped by category
     */
    protected function loadUserDocuments()
    {
        $documents = $this->user->media()
            ->whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])
            ->get();

        // Group documents by category and add proper URLs
        foreach ($documents as $document) {
            $this->userDocuments[$document->category][] = [
                'id' => $document->id,
                'file_name' => $document->file_name,
                'file_path' => $document->file_path,
                'created_at' => $document->created_at,
                'url' => route('documents.view', ['id' => $document->id])
            ];
        }
    }

    /**
     * Load user sessions from the database
     */
    protected function loadUserSessions()
    {
        // Get sessions from the sessions table if your app uses database session driver
        $this->user->sessions = DB::table('sessions')
            ->where('user_id', $this->user->id)
            ->orderBy('last_activity', 'desc')
            ->limit(5)
            ->get();
    }

    /**
     * Calculate performance statistics
     */
    protected function calculatePerformanceStats()
    {
        // Calculate quality scores over time
        $this->qualityScores = $this->callEvaluations->map(function ($evaluation) {
            return [
                'date' => $evaluation->evaluation_date->format('Y-m-d'),
                'score' => $evaluation->overall_score,
            ];
        })->toArray();

        // Calculate average quality score
        $this->avgQualityScore = $this->callEvaluations->avg('overall_score') ?? 0;

        // Calculate call statistics
        $totalCalls = $this->performanceMetrics->sum('calls_handled') + $this->performanceMetrics->sum('outbound_calls');
        $totalTalkTime = $this->performanceMetrics->sum('total_talk_time');
        $avgHandleTime = $totalCalls > 0 ? $this->performanceMetrics->sum('total_talk_time') / $totalCalls : 0;

        $this->callStats = [
            'total_calls' => $totalCalls,
            'calls_handled' => $this->performanceMetrics->sum('calls_handled'),
            'calls_missed' => $this->performanceMetrics->sum('calls_missed'),
            'outbound_calls' => $this->performanceMetrics->sum('outbound_calls'),
            'total_talk_time' => $this->formatTime($totalTalkTime),
            'avg_handle_time' => $this->formatTime($avgHandleTime),
            'appointments_set' => $this->performanceMetrics->sum('appointments_set'),
            'appointments_kept' => $this->performanceMetrics->sum('appointments_kept'),
            'conversion_rate' => $this->performanceMetrics->avg('conversion_rate') ?? 0,
        ];

        // Calculate adherence rate
        $this->adherenceRate = $this->performanceMetrics->avg('adherence_rate') ?? 0;
    }

    /**
     * Format time in seconds to hours:minutes:seconds
     */
    protected function formatTime($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    }

    /**
     * Update period and refresh data
     */
    public function updatedPeriod()
    {
        $this->setDateRange();

        if ($this->user->role_id === 3) { // Assuming role_id 3 is for agents
            $this->loadPerformanceData();
        }
    }

    public function render()
    {
        return view('livewire.users.user-show');
    }
}
