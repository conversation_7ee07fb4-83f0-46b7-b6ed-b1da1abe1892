<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\WithFileUploads;
use Spatie\Permission\Models\Permission;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;

class UserPermissionCreate extends Component
{
    use WithFileUploads;

    // Form properties
    public $name = '';
    public $guard_name = 'web';
    public $description = '';
    public $group = '';
    public $availableGroups = [];

    // UI State
    public $isSubmitting = false;
    public $showSuccess = false;
    public $successMessage = '';

    protected $rules = [
        'name' => ['required', 'string', 'max:255', 'unique:permissions,name'],
        'guard_name' => ['required', 'string', 'in:web,api'],
        'description' => ['nullable', 'string', 'max:500'],
        'group' => ['required', 'string', 'max:100'],
    ];

    protected $messages = [
        'name.required' => 'The permission name is required.',
        'name.unique' => 'A permission with this name already exists.',
        'guard_name.required' => 'The guard name is required.',
        'group.required' => 'Please select a permission group.',
    ];

    public function mount()
    {
        $this->loadAvailableGroups();
    }

    protected function loadAvailableGroups()
    {
        // Get all permissions and extract groups in PHP instead of SQL
        $permissions = Permission::select('name')->get();
        $groups = [];

        foreach ($permissions as $permission) {
            // Extract the first part before the first dot
            $parts = explode('.', $permission->name, 2);
            if (count($parts) > 0) {
                $groups[] = $parts[0];
            }
        }

        // Get unique groups and sort them
        $this->availableGroups = array_unique($groups);
        sort($this->availableGroups);

        // Add common groups if none exist yet
        if (empty($this->availableGroups)) {
            $this->availableGroups = [
                'user', 'role', 'permission', 'settings', 'reports', 'content'
            ];
        }
    }

    public function createPermission()
    {
        $this->validate();
        $this->isSubmitting = true;

        try {
            // Create the permission
            $permission = Permission::create([
                'name' => $this->name,
                'guard_name' => $this->guard_name,
            ]);

            // Add custom properties if needed (using a JSON column or custom table)
            // $permission->setCustomProperty('description', $this->description);
            // $permission->setCustomProperty('group', $this->group);
            // $permission->save();

            // Set success message
            $this->successMessage = 'Permission created successfully!';
            $this->showSuccess = true;

            // Reset form
            $this->reset(['name', 'description', 'group']);
            $this->resetErrorBag();
            $this->resetValidation();

            // Dispatch event to refresh permissions list
            $this->dispatch('permission-created', permissionId: $permission->id);

        } catch (\Exception $e) {
            $this->addError('create_error', 'Failed to create permission: ' . $e->getMessage());
        } finally {
            $this->isSubmitting = false;
        }
    }

    public function updatedName($value)
    {
        // Auto-set group based on permission name if not set
        if (empty($this->group) && str_contains($value, '.')) {
            $this->group = explode('.', $value)[0];
        }
    }

    public function render()
    {
        return view('livewire.users.user-permission-create', [
            'availableGroups' => $this->availableGroups,
        ]);
    }
}
