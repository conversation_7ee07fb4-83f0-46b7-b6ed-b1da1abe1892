<?php

namespace App\Livewire\Users;

use App\Livewire\Global\Page;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\DB;
use Livewire\Attributes\On;

class UserPage extends Page
{
    public ?User $user = null;
    public ?Role $role = null;
    public ?Permission $permission = null;
    public $allPermissions = [];
    public $permissionGroups = [];
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    #[On('to-user-index')]
    public function toUserIndex()
    {
        return $this->redirect(route('users.index'), navigate: true);
    }

    #[On('to-user-show')]
    public function toUserShow(User $user = null)
    {
        if(!$user) {
            return $this->redirect(route('users.index'), navigate: true);
        }
        return $this->redirect(route('users.show', ['user' => $user ?? $this->user]), navigate: true);
    }

    #[On('to-user-edit')]
    public function toUserEdit(User $user = null)
    {
        if(!$user) {
            return $this->redirect(route('users.index'), navigate: true);
        }
        return $this->redirect(route('users.edit', ['user' => $user ?? $this->user]), navigate: true);
    }

    #[On('to-user-delete')]
    public function toUserDelete(User $user = null)
    {
        if(!$user) {
            return $this->redirect(route('users.index'), navigate: true);
        }
        return $this->redirect(route('users.delete', ['user' => $user ?? $this->user]), navigate: true);
    }

    #[On('to-user-create')]
    public function toUserCreate()
    {
        return $this->redirect(route('users.create'), navigate: true);
    }

    #[On('to-user-hierarchy')]
    public function toUserHierarchy()
    {
        return $this->redirect(route('users.hierarchy'), navigate: true);
    }

    #[On('to-org-chart')]
    public function toOrgChart()
    {
        return $this->redirect(route('users.org-chart'), navigate: true);
    }

    // Role Navigation Methods
    #[On('to-user-role-index')]
    public function toUserRoleIndex()
    {
        return $this->redirect(route('users.roles.index'), navigate: true);
    }

    #[On('to-user-role-create')]
    public function toUserRoleCreate()
    {
        return $this->redirect(route('users.roles.create'), navigate: true);
    }

    #[On('to-user-role-show')]
    public function toUserRoleShow(Role $role)
    {
        if (!$role) {
            return $this->redirect(route('users.roles.index'), navigate: true);
        }
        return $this->redirect(route('users.roles.show', ['role' => $role]), navigate: true);
    }

    #[On('to-user-role-edit')]
    public function toUserRoleEdit(Role $role)
    {
        if (!$role) {
            return $this->redirect(route('users.roles.index'), navigate: true);
        }
        return $this->redirect(route('users.roles.edit', ['role' => $role]), navigate: true);
    }

    #[On('to-user-role-delete')]
    public function toUserRoleDelete(Role $role)
    {
        if (!$role) {
            return $this->redirect(route('users.roles.index'), navigate: true);
        }
        return $this->redirect(route('users.roles.delete', ['role' => $role]), navigate: true);
    }

    // Permission Navigation Methods
    #[On('to-user-permission-index')]
    public function toUserPermissionIndex()
    {
        return $this->redirect(route('users.permissions.index'), navigate: true);
    }

    #[On('to-user-permission-create')]
    public function toUserPermissionCreate()
    {
        return $this->redirect(route('users.permissions.create'), navigate: true);
    }

    #[On('to-user-permission-show')]
    public function toUserPermissionShow(Permission $permission)
    {
        if (!$permission) {
            return $this->redirect(route('users.permissions.index'), navigate: true);
        }
        return $this->redirect(route('users.permissions.show', ['permission' => $permission]), navigate: true);
    }

    #[On('to-user-permission-edit')]
    public function toUserPermissionEdit(Permission $permission)
    {
        if (!$permission) {
            return $this->redirect(route('users.permissions.index'), navigate: true);
        }
        return $this->redirect(route('users.permissions.edit', ['permission' => $permission]), navigate: true);
    }

    #[On('to-user-permission-delete')]
    public function toUserPermissionDelete(Permission $permission)
    {
        if (!$permission) {
            return $this->redirect(route('users.permissions.index'), navigate: true);
        }
        return $this->redirect(route('users.permissions.delete', ['permission' => $permission]), navigate: true);
    }

    public function setPageResume($routeName)
    {
        // Initialize with default values
        $this->resumeTitle = 'Users';
        $this->resumeDescription = 'User management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Users',
            'description' => 'User management system'
        ];

        // Initialize current_page_section with default values
        $this->current_page_section = [
            'title' => '',
            'description' => '',
            'sections' => []
        ];

        // For backward compatibility
        $this->current_page_resume = [
            'title' => 'Users',
            'type' => 'chart',
            'description' => 'User management system',
            'sections' => []
        ];

        switch ($routeName) {
            // User Management
            case 'users.index':
                $totalUsers = User::count();
                $activeUsers = User::where('status', 'active')->orWhere('status', 'actif')->count();
                $inactiveUsers = User::where('status', 'inactive')->orWhere('status', 'inactif')->count();
                // $adminUsers = User::role('name', Role::ADMIN)->count();
                $adminUsers = User::whereHas('roles', function($query) {
                    $query->where('name', Role::ADMIN);
                })->count();
                // $managerUsers = User::role('name', Role::MANAGER)->count();
                $managerUsers = User::whereHas('roles', function($query) {
                    $query->where('name', Role::MANAGER);
                })->count();

                // Set dynamic page resume properties
                $this->resumeTitle = 'User Management';
                $this->resumeDescription = 'Comprehensive overview of all users in the system. This page allows you to monitor user status, track role assignments, and manage user records. Use the filters and search functionality to find specific users based on status, role, or other criteria.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'User Management',
                    'description' => 'Comprehensive overview of all users in the system. This page allows you to monitor user status, track role assignments, and manage user records. Use the filters and search functionality to find specific users based on status, role, or other criteria.',
                    'metrics' => [
                        [
                            'label' => 'Total Users',
                            'value' => $totalUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Active Users',
                            'value' => $activeUsers,
                            'change' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100) : 0
                        ],
                        [
                            'label' => 'Inactive Users',
                            'value' => $inactiveUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Admins',
                            'value' => $adminUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Managers',
                            'value' => $managerUsers,
                            'change' => null
                        ]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'User Management',
                    'type' => 'dashboard',
                    'description' => 'Comprehensive overview of all users in the system. This page allows you to monitor user status, track role assignments, and manage user records. Use the filters and search functionality to find specific users based on status, role, or other criteria.',
                    'metrics' => [
                        [
                            'label' => 'Total Users',
                            'value' => $totalUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Active Users',
                            'value' => $activeUsers,
                            'change' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100) : 0
                        ],
                        [
                            'label' => 'Inactive Users',
                            'value' => $inactiveUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Admins',
                            'value' => $adminUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Managers',
                            'value' => $managerUsers,
                            'change' => null
                        ]
                    ]
                ];
                break;

            case 'users.create':
                // Set dynamic page resume properties
                $this->resumeTitle = 'Create New User';
                $this->resumeDescription = 'Add a new user to the system with appropriate role, department, and document assignments.';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create New User',
                    'description' => 'Add a new user to the system with appropriate role, department, and document assignments.',
                    'steps' => [
                        'Enter the user\'s personal information',
                        'Upload profile picture (optional)',
                        'Assign appropriate role and department',
                        'Set up document storage for employee documents',
                        'Configure onboarding process if applicable',
                        'Set secure password',
                        'Save to create the user'
                    ],
                    'notes' => 'New users will receive an email notification with their login credentials if email notifications are enabled in the system settings.'
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Create New User',
                    'type' => 'form',
                    'description' => 'Add a new user to the system with appropriate role, department, and document assignments.',
                    'steps' => [
                        'Enter the user\'s personal information',
                        'Upload profile picture (optional)',
                        'Assign appropriate role and department',
                        'Set up document storage for employee documents',
                        'Configure onboarding process if applicable',
                        'Set secure password',
                        'Save to create the user'
                    ],
                    'notes' => 'New users will receive an email notification with their login credentials if email notifications are enabled in the system settings.'
                ];
                break;

            case 'users.show':
                if ($this->user) {
                    // Get additional user data for the resume
                    $directReportsCount = $this->user->directReports()->count();
                    $documentsCount = $this->user->media()->count();
                    $departmentName = $this->user->department ? $this->user->department->name : 'Not Assigned';
                    $managerName = $this->user->manager ? $this->user->manager->getFullNameAttribute() : 'None';
                    $teamCount = method_exists($this->user, 'teams') ? $this->user->teams()->count() : 0;

                    // Set dynamic page resume properties
                    $this->resumeTitle = $this->user->getFullNameAttribute();
                    $this->resumeDescription = 'Detailed profile for ' . $this->user->getFullNameAttribute() . '. This page provides comprehensive information about the user\'s status, role, department, and document records.';
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => $this->user->getFullNameAttribute(),
                        'subtitle' => $this->user->email,
                        'description' => 'Detailed profile for ' . $this->user->getFullNameAttribute() . '. This page provides comprehensive information about the user\'s status, role, department, and document records. Use this information to evaluate the user\'s profile and make informed management decisions.',
                        'stats' => [
                            'Status' => ucfirst($this->user->status ?? 'Unknown'),
                            'Role' => $this->user->role->name ?? 'No Role',
                            'Department' => $departmentName,
                            'Manager' => $managerName,
                            'Direct Reports' => $directReportsCount,
                            'Teams' => $teamCount,
                            'Documents' => $documentsCount,
                            'Registration' => $this->user->registration_number ?? 'Not Assigned',
                            'Hire Date' => $this->user->hire_date ? $this->user->hire_date->format('M d, Y') : 'Not Set',
                            'Created' => $this->user->created_at ? $this->user->created_at->format('M d, Y') : 'Unknown',
                            'Last Updated' => $this->user->updated_at ? $this->user->updated_at->format('M d, Y') : 'Unknown'
                        ],
                        'metadata' => [
                            'Job Title' => $this->user->job_title ?? 'Not Set',
                            'Employment Type' => $this->user->employment_type ?? 'Not Set',
                            'Hierarchy Level' => $this->user->hierarchy_level ?? 'Not Set',
                            'Phone' => $this->user->phone_number ?? 'Not Provided',
                            'Address' => $this->user->address ? ($this->user->address . ', ' . $this->user->city . ', ' . $this->user->country) : 'Not Provided',
                            'Birth Date' => $this->user->birth_date ? $this->user->birth_date->format('M d, Y') : 'Not Provided'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => $this->user->getFullNameAttribute(),
                        'type' => 'entity',
                        'description' => 'Detailed profile for ' . $this->user->getFullNameAttribute() . '. This page provides comprehensive information about the user\'s status, role, department, and document records. Use this information to evaluate the user\'s profile and make informed management decisions.',
                        'stats' => [
                            'status' => ucfirst($this->user->status ?? 'Unknown'),
                            'role' => $this->user->role->name ?? 'No Role',
                            'department' => $departmentName,
                            'manager' => $managerName,
                            'direct_reports' => $directReportsCount,
                            'teams' => $teamCount,
                            'documents' => $documentsCount,
                            'registration' => $this->user->registration_number ?? 'Not Assigned',
                            'hire_date' => $this->user->hire_date ? $this->user->hire_date->format('M d, Y') : 'Not Set'
                        ]
                    ];
                }
                break;

            case 'users.edit':
                if ($this->user) {
                    // Set dynamic page resume properties
                    $this->resumeTitle = 'Edit User: ' . $this->user->getFullNameAttribute();
                    $this->resumeDescription = 'Update the user\'s information and settings.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit User: ' . $this->user->getFullNameAttribute(),
                        'description' => 'Update the user\'s information and settings.',
                        'steps' => [
                            'Update the user\'s personal information',
                            'Modify role and department if needed',
                            'Update profile picture and documents',
                            'Leave password fields blank to keep the current password',
                            'Save your changes'
                        ],
                        'notes' => 'Changing a user\'s status will affect their access to the system. Make sure to review all changes before saving.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Edit User: ' . $this->user->getFullNameAttribute(),
                        'type' => 'form',
                        'description' => 'Update the user\'s information and settings.',
                        'steps' => [
                            'Update the user\'s personal information',
                            'Modify role and department if needed',
                            'Update profile picture and documents',
                            'Leave password fields blank to keep the current password',
                            'Save your changes'
                        ],
                        'notes' => 'Changing a user\'s status will affect their access to the system. Make sure to review all changes before saving.'
                    ];
                }
                break;

            case 'users.delete':
                if ($this->user) {
                    // Set dynamic page resume properties
                    $this->resumeTitle = 'Delete User: ' . $this->user->getFullNameAttribute();
                    $this->resumeDescription = 'You are about to permanently remove this user from the system.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Delete User: ' . $this->user->getFullNameAttribute(),
                        'description' => 'You are about to permanently remove this user from the system.',
                        'steps' => [
                            'Review the user information',
                            'Confirm deletion',
                            'Click "Yes, I\'m sure" to permanently delete'
                        ],
                        'notes' => 'This action cannot be undone. All user data, including profile information, documents, and relationships will be permanently removed from the system.',
                        'warning' => 'Deleting a user may impact related records such as teams, reports, and assignments. Make sure to reassign any critical responsibilities before deletion.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Delete User: ' . $this->user->getFullNameAttribute(),
                        'type' => 'form',
                        'description' => 'You are about to permanently remove this user from the system.',
                        'steps' => [
                            'Review the user information',
                            'Confirm deletion',
                            'Click "Yes, I\'m sure" to permanently delete'
                        ],
                        'notes' => 'This action cannot be undone. All user data, including profile information, documents, and relationships will be permanently removed from the system.'
                    ];
                }
                break;

            case 'users.hierarchy':
                // Get hierarchy statistics
                $totalTeams = class_exists('App\\Models\\Team') ? \App\Models\Team::count() : 0;
                $totalManagers = User::whereHas('roles', function($query) {
                    $query->where('name', 'manager');
                })->count();
                $totalDepartments = class_exists('App\\Models\\Department') ? \App\Models\Department::count() : 0;
                $usersWithManager = User::whereNotNull('manager_id')->count();

                // Set dynamic page resume properties
                $this->resumeTitle = 'User Hierarchy & Teams';
                $this->resumeDescription = 'Manage organizational structure, reporting relationships, and team assignments across the company.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'User Hierarchy & Teams',
                    'description' => 'Manage organizational structure, reporting relationships, and team assignments across the company. This page provides tools for visualizing and modifying the organizational hierarchy, creating and managing teams, and assigning users to appropriate reporting structures.',
                    'metrics' => [
                        [
                            'label' => 'Total Teams',
                            'value' => $totalTeams,
                            'change' => null
                        ],
                        [
                            'label' => 'Total Managers',
                            'value' => $totalManagers,
                            'change' => null
                        ],
                        [
                            'label' => 'Departments',
                            'value' => $totalDepartments,
                            'change' => null
                        ],
                        [
                            'label' => 'Users with Manager',
                            'value' => $usersWithManager,
                            'change' => User::count() > 0 ? round(($usersWithManager / User::count()) * 100) : 0
                        ]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'User Hierarchy & Teams',
                    'type' => 'dashboard',
                    'description' => 'Manage organizational structure, reporting relationships, and team assignments across the company.',
                    'metrics' => [
                        [
                            'label' => 'Total Teams',
                            'value' => $totalTeams,
                            'change' => null
                        ],
                        [
                            'label' => 'Total Managers',
                            'value' => $totalManagers,
                            'change' => null
                        ],
                        [
                            'label' => 'Departments',
                            'value' => $totalDepartments,
                            'change' => null
                        ],
                        [
                            'label' => 'Users with Manager',
                            'value' => $usersWithManager,
                            'change' => User::count() > 0 ? round(($usersWithManager / User::count()) * 100) : 0
                        ]
                    ]
                ];
                break;

            case 'users.org-chart':
                // Get org chart statistics
                $totalUsers = User::count();
                $topLevelManagers = User::whereNull('manager_id')
                    ->whereHas('directReports')
                    ->count();
                $maxDepth = 0;

                // Try to calculate the maximum depth of the org chart
                try {
                    // This is a simplified approach - in a real app you might have a more efficient way to calculate this
                    $maxDepth = DB::select("
                        WITH RECURSIVE org_hierarchy AS (
                            SELECT id, manager_id, 0 as depth
                            FROM users
                            WHERE manager_id IS NULL
                            UNION ALL
                            SELECT u.id, u.manager_id, oh.depth + 1
                            FROM users u
                            JOIN org_hierarchy oh ON u.manager_id = oh.id
                        )
                        SELECT MAX(depth) as max_depth FROM org_hierarchy
                    ")[0]->max_depth ?? 0;
                } catch (\Exception $e) {
                    // If the query fails, default to 0
                    $maxDepth = 0;
                }

                // Set dynamic page resume properties
                $this->resumeTitle = 'Organization Chart';
                $this->resumeDescription = 'Visual representation of the organizational hierarchy showing reporting relationships and team structures.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Organization Chart',
                    'description' => 'Visual representation of the organizational hierarchy showing reporting relationships and team structures. This interactive chart allows you to explore the company structure, view reporting lines, and understand how different departments and teams are organized.',
                    'metrics' => [
                        [
                            'label' => 'Total Users',
                            'value' => $totalUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Top Managers',
                            'value' => $topLevelManagers,
                            'change' => null
                        ],
                        [
                            'label' => 'Hierarchy Depth',
                            'value' => $maxDepth,
                            'change' => null
                        ]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Organization Chart',
                    'type' => 'dashboard',
                    'description' => 'Visual representation of the organizational hierarchy showing reporting relationships and team structures.',
                    'metrics' => [
                        [
                            'label' => 'Total Users',
                            'value' => $totalUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Top Managers',
                            'value' => $topLevelManagers,
                            'change' => null
                        ],
                        [
                            'label' => 'Hierarchy Depth',
                            'value' => $maxDepth,
                            'change' => null
                        ]
                    ]
                ];
                break;

            // Role Management
            case 'users.roles.index':
                $totalRoles = \Spatie\Permission\Models\Role::count();
                $systemRoles = \Spatie\Permission\Models\Role::where('is_protected', true)->count();
                $customRoles = \Spatie\Permission\Models\Role::where('is_protected', false)->count();
                $rolesWithPermissions = \Spatie\Permission\Models\Role::withCount('permissions')->get();
                $avgPermissions = $rolesWithPermissions->avg('permissions_count');

                $this->resumeTitle = 'Role Management';
                $this->resumeDescription = 'View and manage all user roles in the system. Roles define sets of permissions that can be assigned to users.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Role Management',
                    'description' => 'View and manage all user roles in the system. Roles define sets of permissions that can be assigned to users.',
                    'metrics' => [
                        [
                            'label' => 'Total Roles',
                            'value' => $totalRoles,
                            'change' => null
                        ],
                        [
                            'label' => 'System Roles',
                            'value' => $systemRoles,
                            'change' => $totalRoles > 0 ? round(($systemRoles / $totalRoles) * 100) : 0
                        ],
                        [
                            'label' => 'Avg. Permissions/Role',
                            'value' => round($avgPermissions, 1),
                            'change' => null
                        ]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Role Management',
                    'type' => 'dashboard',
                    'description' => 'View and manage all user roles in the system. Roles define sets of permissions that can be assigned to users.',
                    'metrics' => [
                        [
                            'label' => 'Total Roles',
                            'value' => $totalRoles,
                            'change' => null
                        ],
                        [
                            'label' => 'System Roles',
                            'value' => $systemRoles,
                            'change' => $totalRoles > 0 ? round(($systemRoles / $totalRoles) * 100) : 0
                        ],
                        [
                            'label' => 'Avg. Permissions/Role',
                            'value' => round($avgPermissions, 1),
                            'change' => null
                        ]
                    ],
                    'sections' => []
                ];
                break;

            case 'users.roles.create':
                $this->resumeTitle = 'Create New Role';
                $this->resumeDescription = 'Create a new user role with specific permissions.';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create New Role',
                    'description' => 'Create a new user role with specific permissions.',
                    'steps' => [
                        'Enter a unique name for the role',
                        'Select the guard name (usually web or api)',
                        'Choose permissions to assign to this role',
                        'Add an optional description',
                        'Save to create the role'
                    ],
                    'notes' => 'Roles with the same name but different guards are considered different roles. Be careful when assigning permissions to ensure proper access control.'
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Create New Role',
                    'type' => 'form',
                    'description' => 'Create a new user role with specific permissions.',
                    'steps' => [
                        'Enter a unique name for the role',
                        'Select the guard name (usually web or api)',
                        'Choose permissions to assign to this role',
                        'Add an optional description',
                        'Save to create the role'
                    ],
                    'notes' => 'Roles with the same name but different guards are considered different roles. Be careful when assigning permissions to ensure proper access control.'
                ];
                break;

            case 'users.roles.show':
                if ($this->role) {
                    $permissionsCount = $this->role->permissions()->count();
                    $usersCount = $this->role->users()->count();
                    $createdAt = $this->role->created_at ? $this->role->created_at->format('M d, Y') : 'N/A';
                    $updatedAt = $this->role->updated_at ? $this->role->updated_at->format('M d, Y') : 'N/A';

                    $this->resumeTitle = 'Role: ' . $this->role->name;
                    $this->resumeDescription = 'View detailed information about this role including assigned permissions and users.';
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => $this->role->name,
                        'subtitle' => $this->role->guard_name,
                        'description' => $this->role->description ?? 'No description provided.',
                        'stats' => [
                            'Guard Name' => $this->role->guard_name,
                            'Is Protected' => $this->role->is_protected ? 'Yes' : 'No',
                            'Permissions' => $permissionsCount,
                            'Users' => $usersCount,
                            'Created' => $createdAt,
                            'Last Updated' => $updatedAt
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Role: ' . $this->role->name,
                        'type' => 'entity',
                        'description' => 'View detailed information about this role including assigned permissions and users.',
                        'stats' => [
                            'guard_name' => $this->role->guard_name,
                            'is_protected' => $this->role->is_protected ? 'Yes' : 'No',
                            'permissions' => $permissionsCount,
                            'users' => $usersCount,
                            'created_at' => $createdAt,
                            'updated_at' => $updatedAt
                        ]
                    ];
                }
                break;

            case 'users.roles.edit':
                if ($this->role) {
                    $isProtected = $this->role->is_protected ? ' (System Role)' : '';

                    $this->resumeTitle = 'Edit Role: ' . $this->role->name . $isProtected;
                    $this->resumeDescription = 'Modify role details and permission assignments.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit Role: ' . $this->role->name . $isProtected,
                        'description' => 'Modify role details and permission assignments. ' .
                                        ($this->role->is_protected ? 'Note: This is a system role. Some fields may be restricted.' : ''),
                        'steps' => [
                            'Update role name' . ($this->role->is_protected ? ' (restricted for system roles)' : ''),
                            'Update guard name if needed' . ($this->role->is_protected ? ' (restricted for system roles)' : ''),
                            'Modify permission assignments',
                            'Update the role description',
                            'Review your changes',
                            'Save changes'
                        ],
                        'notes' => $this->role->is_protected ?
                            'Warning: This is a system role. Modifying system roles may affect core functionality.' :
                            'Be cautious when modifying roles as it may affect user access.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Edit Role: ' . $this->role->name . $isProtected,
                        'type' => 'form',
                        'description' => 'Modify role details and permission assignments.',
                        'steps' => [
                            'Update role name' . ($this->role->is_protected ? ' (restricted for system roles)' : ''),
                            'Update guard name if needed' . ($this->role->is_protected ? ' (restricted for system roles)' : ''),
                            'Modify permission assignments',
                            'Update the role description',
                            'Review your changes',
                            'Save changes'
                        ],
                        'notes' => $this->role->is_protected ?
                            'Warning: This is a system role. Modifying system roles may affect core functionality.' :
                            'Be cautious when modifying roles as it may affect user access.'
                    ];
                }
                break;

            case 'users.roles.delete':
                if ($this->role) {
                    $usersCount = $this->role->users()->count();
                    $isProtected = $this->role->is_protected ? ' (System Role - Cannot be deleted)' : '';

                    $this->resumeTitle = 'Delete Role: ' . $this->role->name . $isProtected;
                    $this->resumeDescription = 'Remove a role from the system. This action cannot be undone.';
                    $this->resumeContentType = 'delete';

                    $warnings = [
                        'This action cannot be undone',
                        $usersCount . ' user(s) currently have this role assigned',
                        'Users with this role will lose these permissions',
                    ];

                    if ($this->role->is_protected) {
                        $warnings[] = 'System roles cannot be deleted';
                    }

                    $this->resumeData = [
                        'title' => 'Delete Role: ' . $this->role->name . $isProtected,
                        'description' => $this->role->is_protected ?
                            'System roles cannot be deleted as they are required for system functionality.' :
                            'Remove this role from the system. This action cannot be undone.' .
                            ($usersCount > 0 ? " {$usersCount} user(s) will be affected by this action." : ''),
                        'warnings' => $warnings,
                        'stats' => [
                            'Role Name' => $this->role->name,
                            'Assigned Users' => $usersCount,
                            'Guard Name' => $this->role->guard_name,
                            'Created' => $this->role->created_at ? $this->role->created_at->format('M d, Y') : 'N/A'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Delete Role: ' . $this->role->name . $isProtected,
                        'type' => 'entity',
                        'description' => $this->resumeData['description'],
                        'warnings' => $warnings,
                        'stats' => [
                            'role_name' => $this->role->name,
                            'users_count' => $usersCount,
                            'guard_name' => $this->role->guard_name,
                            'created_at' => $this->role->created_at ? $this->role->created_at->format('M d, Y') : 'N/A'
                        ]
                    ];
                }
                break;

            // Permission Management
            case 'users.permissions.index':
                $totalPermissions = \Spatie\Permission\Models\Permission::count();
                $systemPermissions = \Spatie\Permission\Models\Permission::where('is_protected', true)->count();
                $customPermissions = \Spatie\Permission\Models\Permission::where('is_protected', false)->count();
                $permissionsWithRoles = \Spatie\Permission\Models\Permission::withCount('roles')->get();
                $avgRoles = $permissionsWithRoles->avg('roles_count');
                // Get permission groups in a database-agnostic way
                $permissionGroups = \Spatie\Permission\Models\Permission::all()
                    ->groupBy(function($permission) {
                        return explode('.', $permission->name)[0] ?? 'other';
                    })
                    ->map(function($group) {
                        return (object)['group_name' => $group->first()->name, 'count' => $group->count()];
                    })
                    ->sortByDesc('count');

                $this->resumeTitle = 'Permission Management';
                $this->resumeDescription = 'View and manage all system permissions. Permissions define specific actions users can perform.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Permission Management',
                    'subtitle' => 'Manage system permissions and access controls',
                    'description' => 'View and manage all system permissions. Permissions define specific actions users can perform. Organize permissions into logical groups for better management.',
                    'metrics' => [
                        [
                            'label' => 'Total Permissions',
                            'value' => $totalPermissions,
                            'change' => null,
                            'icon' => 'heroicon-o-key'
                        ],
                        [
                            'label' => 'System',
                            'value' => $systemPermissions,
                            'change' => $totalPermissions > 0 ? round(($systemPermissions / $totalPermissions) * 100) : 0,
                            'icon' => 'heroicon-o-lock-closed'
                        ],
                        [
                            'label' => 'Custom',
                            'value' => $customPermissions,
                            'change' => $totalPermissions > 0 ? round(($customPermissions / $totalPermissions) * 100) : 0,
                            'icon' => 'heroicon-o-pencil'
                        ],
                        [
                            'label' => 'Avg. Roles',
                            'value' => round($avgRoles, 1),
                            'change' => null,
                            'icon' => 'heroicon-o-user-group'
                        ],
                        [
                            'label' => 'Groups',
                            'value' => $permissionGroups->count(),
                            'change' => null,
                            'icon' => 'heroicon-o-folder'
                        ]
                    ],
                    'stats' => [
                        'total_permissions' => $totalPermissions,
                        'system_permissions' => $systemPermissions,
                        'custom_permissions' => $customPermissions,
                        'avg_roles' => round($avgRoles, 1),
                        'permission_groups' => $permissionGroups->count()
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Permission Management',
                    'type' => 'dashboard',
                    'description' => 'View and manage all system permissions. Permissions define specific actions users can perform.',
                    'metrics' => [
                        ['label' => 'Total Permissions', 'value' => $totalPermissions],
                        ['label' => 'System', 'value' => $systemPermissions],
                        ['label' => 'Custom', 'value' => $customPermissions],
                        ['label' => 'Avg. Roles', 'value' => round($avgRoles, 1)],
                        ['label' => 'Groups', 'value' => $permissionGroups->count()]
                    ]
                ];
                break;

            case 'users.permissions.create':
                $this->resumeTitle = 'Create New Permission';
                $this->resumeDescription = 'Define a new system permission that can be assigned to roles.';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create New Permission',
                    'subtitle' => 'Define a new access control rule',
                    'description' => 'Create a new permission that can be assigned to roles. Permissions should follow the naming convention: resource.action (e.g., users.create, posts.delete).',
                    'steps' => [
                        'Enter a unique permission name following the resource.action pattern',
                        'Select the appropriate guard (usually web or api)',
                        'Optionally group the permission for better organization',
                        'Add a clear description of what this permission allows',
                        'Select any roles that should have this permission by default',
                        'Save to create the permission'
                    ],
                    'notes' => 'Use dot notation to group related permissions (e.g., users.view, users.create, users.edit, users.delete).',
                    'metadata' => [
                        'Naming Convention' => 'resource.action (e.g., users.create, posts.delete)',
                        'Default Guard' => 'web',
                        'Required Fields' => 'Name, Guard',
                        'Common Groups' => 'users, posts, settings, reports, permissions'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'Create New Permission',
                    'type' => 'form',
                    'description' => 'Define a new system permission that can be assigned to roles.',
                    'steps' => [
                        'Enter a unique permission name (e.g., users.create)',
                        'Select the guard name (usually web or api)',
                        'Group the permission for better organization',
                        'Add an optional description',
                        'Save to create the permission'
                    ],
                    'notes' => 'Use dot notation to group related permissions (e.g., users.view, users.create, users.edit, users.delete).'
                ];
                break;

            case 'users.permissions.show':
                if ($this->permission) {
                    $rolesCount = $this->permission->roles->count();
                    $createdAt = $this->permission->created_at ? $this->permission->created_at->format('M d, Y') : 'N/A';
                    $updatedAt = $this->permission->updated_at ? $this->permission->updated_at->format('M d, Y') : 'N/A';
                    $group = explode('.', $this->permission->name)[0] ?? 'General';

                    $this->resumeTitle = 'Permission: ' . $this->permission->name;
                    $this->resumeDescription = 'View detailed information about this permission including which roles have it assigned.';
                    $this->resumeContentType = 'entity';
                    $this->resumeData = [
                        'title' => $this->permission->name,
                        'subtitle' => $group . ' • ' . $this->permission->guard_name,
                        'description' => $this->permission->description ?? 'No description provided.',
                        'stats' => [
                            'Group' => ucfirst($group),
                            'Guard' => $this->permission->guard_name,
                            'Is Protected' => $this->permission->is_protected ? 'Yes' : 'No',
                            'Assigned Roles' => $rolesCount,
                            'Created' => $createdAt,
                            'Last Updated' => $updatedAt
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Permission: ' . $this->permission->name,
                        'type' => 'entity',
                        'description' => 'View detailed information about this permission including which roles have it assigned.',
                        'stats' => [
                            'group' => $group,
                            'guard_name' => $this->permission->guard_name,
                            'is_protected' => $this->permission->is_protected,
                            'roles_count' => $rolesCount,
                            'created_at' => $createdAt,
                            'updated_at' => $updatedAt
                        ]
                    ];
                }
                break;

            case 'users.permissions.edit':
                if ($this->permission) {
                    $isProtected = $this->permission->is_protected ? ' (System Permission)' : '';
                    $group = explode('.', $this->permission->name)[0] ?? 'General';

                    $this->resumeTitle = 'Edit Permission: ' . $this->permission->name . $isProtected;
                    $this->resumeDescription = 'Modify permission details and role assignments.';
                    $this->resumeContentType = 'form';
                    $this->resumeData = [
                        'title' => 'Edit Permission: ' . $this->permission->name . $isProtected,
                        'description' => 'Modify permission details and role assignments. ' .
                                        ($this->permission->is_protected ? 'Note: This is a system permission. Some fields may be restricted.' : ''),
                        'steps' => [
                            'Update permission name' . ($this->permission->is_protected ? ' (restricted for system permissions)' : ''),
                            'Update guard name if needed' . ($this->permission->is_protected ? ' (restricted for system permissions)' : ''),
                            'Modify the permission group',
                            'Update the description',
                            'Modify role assignments',
                            'Review your changes',
                            'Save changes'
                        ],
                        'notes' => $this->permission->is_protected ?
                            'Warning: This is a system permission. Modifying system permissions may affect core functionality.' :
                            'Be cautious when modifying permissions as it may affect user access levels.'
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Edit Permission: ' . $this->permission->name . $isProtected,
                        'type' => 'form',
                        'description' => 'Modify permission details and role assignments.',
                        'steps' => [
                            'Update permission name' . ($this->permission->is_protected ? ' (restricted for system permissions)' : ''),
                            'Update guard name if needed' . ($this->permission->is_protected ? ' (restricted for system permissions)' : ''),
                            'Modify the permission group',
                            'Update the description',
                            'Modify role assignments',
                            'Review your changes',
                            'Save changes'
                        ],
                        'notes' => $this->permission->is_protected ?
                            'Warning: This is a system permission. Modifying system permissions may affect core functionality.' :
                            'Be cautious when modifying permissions as it may affect user access levels.'
                    ];
                }
                break;

            case 'users.permissions.delete':
                if ($this->permission) {
                    $rolesCount = $this->permission->roles->count();
                    $isProtected = $this->permission->is_protected ? ' (System Permission - Cannot be deleted)' : '';

                    $this->resumeTitle = 'Delete Permission: ' . $this->permission->name . $isProtected;
                    $this->resumeDescription = 'Remove a permission from the system. This action cannot be undone.';
                    $this->resumeContentType = 'delete';

                    $warnings = [
                        'This action cannot be undone',
                        $rolesCount . ' role(s) currently have this permission assigned',
                        'Users with roles that have this permission will lose access'
                    ];

                    if ($this->permission->is_protected) {
                        $warnings[] = 'System permissions cannot be deleted';
                    }

                    $this->resumeData = [
                        'title' => 'Delete Permission: ' . $this->permission->name . $isProtected,
                        'description' => $this->permission->is_protected ?
                            'System permissions cannot be deleted as they are required for system functionality.' :
                            'Remove this permission from the system. This action cannot be undone.' .
                            ($rolesCount > 0 ? " {$rolesCount} role(s) will be affected by this action." : ''),
                        'warnings' => $warnings,
                        'stats' => [
                            'Permission Name' => $this->permission->name,
                            'Assigned Roles' => $rolesCount,
                            'Guard Name' => $this->permission->guard_name,
                            'Created' => $this->permission->created_at ? $this->permission->created_at->format('M d, Y') : 'N/A'
                        ]
                    ];

                    // For backward compatibility
                    $this->current_page_resume = [
                        'title' => 'Delete Permission: ' . $this->permission->name . $isProtected,
                        'type' => 'delete',
                        'description' => $this->resumeData['description'],
                        'warnings' => $warnings,
                        'stats' => [
                            'permission_name' => $this->permission->name,
                            'roles_count' => $rolesCount,
                            'guard_name' => $this->permission->guard_name,
                            'created_at' => $this->permission->created_at ? $this->permission->created_at->format('M d, Y') : 'N/A'
                        ]
                    ];
                }
                break;

            default:
                // ... (rest of the code remains the same)
                // Set dynamic page resume properties with general user statistics
                $totalUsers = User::count();
                $activeUsers = User::where('status', 'active')->orWhere('status', 'actif')->count();
                $inactiveUsers = User::where('status', 'inactive')->orWhere('status', 'inactif')->count();

                $this->resumeTitle = 'User Management System';
                $this->resumeDescription = 'Comprehensive user management system for administering user accounts, roles, and permissions.';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'User Management System',
                    'description' => 'Comprehensive user management system for administering user accounts, roles, and permissions. This module provides tools for creating, editing, and managing users across the organization.',
                    'metrics' => [
                        [
                            'label' => 'Total Users',
                            'value' => $totalUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Active Users',
                            'value' => $activeUsers,
                            'change' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100) : 0
                        ],
                        [
                            'label' => 'Inactive Users',
                            'value' => $inactiveUsers,
                            'change' => null
                        ]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume = [
                    'title' => 'User Management System',
                    'type' => 'dashboard',
                    'description' => 'Comprehensive user management system for administering user accounts, roles, and permissions.',
                    'metrics' => [
                        [
                            'label' => 'Total Users',
                            'value' => $totalUsers,
                            'change' => null
                        ],
                        [
                            'label' => 'Active Users',
                            'value' => $activeUsers,
                            'change' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100) : 0
                        ],
                        [
                            'label' => 'Inactive Users',
                            'value' => $inactiveUsers,
                            'change' => null
                        ]
                    ],
                    'sections' => []
                ];
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'user-module',
                'title' => 'Management',
                'description' => 'User management',
                'route' => 'users.index',
                'display' => true,
                'authorized_permissions' => ['manage_user'],
                'section_routes' => ['users.create', 'users.edit', 'users.delete', 'users.show'],
                'sections' => [
                    [
                        'title' => __('Create'),
                        'description' => 'Create a user',
                        'route' => 'users.create',
                        'display' => true,
                        'authorized_permissions' => ['create_user'],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a user',
                        'route' => 'users.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_user'],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a user',
                        'route' => 'users.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_user'],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a user',
                        'route' => 'users.show',
                        'display' => true,
                        'authorized_permissions' => ['show_user'],
                    ]
                ]
            ],
            [
                'module_id' => 'user-hierarchy-module',
                'title' => 'Organization',
                'description' => 'User hierarchy and team management',
                'route' => 'users.hierarchy.index',
                'display' => true,
                'authorized_permissions' => ['manage_user_hierarchy'],
                'section_routes' => ['users.org-chart'],
                'sections' => [
                    [
                        'title' => 'Hierarchy',
                        'description' => 'User hierarchy management',
                        'route' => 'users.hierarchy.index',
                        'display' => true,
                        'authorized_permissions' => ['manage_user_hierarchy'],
                    ],
                    [
                        'title' => 'Org Chart',
                        'description' => 'Organization chart visualization',
                        'route' => 'users.org-chart',
                        'display' => true,
                        'authorized_permissions' => ['show_user_org_chart'],
                    ]
                ]
            ],
            [
                'module_id' => 'role-module',
                'title' => 'Role',
                'description' => 'Manage user roles',
                'route' => 'users.roles.index',
                'display' => true,
                'authorized_permissions' => ['manage_user_roles'],
                'section_routes' => ['users.roles.index', 'users.roles.create', 'users.roles.edit', 'users.roles.delete', 'users.roles.show'],
                'sections' => [
                    [
                        'title' => __('Role management'),
                        'description' => 'Role management',
                        'route' => 'users.roles.index',
                        'display' => true,
                        'authorized_permissions' => ['show_user_roles'],
                    ],
                    [
                        'title' => __('Create role'),
                        'description' => 'Create a role',
                        'route' => 'users.roles.create',
                        'display' => true,
                        'authorized_permissions' => ['create_user_roles'],
                    ],
                    [
                        'title' => __('Edit role'),
                        'description' => 'Edit a role',
                        'route' => 'users.roles.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_user_roles'],
                    ],
                    [
                        'title' => __('Delete role'),
                        'description' => 'Delete a role',
                        'route' => 'users.roles.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_user_roles'],
                    ],
                    [
                        'title' => __('Show role'),
                        'description' => 'Show a role',
                        'route' => 'users.roles.show',
                        'display' => true,
                        'authorized_permissions' => ['show_user_roles'],
                    ]
                ]
            ],
            [
                'module_id' => 'permission-module',
                'title' => 'Permission',
                'description' => 'Manage user permissions',
                'route' => 'users.permissions.index',
                'display' => true,
                'authorized_permissions' => ['manage_user_permissions'],
                'section_routes' => ['users.permissions.index', 'users.permissions.create', 'users.permissions.edit', 'users.permissions.delete', 'users.permissions.show'],
                'sections' => [
                    [
                        'title' => __('Permission management'),
                        'description' => 'Permission management',
                        'route' => 'users.permissions.index',
                        'display' => true,
                        'authorized_permissions' => ['show_user_permissions'],
                    ],
                    [
                        'title' => __('Create permission'),
                        'description' => 'Create a permission',
                        'route' => 'users.permissions.create',
                        'display' => true,
                        'authorized_permissions' => ['create_user_permissions'],
                    ],
                    [
                        'title' => __('Edit permission'),
                        'description' => 'Edit a permission',
                        'route' => 'users.permissions.edit',
                        'display' => true,
                        'authorized_permissions' => ['edit_user_permissions'],
                    ],
                    [
                        'title' => __('Delete permission'),
                        'description' => 'Delete a permission',
                        'route' => 'users.permissions.delete',
                        'display' => true,
                        'authorized_permissions' => ['delete_user_permissions'],
                    ],
                    [
                        'title' => __('Show permission'),
                        'description' => 'Show a permission',
                        'route' => 'users.permissions.show',
                        'display' => true,
                        'authorized_permissions' => ['show_user_permissions'],
                    ]
                ]
            ],
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        // If no component is specified, redirect to users index
        if (empty($this->component)) {
            return redirect()->route('users.index');
        }

        return view('livewire.users.user-page', [
            'user' => $this->user,
            'role' => $this->role,
            'permission' => $this->permission,
        ]);
    }
}
