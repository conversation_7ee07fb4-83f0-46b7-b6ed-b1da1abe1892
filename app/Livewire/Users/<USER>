<?php

namespace App\Livewire\Users;

use App\Livewire\Forms\UserForm;
use App\Models\Campaign;
use App\Models\Department;
use App\Models\Role;
use App\Models\Team;
use App\Models\User;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;

class UserEdit extends Component
{
    use WithFileUploads;
    public User $user;
    public UserForm $form;
    public $profilePictureUrl;

    // Document management
    public $documentTypes = [
        'resume' => 'Resume/CV',
        'id_card' => 'ID Card',
        'certificate' => 'Certificates',
        'other_document' => 'Other Documents'
    ];

    public function mount(User $user)
    {
        $this->user = $user;
        $this->form->setUser($user);
        $this->setProfilePictureUrl();
    }

    /**
     * Set the profile picture URL
     */
    protected function setProfilePictureUrl()
    {
        $this->profilePictureUrl = $this->user->getProfilePictureUrl();
    }

    /**
     * Remove profile picture
     */
    public function removeProfilePicture()
    {
        $this->form->profile_picture = null;
        $this->form->removeProfilePicture($this->user);
        $this->profilePictureUrl = asset('images/default-profile.png');

        // Force refresh the component to update the UI
        $this->dispatch('profile-picture-removed');
    }

    #[On('update-user')]
    public function updateUser()
    {
        $this->form->update($this->user);
        session()->flash('message', 'User updated successfully!');
        $this->redirect(route('users.show', ['user' => $this->user->id]), navigate: true);
    }

    /**
     * Remove a certificate from the array
     */
    public function removeCertificate($index)
    {
        if (isset($this->form->certificates[$index])) {
            unset($this->form->certificates[$index]);
            $this->form->certificates = array_values($this->form->certificates);
        }
    }

    /**
     * Remove an other document from the array
     */
    public function removeOtherDocument($index)
    {
        if (isset($this->form->other_documents[$index])) {
            unset($this->form->other_documents[$index]);
            $this->form->other_documents = array_values($this->form->other_documents);
        }
    }

    /**
     * Remove an existing document
     */
    public function removeExistingDocument($documentId, $category)
    {
        $this->form->removeDocument($this->user, $documentId, $category);
    }

    public function render()
    {
        // Get potential managers (users with management roles)
        $managers = User::whereIn('role_id', [1, 2, 3, 4, 5, 8, 10, 12])
            ->where('id', '!=', $this->user->id) // Exclude the current user
            ->orderBy('first_name')
            ->get();

        // Get active teams
        $teams = Team::where('status', 'active')->orderBy('name')->get();

        return view('livewire.users.user-edit', [
            'roles' => Role::all(),
            'campaigns' => Campaign::all(),
            'departments' => Department::orderBy('name')->get(),
            'managers' => $managers,
            'teams' => $teams,
            'documentTypes' => $this->documentTypes,
        ]);
    }
}
