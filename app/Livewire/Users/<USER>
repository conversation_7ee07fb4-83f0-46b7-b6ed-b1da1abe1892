<?php

namespace App\Livewire\Users;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;

class UserIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'name';
    public string $sortDirection = 'asc';
    public int $perPage = 5;
    public array $selectedUsers = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 5, 'as' => 'pp'],
    ];

    public function mount()
    {
        $this->selectedUsers = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedUsers = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedUsers = $value
            ? $this->users->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleUserSelection(int $userId)
    {
        if (in_array($userId, $this->selectedUsers)) {
            $this->selectedUsers = array_diff($this->selectedUsers, [$userId]);
        } else {
            $this->selectedUsers[] = $userId;
        }

        $currentPageIds = $this->users->pluck('id')->all();
        $this->selectAll = !empty($this->selectedUsers) && empty(array_diff($currentPageIds, $this->selectedUsers));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedUsers)) {
            User::whereIn('id', $this->selectedUsers)->delete();
            $this->selectedUsers = [];
            $this->selectAll = false;
            session()->flash('message', 'Utilisateurs sélectionnés supprimés avec succès !');
            $this->resetPage(); // Réinitialise la page après suppression
        }
    }

    protected function getUsersQuery()
    {
        $query = User::query()
            ->with(['roles', 'media' => function($query) {
                $query->where('category', 'profile_picture');
            }])
            ->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                  ->orWhere('last_name', 'like', $searchTerm)
                  ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    public function getUsersProperty()
    {
        return $this->getUsersQuery()->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.users.user-index', [
            'users' => $this->users,
        ]);
    }
}
