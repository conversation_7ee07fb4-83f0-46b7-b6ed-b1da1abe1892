<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Log;

class UserRoleDelete extends Component
{
    public Role $role;
    public bool $forceDelete = false;
    public $name;

    public function mount(Role $role)
    {
        $this->role = $role;
        $this->name = $role->name;
    }

    public function toggleForceDelete()
    {
        $this->forceDelete = !$this->forceDelete;
    }

    #[On('destroy-role')]
    public function destroyRole()
    {
        try {
            Log::info('Attempting to delete role: ' . $this->role->id);
            
            // Check if role is protected
            if ($this->role->is_protected) {
                throw new \Exception('This is a protected role and cannot be deleted.');
            }

            // Check if role has users assigned
            if ($this->role->users()->count() > 0 && !$this->forceDelete) {
                throw new \Exception('This role has users assigned. Enable force delete to remove it anyway.');
            }

            // If force delete, detach all users first
            if ($this->forceDelete) {
                $this->role->users()->detach();
            }

            // Delete the role
            $this->role->delete();

            Log::info('Role deleted successfully: ' . $this->role->id);
            session()->flash('message', [
                'type' => 'success',
                'text' => 'Role deleted successfully!'
            ]);

            // Redirect to roles index
            return $this->redirect(route('users.roles.index'), navigate: true);
        } catch (\Exception $e) {
            Log::error('Error deleting role: ' . $e->getMessage());
            $this->addError('delete_error', 'Error deleting role: ' . $e->getMessage());
            return null;
        }
    }

    public function render()
    {
        return view('livewire.users.user-role-delete', [
            'usersCount' => $this->role->users()->count(),
            'permissionsCount' => $this->role->permissions()->count()
        ]);
    }
}
