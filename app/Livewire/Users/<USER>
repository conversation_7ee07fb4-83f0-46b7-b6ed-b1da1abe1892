<?php

namespace App\Livewire\Users;

use App\Models\Role;
use Livewire\Component;
use Livewire\WithPagination;

class UserRoleIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'name';
    public string $sortDirection = 'asc';
    public int $perPage = 5;
    public array $selectedRoles = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 5, 'as' => 'pp'],
    ];

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedRoles = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedRoles = $value
            ? $this->roles->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleRoleSelection(int $roleId)
    {
        if (in_array($roleId, $this->selectedRoles)) {
            $this->selectedRoles = array_diff($this->selectedRoles, [$roleId]);
        } else {
            $this->selectedRoles[] = $roleId;
        }

        $currentPageIds = $this->roles->pluck('id')->all();
        $this->selectAll = !empty($this->selectedRoles) && empty(array_diff($currentPageIds, $this->selectedRoles));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedRoles)) {
            Role::whereIn('id', $this->selectedRoles)->delete();
            $this->selectedRoles = [];
            $this->selectAll = false;
            session()->flash('message', 'Rôles sélectionnés supprimés avec succès !');
            $this->resetPage();
        }
    }

    protected function getRolesQuery()
    {
        return Role::query()
            ->when($this->search, function ($query, $search) {
                $searchTerm = '%' . trim($search) . '%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'like', $searchTerm)
                      ->orWhere('description', 'like', $searchTerm);
                });
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function getRolesProperty()
    {
        return $this->getRolesQuery()->paginate($this->perPage);
    }

    public function exportRoles()
    {
        return response()->download(Role::export($this->roles));
    }

    public function render()
    {
        return view('livewire.users.user-role-index', [
            'roles' => $this->roles,
        ]);
    }
}
