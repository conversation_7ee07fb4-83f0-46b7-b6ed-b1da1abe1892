<?php

namespace App\Livewire\Users;

use App\Models\Skill;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class UserSkills extends Component
{
    use WithPagination;
    
    public User $user;
    public $search = '';
    public $category = '';
    public $perPage = 10;
    
    // For adding new skill
    public $selectedSkill = null;
    public $proficiencyLevel = 1;
    public $notes = '';
    public $acquiredAt = null;
    
    // For editing skill
    public $editingSkillId = null;
    public $editProficiencyLevel = 1;
    public $editNotes = '';
    public $editAcquiredAt = null;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'category' => ['except' => ''],
        'perPage' => ['except' => 10],
    ];
    
    protected $rules = [
        'selectedSkill' => 'required|exists:skills,id',
        'proficiencyLevel' => 'required|integer|min:1|max:5',
        'notes' => 'nullable|string',
        'acquiredAt' => 'nullable|date',
        'editProficiencyLevel' => 'required|integer|min:1|max:5',
        'editNotes' => 'nullable|string',
        'editAcquiredAt' => 'nullable|date',
    ];
    
    public function mount(User $user)
    {
        $this->user = $user;
        $this->acquiredAt = now()->format('Y-m-d');
    }
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingCategory()
    {
        $this->resetPage();
    }
    
    public function addSkill()
    {
        $this->validate([
            'selectedSkill' => 'required|exists:skills,id',
            'proficiencyLevel' => 'required|integer|min:1|max:5',
            'notes' => 'nullable|string',
            'acquiredAt' => 'nullable|date',
        ]);
        
        // Check if user already has this skill
        if ($this->user->skills()->where('skill_id', $this->selectedSkill)->exists()) {
            session()->flash('error', 'User already has this skill.');
            return;
        }
        
        $this->user->skills()->attach($this->selectedSkill, [
            'proficiency_level' => $this->proficiencyLevel,
            'notes' => $this->notes,
            'acquired_at' => $this->acquiredAt,
            'last_verified_at' => now(),
            'verified_by' => auth()->id(),
        ]);
        
        session()->flash('message', 'Skill added successfully.');
        
        // Reset form
        $this->selectedSkill = null;
        $this->proficiencyLevel = 1;
        $this->notes = '';
        $this->acquiredAt = now()->format('Y-m-d');
    }
    
    public function startEditing($skillId)
    {
        $this->editingSkillId = $skillId;
        
        $userSkill = $this->user->skills()->where('skill_id', $skillId)->first()->pivot;
        
        $this->editProficiencyLevel = $userSkill->proficiency_level;
        $this->editNotes = $userSkill->notes;
        $this->editAcquiredAt = $userSkill->acquired_at ? $userSkill->acquired_at->format('Y-m-d') : null;
    }
    
    public function cancelEditing()
    {
        $this->editingSkillId = null;
        $this->editProficiencyLevel = 1;
        $this->editNotes = '';
        $this->editAcquiredAt = null;
    }
    
    public function updateSkill()
    {
        $this->validate([
            'editProficiencyLevel' => 'required|integer|min:1|max:5',
            'editNotes' => 'nullable|string',
            'editAcquiredAt' => 'nullable|date',
        ]);
        
        $this->user->skills()->updateExistingPivot($this->editingSkillId, [
            'proficiency_level' => $this->editProficiencyLevel,
            'notes' => $this->editNotes,
            'acquired_at' => $this->editAcquiredAt,
            'last_verified_at' => now(),
            'verified_by' => auth()->id(),
        ]);
        
        session()->flash('message', 'Skill updated successfully.');
        
        $this->cancelEditing();
    }
    
    public function removeSkill($skillId)
    {
        $this->user->skills()->detach($skillId);
        
        session()->flash('message', 'Skill removed successfully.');
    }
    
    public function render()
    {
        $userSkills = $this->user->skills()
            ->when($this->search, function ($query) {
                return $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->when($this->category, function ($query) {
                return $query->where('category', $this->category);
            })
            ->paginate($this->perPage);
            
        $availableSkills = Skill::where('is_active', true)
            ->whereNotIn('id', $this->user->skills->pluck('id'))
            ->get();
            
        $categories = Skill::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category');
            
        return view('livewire.users.user-skills', [
            'userSkills' => $userSkills,
            'availableSkills' => $availableSkills,
            'categories' => $categories,
        ]);
    }
}
