<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\WithFileUploads;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;

class UserRoleCreate extends Component
{
    use WithFileUploads;

    // Form properties
    public $name = '';
    public $guard_name = 'web';
    public $selectedPermissions = [];
    public $search = '';
    public $expandedGroups = [];
    
    // Data properties
    public $permissionGroups = [];
    public $allPermissions = [];

    // UI State
    public $isSubmitting = false;
    public $showSuccess = false;
    public $successMessage = '';

    protected $rules = [
        'name' => ['required', 'string', 'max:255', 'unique:roles,name'],
        'guard_name' => ['required', 'string', 'in:web,api'],
        'selectedPermissions' => ['array'],
        'selectedPermissions.*' => ['string', 'exists:permissions,name'],
    ];

    protected $messages = [
        'name.required' => 'The role name is required.',
        'name.unique' => 'A role with this name already exists.',
        'guard_name.required' => 'The guard name is required.',
        'selectedPermissions.array' => 'Invalid permissions format.',
    ];

    public function mount()
    {
        $this->loadPermissions();
    }

    protected function loadPermissions()
    {
        $permissions = Permission::orderBy('name')->get();
        
        // Convert to array to avoid issues with Livewire and morphClass
        $this->allPermissions = $permissions->map(function($permission) {
            return [
                'id' => $permission->id,
                'name' => $permission->name,
                'guard_name' => $permission->guard_name,
                'created_at' => $permission->created_at,
                'updated_at' => $permission->updated_at,
            ];
        })->toArray();
        
        // Group permissions by their first segment (e.g., 'user.create' -> 'user')
        $grouped = $permissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        })->map(function($group) {
            return $group->map(function($perm) {
                return [
                    'id' => $perm->id,
                    'name' => $perm->name,
                    'guard_name' => $perm->guard_name,
                    'created_at' => $perm->created_at,
                    'updated_at' => $perm->updated_at,
                ];
            })->toArray();
        })->toArray();
        
        // Sort groups and set expanded state
        ksort($grouped);
        $this->permissionGroups = $grouped;
        
        // Initialize expanded groups
        foreach (array_keys($grouped) as $group) {
            if (!isset($this->expandedGroups[$group])) {
                $this->expandedGroups[$group] = true;
            }
        }
    }

    public function createRole()
    {
        $this->validate();
        $this->isSubmitting = true;

        try {
            // Create the role
            $role = Role::create([
                'name' => $this->name,
                'guard_name' => $this->guard_name,
            ]);

            // Sync permissions if any selected
            if (!empty($this->selectedPermissions)) {
                $role->syncPermissions($this->selectedPermissions);
            }

            // Set success message
            $this->successMessage = 'Role created successfully!';
            $this->showSuccess = true;

            // Reset form
            $this->reset(['name', 'selectedPermissions']);
            $this->resetErrorBag();
            $this->resetValidation();

            // Dispatch event to refresh roles list
            $this->dispatch('role-created', roleId: $role->id);

        } catch (\Exception $e) {
            $this->addError('create_error', 'Failed to create role: ' . $e->getMessage());
        } finally {
            $this->isSubmitting = false;
        }
    }

    public function render()
    {
        // Filter permissions based on search
        $filteredGroups = [];
        $searchTerm = strtolower($this->search);
        
        foreach ($this->permissionGroups as $group => $permissions) {
            $filtered = array_filter($permissions, function($permission) use ($searchTerm) {
                return empty($searchTerm) || 
                       str_contains(strtolower($permission['name']), $searchTerm) ||
                       str_contains(strtolower($group), $searchTerm);
            });
            
            if (!empty($filtered) || empty($searchTerm)) {
                $filteredGroups[$group] = $filtered;
            }
        }
        
        return view('livewire.users.user-role-create', [
            'permissionGroups' => $filteredGroups,
            'allPermissions' => $this->allPermissions,
        ]);
    }
}
