<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\Attributes\Url;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserRoleShow extends Component
{
    #[Url(as: 'role')]
    public $roleId;
    public $role;
    public $permissions = [];
    public $users = [];
    public $permissionGroups = [];
    public $totalUsers = 0;
    public $totalPermissions = 0;
    public $period = 'month'; // 'week', 'month', 'quarter', 'year'
    public $startDate;
    public $endDate;

    public function mount($role = null)
    {
        $this->roleId = $role?->id ?? $this->roleId;
        $this->loadRole();
        $this->setDateRange();
        $this->loadRoleData();
    }

    protected function setDateRange()
    {
        $now = Carbon::now();
        
        switch ($this->period) {
            case 'week':
                $this->startDate = $now->copy()->startOfWeek();
                $this->endDate = $now->copy()->endOfWeek();
                break;
            case 'month':
                $this->startDate = $now->copy()->startOfMonth();
                $this->endDate = $now->copy()->endOfMonth();
                break;
            case 'quarter':
                $this->startDate = $now->copy()->startOfQuarter();
                $this->endDate = $now->copy()->endOfQuarter();
                break;
            case 'year':
                $this->startDate = $now->copy()->startOfYear();
                $this->endDate = $now->copy()->endOfYear();
                break;
            default:
                $this->startDate = $now->copy()->startOfMonth();
                $this->endDate = $now->copy()->endOfMonth();
        }
    }

    public function loadRole()
    {
        if ($this->roleId) {
            $this->role = Role::with(['permissions', 'users'])->findOrFail($this->roleId);
        }
    }

    protected function loadRoleData()
    {
        if (!$this->role) return;

        $this->permissions = $this->role->permissions;
        $this->users = $this->role->users()->with('roles')->get();
        $this->totalUsers = $this->users->count();
        $this->totalPermissions = $this->permissions->count();
        
        // Group permissions by their first segment (e.g., 'user.create' -> 'user')
        $this->permissionGroups = $this->permissions->groupBy(function ($permission) {
            return explode('.', $permission->name)[0] ?? 'other';
        })->sortKeys();
    }

    public function updatedPeriod()
    {
        $this->setDateRange();
        $this->loadRoleData();
    }

    public function render()
    {
        if (!$this->role) {
            return view('livewire.users.user-role-not-found');
        }

        return view('livewire.users.user-role-show', [
            'role' => $this->role,
            'permissions' => $this->permissions,
            'users' => $this->users,
            'permissionGroups' => $this->permissionGroups,
            'totalUsers' => $this->totalUsers,
            'totalPermissions' => $this->totalPermissions,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ]);
    }
}
