<?php

namespace App\Livewire\Users;

use App\Models\Permission;
use Livewire\Component;
use Livewire\WithPagination;

class UserPermissionIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'name';
    public string $sortDirection = 'asc';
    public int $perPage = 5;
    public array $selectedPermissions = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 5, 'as' => 'pp'],
    ];

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedPermissions = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedPermissions = $value
            ? $this->permissions->pluck('id')->map('intval')->all()
            : [];
    }

    public function togglePermissionSelection(int $permissionId)
    {
        if (in_array($permissionId, $this->selectedPermissions)) {
            $this->selectedPermissions = array_diff($this->selectedPermissions, [$permissionId]);
        } else {
            $this->selectedPermissions[] = $permissionId;
        }

        $currentPageIds = $this->permissions->pluck('id')->all();
        $this->selectAll = !empty($this->selectedPermissions) && empty(array_diff($currentPageIds, $this->selectedPermissions));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedPermissions)) {
            Permission::whereIn('id', $this->selectedPermissions)->delete();
            $this->selectedPermissions = [];
            $this->selectAll = false;
            session()->flash('message', 'Permissions sélectionnées supprimées avec succès !');
            $this->resetPage();
        }
    }

    protected function getPermissionsQuery()
    {
        return Permission::query()
            ->when($this->search, function ($query, $search) {
                $searchTerm = '%' . trim($search) . '%';
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'like', $searchTerm)
                      ->orWhere('description', 'like', $searchTerm);
                });
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function getPermissionsProperty()
    {
        return $this->getPermissionsQuery()->paginate($this->perPage);
    }

    public function exportPermissions()
    {
        return response()->download(Permission::export($this->permissions));
    }

    public function render()
    {
        return view('livewire.users.user-permission-index', [
            'permissions' => $this->permissions,
        ]);
    }
}
