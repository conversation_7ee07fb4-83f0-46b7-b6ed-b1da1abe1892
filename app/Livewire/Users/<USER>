<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\Attributes\Url;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UserPermissionShow extends Component
{
    #[Url(as: 'permission')]
    public $permissionId;
    public $permission;
    public $roles = [];
    public $users = [];
    public $totalRoles = 0;
    public $totalUsers = 0;
    public $period = 'month'; // 'week', 'month', 'quarter', 'year'
    public $startDate;
    public $endDate;
    public $permissionUsage = [];

    public function mount($permission = null)
    {
        $this->permissionId = $permission?->id ?? $this->permissionId;
        $this->loadPermission();
        $this->setDateRange();
        $this->loadPermissionData();
    }

    protected function setDateRange()
    {
        $now = Carbon::now();
        
        switch ($this->period) {
            case 'week':
                $this->startDate = $now->copy()->startOfWeek();
                $this->endDate = $now->copy()->endOfWeek();
                break;
            case 'month':
                $this->startDate = $now->copy()->startOfMonth();
                $this->endDate = $now->copy()->endOfMonth();
                break;
            case 'quarter':
                $this->startDate = $now->copy()->startOfQuarter();
                $this->endDate = $now->copy()->endOfQuarter();
                break;
            case 'year':
                $this->startDate = $now->copy()->startOfYear();
                $this->endDate = $now->copy()->endOfYear();
                break;
            default:
                $this->startDate = $now->copy()->startOfMonth();
                $this->endDate = $now->copy()->endOfMonth();
        }
    }

    public function loadPermission()
    {
        if ($this->permissionId) {
            $this->permission = Permission::with(['roles', 'users'])
                ->findOrFail($this->permissionId);
        }
    }

    protected function loadPermissionData()
    {
        if (!$this->permission) return;

        $this->roles = $this->permission->roles;
        $this->users = $this->permission->users()->with('roles')->get();
        $this->totalRoles = $this->roles->count();
        $this->totalUsers = $this->users->count();
        
        // Load permission usage statistics (example - customize based on your needs)
        $this->loadPermissionUsage();
    }

    protected function loadPermissionUsage()
    {
        // Example: Load permission usage data
        // This is a placeholder - replace with actual permission usage tracking
        $this->permissionUsage = [
            'last_used' => now()->subDays(rand(0, 30))->format('Y-m-d H:i:s'),
            'usage_count' => rand(10, 1000),
            'assigned_to_roles' => $this->totalRoles,
            'directly_assigned' => $this->totalUsers,
        ];
    }

    public function updatedPeriod()
    {
        $this->setDateRange();
        $this->loadPermissionData();
    }

    public function render()
    {
        if (!$this->permission) {
            return view('livewire.users.user-permission-not-found');
        }

        return view('livewire.users.user-permission-show', [
            'permission' => $this->permission,
            'roles' => $this->roles,
            'users' => $this->users,
            'totalRoles' => $this->totalRoles,
            'totalUsers' => $this->totalUsers,
            'permissionUsage' => $this->permissionUsage,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ]);
    }
}
