<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Spatie\Permission\Models\Permission;

class PermissionEdit extends Component
{
    public $permission;
    public $name;
    public $guard_name;
    public $description;

    protected $rules = [
        'name' => 'required|string|max:255|unique:permissions,name,{{ $permission->id }}',
        'guard_name' => 'required|string',
        'description' => 'nullable|string',
    ];

    public function mount($permissionId)
    {
        $this->permission = Permission::findOrFail($permissionId);
        $this->name = $this->permission->name;
        $this->guard_name = $this->permission->guard_name;
        $this->description = $this->permission->description;
    }

    public function updatePermission()
    {
        $validatedData = $this->validate();

        try {
            $this->permission->update($validatedData);
            
            $this->dispatch('permission-updated', $this->permission->id);
            
            session()->flash('success', 'Permission updated successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Error updating permission: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.users.permission-edit');
    }
}
