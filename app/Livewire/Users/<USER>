<?php

namespace App\Livewire\Users;

use Livewire\Component;

use Spatie\Permission\Models\Role;

class RoleIndex extends Component
{
    public $roles = [];
    public $selectAll = false;
    public $selectedRoles = [];

    public function mount()
    {
        $this->roles = Role::all();
    }

    public function deleteSelected()
    {
        if (empty($this->selectedRoles)) {
            return;
        }

        Role::whereIn('id', $this->selectedRoles)->delete();
        $this->selectedRoles = [];
        $this->selectAll = false;
        $this->roles = Role::all();
    }

    public function render()
    {
        return view('livewire.users.role-index', [
            'roles' => $this->roles,
            'selectAll' => $this->selectAll,
            'selectedRoles' => $this->selectedRoles,
        ]);
    }
}
