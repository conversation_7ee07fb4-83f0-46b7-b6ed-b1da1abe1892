<?php

namespace App\Livewire\Users;

use App\Livewire\Forms\UserForm;
use App\Models\Campaign;
use App\Models\EmployeeOnboarding;
use App\Models\OnboardingTemplate;
use App\Models\Role;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class UserCreate extends Component
{
    use WithFileUploads;

    public UserForm $form;
    public bool $showSuccessMessage = false;

    // Document management
    public $documentTypes = [
        'resume' => 'Resume/CV',
        'id_card' => 'ID Card',
        'certificate' => 'Certificates',
        'other_document' => 'Other Documents'
    ];

    // Onboarding options
    public $startOnboarding = true;
    public $onboardingTemplateId;
    public $onboardingStartDate;
    public $onboardingAssignedTo;

    // Password visibility toggles
    public $showPassword = false;
    public $showPasswordConfirmation = false;

    public function mount()
    {
        // Initialize the form with default values if needed
        $this->form->status = 'in_training';

        // Initialize empty arrays for file uploads
        $this->form->certificates = [];
        $this->form->other_documents = [];

        // Initialize onboarding fields
        $this->onboardingStartDate = now()->format('Y-m-d');
        $this->onboardingAssignedTo = auth()->id();

        // Try to find a default template
        $defaultTemplate = OnboardingTemplate::where('is_active', true)
            ->orderBy('id', 'asc')
            ->first();

        if ($defaultTemplate) {
            $this->onboardingTemplateId = $defaultTemplate->id;
        }
    }

    public function updated($property)
    {
        // Validate the field that was just updated
        $this->validateOnly($property);

        // Show campaign field only for manager role
        if ($property === 'form.role_id') {
            $role = Role::find($this->form->role_id);
            $this->form->canDisplayCampaign = $role && $role->name === 'manager';
        }
    }

    public function validateOnly($field, $rules = null, $messages = [], $attributes = [], $dataOverrides = [])
    {
        // This method is used to provide real-time validation feedback
        if (str_starts_with($field, 'form.')) {
            $formField = substr($field, 5); // Remove 'form.' prefix
            $this->form->validateOnly($formField);
        } else {
            // Call the parent validateOnly method for non-form fields
            parent::validateOnly($field, $rules, $messages, $attributes, $dataOverrides);
        }
    }

    public function submit()
    {
        try {
            // Validate all form fields before storing
            $validatedData = $this->form->validateAllFields();

            // Validate onboarding fields if starting onboarding
            if ($this->startOnboarding) {
                $this->validate([
                    'onboardingTemplateId' => 'nullable',
                    'onboardingStartDate' => 'required|date|after_or_equal:today',
                    'onboardingAssignedTo' => 'nullable|exists:users,id',
                ]);
            }

            // Store the user
            $user = $this->form->store();

            if (!$user) {
                throw new \Exception('Failed to create user. Please check the form data and try again.');
            }

            $this->showSuccessMessage = true;

            // Create onboarding process if requested and a template is selected
            if ($this->startOnboarding && !empty($this->onboardingTemplateId) && $user) {
                $template = OnboardingTemplate::find($this->onboardingTemplateId);

                if ($template) {
                    // Calculate target completion date based on template
                    $maxDueDays = $template->tasks()->max('due_days') ?? 14;
                    $targetDate = date('Y-m-d', strtotime($this->onboardingStartDate . ' + ' . $maxDueDays . ' days'));

                    // Create the onboarding process
                    $onboarding = EmployeeOnboarding::create([
                        'user_id' => $user->id,
                        'template_id' => $this->onboardingTemplateId,
                        'start_date' => $this->onboardingStartDate,
                        'target_completion_date' => $targetDate,
                        'status' => 'pending',
                        'progress_percentage' => 0,
                        'assigned_to' => $this->onboardingAssignedTo,
                        'notes' => 'Created during user registration',
                    ]);

                    // Create tasks from the template
                    $onboarding->createTasksFromTemplate();

                    session()->flash('onboarding_message', 'Onboarding process started successfully!');
                }
            } else if ($this->startOnboarding && empty($this->onboardingTemplateId) && $user) {
                // If onboarding is requested but no template is selected, just log a message
                \Illuminate\Support\Facades\Log::info('User created without onboarding template', [
                    'user_id' => $user->id,
                    'message' => 'Onboarding was requested but no template was selected'
                ]);

                session()->flash('warning', 'User created successfully, but no onboarding template was selected.');
            }

            // Dispatch event for any listeners
            $this->dispatch('user-created-successfully');

            // Redirect to user index
            session()->flash('message', 'User created successfully!');
            return $this->redirect(route('users.index'), navigate: true);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // This ensures validation errors are added to the error bag
            $this->setErrorBag($e->validator->getMessageBag());
            return;
        } catch (\PDOException $e) {
            // Handle database exceptions
            \Illuminate\Support\Facades\Log::error('Database error creating user: ' . $e->getMessage(), [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Database error creating user: ' . $e->getMessage());
            return;
        } catch (\Exception $e) {
            // Handle other exceptions
            \Illuminate\Support\Facades\Log::error('Error creating user: ' . $e->getMessage(), [
                'exception' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Error creating user: ' . $e->getMessage());
            return;
        }
    }

    #[On('create-user')]
    public function storeUser()
    {
        return $this->submit();
    }

    /**
     * Remove a certificate from the array
     */
    public function removeCertificate($index)
    {
        if (isset($this->form->certificates[$index])) {
            unset($this->form->certificates[$index]);
            $this->form->certificates = array_values($this->form->certificates);

            // Also remove metadata for this certificate
            if (isset($this->form->certificate_titles[$index])) {
                $titles = $this->form->certificate_titles;
                unset($titles[$index]);
                $this->form->certificate_titles = array_values($titles);
            }

            if (isset($this->form->certificate_descriptions[$index])) {
                $descriptions = $this->form->certificate_descriptions;
                unset($descriptions[$index]);
                $this->form->certificate_descriptions = array_values($descriptions);
            }

            if (isset($this->form->certificate_expiry_dates[$index])) {
                $expiryDates = $this->form->certificate_expiry_dates;
                unset($expiryDates[$index]);
                $this->form->certificate_expiry_dates = array_values($expiryDates);
            }
        }
    }

    /**
     * Remove an other document from the array
     */
    public function removeOtherDocument($index)
    {
        if (isset($this->form->other_documents[$index])) {
            unset($this->form->other_documents[$index]);
            $this->form->other_documents = array_values($this->form->other_documents);

            // Also remove metadata for this document
            if (isset($this->form->other_document_titles[$index])) {
                $titles = $this->form->other_document_titles;
                unset($titles[$index]);
                $this->form->other_document_titles = array_values($titles);
            }

            if (isset($this->form->other_document_descriptions[$index])) {
                $descriptions = $this->form->other_document_descriptions;
                unset($descriptions[$index]);
                $this->form->other_document_descriptions = array_values($descriptions);
            }

            if (isset($this->form->other_document_expiry_dates[$index])) {
                $expiryDates = $this->form->other_document_expiry_dates;
                unset($expiryDates[$index]);
                $this->form->other_document_expiry_dates = array_values($expiryDates);
            }
        }
    }

    /**
     * Remove an existing document
     */
    public function removeExistingDocument($documentId, $category)
    {
        if (isset($this->user)) {
            $this->form->removeDocument($this->user, $documentId, $category);
        }
    }

    /**
     * Remove profile picture
     */
    public function removeProfilePicture()
    {
        $this->form->profile_picture = null;
    }

    public function render()
    {
        // Get HR staff for onboarding assignment
        $hrStaff = \App\Models\User::whereHas('roles', function($q) {
                $q->whereIn('name', ['administrator', 'director', 'platform_manager', 'supervisor', 'hr_manager']);
            })
            ->where('status', 'active')
            ->orderBy('first_name')
            ->get();

        // Get active onboarding templates
        $onboardingTemplates = OnboardingTemplate::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Get departments
        $departments = \App\Models\Department::where('status', 'active')
            ->orderBy('name')
            ->get();

        return view('livewire.users.user-create', [
            'roles' => Role::all(),
            'campaigns' => Campaign::all(),
            'departments' => $departments,
            'documentTypes' => $this->documentTypes,
            'onboardingTemplates' => $onboardingTemplates,
            'hrStaff' => $hrStaff,
        ]);
    }
}
