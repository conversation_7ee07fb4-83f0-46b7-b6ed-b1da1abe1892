<?php

namespace App\Livewire\Users;

use App\Models\Department;
use App\Models\Team;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class UserHierarchy extends Component
{
    use WithPagination;

    public $selectedDepartmentId = null;
    public $selectedTeamId = null;
    public $selectedUserId = null;
    public $searchQuery = '';
    public $viewMode = 'hierarchy'; // hierarchy, department, team

    // For data loading
    public $departments = [];
    public $teams = [];
    public $selectedTeam = null;

    protected $queryString = [
        'selectedDepartmentId' => ['except' => null],
        'selectedTeamId' => ['except' => null],
        'selectedUserId' => ['except' => null],
        'viewMode' => ['except' => 'hierarchy'],
        'searchQuery' => ['except' => ''],
    ];

    public function mount()
    {
        $this->departments = Department::orderBy('name')->get();
        $this->teams = Team::orderBy('name')->get();

        // If a team is selected, load its data
        if ($this->selectedTeamId) {
            $this->loadSelectedTeam();
        }
    }

    public function loadSelectedTeam()
    {
        $team = Team::with('members')->find($this->selectedTeamId);
        if ($team) {
            $this->selectedTeam = $team;
        }
    }

    public function selectDepartment($departmentId)
    {
        $this->selectedDepartmentId = $departmentId;
        $this->viewMode = 'department';
        $this->resetPage();
    }

    public function selectTeam($teamId)
    {
        $this->selectedTeamId = $teamId;
        $this->viewMode = 'team';
        $this->loadSelectedTeam();
        $this->resetPage();
    }

    public function editUser($userId)
    {
        return $this->redirect(route('users.hierarchy.edit', ['userId' => $userId]), navigate: true);
    }

    public function manageTeamMembers($teamId)
    {
        return $this->redirect(route('users.team.members', ['teamId' => $teamId]), navigate: true);
    }

    public function viewOrgChart()
    {
        $this->dispatch('to-org-chart');
    }

    public function render()
    {
        $users = User::query()
            ->when($this->searchQuery, function ($query) {
                $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->searchQuery . '%')
                      ->orWhere('last_name', 'like', '%' . $this->searchQuery . '%')
                      ->orWhere('email', 'like', '%' . $this->searchQuery . '%')
                      ->orWhere('job_title', 'like', '%' . $this->searchQuery . '%');
                });
            })
            ->when($this->viewMode === 'department' && $this->selectedDepartmentId, function ($query) {
                $query->where('department_id', $this->selectedDepartmentId);
            })
            ->when($this->viewMode === 'team' && $this->selectedTeamId, function ($query) {
                $query->whereHas('teams', function ($q) {
                    $q->where('team_id', $this->selectedTeamId)
                      ->wherePivotNull('left_at');
                });
            })
            ->with(['department', 'manager', 'directReports', 'teams'])
            ->orderBy('last_name')
            ->paginate(10);

        return view('livewire.users.user-hierarchy', [
            'users' => $users,
            'departments' => $this->departments,
            'teams' => $this->teams,
            'selectedDepartment' => $this->selectedDepartmentId ? Department::find($this->selectedDepartmentId) : null,
            'selectedTeam' => $this->selectedTeam,
        ]);
    }
}
