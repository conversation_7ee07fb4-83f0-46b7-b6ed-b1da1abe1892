<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Spatie\Permission\Models\Permission;

class PermissionCreate extends Component
{
    public $name;
    public $guard_name = 'web';
    public $description;

    protected $rules = [
        'name' => 'required|string|max:255|unique:permissions',
        'guard_name' => 'required|string',
        'description' => 'nullable|string',
    ];

    public function createPermission()
    {
        $validatedData = $this->validate();

        try {
            Permission::create($validatedData);
            
            $this->reset(['name', 'description']);
            
            $this->dispatch('permission-created');
            
            session()->flash('success', 'Permission created successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Error creating permission: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.users.permission-create');
    }
}
