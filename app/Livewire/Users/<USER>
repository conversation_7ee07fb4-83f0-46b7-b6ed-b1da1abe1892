<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleEdit extends Component
{
    public $role;
    public $name;
    public $guard_name;
    public $selectedPermissions = [];

    protected $rules = [
        'name' => 'required|string|max:255|unique:roles,name,{{ $role->id }}',
        'guard_name' => 'required|string',
    ];

    public function mount($roleId)
    {
        $this->role = Role::findOrFail($roleId);
        $this->name = $this->role->name;
        $this->guard_name = $this->role->guard_name;
        $this->selectedPermissions = $this->role->permissions->pluck('id')->toArray();
    }

    public function updateRole()
    {
        $validatedData = $this->validate();

        try {
            $this->role->update($validatedData);
            
            // Sync permissions
            $this->role->permissions()->sync($this->selectedPermissions);

            $this->dispatch('role-updated', $this->role->id);
            
            session()->flash('success', 'Role updated successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Error updating role: ' . $e->getMessage());
        }
    }

    public function render()
    {
        $permissions = Permission::pluck('name', 'id')->toArray();

        return view('livewire.users.role-edit', [
            'permissions' => $permissions,
        ]);
    }
}
