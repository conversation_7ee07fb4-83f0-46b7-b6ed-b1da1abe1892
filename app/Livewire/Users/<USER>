<?php

namespace App\Livewire\Users;

use Livewire\Component;
use Livewire\WithPagination;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Validation\Rule;

class UserRoleEdit extends Component
{
    use WithPagination;

    public Role $role;
    public $permissions;
    public $selectedPermissions = [];
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';

    protected $queryString = [
        'search' => ['except' => ''],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    protected $listeners = ['refreshComponent' => '$refresh'];

    public function mount(Role $role)
    {
        $this->authorize('update', $role);
        $this->role = $role;
        $this->selectedPermissions = $role->permissions->pluck('id')->toArray();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    public function togglePermission($permissionId, $checked)
    {
        if ($checked && !in_array($permissionId, $this->selectedPermissions)) {
            $this->selectedPermissions[] = $permissionId;
        } else {
            $this->selectedPermissions = array_diff($this->selectedPermissions, [$permissionId]);
        }
    }

    public function save()
    {
        $this->validate([
            'selectedPermissions' => 'required|array|min:1',
            'selectedPermissions.*' => 'exists:permissions,id',
        ], [
            'selectedPermissions.required' => 'Please select at least one permission.',
            'selectedPermissions.*.exists' => 'One or more selected permissions are invalid.',
        ]);

        try {
            $this->role->syncPermissions($this->selectedPermissions);
            
            session()->flash('message', [
                'type' => 'success',
                'text' => 'Role permissions updated successfully!'
            ]);
            
            $this->redirect(route('users.roles.show', $this->role), navigate: true);
        } catch (\Exception $e) {
            $this->addError('error', 'Error updating role permissions: ' . $e->getMessage());
        }
    }

    public function render()
    {
        $query = Permission::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->orderBy($this->sortField, $this->sortDirection);

        $permissions = $query->paginate($this->perPage);

        return view('livewire.users.user-role-edit', [
            'permissions' => $permissions,
            'assignedPermissions' => $this->role->permissions->pluck('id')->toArray(),
        ]);
    }
}
