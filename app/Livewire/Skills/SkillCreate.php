<?php

namespace App\Livewire\Skills;

use App\Models\Skill;
use Livewire\Component;

class SkillCreate extends Component
{
    public $name = '';
    public $category = '';
    public $description = '';
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255|unique:skills,name',
        'category' => 'nullable|string|max:255',
        'description' => 'nullable|string',
        'is_active' => 'boolean',
    ];

    public function save()
    {
        $this->validate();

        Skill::create([
            'name' => $this->name,
            'category' => $this->category,
            'description' => $this->description,
            'is_active' => $this->is_active,
        ]);

        session()->flash('message', 'Skill created successfully.');
        
        return $this->redirect(route('skills.index'), navigate: true);
    }

    public function render()
    {
        $categories = Skill::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category');
            
        return view('livewire.skills.skill-create', [
            'categories' => $categories,
        ]);
    }
}
