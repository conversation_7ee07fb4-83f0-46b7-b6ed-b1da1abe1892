<?php

namespace App\Livewire\Skills;

use App\Models\Skill;
use Livewire\Component;
use Livewire\WithPagination;

class SkillIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $category = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';

    protected $queryString = [
        'search' => ['except' => ''],
        'category' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategory()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function toggleSkillStatus(Skill $skill)
    {
        $skill->update([
            'is_active' => !$skill->is_active
        ]);
        
        session()->flash('message', 'Skill status updated successfully.');
    }

    public function render()
    {
        $skills = Skill::query()
            ->when($this->search, function ($query) {
                return $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->when($this->category, function ($query) {
                return $query->where('category', $this->category);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        $categories = Skill::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category');

        return view('livewire.skills.skill-index', [
            'skills' => $skills,
            'categories' => $categories,
        ]);
    }
}
