<?php

namespace App\Livewire\Skills;

use App\Livewire\Global\Page;
use App\Models\Skill;
use App\Models\User;

class SkillPage extends Page
{
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    public function mount($component = '')
    {
        parent::mount($component);

        $this->pages = [
            [
                'module_id' => 'skills-management-module',
                'title' => 'Skills Management',
                'description' => 'Manage skills and competencies across the organization',
                'route' => 'skills.index',
                'display' => true,
                'authorized_permissions' => ['manage_skills'],
                'section_routes' => ['skills.index', 'skills.create', 'skills.edit', 'skills.show'],
                'sections' => []
            ],
            [
                'module_id' => 'certifications-management-module',
                'title' => 'Certifications Management',
                'description' => 'Manage certifications and professional requirements',
                'route' => 'certifications.index',
                'display' => true,
                'authorized_permissions' => ['manage_certifications'],
                'section_routes' => ['certifications.index', 'certifications.create', 'certifications.edit', 'certifications.show'],
                'sections' => []
            ]
        ];

        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function setPageResume($route)
    {
        switch ($route) {
            case 'skills.index':
                $totalSkills = Skill::count();
                $activeSkills = Skill::where('is_active', true)->count();
                $usersWithSkills = User::whereHas('skills')->count();
                $skillCategories = Skill::select('category')->distinct()->whereNotNull('category')->count();

                $this->resumeTitle = 'Skills Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Skills Management',
                    'description' => 'Manage skills and competencies across the organization',
                    'metrics' => [
                        ['label' => 'Total Skills', 'value' => $totalSkills],
                        ['label' => 'Active Skills', 'value' => $activeSkills],
                        ['label' => 'Users with Skills', 'value' => $usersWithSkills],
                        ['label' => 'Skill Categories', 'value' => $skillCategories]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Skills Management';
                $this->current_page_resume['type'] = 'dashboard';
                $this->current_page_resume['value'] = $totalSkills;
                $this->current_page_resume['description'] = 'Total Skills';
                break;

            case 'skills.create':
                $this->resumeTitle = 'Create Skill';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create New Skill',
                    'description' => 'Add a new skill to the system',
                    'steps' => [
                        'Enter skill information',
                        'Set skill category and level',
                        'Configure skill requirements',
                        'Save skill'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Skill';
                $this->current_page_resume['type'] = 'form';
                $this->current_page_resume['description'] = 'Add a new skill to the system';
                break;

            case 'skills.edit':
                $this->resumeTitle = 'Edit Skill';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit Skill',
                    'description' => 'Modify existing skill information',
                    'steps' => [
                        'Update skill information',
                        'Modify category and level',
                        'Adjust skill requirements',
                        'Save changes'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Edit Skill';
                $this->current_page_resume['type'] = 'form';
                $this->current_page_resume['description'] = 'Modify existing skill information';
                break;

            case 'skills.show':
                $this->resumeTitle = 'Skill Details';
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Skill Details',
                    'description' => 'View detailed skill information and user assignments'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Skill Details';
                $this->current_page_resume['type'] = 'entity';
                $this->current_page_resume['description'] = 'View detailed skill information';
                break;

            default:
                $totalSkills = Skill::count();

                $this->resumeTitle = 'Skills Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Skills Management',
                    'description' => 'Manage skills and competencies',
                    'metrics' => [
                        ['label' => 'Total Skills', 'value' => $totalSkills],
                        ['label' => 'Active Users', 'value' => User::count()],
                        ['label' => 'Skill Categories', 'value' => 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Skills Management';
                $this->current_page_resume['type'] = 'dashboard';
                $this->current_page_resume['value'] = $totalSkills;
                $this->current_page_resume['description'] = 'Total Skills';
                break;
        }
    }

    public function render()
    {
        return view('livewire.skills.skill-page');
    }
}
