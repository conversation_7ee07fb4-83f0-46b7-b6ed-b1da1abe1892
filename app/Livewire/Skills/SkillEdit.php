<?php

namespace App\Livewire\Skills;

use App\Models\Skill;
use Livewire\Component;

class SkillEdit extends Component
{
    public Skill $skill;
    public $name;
    public $category;
    public $description;
    public $is_active;

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:skills,name,' . $this->skill->id,
            'category' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ];
    }

    public function mount(Skill $skill)
    {
        $this->skill = $skill;
        $this->name = $skill->name;
        $this->category = $skill->category;
        $this->description = $skill->description;
        $this->is_active = $skill->is_active;
    }

    public function save()
    {
        $this->validate();

        $this->skill->update([
            'name' => $this->name,
            'category' => $this->category,
            'description' => $this->description,
            'is_active' => $this->is_active,
        ]);

        session()->flash('message', 'Skill updated successfully.');
        
        return $this->redirect(route('skills.index'), navigate: true);
    }

    public function render()
    {
        $categories = Skill::select('category')
            ->distinct()
            ->whereNotNull('category')
            ->pluck('category');
            
        return view('livewire.skills.skill-edit', [
            'categories' => $categories,
        ]);
    }
}
