<?php

namespace App\Livewire\Skills;

use App\Models\Skill;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class SkillShow extends Component
{
    use WithPagination;
    
    public Skill $skill;
    public $search = '';
    public $perPage = 10;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
    ];
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function mount(Skill $skill)
    {
        $this->skill = $skill;
    }
    
    public function render()
    {
        $users = User::whereHas('skills', function ($query) {
                $query->where('skill_id', $this->skill->id);
            })
            ->when($this->search, function ($query) {
                return $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->with(['skills' => function ($query) {
                $query->where('skill_id', $this->skill->id);
            }])
            ->paginate($this->perPage);
            
        $campaigns = $this->skill->requiredByCampaigns;
        $trainingModules = $this->skill->trainingModules;
        
        return view('livewire.skills.skill-show', [
            'skill' => $this->skill,
            'users' => $users,
            'campaigns' => $campaigns,
            'trainingModules' => $trainingModules,
        ]);
    }
}
