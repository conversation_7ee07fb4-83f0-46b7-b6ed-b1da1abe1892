<?php

namespace App\Livewire\Accountant;

use App\Models\Shift;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class AgentAttendance extends Component
{
    use WithPagination;

    public $search = '';
    public $dateRange = 'month';
    public $startDate;
    public $endDate;
    public $selectedAgent = '';
    public $perPage = 10;

    public function mount()
    {
        // Set default date range (last 30 days)
        $this->startDate = now()->subDays(30)->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();

        switch ($this->dateRange) {
            case 'today':
                $this->startDate = now()->format('Y-m-d');
                $this->endDate = now()->format('Y-m-d');
                break;
            case 'week':
                $this->startDate = now()->startOfWeek()->format('Y-m-d');
                $this->endDate = now()->endOfWeek()->format('Y-m-d');
                break;
            case 'month':
                $this->startDate = now()->startOfMonth()->format('Y-m-d');
                $this->endDate = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'quarter':
                $this->startDate = now()->startOfQuarter()->format('Y-m-d');
                $this->endDate = now()->endOfQuarter()->format('Y-m-d');
                break;
            case 'year':
                $this->startDate = now()->startOfYear()->format('Y-m-d');
                $this->endDate = now()->endOfYear()->format('Y-m-d');
                break;
            case 'custom':
                // Keep current dates
                break;
        }
    }

    public function updatedStartDate()
    {
        $this->dateRange = 'custom';
        $this->resetPage();
    }

    public function updatedEndDate()
    {
        $this->dateRange = 'custom';
        $this->resetPage();
    }

    public function updatedSelectedAgent()
    {
        $this->resetPage();
    }

    public function render()
    {
        $shifts = Shift::query()
            ->whereBetween('date', [$this->startDate, $this->endDate])
            ->when($this->selectedAgent, function ($query) {
                $query->where('user_id', $this->selectedAgent);
            })
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%');
                });
            })
            ->with('user')
            ->orderBy('date', 'desc')
            ->paginate($this->perPage);

        // Ensure dates are properly formatted
        $shifts->getCollection()->transform(function ($shift) {
            if (is_string($shift->date)) {
                $shift->date = \Carbon\Carbon::parse($shift->date);
            }
            return $shift;
        });

        $agents = User::whereIn('role_id', [6]) // Agent roles
            ->where('status', 'actif')
            ->orderBy('first_name')
            ->get();

        // Calculate total hours and statistics
        $totalHours = $shifts->sum('hours_worked');
        $averageHours = $shifts->count() > 0 ? $totalHours / $shifts->count() : 0;

        return view('livewire.accountant.agent-attendance', [
            'shifts' => $shifts,
            'agents' => $agents,
            'totalHours' => $totalHours,
            'averageHours' => $averageHours
        ]);
    }
}
