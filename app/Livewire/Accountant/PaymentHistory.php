<?php

namespace App\Livewire\Accountant;

use App\Models\Payment;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class PaymentHistory extends Component
{
    use WithPagination;

    public $search = '';
    public $dateRange = 'all';
    public $startDate;
    public $endDate;
    public $selectedAgent = '';
    public $selectedStatus = '';
    public $perPage = 10;

    public function mount()
    {
        // Set default date range (all time)
        $this->startDate = now()->subYears(5)->format('Y-m-d'); // 5 years ago
        $this->endDate = now()->format('Y-m-d');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();

        switch ($this->dateRange) {
            case 'today':
                $this->startDate = now()->format('Y-m-d');
                $this->endDate = now()->format('Y-m-d');
                break;
            case 'week':
                $this->startDate = now()->startOfWeek()->format('Y-m-d');
                $this->endDate = now()->endOfWeek()->format('Y-m-d');
                break;
            case 'month':
                $this->startDate = now()->startOfMonth()->format('Y-m-d');
                $this->endDate = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'quarter':
                $this->startDate = now()->startOfQuarter()->format('Y-m-d');
                $this->endDate = now()->endOfQuarter()->format('Y-m-d');
                break;
            case 'year':
                $this->startDate = now()->startOfYear()->format('Y-m-d');
                $this->endDate = now()->endOfYear()->format('Y-m-d');
                break;
            case 'all':
                $this->startDate = now()->subYears(5)->format('Y-m-d'); // 5 years ago
                $this->endDate = now()->format('Y-m-d');
                break;
            case 'custom':
                // Keep current dates
                break;
        }
    }

    public function updatedStartDate()
    {
        $this->dateRange = 'custom';
        $this->resetPage();
    }

    public function updatedEndDate()
    {
        $this->dateRange = 'custom';
        $this->resetPage();
    }

    public function updatedSelectedAgent()
    {
        $this->resetPage();
    }

    public function updatedSelectedStatus()
    {
        $this->resetPage();
    }

    public function exportPaymentHistory()
    {
        // This would be implemented to export payment history to CSV/Excel
        session()->flash('message', 'Export functionality coming soon!');
    }

    public function render()
    {
        $payments = Payment::query()
            ->when($this->selectedAgent, function ($query) {
                $query->where('user_id', $this->selectedAgent);
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%');
                })
                ->orWhere('period', 'like', '%' . $this->search . '%');
            })
            ->whereBetween('created_at', [$this->startDate . ' 00:00:00', $this->endDate . ' 23:59:59'])
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate($this->perPage);

        $agents = User::whereIn('role_id', [6]) // Agent roles
            ->where('status', 'actif')
            ->orderBy('first_name')
            ->get();

        // Calculate summary statistics
        $totalPayments = $payments->total();
        $totalAmount = Payment::when($this->selectedAgent, function ($query) {
                $query->where('user_id', $this->selectedAgent);
            })
            ->when($this->selectedStatus, function ($query) {
                $query->where('status', $this->selectedStatus);
            })
            ->whereBetween('created_at', [$this->startDate . ' 00:00:00', $this->endDate . ' 23:59:59'])
            ->sum('amount');

        $paidAmount = Payment::when($this->selectedAgent, function ($query) {
                $query->where('user_id', $this->selectedAgent);
            })
            ->where('status', 'paid')
            ->whereBetween('created_at', [$this->startDate . ' 00:00:00', $this->endDate . ' 23:59:59'])
            ->sum('amount');

        $pendingAmount = Payment::when($this->selectedAgent, function ($query) {
                $query->where('user_id', $this->selectedAgent);
            })
            ->where('status', 'on_hold')
            ->whereBetween('created_at', [$this->startDate . ' 00:00:00', $this->endDate . ' 23:59:59'])
            ->sum('amount');

        return view('livewire.accountant.payment-history', [
            'payments' => $payments,
            'agents' => $agents,
            'totalPayments' => $totalPayments,
            'totalAmount' => $totalAmount,
            'paidAmount' => $paidAmount,
            'pendingAmount' => $pendingAmount,
        ]);
    }
}
