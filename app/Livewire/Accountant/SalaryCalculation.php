<?php

namespace App\Livewire\Accountant;

use App\Models\Contract;
use App\Models\Payment;
use App\Models\Shift;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class SalaryCalculation extends Component
{
    public $selectedAgent = '';
    public $selectedMonth;
    public $selectedYear;
    public $paymentPeriod;

    // Calculation results
    public $hoursWorked = 0;
    public $hourlyRate = 0;
    public $baseSalary = 0;
    public $bonusAmount = 0;
    public $deductionsAmount = 0;
    public $totalAmount = 0;

    // Payment creation
    public $showPaymentModal = false;
    public $paymentStatus = 'on_hold';
    public $paymentNotes = '';

    public function mount()
    {
        $this->selectedMonth = now()->month;
        $this->selectedYear = now()->year;
        $this->paymentPeriod = now()->format('F Y');
    }

    public function updatedSelectedMonth()
    {
        $this->calculateSalary();
        $this->paymentPeriod = date('F Y', mktime(0, 0, 0, $this->selectedMonth, 1, $this->selectedYear));
    }

    public function updatedSelectedYear()
    {
        $this->calculateSalary();
        $this->paymentPeriod = date('F Y', mktime(0, 0, 0, $this->selectedMonth, 1, $this->selectedYear));
    }

    public function updatedSelectedAgent()
    {
        $this->calculateSalary();
    }

    public function calculateSalary()
    {
        if (empty($this->selectedAgent)) {
            $this->resetCalculation();
            return;
        }

        // Get the agent's contract to determine hourly rate
        $contract = Contract::where('user_id', $this->selectedAgent)
            ->where('status', 'active')
            ->first();

        // Calculate hours worked in the selected month
        $startDate = \Carbon\Carbon::createFromDate($this->selectedYear, $this->selectedMonth, 1)->startOfMonth();
        $endDate = \Carbon\Carbon::createFromDate($this->selectedYear, $this->selectedMonth, 1)->endOfMonth();

        $this->hoursWorked = Shift::where('user_id', $this->selectedAgent)
            ->whereBetween('date', [$startDate, $endDate])
            ->sum('hours_worked');

        // Calculate salary based on contract
        if ($contract) {
            if ($contract->payment_type === 'hourly') {
                $this->hourlyRate = $contract->hourly_rate ?? 0;
                $this->baseSalary = $this->hoursWorked * $this->hourlyRate;
            } else {
                // Monthly salary
                $this->baseSalary = $contract->salary ?? 0;
                $this->hourlyRate = $this->hoursWorked > 0 ? round($this->baseSalary / $this->hoursWorked, 2) : 0;
            }

            // Add bonuses based on performance or other metrics
            // This is a placeholder - implement your bonus logic here
            $this->bonusAmount = 0;

            // Apply deductions (taxes, etc.)
            // This is a placeholder - implement your deductions logic here
            $this->deductionsAmount = 0;

            $this->totalAmount = $this->baseSalary + $this->bonusAmount - $this->deductionsAmount;
        } else {
            // Default hourly rate if no contract found
            $this->hourlyRate = 10;
            $this->baseSalary = $this->hoursWorked * $this->hourlyRate;
            $this->bonusAmount = 0;
            $this->deductionsAmount = 0;
            $this->totalAmount = $this->baseSalary;
        }
    }

    public function resetCalculation()
    {
        $this->hoursWorked = 0;
        $this->hourlyRate = 0;
        $this->baseSalary = 0;
        $this->bonusAmount = 0;
        $this->deductionsAmount = 0;
        $this->totalAmount = 0;
    }

    public function openPaymentModal()
    {
        if (empty($this->selectedAgent) || $this->totalAmount <= 0) {
            session()->flash('error', 'Please select an agent and calculate salary first.');
            return;
        }

        $this->showPaymentModal = true;
    }

    public function closePaymentModal()
    {
        $this->showPaymentModal = false;
        $this->paymentStatus = 'on_hold';
        $this->paymentNotes = '';
    }

    public function createPayment()
    {
        if (empty($this->selectedAgent) || $this->totalAmount <= 0) {
            session()->flash('error', 'Please select an agent and calculate salary first.');
            return;
        }

        // Check if payment already exists for this period
        $existingPayment = Payment::where('user_id', $this->selectedAgent)
            ->where('period', $this->paymentPeriod)
            ->first();

        if ($existingPayment) {
            session()->flash('error', 'A payment for this agent and period already exists.');
            $this->closePaymentModal();
            return;
        }

        // Create the payment
        Payment::create([
            'user_id' => $this->selectedAgent,
            'period' => $this->paymentPeriod,
            'hours_worked' => $this->hoursWorked,
            'amount' => $this->totalAmount,
            'paid_at' => $this->paymentStatus === 'paid' ? now() : null,
            'status' => $this->paymentStatus,
            'notes' => $this->paymentNotes,
        ]);

        session()->flash('message', 'Payment created successfully!');
        $this->closePaymentModal();
    }

    public function render()
    {
        $agents = User::whereIn('role_id', [6]) // Agent roles
            ->where('status', 'actif')
            ->orderBy('first_name')
            ->get();

        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $months[$i] = date('F', mktime(0, 0, 0, $i, 1));
        }

        $years = range(now()->year - 5, now()->year + 1);

        return view('livewire.accountant.salary-calculation', [
            'agents' => $agents,
            'months' => $months,
            'years' => $years,
        ]);
    }
}
