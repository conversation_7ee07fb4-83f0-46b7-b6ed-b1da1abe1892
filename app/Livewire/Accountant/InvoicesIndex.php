<?php

namespace App\Livewire\Accountant;

use App\Models\Invoice;
use Illuminate\Support\Facades\Schema;
use Livewire\Component;
use Livewire\WithPagination;

class InvoicesIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedStatus = '';

    public $statuses = [
        'draft' => 'Draft',
        'sent' => 'Sent',
        'paid' => 'Paid',
        'overdue' => 'Overdue',
        'cancelled' => 'Cancelled'
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        if (!Schema::hasTable('invoices')) {
            return view('livewire.accountant.invoices-index', [
                'invoices' => collect([])
            ]);
        }

        try {
            $invoices = Invoice::query()
                ->when($this->search, function ($query) {
                    $query->where(function ($q) {
                        $q->where('invoice_number', 'like', '%' . $this->search . '%')
                            ->orWhere('client_name', 'like', '%' . $this->search . '%')
                            ->orWhere('client_email', 'like', '%' . $this->search . '%');
                    });
                })
                ->when($this->selectedStatus, function ($query) {
                    $query->where('status', $this->selectedStatus);
                })
                ->orderBy($this->sortField, $this->sortDirection)
                ->paginate($this->perPage);

            return view('livewire.accountant.invoices-index', [
                'invoices' => $invoices
            ]);
        } catch (\Exception $e) {
            return view('livewire.accountant.invoices-index', [
                'invoices' => collect([])
            ]);
        }
    }
}
