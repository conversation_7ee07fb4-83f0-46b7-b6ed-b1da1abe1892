<?php

namespace App\Livewire\Accountant;

use App\Livewire\Forms\InvoiceForm;
use App\Models\Customer;
use Livewire\Attributes\On;
use Livewire\Component;

class InvoicesCreate extends Component
{
    public InvoiceForm $form;
    public $customers = [];
    public $isNewCustomer = false;
    public $selectedCustomerId = '';

    public function mount()
    {
        $this->form->mount();
        $this->customers = Customer::orderBy('name')->get();
    }

    public function toggleCustomerMode($mode)
    {
        $this->isNewCustomer = $mode === 'new';
        if ($this->isNewCustomer) {
            // Clear customer fields when switching to new customer mode
            $this->form->client_name = '';
            $this->form->client_email = '';
            $this->form->client_address = '';
            $this->form->client_phone = '';
            $this->selectedCustomerId = '';
        }
    }

    public function updatedFormTaxRate()
    {
        // Update tax rate for all items
        foreach ($this->form->items as $index => $item) {
            $this->form->items[$index]['tax_rate'] = $this->form->tax_rate;
            $this->form->calculateItemTotal($index);
        }
    }

    public function updatedFormItems($value, $key)
    {
        // Extract the index from the key (e.g., "items.0.quantity" -> 0)
        $parts = explode('.', $key);
        if (count($parts) === 3) {
            $index = $parts[1];
            $this->form->calculateItemTotal($index);
        }
    }

    public function updatedFormDiscountAmount()
    {
        // Recalculate totals when discount amount changes
        $this->form->calculateTotals();
    }

    public function selectCustomer($customerId)
    {
        $this->selectedCustomerId = $customerId;

        if (empty($customerId)) {
            // User selected the empty option, switch to new customer mode
            $this->toggleCustomerMode('new');
            return;
        }

        // User selected an existing customer
        $this->isNewCustomer = false;
        $customer = Customer::find($customerId);
        if ($customer) {
            $this->form->client_name = $customer->name;
            $this->form->client_email = $customer->email;
            $this->form->client_address = $customer->address;
            $this->form->client_phone = $customer->phone;
        }
    }

    public function addItem()
    {
        $this->form->addItem();
    }

    public function removeItem($index)
    {
        $this->form->removeItem($index);
    }

    #[On('create-invoice')]
    public function createInvoice()
    {
        $invoice = $this->form->store();

        if ($invoice) {
            session()->flash('message', 'Invoice created successfully!');
            $this->redirect(route('accountant.invoices.show', ['invoice' => $invoice->id]), navigate: true);
        }
    }

    public function render()
    {
        return view('livewire.accountant.invoices-create');
    }
}
