<?php

namespace App\Livewire\Accountant;

use App\Models\Invoice;
use Livewire\Component;

class InvoicesDelete extends Component
{
    public Invoice $invoice;
    public $confirmingDelete = false;

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->confirmingDelete = true;
    }

    public function deleteInvoice()
    {
        // Delete the invoice and related items
        $this->invoice->items()->delete();
        $this->invoice->payments()->delete();
        $this->invoice->delete();

        session()->flash('message', 'Invoice deleted successfully!');
        $this->redirect(route('accountant.invoices'), navigate: true);
    }

    public function cancelDelete()
    {
        $this->redirect(route('accountant.invoices.show', ['invoice' => $this->invoice->id]), navigate: true);
    }

    public function render()
    {
        return view('livewire.accountant.invoices-delete');
    }
}
