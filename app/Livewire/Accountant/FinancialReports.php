<?php

namespace App\Livewire\Accountant;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Livewire\Component;

class FinancialReports extends Component
{
    public $selectedYear;
    public $selectedMonth = 'all';
    public $reportType = 'income';

    public $years = [];
    public $months = [
        'all' => 'All Months',
        '01' => 'January',
        '02' => 'February',
        '03' => 'March',
        '04' => 'April',
        '05' => 'May',
        '06' => 'June',
        '07' => 'July',
        '08' => 'August',
        '09' => 'September',
        '10' => 'October',
        '11' => 'November',
        '12' => 'December'
    ];

    public $reportTypes = [
        'income' => 'Income Report',
        'expenses' => 'Expenses Report',
        'profit' => 'Profit & Loss',
        'tax' => 'Tax Summary',
        'agent' => 'Agent Payments'
    ];

    public $reportData = [];
    public $chartData = [];
    public $totalAmount = 0;

    public function mount()
    {
        try {
            // Get available years from the database
            $paymentYears = Payment::selectRaw('YEAR(created_at) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year')
                ->toArray();

            $invoiceYears = [];
            if (Schema::hasTable('invoices')) {
                $invoiceYears = Invoice::selectRaw('YEAR(created_at) as year')
                    ->distinct()
                    ->orderBy('year', 'desc')
                    ->pluck('year')
                    ->toArray();
            }

            $this->years = array_unique(array_merge($paymentYears, $invoiceYears));

            if (empty($this->years)) {
                $this->years = [date('Y')];
            }

            $this->selectedYear = $this->years[0];

            $this->generateReport();
        } catch (\Exception $e) {
            $this->years = [date('Y')];
            $this->selectedYear = date('Y');
            $this->reportData = [];
            $this->chartData = [
                'labels' => [],
                'data' => []
            ];
            $this->totalAmount = 0;
        }
    }

    public function updatedSelectedYear()
    {
        $this->generateReport();
    }

    public function updatedSelectedMonth()
    {
        $this->generateReport();
    }

    public function updatedReportType()
    {
        $this->generateReport();
    }

    public function generateReport()
    {
        $this->reportData = [];
        $this->chartData = [];
        $this->totalAmount = 0;

        switch ($this->reportType) {
            case 'income':
                $this->generateIncomeReport();
                break;
            case 'expenses':
                $this->generateExpensesReport();
                break;
            case 'profit':
                $this->generateProfitLossReport();
                break;
            case 'tax':
                $this->generateTaxReport();
                break;
            case 'agent':
                $this->generateAgentPaymentsReport();
                break;
        }
    }

    private function generateIncomeReport()
    {
        if (!Schema::hasTable('invoices')) {
            $this->reportData = [];
            $this->chartData = [
                'labels' => [],
                'data' => []
            ];
            $this->totalAmount = 0;
            return;
        }

        try {
            $query = Invoice::whereYear('created_at', $this->selectedYear)
                ->where('status', 'paid');

            if ($this->selectedMonth !== 'all') {
                $query->whereMonth('created_at', $this->selectedMonth);
            }

            $invoices = $query->get();

            $this->totalAmount = $invoices->sum('total_amount');

            // Group by month for chart
            $monthlyData = [];
            foreach ($invoices as $invoice) {
                $month = $invoice->created_at->format('M');
                if (!isset($monthlyData[$month])) {
                    $monthlyData[$month] = 0;
                }
                $monthlyData[$month] += $invoice->total_amount;
            }

            $this->chartData = [
                'labels' => array_keys($monthlyData),
                'data' => array_values($monthlyData)
            ];

            $this->reportData = $invoices;
        } catch (\Exception $e) {
            $this->reportData = [];
            $this->chartData = [
                'labels' => [],
                'data' => []
            ];
            $this->totalAmount = 0;
        }
    }

    private function generateExpensesReport()
    {
        // Placeholder for expenses report
        $this->reportData = [];
        $this->chartData = [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            'data' => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        ];
        $this->totalAmount = 0;
    }

    private function generateProfitLossReport()
    {
        // Placeholder for profit & loss report
        $this->reportData = [];
        $this->chartData = [
            'labels' => ['Income', 'Expenses', 'Profit'],
            'data' => [0, 0, 0]
        ];
        $this->totalAmount = 0;
    }

    private function generateTaxReport()
    {
        if (!Schema::hasTable('invoices')) {
            $this->reportData = [];
            $this->chartData = [
                'labels' => [],
                'data' => []
            ];
            $this->totalAmount = 0;
            return;
        }

        try {
            $query = Invoice::whereYear('created_at', $this->selectedYear)
                ->where('status', 'paid');

            if ($this->selectedMonth !== 'all') {
                $query->whereMonth('created_at', $this->selectedMonth);
            }

            $invoices = $query->get();

            $this->totalAmount = $invoices->sum('tax_amount');

            // Group by month for chart
            $monthlyData = [];
            foreach ($invoices as $invoice) {
                $month = $invoice->created_at->format('M');
                if (!isset($monthlyData[$month])) {
                    $monthlyData[$month] = 0;
                }
                $monthlyData[$month] += $invoice->tax_amount;
            }

            $this->chartData = [
                'labels' => array_keys($monthlyData),
                'data' => array_values($monthlyData)
            ];

            $this->reportData = $invoices;
        } catch (\Exception $e) {
            $this->reportData = [];
            $this->chartData = [
                'labels' => [],
                'data' => []
            ];
            $this->totalAmount = 0;
        }
    }

    private function generateAgentPaymentsReport()
    {
        $query = Payment::whereYear('created_at', $this->selectedYear);

        if ($this->selectedMonth !== 'all') {
            $query->whereMonth('created_at', $this->selectedMonth);
        }

        $payments = $query->with('user')->get();

        $this->totalAmount = $payments->sum('amount');

        // Group by agent for chart
        $agentData = [];
        foreach ($payments as $payment) {
            $agentName = $payment->user ? $payment->user->first_name . ' ' . $payment->user->last_name : 'Unknown';
            if (!isset($agentData[$agentName])) {
                $agentData[$agentName] = 0;
            }
            $agentData[$agentName] += $payment->amount;
        }

        // Sort by amount
        arsort($agentData);

        // Take top 10 agents
        $agentData = array_slice($agentData, 0, 10, true);

        $this->chartData = [
            'labels' => array_keys($agentData),
            'data' => array_values($agentData)
        ];

        $this->reportData = $payments;
    }

    public function render()
    {
        return view('livewire.accountant.financial-reports');
    }
}
