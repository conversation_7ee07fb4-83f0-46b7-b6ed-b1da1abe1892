<?php

namespace App\Livewire\Accountant;

use App\Models\Invoice;
use App\Models\InvoicePayment;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class InvoicesShow extends Component
{
    public Invoice $invoice;
    public $showPaymentModal = false;
    public $paymentAmount;
    public $paymentDate;
    public $paymentMethod = 'bank_transfer';
    public $transactionReference;
    public $paymentNotes;

    public $paymentMethods = [
        'cash' => 'Cash',
        'bank_transfer' => 'Bank Transfer',
        'credit_card' => 'Credit Card',
        'check' => 'Check',
        'paypal' => 'PayPal',
        'other' => 'Other'
    ];

    public function mount(Invoice $invoice)
    {
        $this->invoice = $invoice;
        $this->paymentAmount = $invoice->remaining_balance;
        $this->paymentDate = now()->format('Y-m-d');
    }

    public function openPaymentModal()
    {
        $this->showPaymentModal = true;
    }

    public function closePaymentModal()
    {
        $this->showPaymentModal = false;
        $this->resetPaymentForm();
    }

    public function resetPaymentForm()
    {
        $this->paymentAmount = $this->invoice->remaining_balance;
        $this->paymentDate = now()->format('Y-m-d');
        $this->paymentMethod = 'bank_transfer';
        $this->transactionReference = '';
        $this->paymentNotes = '';
    }

    public function recordPayment()
    {
        $this->validate([
            'paymentAmount' => 'required|numeric|min:0.01|max:' . $this->invoice->remaining_balance,
            'paymentDate' => 'required|date',
            'paymentMethod' => 'required|string',
            'transactionReference' => 'nullable|string|max:255',
            'paymentNotes' => 'nullable|string',
        ]);

        // Create payment record
        InvoicePayment::create([
            'invoice_id' => $this->invoice->id,
            'amount' => $this->paymentAmount,
            'payment_date' => $this->paymentDate,
            'payment_method' => $this->paymentMethod,
            'transaction_reference' => $this->transactionReference,
            'notes' => $this->paymentNotes,
            'recorded_by' => Auth::id(),
        ]);

        // Update invoice status if fully paid
        $this->invoice->refresh();
        if ($this->invoice->is_paid) {
            $this->invoice->update(['status' => 'paid']);
        }

        session()->flash('message', 'Payment recorded successfully!');
        $this->closePaymentModal();
    }

    public function markAsSent()
    {
        $this->invoice->update(['status' => 'sent']);
        session()->flash('message', 'Invoice marked as sent!');
    }

    public function markAsPaid()
    {
        $this->invoice->update(['status' => 'paid']);
        session()->flash('message', 'Invoice marked as paid!');
    }

    public function markAsOverdue()
    {
        $this->invoice->update(['status' => 'overdue']);
        session()->flash('message', 'Invoice marked as overdue!');
    }

    public function markAsCancelled()
    {
        $this->invoice->update(['status' => 'cancelled']);
        session()->flash('message', 'Invoice marked as cancelled!');
    }

    public function render()
    {
        return view('livewire.accountant.invoices-show', [
            'invoice' => $this->invoice->fresh(),
        ]);
    }
}
