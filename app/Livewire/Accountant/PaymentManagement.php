<?php

namespace App\Livewire\Accountant;

use App\Models\Contract;
use App\Models\Payment;
use App\Models\Shift;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class PaymentManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $dateRange = 'month';
    public $startDate;
    public $endDate;
    public $selectedAgent = '';
    public $perPage = 10;

    // Payment creation form
    public $showPaymentModal = false;
    public $paymentAgent;
    public $paymentPeriod;
    public $paymentHoursWorked;
    public $paymentAmount;
    public $paymentStatus = 'on_hold';

    // Bulk payment processing
    public $selectedAgents = [];
    public $selectAll = false;
    public $bulkAction = '';

    public function mount()
    {
        // Set default date range (current month)
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->endOfMonth()->format('Y-m-d');
        $this->paymentPeriod = now()->format('F Y');
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();

        switch ($this->dateRange) {
            case 'today':
                $this->startDate = now()->format('Y-m-d');
                $this->endDate = now()->format('Y-m-d');
                break;
            case 'week':
                $this->startDate = now()->startOfWeek()->format('Y-m-d');
                $this->endDate = now()->endOfWeek()->format('Y-m-d');
                break;
            case 'month':
                $this->startDate = now()->startOfMonth()->format('Y-m-d');
                $this->endDate = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'quarter':
                $this->startDate = now()->startOfQuarter()->format('Y-m-d');
                $this->endDate = now()->endOfQuarter()->format('Y-m-d');
                break;
            case 'year':
                $this->startDate = now()->startOfYear()->format('Y-m-d');
                $this->endDate = now()->endOfYear()->format('Y-m-d');
                break;
            case 'custom':
                // Keep current dates
                break;
        }
    }

    public function updatedStartDate()
    {
        $this->dateRange = 'custom';
        $this->resetPage();
    }

    public function updatedEndDate()
    {
        $this->dateRange = 'custom';
        $this->resetPage();
    }

    public function updatedSelectedAgent()
    {
        $this->resetPage();
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedAgents = $this->getAgentsQuery()->pluck('id')->map(fn($id) => (string) $id)->toArray();
        } else {
            $this->selectedAgents = [];
        }
    }

    public function openPaymentModal()
    {
        $this->resetPaymentForm();
        $this->showPaymentModal = true;
    }

    public function closePaymentModal()
    {
        $this->showPaymentModal = false;
        $this->resetPaymentForm();
    }

    public function resetPaymentForm()
    {
        $this->paymentAgent = '';
        $this->paymentPeriod = now()->format('F Y');
        $this->paymentHoursWorked = 0;
        $this->paymentAmount = 0;
        $this->paymentStatus = 'on_hold';
    }

    public function calculatePaymentAmount()
    {
        if (empty($this->paymentAgent) || empty($this->paymentHoursWorked)) {
            $this->paymentAmount = 0;
            return;
        }

        $agent = User::find($this->paymentAgent);
        if (!$agent) {
            $this->paymentAmount = 0;
            return;
        }

        // Get the agent's contract to determine hourly rate
        $contract = Contract::where('user_id', $agent->id)
            ->where('status', 'active')
            ->first();

        if ($contract && $contract->salary) {
            // Assuming salary is monthly and standard work month is 160 hours
            $hourlyRate = $contract->salary / 160;
            $this->paymentAmount = round($hourlyRate * $this->paymentHoursWorked, 2);
        } else {
            // Default hourly rate if no contract found
            $this->paymentAmount = round(10 * $this->paymentHoursWorked, 2);
        }
    }

    public function createPayment()
    {
        $this->validate([
            'paymentAgent' => 'required|exists:users,id',
            'paymentPeriod' => 'required|string',
            'paymentHoursWorked' => 'required|numeric|min:0',
            'paymentAmount' => 'required|numeric|min:0',
            'paymentStatus' => 'required|in:on_hold,paid',
        ]);

        Payment::create([
            'user_id' => $this->paymentAgent,
            'period' => $this->paymentPeriod,
            'hours_worked' => $this->paymentHoursWorked,
            'amount' => $this->paymentAmount,
            'paid_at' => $this->paymentStatus === 'paid' ? now() : null,
            'status' => $this->paymentStatus,
        ]);

        session()->flash('message', 'Payment created successfully!');
        $this->closePaymentModal();
    }

    public function markAsPaid($paymentId)
    {
        $payment = Payment::find($paymentId);
        if ($payment) {
            $payment->update([
                'status' => 'paid',
                'paid_at' => now(),
            ]);
            session()->flash('message', 'Payment marked as paid!');
        }
    }

    public function markAsOnHold($paymentId)
    {
        $payment = Payment::find($paymentId);
        if ($payment) {
            $payment->update([
                'status' => 'on_hold',
                'paid_at' => null,
            ]);
            session()->flash('message', 'Payment marked as on hold!');
        }
    }

    public function deletePayment($paymentId)
    {
        $payment = Payment::find($paymentId);
        if ($payment) {
            $payment->delete();
            session()->flash('message', 'Payment deleted successfully!');
        }
    }

    public function processBulkAction()
    {
        if (empty($this->selectedAgents) || empty($this->bulkAction)) {
            return;
        }

        if ($this->bulkAction === 'mark_paid') {
            Payment::whereIn('id', $this->selectedAgents)->update([
                'status' => 'paid',
                'paid_at' => now(),
            ]);
            session()->flash('message', count($this->selectedAgents) . ' payments marked as paid!');
        } elseif ($this->bulkAction === 'mark_on_hold') {
            Payment::whereIn('id', $this->selectedAgents)->update([
                'status' => 'on_hold',
                'paid_at' => null,
            ]);
            session()->flash('message', count($this->selectedAgents) . ' payments marked as on hold!');
        } elseif ($this->bulkAction === 'delete') {
            Payment::whereIn('id', $this->selectedAgents)->delete();
            session()->flash('message', count($this->selectedAgents) . ' payments deleted!');
        }

        $this->selectedAgents = [];
        $this->selectAll = false;
        $this->bulkAction = '';
    }

    public function calculateHoursWorked()
    {
        if (empty($this->paymentAgent)) {
            $this->paymentHoursWorked = 0;
            return;
        }

        // Calculate total hours worked in the current month
        $startOfMonth = now()->startOfMonth();
        $endOfMonth = now()->endOfMonth();

        $totalHours = Shift::where('user_id', $this->paymentAgent)
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->sum('hours_worked');

        $this->paymentHoursWorked = $totalHours;
        $this->calculatePaymentAmount();
    }

    private function getAgentsQuery()
    {
        return User::whereIn('role_id', [6]) // Agent roles
            ->where('status', 'actif')
            ->orderBy('first_name');
    }

    public function render()
    {
        $payments = Payment::query()
            ->when($this->selectedAgent, function ($query) {
                $query->where('user_id', $this->selectedAgent);
            })
            ->when($this->search, function ($query) {
                $query->whereHas('user', function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%');
                });
            })
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate($this->perPage);

        $agents = $this->getAgentsQuery()->get();

        return view('livewire.accountant.payment-management', [
            'payments' => $payments,
            'agents' => $agents,
        ]);
    }
}
