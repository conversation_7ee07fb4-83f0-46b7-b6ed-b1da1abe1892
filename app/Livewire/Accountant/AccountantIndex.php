<?php

namespace App\Livewire\Accountant;

use App\Models\Invoice;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Schema;
use Livewire\Component;

class AccountantIndex extends Component
{
    public $totalAgents;
    public $totalPayments;
    public $totalPaid;
    public $totalInvoices;
    public $pendingInvoices;
    public $recentPayments;
    public $recentInvoices;
    public $monthlyPayments = [];

    public function mount()
    {
        try {
            $this->totalAgents = User::where('status', 'actif')->count();
            $this->totalPayments = Payment::count();
            $this->totalPaid = Payment::sum('amount') ?? 0;

            // Check if invoices table exists
            if (Schema::hasTable('invoices')) {
                $this->totalInvoices = Invoice::count();
                $this->pendingInvoices = Invoice::whereIn('status', ['draft', 'sent', 'overdue'])->count();

                // Get recent invoices
                $this->recentInvoices = Invoice::orderBy('created_at', 'desc')
                    ->take(5)
                    ->get();
            } else {
                $this->totalInvoices = 0;
                $this->pendingInvoices = 0;
                $this->recentInvoices = collect([]);
            }

            // Get recent payments
            $this->recentPayments = Payment::with('user')
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();

            // Get monthly payments for the current year
            $year = date('Y');
            for ($month = 1; $month <= 12; $month++) {
                $startDate = "{$year}-{$month}-01";
                $endDate = date('Y-m-t', strtotime($startDate));

                $this->monthlyPayments[] = [
                    'month' => date('M', strtotime($startDate)),
                    'amount' => Payment::whereBetween('created_at', [$startDate, $endDate])->sum('amount') ?? 0
                ];
            }
        } catch (\Exception $e) {
            // Handle any exceptions
            $this->totalAgents = 0;
            $this->totalPayments = 0;
            $this->totalPaid = 0;
            $this->totalInvoices = 0;
            $this->pendingInvoices = 0;
            $this->recentPayments = collect([]);
            $this->recentInvoices = collect([]);
            $this->monthlyPayments = array_map(function($month) {
                return [
                    'month' => date('M', strtotime("2023-{$month}-01")),
                    'amount' => 0
                ];
            }, range(1, 12));
        }
    }

    public function render()
    {
        return view('livewire.accountant.accountant-index');
    }
}
