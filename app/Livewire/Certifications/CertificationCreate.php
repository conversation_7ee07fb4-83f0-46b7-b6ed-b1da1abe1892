<?php

namespace App\Livewire\Certifications;

use App\Models\Certification;
use Livewire\Component;

class CertificationCreate extends Component
{
    public $name = '';
    public $issuer = '';
    public $description = '';
    public $validity_period = null;
    public $is_active = true;

    protected $rules = [
        'name' => 'required|string|max:255|unique:certifications,name',
        'issuer' => 'nullable|string|max:255',
        'description' => 'nullable|string',
        'validity_period' => 'nullable|integer|min:1',
        'is_active' => 'boolean',
    ];

    public function save()
    {
        $this->validate();

        Certification::create([
            'name' => $this->name,
            'issuer' => $this->issuer,
            'description' => $this->description,
            'validity_period' => $this->validity_period,
            'is_active' => $this->is_active,
        ]);

        session()->flash('message', 'Certification created successfully.');
        
        return $this->redirect(route('certifications.index'), navigate: true);
    }

    public function render()
    {
        $issuers = Certification::select('issuer')
            ->distinct()
            ->whereNotNull('issuer')
            ->pluck('issuer');
            
        return view('livewire.certifications.certification-create', [
            'issuers' => $issuers,
        ]);
    }
}
