<?php

namespace App\Livewire\Certifications;

use App\Models\Certification;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class CertificationShow extends Component
{
    use WithPagination;
    
    public Certification $certification;
    public $search = '';
    public $status = '';
    public $perPage = 10;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'perPage' => ['except' => 10],
    ];
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingStatus()
    {
        $this->resetPage();
    }
    
    public function mount(Certification $certification)
    {
        $this->certification = $certification;
    }
    
    public function render()
    {
        $users = User::whereHas('certifications', function ($query) {
                $query->where('certification_id', $this->certification->id)
                    ->when($this->status, function ($q) {
                        return $q->where('status', $this->status);
                    });
            })
            ->when($this->search, function ($query) {
                return $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            })
            ->with(['certifications' => function ($query) {
                $query->where('certification_id', $this->certification->id);
            }])
            ->paginate($this->perPage);
            
        $campaigns = $this->certification->requiredByCampaigns;
        $trainingModules = $this->certification->trainingModules;
        
        return view('livewire.certifications.certification-show', [
            'certification' => $this->certification,
            'users' => $users,
            'campaigns' => $campaigns,
            'trainingModules' => $trainingModules,
        ]);
    }
}
