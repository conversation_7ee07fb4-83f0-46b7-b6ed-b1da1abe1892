<?php

namespace App\Livewire\Certifications;

use App\Models\Certification;
use Livewire\Component;

class CertificationEdit extends Component
{
    public Certification $certification;
    public $name;
    public $issuer;
    public $description;
    public $validity_period;
    public $is_active;

    protected function rules()
    {
        return [
            'name' => 'required|string|max:255|unique:certifications,name,' . $this->certification->id,
            'issuer' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'validity_period' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
        ];
    }

    public function mount(Certification $certification)
    {
        $this->certification = $certification;
        $this->name = $certification->name;
        $this->issuer = $certification->issuer;
        $this->description = $certification->description;
        $this->validity_period = $certification->validity_period;
        $this->is_active = $certification->is_active;
    }

    public function save()
    {
        $this->validate();

        $this->certification->update([
            'name' => $this->name,
            'issuer' => $this->issuer,
            'description' => $this->description,
            'validity_period' => $this->validity_period,
            'is_active' => $this->is_active,
        ]);

        session()->flash('message', 'Certification updated successfully.');
        
        return $this->redirect(route('certifications.index'), navigate: true);
    }

    public function render()
    {
        $issuers = Certification::select('issuer')
            ->distinct()
            ->whereNotNull('issuer')
            ->pluck('issuer');
            
        return view('livewire.certifications.certification-edit', [
            'issuers' => $issuers,
        ]);
    }
}
