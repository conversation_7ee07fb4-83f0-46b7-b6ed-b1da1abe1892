<?php

namespace App\Livewire\Certifications;

use App\Models\Certification;
use Livewire\Component;
use Livewire\WithPagination;

class CertificationIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $issuer = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';

    protected $queryString = [
        'search' => ['except' => ''],
        'issuer' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingIssuer()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function toggleCertificationStatus(Certification $certification)
    {
        $certification->update([
            'is_active' => !$certification->is_active
        ]);
        
        session()->flash('message', 'Certification status updated successfully.');
    }

    public function render()
    {
        $certifications = Certification::query()
            ->when($this->search, function ($query) {
                return $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->when($this->issuer, function ($query) {
                return $query->where('issuer', $this->issuer);
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        $issuers = Certification::select('issuer')
            ->distinct()
            ->whereNotNull('issuer')
            ->pluck('issuer');

        return view('livewire.certifications.certification-index', [
            'certifications' => $certifications,
            'issuers' => $issuers,
        ]);
    }
}
