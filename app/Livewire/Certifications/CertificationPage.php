<?php

namespace App\Livewire\Certifications;

use App\Livewire\Global\Page;
use App\Models\Certification;
use App\Models\User;

class CertificationPage extends Page
{
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = []; // For backward compatibility
    public array $current_page_section = [];

    public function mount($component = '')
    {
        parent::mount($component);

        $this->pages = [
            [
                'module_id' => 'skills-management-module',
                'title' => 'Skills Management',
                'description' => 'Manage skills and competencies across the organization',
                'route' => 'skills.index',
                'display' => true,
                'authorized_permissions' => ['manage_skills'],
                'section_routes' => ['skills.index', 'skills.create', 'skills.edit', 'skills.show'],
                'sections' => []
            ],
            [
                'module_id' => 'certifications-management-module',
                'title' => 'Certifications Management',
                'description' => 'Manage certifications and professional requirements',
                'route' => 'certifications.index',
                'display' => true,
                'authorized_permissions' => ['manage_certifications'],
                'section_routes' => ['certifications.index', 'certifications.create', 'certifications.edit', 'certifications.show'],
                'sections' => []
            ]
        ];

        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function setPageResume($route)
    {
        switch ($route) {
            case 'certifications.index':
                $totalCertifications = Certification::count();
                $activeCertifications = Certification::where('is_active', true)->count();
                $usersWithCertifications = User::whereHas('certifications')->count();
                $uniqueIssuers = Certification::select('issuer')->distinct()->whereNotNull('issuer')->count();

                $this->resumeTitle = 'Certifications Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Certifications Management',
                    'description' => 'Manage certifications and professional requirements',
                    'metrics' => [
                        ['label' => 'Total Certifications', 'value' => $totalCertifications],
                        ['label' => 'Active Certifications', 'value' => $activeCertifications],
                        ['label' => 'Certified Users', 'value' => $usersWithCertifications],
                        ['label' => 'Certification Issuers', 'value' => $uniqueIssuers]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Certifications Management';
                $this->current_page_resume['type'] = 'dashboard';
                $this->current_page_resume['value'] = $totalCertifications;
                $this->current_page_resume['description'] = 'Total Certifications';
                break;

            case 'certifications.create':
                $this->resumeTitle = 'Create Certification';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create New Certification',
                    'description' => 'Add a new certification to the system',
                    'steps' => [
                        'Enter certification details',
                        'Set issuer and validity period',
                        'Configure certification requirements',
                        'Save certification'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create Certification';
                $this->current_page_resume['type'] = 'form';
                $this->current_page_resume['description'] = 'Add a new certification to the system';
                break;

            case 'certifications.edit':
                $this->resumeTitle = 'Edit Certification';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit Certification',
                    'description' => 'Modify existing certification information',
                    'steps' => [
                        'Update certification details',
                        'Modify issuer and validity period',
                        'Adjust certification requirements',
                        'Save changes'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Edit Certification';
                $this->current_page_resume['type'] = 'form';
                $this->current_page_resume['description'] = 'Modify existing certification information';
                break;

            case 'certifications.show':
                $this->resumeTitle = 'Certification Details';
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Certification Details',
                    'description' => 'View detailed certification information and user assignments'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Certification Details';
                $this->current_page_resume['type'] = 'entity';
                $this->current_page_resume['description'] = 'View detailed certification information';
                break;

            default:
                $totalCertifications = Certification::count();

                $this->resumeTitle = 'Certifications Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Certifications Management',
                    'description' => 'Manage certifications and requirements',
                    'metrics' => [
                        ['label' => 'Total Certifications', 'value' => $totalCertifications],
                        ['label' => 'Active Users', 'value' => User::count()],
                        ['label' => 'Certification Issuers', 'value' => 0]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Certifications Management';
                $this->current_page_resume['type'] = 'dashboard';
                $this->current_page_resume['value'] = $totalCertifications;
                $this->current_page_resume['description'] = 'Total Certifications';
                break;
        }
    }

    public function render()
    {
        return view('livewire.certifications.certification-page');
    }
}
