<?php

namespace App\Livewire\Platforms;

use App\Models\Platform;
use Livewire\Attributes\On;
use Livewire\Component;

class PlatformShow extends Component
{
    public Platform $platform;
    
    public function mount(Platform $platform)
    {
        $this->platform = $platform;
    }
    
    #[On('to-platform-show')]
    public function handleRedirect($data)
    {
        if (isset($data['platform'])) {
            return $this->redirect(route('platforms.show', $data['platform']), navigate: true);
        }
    }
    
    public function render()
    {
        return view('livewire.platforms.platform-show', [
            'platform' => $this->platform->load(['site', 'manager', 'itSupport', 'campaigns.customer'])
        ]);
    }
}
