<?php

namespace App\Livewire\Platforms;

use App\Models\Platform;
use Livewire\Component;

class PlatformDelete extends Component
{
    public Platform $platform;
    
    public function mount(Platform $platform)
    {
        $this->platform = $platform;
    }
    
    public function delete()
    {
        $siteId = $this->platform->site_id;
        $this->platform->delete();
        
        session()->flash('message', 'Platform deleted successfully!');
        
        return $this->redirect(route('sites.show', $siteId), navigate: true);
    }
    
    public function cancel()
    {
        return $this->redirect(route('platforms.show', $this->platform), navigate: true);
    }
    
    public function render()
    {
        return view('livewire.platforms.platform-delete');
    }
}
