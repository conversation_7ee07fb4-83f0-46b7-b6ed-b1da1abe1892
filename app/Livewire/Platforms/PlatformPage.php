<?php

namespace App\Livewire\Platforms;

use App\Livewire\Global\Page;
use App\Models\Platform;
use Livewire\Attributes\On;

class PlatformPage extends Page
{
    public ?Platform $platform = null;
    public array $current_page_resume = []; // For backward compatibility

    #[On('to-platform-index')]
    public function toPlatformIndex()
    {
        return $this->redirect(route('platforms.index'), navigate: true);
    }

    #[On('to-platform-show')]
    public function toPlatformShow($platform)
    {
        if (isset($platform)) {
            return $this->redirect(route('platforms.show', ['platform' => $platform]), navigate: true);
        }
        return $this->redirect(route('platforms.index'), navigate: true);
    }

    #[On('to-platform-edit')]
    public function toPlatformEdit($platform)
    {
        if (isset($platform)) {
            return $this->redirect(route('platforms.edit', ['platform' => $platform]), navigate: true);
        }
        return $this->redirect(route('platforms.index'), navigate: true);
    }

    #[On('to-platform-delete')]
    public function toPlatformDelete($platform)
    {
        if (isset($platform)) {
            return $this->redirect(route('platforms.delete', ['platform' => $platform]), navigate: true);
        }
        return $this->redirect(route('platforms.index'), navigate: true);
    }

    #[On('to-platform-create')]
    public function toPlatformCreate($site = null)
    {
        if (isset($site)) {
            return $this->redirect(route('platforms.create', ['site_id' => $site]), navigate: true);
        }
        return $this->redirect(route('platforms.create'), navigate: true);
    }

    #[On('to-site-show')]
    public function toSiteShow($site)
    {
        if (isset($site)) {
            return $this->redirect(route('sites.show', ['site' => $site]), navigate: true);
        }
        return $this->redirect(route('sites.index'), navigate: true);
    }

    #[On('to-campaign-create')]
    public function toCampaignCreate($platform)
    {
        if (isset($platform)) {
            return $this->redirect(route('campaigns.create', ['platform_id' => $platform]), navigate: true);
        }
        return $this->redirect(route('campaigns.create'), navigate: true);
    }

    public function setPageResume($route)
    {
        // Set default values
        $this->resumeTitle = 'Platforms';
        $this->resumeDescription = 'Platform management system';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Platforms',
            'description' => 'Platform management system'
        ];

        switch ($route) {
            case 'platforms.index':
                $totalPlatforms = Platform::count();

                $this->resumeTitle = 'Platforms Management';
                $this->resumeContentType = 'dashboard';
                $this->resumeData = [
                    'title' => 'Platforms Management',
                    'description' => 'Manage all platforms and their details',
                    'metrics' => [
                        ['label' => 'Total Platforms', 'value' => $totalPlatforms],
                        ['label' => 'Active Platforms', 'value' => Platform::where('status', 'active')->count()]
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Platforms Management';
                $this->current_page_resume['description'] = 'Manage all platforms and their details';
                $this->current_page_resume['type'] = 'list';
                break;

            case 'platforms.create':
                $this->resumeTitle = 'Create New Platform';
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Create New Platform',
                    'description' => 'Add a new platform to the system',
                    'instructions' => [
                        'Fill in all required fields marked with *',
                        'Specify platform type and capabilities',
                        'Assign the platform to a site if applicable'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Create New Platform';
                $this->current_page_resume['description'] = 'Add a new platform to the system';
                $this->current_page_resume['type'] = 'form';
                break;

            case 'platforms.edit':
                $platformName = $this->platform ? $this->platform->name : 'Platform';

                $this->resumeTitle = 'Edit: ' . $platformName;
                $this->resumeContentType = 'form';
                $this->resumeData = [
                    'title' => 'Edit: ' . $platformName,
                    'description' => 'Modify platform details',
                    'instructions' => [
                        'Update the necessary fields',
                        'You can change platform type, capabilities, and status'
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Edit Platform';
                $this->current_page_resume['description'] = 'Modify platform details';
                $this->current_page_resume['type'] = 'form';
                break;

            case 'platforms.delete':
                $platformName = $this->platform ? $this->platform->name : 'Platform';

                $this->resumeTitle = 'Delete: ' . $platformName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => 'Delete: ' . $platformName,
                    'description' => 'Remove platform from the system',
                    'warning' => 'This action cannot be undone. All associated campaigns will also be removed.'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Delete Platform';
                $this->current_page_resume['description'] = 'Remove platform from the system';
                $this->current_page_resume['type'] = 'delete';
                break;

            case 'platforms.show':
                $platformName = $this->platform ? $this->platform->name : 'Platform';

                $this->resumeTitle = $platformName;
                $this->resumeContentType = 'entity';
                $this->resumeData = [
                    'title' => $platformName,
                    'subtitle' => 'Platform Details',
                    'stats' => [
                        'Status' => $this->platform ? ucfirst($this->platform->status) : 'Unknown',
                        'Type' => $this->platform ? $this->platform->type : 'Not specified',
                        'Site' => $this->platform && $this->platform->site ? $this->platform->site->name : 'Not assigned',
                        'Campaigns' => $this->platform ? $this->platform->campaigns()->count() : 0
                    ]
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Platform Details';
                $this->current_page_resume['description'] = 'View platform information and campaigns';
                $this->current_page_resume['type'] = 'detail';
                break;

            default:
                $this->resumeTitle = 'Platforms';
                $this->resumeContentType = 'default';
                $this->resumeData = [
                    'title' => 'Platforms',
                    'description' => 'Manage platforms'
                ];

                // For backward compatibility
                $this->current_page_resume['title'] = 'Platforms';
                $this->current_page_resume['description'] = 'Manage platforms';
                $this->current_page_resume['type'] = 'list';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->setPageResume($this->current_route);
    }
}
