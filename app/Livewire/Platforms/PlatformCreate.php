<?php

namespace App\Livewire\Platforms;

use App\Livewire\Forms\PlatformForm;
use App\Models\Platform;
use App\Models\Site;
use App\Models\User;
use Livewire\Component;

class PlatformCreate extends Component
{
    public PlatformForm $form;

    public $sites = [];
    public $managers = [];
    public $itSupports = [];

    public function mount()
    {
        $this->sites = Site::orderBy('name')->get();
        $this->managers = User::where('role_id', 4)->get(); // Platform Manager role
        $this->itSupports = User::where('role_id', 9)->get(); // IT Support role

        // Pre-select site_id if passed as query parameter
        if (request()->has('site_id')) {
            $this->form->site_id = request()->get('site_id');
        }
    }

    public function submit()
    {
        $platform = $this->form->store();

        session()->flash('message', 'Platform created successfully!');

        return $this->redirect(route('platforms.show', $platform), navigate: true);
    }

    public function render()
    {
        return view('livewire.platforms.platform-create');
    }
}
