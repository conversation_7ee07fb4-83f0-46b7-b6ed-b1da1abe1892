<?php

namespace App\Livewire\Platforms;

use App\Models\Platform;
use Livewire\Component;
use Livewire\WithPagination;

class PlatformIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $siteId = '';
    public $type = '';
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $perPage = 10;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'siteId' => ['except' => '', 'as' => 'site'],
        'type' => ['except' => ''],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
        'perPage' => ['except' => 10]
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function updatingSiteId()
    {
        $this->resetPage();
    }

    public function updatingType()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function render()
    {
        $platforms = Platform::query()
            ->when($this->search, function ($query) {
                return $query->where('name', 'like', '%' . $this->search . '%');
            })
            ->when($this->status, function ($query) {
                return $query->where('status', $this->status);
            })
            ->when($this->siteId, function ($query) {
                return $query->where('site_id', $this->siteId);
            })
            ->when($this->type, function ($query) {
                return $query->where('type', $this->type);
            })
            ->with(['site', 'manager', 'itSupport', 'campaigns'])
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        return view('livewire.platforms.platform-index', [
            'platforms' => $platforms,
            'sites' => \App\Models\Site::all()
        ]);
    }
}