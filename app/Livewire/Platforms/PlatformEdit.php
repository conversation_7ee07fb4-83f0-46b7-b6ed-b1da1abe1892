<?php

namespace App\Livewire\Platforms;

use App\Livewire\Forms\PlatformForm;
use App\Models\Platform;
use App\Models\Site;
use App\Models\User;
use Livewire\Component;

class PlatformEdit extends Component
{
    public Platform $platform;
    public PlatformForm $form;

    public $sites = [];
    public $managers = [];
    public $itSupports = [];

    public function mount(Platform $platform)
    {
        $this->platform = $platform;
        $this->form->setPlatform($platform);

        $this->sites = Site::orderBy('name')->get();
        $this->managers = User::where('role_id', 4)->get(); // Platform Manager role
        $this->itSupports = User::where('role_id', 9)->get(); // IT Support role
    }

    public function submit()
    {
        $this->form->update($this->platform);

        session()->flash('message', 'Platform updated successfully!');

        return $this->redirect(route('platforms.show', $this->platform), navigate: true);
    }

    public function render()
    {
        return view('livewire.platforms.platform-edit');
    }
}
