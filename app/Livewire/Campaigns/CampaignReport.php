<?php

namespace App\Livewire\Campaigns;

use App\Livewire\Global\Page;
use App\Models\Campaign;
use App\Models\Comment;
use App\Models\Report;
use App\Models\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Barryvdh\DomPDF\Facade\Pdf;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

class CampaignReport extends Page
{
    use WithFileUploads;
    use WithPagination;

    // Basic filters
    public array $campaigns = [];
    public ?int $selectedCampaignId = null;
    public ?string $campaignSearch = '';
    public ?string $startDate = null;
    public ?string $endDate = null;
    public string $dateRange = 'this_month';
    public string $reportType = 'all';
    public string $reportCategory = 'all';
    public string $reportStatus = 'all';
    public string $reportPriority = 'all';
    public array $selectedTags = [];

    // Report creation/editing
    public string $newReport = '';
    public string $newReportTitle = '';
    public string $newReportType = 'daily';
    public string $newReportCategory = 'performance';
    public string $newReportPriority = 'normal';
    public array $newReportTags = [];
    public array $newReportMetrics = [];
    public ?float $newReportPerformanceScore = null;
    public ?float $newReportQualityScore = null;
    public ?float $newReportComplianceScore = null;

    // Editing
    public string $updatedReport = '';
    public ?int $editingReportId = null;
    public string $newResponse = '';
    public string $updatedResponse = '';
    public ?int $editingResponseId = null;

    // Comments
    public string $newComment = '';
    public ?int $replyingToCommentId = null;

    // Attachments
    public array $attachments = [];

    // Dashboard metrics
    public array $metrics = [];

    // View options
    public string $viewMode = 'list';
    public int $perPage = 10;
    public string $sortField = 'date';
    public string $sortDirection = 'desc';

    // Selected report for modal
    public $selectedReport = null;

    protected function getListeners()
    {
        return [
            'reportApproved' => 'handleReportApproved',
            'reportRejected' => 'handleReportRejected',
            'exportReport' => 'exportReport',
        ];
    }

    protected $queryString = [
        'campaignSearch' => ['except' => '', 'as' => 'as'],
        'selectedCampaignId' => ['except' => null, 'as' => 'campaign'],
        'dateRange' => ['except' => 'this_month', 'as' => 'date'],
        'reportType' => ['except' => 'all', 'as' => 'type'],
        'reportCategory' => ['except' => 'all', 'as' => 'category'],
        'reportStatus' => ['except' => 'all', 'as' => 'status'],
        'reportPriority' => ['except' => 'all', 'as' => 'priority'],
        'viewMode' => ['except' => 'list', 'as' => 'view'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
        'sortField' => ['except' => 'date', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
    ];

    protected function rules(): array
    {
        return [
            'newReport' => 'required|min:10',
            'newReportTitle' => 'required|min:3|max:255',
            'newReportType' => 'required|in:daily,weekly,monthly,quarterly,annual,ad_hoc',
            'newReportCategory' => 'required|in:performance,quality,compliance,training,operations,financial',
            'newReportPriority' => 'required|in:low,normal,high,urgent',
            'newReportTags' => 'nullable|array',
            'newReportMetrics' => 'nullable|array',
            'newReportPerformanceScore' => 'nullable|numeric|min:0|max:100',
            'newReportQualityScore' => 'nullable|numeric|min:0|max:100',
            'newReportComplianceScore' => 'nullable|numeric|min:0|max:100',
            'updatedReport' => 'required|min:10',
            'newResponse' => 'required|min:5',
            'updatedResponse' => 'required|min:5',
            'newComment' => 'required|min:2',
            'attachments.*' => 'nullable|file|max:10240',
        ];
    }

    public function mount($component = '')
    {
        parent::mount($component);

        $user = Auth::user();
        $this->campaigns = in_array($user->role_id, [1, 2])
            ? Campaign::all()->toArray()
            : [$user->campaign->toArray()];

        $this->selectedCampaignId = null;

        // Set default date range
        $this->setDateRange($this->dateRange);

        // Load dashboard metrics
        $this->loadMetrics();
    }

    /**
     * Set the page resume data based on the current view and metrics
     */
    public function setPageResume($routeName)
    {
        // Set default values
        $this->resumeTitle = 'Campaign Reports';
        $this->resumeDescription = 'Manage and view campaign reports';
        $this->resumeContentType = 'dashboard';

        // Load metrics if they're not already loaded
        if (empty($this->metrics)) {
            $this->loadMetrics();
        }

        $this->resumeData = [
            'title' => 'Reports Summary',
            'description' => 'Overview of campaign reporting metrics',
            'metrics' => [
                ['label' => 'Total Reports', 'value' => $this->metrics['total_reports'] ?? 0, 'color' => 'blue'],
                ['label' => 'Avg Performance', 'value' => $this->metrics['avg_performance_score'] ?? 0, 'color' => 'green'],
                ['label' => 'Avg Quality', 'value' => $this->metrics['avg_quality_score'] ?? 0, 'color' => 'purple'],
                ['label' => 'Avg Compliance', 'value' => $this->metrics['avg_compliance_score'] ?? 0, 'color' => 'yellow']
            ]
        ];

        // For backward compatibility
        return [
            'title' => 'Reports Summary',
            'type' => 'chart',
            'description' => 'Campaign reporting metrics'
        ];
    }

    /**
     * Set the date range based on predefined options
     */
    public function setDateRange($range)
    {
        $this->dateRange = $range;

        switch ($range) {
            case 'today':
                $this->startDate = now()->toDateString();
                $this->endDate = now()->toDateString();
                break;
            case 'yesterday':
                $this->startDate = now()->subDay()->toDateString();
                $this->endDate = now()->subDay()->toDateString();
                break;
            case 'this_week':
                $this->startDate = now()->startOfWeek()->toDateString();
                $this->endDate = now()->endOfWeek()->toDateString();
                break;
            case 'last_week':
                $this->startDate = now()->subWeek()->startOfWeek()->toDateString();
                $this->endDate = now()->subWeek()->endOfWeek()->toDateString();
                break;
            case 'this_month':
                $this->startDate = now()->startOfMonth()->toDateString();
                $this->endDate = now()->endOfMonth()->toDateString();
                break;
            case 'last_month':
                $this->startDate = now()->subMonth()->startOfMonth()->toDateString();
                $this->endDate = now()->subMonth()->endOfMonth()->toDateString();
                break;
            case 'this_quarter':
                $this->startDate = now()->startOfQuarter()->toDateString();
                $this->endDate = now()->endOfQuarter()->toDateString();
                break;
            case 'last_quarter':
                $this->startDate = now()->subQuarter()->startOfQuarter()->toDateString();
                $this->endDate = now()->subQuarter()->endOfQuarter()->toDateString();
                break;
            case 'this_year':
                $this->startDate = now()->startOfYear()->toDateString();
                $this->endDate = now()->endOfYear()->toDateString();
                break;
            case 'last_year':
                $this->startDate = now()->subYear()->startOfYear()->toDateString();
                $this->endDate = now()->subYear()->endOfYear()->toDateString();
                break;
            case 'custom':
                // Don't change the dates if custom is selected
                break;
        }

        $this->resetPage();
    }

    /**
     * Load dashboard metrics
     */
    protected function loadMetrics()
    {
        $query = Report::query()
            ->when(Auth::user()->role_id !== 2, fn($q) => $q->where('created_by', Auth::id()))
            ->when($this->selectedCampaignId, fn($q) => $q->where('campaign_id', $this->selectedCampaignId))
            ->when($this->startDate, fn($q) => $q->whereDate('date', '>=', $this->startDate))
            ->when($this->endDate, fn($q) => $q->whereDate('date', '<=', $this->endDate));

        // Total reports
        $this->metrics['total_reports'] = $query->count();

        // Reports by status
        $this->metrics['status_breakdown'] = $query->clone()
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Reports by priority
        $this->metrics['priority_breakdown'] = $query->clone()
            ->select('priority', DB::raw('count(*) as count'))
            ->groupBy('priority')
            ->pluck('count', 'priority')
            ->toArray();

        // Average scores
        $this->metrics['avg_performance_score'] = round($query->clone()->avg('performance_score') ?? 0, 2);
        $this->metrics['avg_quality_score'] = round($query->clone()->avg('quality_score') ?? 0, 2);
        $this->metrics['avg_compliance_score'] = round($query->clone()->avg('compliance_score') ?? 0, 2);

        // Reports by category
        $this->metrics['category_breakdown'] = $query->clone()
            ->select('category', DB::raw('count(*) as count'))
            ->groupBy('category')
            ->pluck('count', 'category')
            ->toArray();

        // Reports by type
        $this->metrics['type_breakdown'] = $query->clone()
            ->select('type', DB::raw('count(*) as count'))
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        // Reports over time (for trend chart)
        $this->metrics['reports_over_time'] = $query->clone()
            ->select(DB::raw('DATE(date) as report_date'), DB::raw('count(*) as count'))
            ->groupBy('report_date')
            ->orderBy('report_date')
            ->get()
            ->map(function ($item) {
                return [
                    'date' => $item->report_date,
                    'count' => $item->count
                ];
            })
            ->toArray();
    }

    public function getReportsProperty()
    {
        $query = Report::with(['creator', 'campaign', 'comments.user', 'comments.replies.user'])
            ->when(Auth::user()->role_id !== 2, fn($q) => $q->where('created_by', Auth::id()))
            ->when($this->selectedCampaignId, fn($q) => $q->where('campaign_id', $this->selectedCampaignId))
            ->when($this->startDate, fn($q) => $q->whereDate('date', '>=', $this->startDate))
            ->when($this->endDate, fn($q) => $q->whereDate('date', '<=', $this->endDate));

        // Apply filters
        if ($this->reportType !== 'all') {
            $query->where('type', $this->reportType);
        }

        if ($this->reportCategory !== 'all') {
            $query->where('category', $this->reportCategory);
        }

        if ($this->reportStatus !== 'all') {
            $query->where('status', $this->reportStatus);
        }

        if ($this->reportPriority !== 'all') {
            $query->where('priority', $this->reportPriority);
        }

        // Apply tag filters
        if (!empty($this->selectedTags)) {
            foreach ($this->selectedTags as $tag) {
                $query->where('tags', 'like', "%\"{$tag}\"%");
            }
        }

        // Apply sorting
        $query->orderBy($this->sortField, $this->sortDirection);

        // Return paginated results
        return $query->paginate($this->perPage);
    }

    /**
     * Get all available tags from reports
     */
    public function getAvailableTagsProperty()
    {
        $tags = Report::select('tags')
            ->whereNotNull('tags')
            ->get()
            ->pluck('tags')
            ->flatten()
            ->filter()
            ->unique()
            ->values()
            ->toArray();

        return $tags;
    }

    /**
     * Get chart data for the dashboard
     */
    public function getChartDataProperty()
    {
        // Prepare data for ApexCharts
        $labels = collect($this->metrics['reports_over_time'])->pluck('date')->toArray();

        $series = [
            [
                'name' => 'Reports',
                'data' => collect($this->metrics['reports_over_time'])->pluck('count')->toArray()
            ]
        ];

        return [
            'labels' => $labels,
            'series' => $series
        ];
    }

    /**
     * Get score chart data
     */
    public function getScoreChartDataProperty()
    {
        return [
            'labels' => ['Performance', 'Quality', 'Compliance'],
            'series' => [
                $this->metrics['avg_performance_score'] ?? 0,
                $this->metrics['avg_quality_score'] ?? 0,
                $this->metrics['avg_compliance_score'] ?? 0
            ]
        ];
    }

    /**
     * Submit a new report
     */
    public function submitReport()
    {
        $this->validate([
            'newReport' => 'required|min:10',
            'newReportTitle' => 'required|min:3|max:255',
            'newReportType' => 'required',
            'newReportCategory' => 'required',
            'newReportPriority' => 'required',
        ]);

        $report = Report::create([
            'title' => $this->newReportTitle,
            'content' => $this->newReport,
            'created_by' => Auth::id(),
            'date' => now()->toDateString(),
            'sent_at' => now(),
            'campaign_id' => Auth::user()->campaign->id,
            'status' => 'submitted',
            'type' => $this->newReportType,
            'category' => $this->newReportCategory,
            'priority' => $this->newReportPriority,
            'tags' => $this->newReportTags,
            'metrics' => $this->newReportMetrics,
            'performance_score' => $this->newReportPerformanceScore,
            'quality_score' => $this->newReportQualityScore,
            'compliance_score' => $this->newReportComplianceScore,
        ]);

        foreach ($this->attachments as $file) {
            $path = $file->store('reports');
            $report->media()->create([
                'file_path' => $path,
                'file_name' => $file->getClientOriginalName(),
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'uploaded_by' => Auth::id(),
            ]);
        }

        $this->reset([
            'newReport',
            'newReportTitle',
            'attachments',
            'newReportTags',
            'newReportMetrics',
            'newReportPerformanceScore',
            'newReportQualityScore',
            'newReportComplianceScore'
        ]);

        // Refresh metrics
        $this->loadMetrics();

        session()->flash('message', 'Report submitted successfully.');
    }

    /**
     * Edit a report
     */
    public function editReport(int $id)
    {
        $report = Report::findOrFail($id);
        $this->editingReportId = $id;
        $this->updatedReport = $report->content;

        // Load other report fields for editing
        $this->newReportTitle = $report->title;
        $this->newReportType = $report->type;
        $this->newReportCategory = $report->category;
        $this->newReportPriority = $report->priority;
        $this->newReportTags = $report->tags ?? [];
        $this->newReportMetrics = $report->metrics ?? [];
        $this->newReportPerformanceScore = $report->performance_score;
        $this->newReportQualityScore = $report->quality_score;
        $this->newReportComplianceScore = $report->compliance_score;
    }

    /**
     * Update a report
     */
    public function updateReport()
    {
        $this->validate([
            'updatedReport' => 'required|min:10',
            'newReportTitle' => 'required|min:3|max:255',
            'newReportType' => 'required',
            'newReportCategory' => 'required',
            'newReportPriority' => 'required',
        ]);

        $report = Report::findOrFail($this->editingReportId);

        $report->update([
            'title' => $this->newReportTitle,
            'content' => $this->updatedReport,
            'type' => $this->newReportType,
            'category' => $this->newReportCategory,
            'priority' => $this->newReportPriority,
            'tags' => $this->newReportTags,
            'metrics' => $this->newReportMetrics,
            'performance_score' => $this->newReportPerformanceScore,
            'quality_score' => $this->newReportQualityScore,
            'compliance_score' => $this->newReportComplianceScore,
        ]);

        foreach ($this->attachments as $file) {
            $path = $file->store('reports');
            $report->media()->create([
                'file_path' => $path,
                'file_name' => $file->getClientOriginalName(),
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'uploaded_by' => Auth::id(),
            ]);
        }

        $this->reset([
            'editingReportId',
            'updatedReport',
            'attachments',
            'newReportTitle',
            'newReportType',
            'newReportCategory',
            'newReportPriority',
            'newReportTags',
            'newReportMetrics',
            'newReportPerformanceScore',
            'newReportQualityScore',
            'newReportComplianceScore'
        ]);

        // Refresh metrics
        $this->loadMetrics();

        session()->flash('message', 'Report updated successfully.');
    }

    /**
     * Cancel editing a report
     */
    public function cancelEdit()
    {
        $this->reset([
            'editingReportId',
            'updatedReport',
            'newReportTitle',
            'newReportType',
            'newReportCategory',
            'newReportPriority',
            'newReportTags',
            'newReportMetrics',
            'newReportPerformanceScore',
            'newReportQualityScore',
            'newReportComplianceScore'
        ]);
    }

    /**
     * Add a comment to a report
     */
    public function addComment($reportId)
    {
        $this->validate([
            'newComment' => 'required|min:2'
        ]);

        $report = Report::findOrFail($reportId);

        $comment = new Comment([
            'content' => $this->newComment,
            'user_id' => Auth::id(),
            'parent_id' => $this->replyingToCommentId
        ]);

        $report->comments()->save($comment);

        $this->reset(['newComment', 'replyingToCommentId']);

        session()->flash('message', 'Comment added successfully.');
    }

    /**
     * Set up to reply to a comment
     */
    public function replyToComment($commentId)
    {
        $this->replyingToCommentId = $commentId;
    }

    /**
     * Cancel replying to a comment
     */
    public function cancelReply()
    {
        $this->replyingToCommentId = null;
    }

    /**
     * Mark a comment as resolved
     */
    public function resolveComment($commentId)
    {
        $comment = Comment::findOrFail($commentId);

        $comment->update([
            'is_resolved' => true,
            'resolved_by' => Auth::id(),
            'resolved_at' => now()
        ]);

        session()->flash('message', 'Comment marked as resolved.');
    }

    /**
     * Approve a report
     */
    public function approveReport($reportId, $notes = '')
    {
        // Check if user has permission to approve reports
        if (Auth::user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to approve reports.');
            return;
        }

        $report = Report::findOrFail($reportId);

        $report->update([
            'approval_status' => 'approved',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'approval_notes' => $notes
        ]);

        // Refresh metrics
        $this->loadMetrics();

        session()->flash('message', 'Report approved successfully.');
    }

    /**
     * Reject a report
     */
    public function rejectReport($reportId, $notes = '')
    {
        // Check if user has permission to reject reports
        if (Auth::user()->role_id > 3) {
            session()->flash('error', 'You do not have permission to reject reports.');
            return;
        }

        $report = Report::findOrFail($reportId);

        $report->update([
            'approval_status' => 'rejected',
            'approved_by' => Auth::id(),
            'approved_at' => now(),
            'approval_notes' => $notes
        ]);

        // Refresh metrics
        $this->loadMetrics();

        session()->flash('message', 'Report rejected.');
    }

    /**
     * Change the view mode
     */
    public function setViewMode($mode)
    {
        $this->viewMode = $mode;
    }

    /**
     * Sort by field
     */
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    /**
     * Toggle tag selection
     */
    public function toggleTag($tag)
    {
        if (in_array($tag, $this->selectedTags)) {
            $this->selectedTags = array_values(array_diff($this->selectedTags, [$tag]));
        } else {
            $this->selectedTags[] = $tag;
        }

        $this->resetPage();
    }

    /**
     * Reset all filters
     */
    public function resetFilters()
    {
        $this->reset([
            'reportType',
            'reportCategory',
            'reportStatus',
            'reportPriority',
            'selectedTags',
            'dateRange'
        ]);

        $this->setDateRange('this_month');
        $this->resetPage();
    }

    public function deleteReport(int $id)
    {
        Report::findOrFail($id)->delete();
        session()->flash('message', 'Report deleted successfully.');
    }

    public function submitResponse(int $id)
    {
        $this->validateOnly('newResponse');

        Report::findOrFail($id)->update([
            'response' => $this->newResponse,
            'responded_at' => now(),
            'status' => 'answered',
        ]);

        $this->reset('newResponse');
        session()->flash('message', 'Response submitted successfully.');
    }

    public function editResponse(int $id)
    {
        $report = Report::findOrFail($id);
        $this->editingResponseId = $id;
        $this->updatedResponse = $report->response;
    }

    public function updateResponse()
    {
        $this->validateOnly('updatedResponse');

        Report::findOrFail($this->editingResponseId)->update([
            'response' => $this->updatedResponse,
            'responded_at' => now(),
            'status' => 'answered',
        ]);

        $this->reset(['editingResponseId', 'updatedResponse']);
        session()->flash('message', 'Response updated successfully.');
    }

    public function deleteResponse(int $id)
    {
        Report::findOrFail($id)->update([
            'response' => null,
            'responded_at' => null,
            'status' => 'pending',
        ]);

        session()->flash('message', 'Response deleted successfully.');
    }

    /**
     * Export a report in the specified format
     */
    public function exportReport(int $id, string $format): StreamedResponse
    {
        $report = Report::with(['creator', 'campaign', 'comments.user', 'comments.replies.user', 'media', 'approver'])
            ->findOrFail($id);

        return response()->streamDownload(
            fn() => print ($this->generateExportContent($report, $format)),
            "report-{$report->type}-{$report->id}.{$format}"
        );
    }

    /**
     * Generate export content for a report
     */
    protected function generateExportContent(Report $report, string $format): string
    {
        if ($format === 'pdf') {
            $pdf = Pdf::loadView('exports.report', [
                'report' => $report,
                'includeComments' => true,
                'includeMetrics' => true
            ]);
            return $pdf->output(); // Return raw PDF string
        }

        if ($format === 'docx') {
            $phpWord = new PhpWord();

            // Add styles
            $titleStyle = ['bold' => true, 'size' => 18, 'color' => '333333'];
            $headingStyle = ['bold' => true, 'size' => 14, 'color' => '333333'];
            $subheadingStyle = ['bold' => true, 'size' => 12, 'color' => '666666'];
            $normalStyle = ['size' => 11, 'color' => '333333'];
            $metadataStyle = ['size' => 10, 'color' => '666666', 'italic' => true];

            // Create the document
            $section = $phpWord->addSection();

            // Add header
            $header = $section->addHeader();
            $header->addText("Report #{$report->id} - {$report->title}", ['bold' => true, 'size' => 10]);

            // Add footer
            $footer = $section->addFooter();
            $footer->addText("Generated on " . now()->format('Y-m-d H:i'), ['size' => 8], ['alignment' => 'right']);

            // Title
            $section->addText($report->title ?? "Report #{$report->id}", $titleStyle);
            $section->addTextBreak();

            // Metadata table
            $table = $section->addTable(['borderSize' => 0, 'borderColor' => 'white', 'cellMargin' => 80]);

            // Row 1
            $table->addRow();
            $table->addCell(1500)->addText('Report Type:', $subheadingStyle);
            $table->addCell(3000)->addText(ucfirst($report->type ?? 'Standard'), $normalStyle);
            $table->addCell(1500)->addText('Category:', $subheadingStyle);
            $table->addCell(3000)->addText(ucfirst($report->category ?? 'General'), $normalStyle);

            // Row 2
            $table->addRow();
            $table->addCell(1500)->addText('Date:', $subheadingStyle);
            $table->addCell(3000)->addText($report->date->format('Y-m-d'), $normalStyle);
            $table->addCell(1500)->addText('Campaign:', $subheadingStyle);
            $table->addCell(3000)->addText($report->campaign->name, $normalStyle);

            // Row 3
            $table->addRow();
            $table->addCell(1500)->addText('Created By:', $subheadingStyle);
            $table->addCell(3000)->addText("{$report->creator->first_name} {$report->creator->last_name}", $normalStyle);
            $table->addCell(1500)->addText('Status:', $subheadingStyle);
            $table->addCell(3000)->addText(ucfirst($report->status), $normalStyle);

            // Row 4 if priority and approval status are available
            if ($report->priority || $report->approval_status) {
                $table->addRow();
                $table->addCell(1500)->addText('Priority:', $subheadingStyle);
                $table->addCell(3000)->addText(ucfirst($report->priority ?? 'Normal'), $normalStyle);
                $table->addCell(1500)->addText('Approval:', $subheadingStyle);
                $table->addCell(3000)->addText(ucfirst($report->approval_status ?? 'Pending'), $normalStyle);
            }

            $section->addTextBreak();

            // Main content
            $section->addText('Report Content', $headingStyle);
            $textLines = explode("\n", strip_tags($report->content));
            foreach ($textLines as $line) {
                $section->addText($line, $normalStyle);
            }
            $section->addTextBreak();

            // Response if available
            if ($report->response) {
                $section->addText('Response', $headingStyle);
                $responseLines = explode("\n", strip_tags($report->response));
                foreach ($responseLines as $line) {
                    $section->addText($line, $normalStyle);
                }
                $section->addTextBreak();
            }

            // Save to temp file
            $tempPath = storage_path("app/temp-report-{$report->id}.docx");
            $writer = IOFactory::createWriter($phpWord, 'Word2007');
            $writer->save($tempPath);

            return file_get_contents($tempPath);
        }

        return "Unsupported export format.";
    }

    /**
     * Open the report detail modal
     */
    public function openReportModal($reportId)
    {
        $this->selectedReport = Report::findOrFail($reportId);
    }

    /**
     * Handle report approval from modal
     */
    public function handleReportApproved($reportId)
    {
        // Refresh metrics
        $this->loadMetrics();

        session()->flash('message', 'Report approved successfully.');
    }

    /**
     * Handle report rejection from modal
     */
    public function handleReportRejected($reportId)
    {
        // Refresh metrics
        $this->loadMetrics();

        session()->flash('message', 'Report rejected.');
    }

    public function updatedCampaignSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedCampaignId = null;
        }
    }

    public function getFilteredCampaignsProperty()
    {
        $term = trim($this->campaignSearch);
        $query = Campaign::query();

        if (Auth::user()->role_id === 3) {
            $query->where('user_id', Auth::id());
        }

        if ($term !== '') {
            $query->where('name', 'like', "%{$term}%");
        }

        return $query->orderBy('name')->limit(10)->get();
    }

    public function selectCampaign(int $id)
    {
        $this->selectedCampaignId = $id;

        $campaign = collect($this->campaigns)
            ->first(fn($c) => $c['id'] === $id);

        $this->campaignSearch = $campaign['name'] ?? '';
    }

    public function render()
    {
        // Emit event to update charts when view mode changes to dashboard
        if ($this->viewMode === 'dashboard') {
            $this->dispatch('chartsUpdated');
        }

        // Set page resume data
        $this->setPageResume($this->current_route);

        return view('livewire.campaigns.campaign-report', [
            'reports' => $this->reports,
            'director' => User::where('role_id', 2)->first(),
            'filteredCampaigns' => $this->filteredCampaigns,
        ]);
    }
}