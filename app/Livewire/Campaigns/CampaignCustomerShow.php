<?php

namespace App\Livewire\Campaigns;

use App\Models\Customer;
use Livewire\Component;

class CampaignCustomerShow extends Component
{
    public $customer;

    public function mount(Customer $customer)
    {
        $this->customer = $customer->load(['campaigns', 'contacts', 'documents', 'accountManager']);
    }

    public function delete()
    {
        return $this->redirect(route('campaigns.customers.delete', $this->customer), navigate: true);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-customer-show');
    }
}
