<?php

namespace App\Livewire\Campaigns;

use App\Livewire\Forms\CampaignForm;
use App\Models\Campaign;
use App\Models\Customer;
use App\Models\Platform;
use App\Models\User;
use Livewire\Component;

class CampaignCreate extends Component
{
    public CampaignForm $form;
    
    public $customers = [];
    public $platforms = [];
    public $campaignManagers = [];
    
    public function mount()
    {
        $this->customers = Customer::all();
        $this->platforms = Platform::where('status', 'active')->get();
        $this->campaignManagers = User::where('role_id', 5)->get(); // Campaign Supervisor role
    }
    
    public function save()
    {
        $this->validate();
        
        Campaign::create([
            'name' => $this->form->name,
            'customer_id' => $this->form->customer_id,
            'platform_id' => $this->form->platform_id,
            'manager_id' => $this->form->manager_id,
            'start_date' => $this->form->start_date,
            'end_date' => $this->form->end_date,
            'status' => $this->form->status,
        ]);
        
        session()->flash('message', 'Campaign created successfully.');
        
        return $this->redirect(route('campaigns.index'), navigate: true);
    }
    
    public function render()
    {
        return view('livewire.campaigns.campaign-create');
    }
}
