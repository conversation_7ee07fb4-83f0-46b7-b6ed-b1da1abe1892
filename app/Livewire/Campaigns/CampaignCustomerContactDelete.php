<?php

namespace App\Livewire\Campaigns;

use App\Models\CustomerContact;
use Livewire\Attributes\On;
use Livewire\Component;

class CampaignCustomerContactDelete extends Component
{
    public $contact;
    public $customerId;
    public $confirmationText = '';
    public $confirmationValid = false;

    public function mount(CustomerContact $contact)
    {
        $this->contact = $contact;
        $this->customerId = $contact->customer_id;
    }

    public function updatedConfirmationText()
    {
        $this->confirmationValid = ($this->confirmationText === $this->contact->name);
    }

    #[On('destroy-contact')]
    public function destroyContact()
    {
        // Validate that the confirmation text matches the contact name
        if ($this->confirmationText !== $this->contact->name) {
            $this->addError('confirmationText', 'Please type the exact contact name to confirm deletion.');
            return;
        }

        try {
            // Store the name for the success message
            $name = $this->contact->name;

            // Delete the contact
            $this->contact->delete();

            // Flash success message
            session()->flash('message', "Contact '{$name}' has been deleted successfully.");

            // Redirect back to the customer show page
            return $this->redirect(route('campaigns.customers.show', $this->customerId), navigate: true);
        } catch (\Exception $e) {
            // Handle any exceptions that might occur during deletion
            session()->flash('error', 'An error occurred while deleting the contact: ' . $e->getMessage());
            return $this->redirect(route('campaigns.customers.show', $this->customerId), navigate: true);
        }
    }

    // Keep the original delete method for backward compatibility
    public function delete()
    {
        return $this->destroyContact();
    }

    #[On('to-customer-show')]
    public function navigateToShow($data = null)
    {
        if (isset($data['customer'])) {
            return redirect()->route('campaigns.customers.show', ['customer' => $data['customer']]);
        } else {
            // If no specific customer is provided, use the current one
            return redirect()->route('campaigns.customers.show', ['customer' => $this->customerId]);
        }
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-customer-contact-delete');
    }
}
