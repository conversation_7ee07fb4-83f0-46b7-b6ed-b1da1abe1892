<?php

namespace App\Livewire\Campaigns;

use App\Models\Customer;
use App\Models\CustomerContact;
use Livewire\Component;
use Livewire\WithPagination;

class CampaignCustomerContacts extends Component
{
    use WithPagination;

    public $customerId;
    public $showForm = false;
    public $isEditing = false;
    public $contactId = null;

    // Contact properties
    public $firstName;
    public $lastName;
    public $position;
    public $department;
    public $email;
    public $phone;
    public $mobile;
    public $isPrimary = false;
    public $isDecisionMaker = false;
    public $isBillingContact = false;
    public $isTechnicalContact = false;
    public $notes;
    public $status = 'active';

    // Search and filter
    public $search = '';
    public $filterStatus = '';
    public $perPage = 10;

    protected $listeners = ['refreshContacts' => '$refresh'];

    public function mount($customerId)
    {
        $this->customerId = $customerId;
    }

    // Reset pagination when filters change
    public function updatedSearch() { $this->resetPage(); }
    public function updatedFilterStatus() { $this->resetPage(); }
    public function updatedPerPage() { $this->resetPage(); }

    // Show create form
    public function create()
    {
        $this->resetForm();
        $this->showForm = true;
        $this->isEditing = false;
    }

    // Show edit form
    public function edit($contactId)
    {
        $this->resetForm();
        $this->contactId = $contactId;
        $this->isEditing = true;
        $this->showForm = true;

        $contact = CustomerContact::findOrFail($contactId);

        // Fill form with contact data
        $this->firstName = $contact->first_name;
        $this->lastName = $contact->last_name;
        $this->position = $contact->position;
        $this->department = $contact->department;
        $this->email = $contact->email;
        $this->phone = $contact->phone;
        $this->mobile = $contact->mobile;
        $this->isPrimary = $contact->is_primary;
        $this->isDecisionMaker = $contact->is_decision_maker;
        $this->isBillingContact = $contact->is_billing_contact;
        $this->isTechnicalContact = $contact->is_technical_contact;
        $this->notes = $contact->notes;
        $this->status = $contact->status;
    }

    // Reset form fields
    public function resetForm()
    {
        $this->reset([
            'contactId', 'firstName', 'lastName', 'position', 'department',
            'email', 'phone', 'mobile', 'isPrimary', 'isDecisionMaker',
            'isBillingContact', 'isTechnicalContact', 'notes'
        ]);

        $this->status = 'active';
    }

    // Close form
    public function cancelForm()
    {
        $this->showForm = false;
        $this->resetForm();
    }

    // Save contact
    public function save()
    {
        $this->validate([
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive',
        ]);

        $contactData = [
            'customer_id' => $this->customerId,
            'first_name' => $this->firstName,
            'last_name' => $this->lastName,
            'position' => $this->position,
            'department' => $this->department,
            'email' => $this->email,
            'phone' => $this->phone,
            'mobile' => $this->mobile,
            'is_primary' => $this->isPrimary,
            'is_decision_maker' => $this->isDecisionMaker,
            'is_billing_contact' => $this->isBillingContact,
            'is_technical_contact' => $this->isTechnicalContact,
            'notes' => $this->notes,
            'status' => $this->status,
        ];

        // If setting as primary, unset any existing primary contacts
        if ($this->isPrimary) {
            CustomerContact::where('customer_id', $this->customerId)
                ->where('is_primary', true)
                ->when($this->isEditing, function ($query) {
                    $query->where('id', '!=', $this->contactId);
                })
                ->update(['is_primary' => false]);
        }

        if ($this->isEditing) {
            $contact = CustomerContact::findOrFail($this->contactId);
            $contact->update($contactData);
            $message = 'Contact updated successfully.';
        } else {
            CustomerContact::create($contactData);
            $message = 'Contact created successfully.';
        }

        $this->showForm = false;
        $this->resetForm();
        $this->dispatch('show-message', ['message' => $message]);
        $this->dispatch('refreshContacts');
    }

    // Delete contact
    public function delete($contactId)
    {
        $contact = CustomerContact::findOrFail($contactId);
        return $this->redirect(route('campaigns.customers.contacts.delete', ['customer' => $this->customerId, 'contact' => $contactId]), navigate: true);
    }

    // Get contacts query with filters applied
    public function getContactsQueryProperty()
    {
        return CustomerContact::where('customer_id', $this->customerId)
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('first_name', 'like', '%' . $this->search . '%')
                        ->orWhere('last_name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%')
                        ->orWhere('phone', 'like', '%' . $this->search . '%')
                        ->orWhere('position', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->filterStatus, fn($query) => $query->where('status', $this->filterStatus))
            ->orderBy('is_primary', 'desc')
            ->orderBy('first_name');
    }

    // Get contacts with pagination
    public function getContactsProperty()
    {
        return $this->contactsQuery->paginate($this->perPage);
    }

    // Get customer
    public function getCustomerProperty()
    {
        return Customer::findOrFail($this->customerId);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-customer-contacts', [
            'contacts' => $this->contacts,
            'customer' => $this->customer,
        ]);
    }
}
