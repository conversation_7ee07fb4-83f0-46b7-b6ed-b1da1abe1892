<?php

namespace App\Livewire\Campaigns;

use App\Models\Report;
use App\Models\Comment;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ReportDetailModal extends Component
{
    public $reportId;
    public $report;
    public $newComment = '';
    public $replyingToCommentId = null;

    protected function getListeners()
    {
        return [
            'openReportModal' => 'openModal',
        ];
    }

    public function mount()
    {
        // Initialize with empty report
        $this->report = null;
    }

    public function openModal($params)
    {
        $this->reportId = $params['id'] ?? null;
        if ($this->reportId) {
            $this->loadReport();
            $this->dispatch('open-modal');
        }
    }

    public function loadReport()
    {
        $this->report = Report::with([
            'creator',
            'campaign',
            'comments.user',
            'comments.replies.user',
            'comments.resolver',
            'media',
            'approver'
        ])->findOrFail($this->reportId);
    }

    public function addComment($reportId)
    {
        $this->validate([
            'newComment' => 'required|min:2'
        ]);

        $report = Report::findOrFail($reportId);

        $comment = new Comment([
            'content' => $this->newComment,
            'user_id' => Auth::id(),
            'parent_id' => $this->replyingToCommentId
        ]);

        $report->comments()->save($comment);

        $this->reset(['newComment', 'replyingToCommentId']);
        $this->loadReport();

        $this->dispatch('comment-added');
    }

    public function replyToComment($commentId)
    {
        $this->replyingToCommentId = $commentId;
    }

    public function cancelReply()
    {
        $this->replyingToCommentId = null;
    }

    public function resolveComment($commentId)
    {
        $comment = Comment::findOrFail($commentId);

        $comment->update([
            'is_resolved' => true,
            'resolved_by' => Auth::id(),
            'resolved_at' => now()
        ]);

        $this->loadReport();

        $this->dispatch('comment-resolved');
    }

    public function approveReport($reportId)
    {
        // Check if user has permission to approve reports
        if (Auth::user()->role_id > 3) {
            $this->dispatch('show-error', ['message' => 'You do not have permission to approve reports.']);
            return;
        }

        $report = Report::findOrFail($reportId);

        $report->update([
            'approval_status' => 'approved',
            'approved_by' => Auth::id(),
            'approved_at' => now()
        ]);

        $this->loadReport();
        $this->dispatch('reportApproved', $reportId);

        $this->dispatch('show-message', ['message' => 'Report approved successfully.']);
    }

    public function rejectReport($reportId)
    {
        // Check if user has permission to reject reports
        if (Auth::user()->role_id > 3) {
            $this->dispatch('show-error', ['message' => 'You do not have permission to reject reports.']);
            return;
        }

        $report = Report::findOrFail($reportId);

        $report->update([
            'approval_status' => 'rejected',
            'approved_by' => Auth::id(),
            'approved_at' => now()
        ]);

        $this->loadReport();
        $this->dispatch('reportRejected', $reportId);

        $this->dispatch('show-message', ['message' => 'Report rejected.']);
    }

    public function exportReport($reportId, $format)
    {
        $this->dispatch('exportReport', $reportId, $format);
    }

    public function render()
    {
        return view('livewire.campaigns.report-detail-modal');
    }
}
