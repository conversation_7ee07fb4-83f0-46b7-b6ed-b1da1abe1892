<?php

namespace App\Livewire\Campaigns;

use App\Models\Campaign;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class CampaignEligibleAgents extends Component
{
    use WithPagination;
    
    public Campaign $campaign;
    public $search = '';
    public $perPage = 10;
    public $showOnlyEligible = true;
    
    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'showOnlyEligible' => ['except' => true],
    ];
    
    public function mount(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingShowOnlyEligible()
    {
        $this->resetPage();
    }
    
    public function assignAgent($agentId)
    {
        $agent = User::findOrFail($agentId);
        
        if (!$agent->isEligibleFor($this->campaign) && $this->showOnlyEligible) {
            session()->flash('error', 'This agent does not meet all the requirements for this campaign.');
            return;
        }
        
        $agent->update([
            'campaign_id' => $this->campaign->id,
            'status' => 'active',
        ]);
        
        session()->flash('message', 'Agent assigned to campaign successfully.');
    }
    
    public function render()
    {
        $query = User::where('role_id', 6) // Agent role
            ->where(function($query) {
                $query->where('status', 'validated')
                    ->orWhere('status', 'active');
            })
            ->whereNull('campaign_id')
            ->when($this->search, function ($query) {
                return $query->where(function ($q) {
                    $q->where('first_name', 'like', '%' . $this->search . '%')
                      ->orWhere('last_name', 'like', '%' . $this->search . '%')
                      ->orWhere('email', 'like', '%' . $this->search . '%');
                });
            });
            
        if ($this->showOnlyEligible) {
            $agents = $query->get()->filter(function($agent) {
                return $agent->isEligibleFor($this->campaign);
            });
            
            // Manual pagination for filtered collection
            $page = $this->page ?: 1;
            $perPage = $this->perPage;
            $offset = ($page - 1) * $perPage;
            
            $paginatedAgents = new \Illuminate\Pagination\LengthAwarePaginator(
                $agents->slice($offset, $perPage),
                $agents->count(),
                $perPage,
                $page,
                ['path' => \Illuminate\Pagination\Paginator::resolveCurrentPath()]
            );
        } else {
            $paginatedAgents = $query->paginate($this->perPage);
            
            // Add eligibility information to each agent
            $paginatedAgents->getCollection()->each(function($agent) {
                $agent->is_eligible = $agent->isEligibleFor($this->campaign);
                $agent->missing_skills = $this->getMissingSkills($agent);
                $agent->missing_certifications = $this->getMissingCertifications($agent);
            });
        }
        
        return view('livewire.campaigns.campaign-eligible-agents', [
            'agents' => $paginatedAgents,
        ]);
    }
    
    private function getMissingSkills(User $agent)
    {
        $missingSkills = [];
        
        foreach ($this->campaign->requiredSkills as $requiredSkill) {
            if ($requiredSkill->pivot->is_mandatory && 
                !$agent->hasSkill($requiredSkill->id, $requiredSkill->pivot->minimum_proficiency_level)) {
                $missingSkills[] = [
                    'name' => $requiredSkill->name,
                    'required_level' => $requiredSkill->pivot->minimum_proficiency_level,
                    'current_level' => $agent->skills()
                        ->where('skill_id', $requiredSkill->id)
                        ->first()
                        ?->pivot
                        ?->proficiency_level ?? 0
                ];
            }
        }
        
        return $missingSkills;
    }
    
    private function getMissingCertifications(User $agent)
    {
        $missingCertifications = [];
        
        foreach ($this->campaign->requiredCertifications as $requiredCertification) {
            if ($requiredCertification->pivot->is_mandatory && 
                !$agent->hasCertification($requiredCertification->id)) {
                $missingCertifications[] = [
                    'name' => $requiredCertification->name,
                    'issuer' => $requiredCertification->issuer,
                ];
            }
        }
        
        return $missingCertifications;
    }
}
