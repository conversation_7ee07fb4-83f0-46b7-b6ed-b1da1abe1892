<?php

namespace App\Livewire\Campaigns;

use App\Models\Customer;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;

class CampaignCustomers extends Component
{
    use WithPagination, WithFileUploads;

    // Search and filter properties
    public $search = '';
    public $filterStatus = '';
    public $filterType = '';
    public $filterSegment = '';
    public $filterSize = '';
    public $filterTier = '';
    public $filterIndustry = '';
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $perPage = 10;

    // View mode
    public $viewMode = 'list'; // list, grid, dashboard

    // Selection properties
    public $selectedCustomers = [];
    public $selectAll = false;

    // Listeners
    protected function getListeners()
    {
        return [
            'refreshCustomers' => '$refresh',
        ];
    }

    // Handle select all checkbox
    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedCustomers = $this->customers->pluck('id')->map(fn($id) => (string) $id)->toArray();
        } else {
            $this->selectedCustomers = [];
        }
    }

    // Toggle customer selection
    public function toggleCustomerSelection($customerId)
    {
        $customerId = (string) $customerId;
        if (in_array($customerId, $this->selectedCustomers)) {
            $this->selectedCustomers = array_diff($this->selectedCustomers, [$customerId]);
        } else {
            $this->selectedCustomers[] = $customerId;
        }

        // Update selectAll property based on whether all customers are selected
        $this->selectAll = count($this->selectedCustomers) === $this->customers->count();
    }

    // Delete selected customers
    public function deleteSelected()
    {
        if (empty($this->selectedCustomers)) {
            $this->dispatch('show-error', ['message' => 'No customers selected.']);
            return;
        }

        $customersWithActiveCampaigns = Customer::whereIn('id', $this->selectedCustomers)
            ->whereHas('campaigns', function($query) {
                $query->where('status', 'active');
            })
            ->count();

        if ($customersWithActiveCampaigns > 0) {
            $this->dispatch('show-error', ['message' => 'Cannot delete customers with active campaigns.']);
            return;
        }

        Customer::whereIn('id', $this->selectedCustomers)->delete();
        $this->selectedCustomers = [];
        $this->selectAll = false;
        $this->dispatch('show-message', ['message' => 'Selected customers deleted successfully.']);
    }

    // Reset pagination when filters change
    public function updatedSearch() { $this->resetPage(); }
    public function updatedFilterStatus() { $this->resetPage(); }
    public function updatedFilterType() { $this->resetPage(); }
    public function updatedFilterSegment() { $this->resetPage(); }
    public function updatedFilterSize() { $this->resetPage(); }
    public function updatedFilterTier() { $this->resetPage(); }
    public function updatedFilterIndustry() { $this->resetPage(); }
    public function updatedPerPage() { $this->resetPage(); }

    // Sort results
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
    }

    // Change view mode
    public function setViewMode($mode)
    {
        $this->viewMode = $mode;

        if ($mode === 'dashboard') {
            $this->dispatch('dashboardUpdated');
        }
    }

    // Delete customer
    public function delete($customerId)
    {
        return $this->redirect(route('campaigns.customers.delete', $customerId), navigate: true);
    }

    // Get available account managers
    public function getAccountManagersProperty()
    {
        return User::whereIn('role_id', [1, 2, 3]) // Admin, Director, Manager roles
            ->orderBy('name')
            ->get();
    }

    // Get available industries
    public function getIndustriesProperty()
    {
        return Customer::distinct()->pluck('industry')->filter()->values();
    }

    // Get available customer types
    public function getCustomerTypesProperty()
    {
        try {
            return Customer::distinct()->pluck('customer_type')->filter()->values();
        } catch (\Exception $e) {
            return collect([]);
        }
    }

    // Get available segments
    public function getSegmentsProperty()
    {
        try {
            return Customer::distinct()->pluck('segment')->filter()->values();
        } catch (\Exception $e) {
            return collect([]);
        }
    }

    // Get available sizes
    public function getSizesProperty()
    {
        try {
            return Customer::distinct()->pluck('size')->filter()->values();
        } catch (\Exception $e) {
            return collect([]);
        }
    }

    // Get available tiers
    public function getTiersProperty()
    {
        try {
            return Customer::distinct()->pluck('customer_tier')->filter()->values();
        } catch (\Exception $e) {
            return collect([]);
        }
    }

    // Get customers query with filters applied
    public function getCustomersQueryProperty()
    {
        $query = Customer::query()
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%')
                        ->orWhere('phone', 'like', '%' . $this->search . '%')
                        ->orWhere('contact_person', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->filterStatus, fn($query) => $query->where('status', $this->filterStatus))
            ->when($this->filterIndustry, fn($query) => $query->where('industry', $this->filterIndustry));

        // Add filters for new columns only if they exist
        try {
            if ($this->filterType) {
                $query->where('customer_type', $this->filterType);
            }

            if ($this->filterSegment) {
                $query->where('segment', $this->filterSegment);
            }

            if ($this->filterSize) {
                $query->where('size', $this->filterSize);
            }

            if ($this->filterTier) {
                $query->where('customer_tier', $this->filterTier);
            }
        } catch (\Exception $e) {
            // Ignore errors if columns don't exist
        }

        return $query->orderBy($this->sortField, $this->sortDirection);
    }

    // Get customers with pagination
    public function getCustomersProperty()
    {
        return $this->customersQuery->paginate($this->perPage);
    }

    // Get customer statistics for dashboard
    public function getCustomerStatsProperty()
    {
        $stats = [
            'total' => Customer::count(),
            'active' => Customer::where('status', 'active')->count(),
            'inactive' => Customer::where('status', 'inactive')->count(),
            'withCampaigns' => Customer::has('campaigns')->count(),
            'withoutCampaigns' => Customer::doesntHave('campaigns')->count(),
            'byIndustry' => Customer::selectRaw('industry, count(*) as count')
                ->groupBy('industry')
                ->having('industry', '!=', '')
                ->whereNotNull('industry')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get(),
        ];

        // Add stats for new columns only if they exist
        try {
            $stats['byType'] = Customer::selectRaw('customer_type, count(*) as count')
                ->groupBy('customer_type')
                ->having('customer_type', '!=', '')
                ->whereNotNull('customer_type')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get();

            $stats['bySegment'] = Customer::selectRaw('segment, count(*) as count')
                ->groupBy('segment')
                ->having('segment', '!=', '')
                ->whereNotNull('segment')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            // Set empty collections if columns don't exist
            $stats['byType'] = collect([]);
            $stats['bySegment'] = collect([]);
        }

        return $stats;
    }

    public function render()
    {
        // Emit event to update charts when view mode changes to dashboard
        if ($this->viewMode === 'dashboard') {
            $this->dispatch('dashboardUpdated');
        }

        return view('livewire.campaigns.campaign-customers', [
            'customers' => $this->customers,
            'accountManagers' => $this->accountManagers,
            'industries' => $this->industries,
            'customerTypes' => $this->customerTypes,
            'segments' => $this->segments,
            'sizes' => $this->sizes,
            'tiers' => $this->tiers,
            'customerStats' => $this->customerStats,
        ]);
    }
}
