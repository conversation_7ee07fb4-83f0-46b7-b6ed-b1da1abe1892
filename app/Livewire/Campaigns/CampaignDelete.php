<?php

namespace App\Livewire\Campaigns;

use App\Models\Campaign;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;

class CampaignDelete extends Component
{
    public Campaign $campaign;
    public $name;
    
    #[On('destroy-campaign')]
    public function destroyCampaign()
    {
        $this->campaign->delete();

        session()->flash('message', 'Selected campaigns deleted successfully!');

        return $this->redirect(route('campaigns.index'), navigate: true);
    }

    public function mount(Campaign $campaign)
    {
        $this->name = $campaign->name;
    }
    
    public function render()
    {
        return view('livewire.campaigns.campaign-delete');
    }
}

