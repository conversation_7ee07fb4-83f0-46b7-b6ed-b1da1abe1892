<?php

namespace App\Livewire\Campaigns;

use App\Models\Customer;
use App\Models\CustomerDocument;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class CampaignCustomerDocuments extends Component
{
    use WithPagination, WithFileUploads;

    public $customerId;
    public $showForm = false;
    public $isEditing = false;
    public $documentId = null;

    // Document properties
    public $title;
    public $documentType;
    public $description;
    public $documentDate;
    public $expiryDate;
    public $isConfidential = false;
    public $file; // For file upload
    public $existingFilePath; // For editing

    // Search and filter
    public $search = '';
    public $filterType = '';
    public $filterConfidential = '';
    public $perPage = 10;

    protected $listeners = ['refreshDocuments' => '$refresh'];

    public function mount($customerId)
    {
        $this->customerId = $customerId;
    }

    // Reset pagination when filters change
    public function updatedSearch() { $this->resetPage(); }
    public function updatedFilterType() { $this->resetPage(); }
    public function updatedFilterConfidential() { $this->resetPage(); }
    public function updatedPerPage() { $this->resetPage(); }

    // Show create form
    public function create()
    {
        $this->resetForm();
        $this->showForm = true;
        $this->isEditing = false;
    }

    // Show edit form
    public function edit($documentId)
    {
        $this->resetForm();
        $this->documentId = $documentId;
        $this->isEditing = true;
        $this->showForm = true;

        $document = CustomerDocument::findOrFail($documentId);

        // Fill form with document data
        $this->title = $document->title;
        $this->documentType = $document->document_type;
        $this->description = $document->description;
        $this->documentDate = $document->document_date ? $document->document_date->format('Y-m-d') : null;
        $this->expiryDate = $document->expiry_date ? $document->expiry_date->format('Y-m-d') : null;
        $this->isConfidential = $document->is_confidential;
        $this->existingFilePath = $document->file_path;
    }

    // Reset form fields
    public function resetForm()
    {
        $this->reset([
            'documentId', 'title', 'documentType', 'description', 'documentDate',
            'expiryDate', 'isConfidential', 'file', 'existingFilePath'
        ]);
    }

    // Close form
    public function cancelForm()
    {
        $this->showForm = false;
        $this->resetForm();
    }

    // Save document
    public function save()
    {
        $rules = [
            'title' => 'required|string|max:255',
            'documentType' => 'required|string|max:255',
            'description' => 'nullable|string',
            'documentDate' => 'nullable|date',
            'expiryDate' => 'nullable|date',
            'isConfidential' => 'boolean',
        ];

        if (!$this->isEditing || $this->file) {
            $rules['file'] = 'required|file|max:10240'; // 10MB max
        }

        $this->validate($rules);

        $filePath = $this->existingFilePath;
        $fileName = null;
        $fileType = null;
        $fileSize = null;

        // Handle file upload
        if ($this->file) {
            $fileName = $this->file->getClientOriginalName();
            $fileType = $this->file->getMimeType();
            $fileSize = $this->file->getSize();

            // Generate a unique path for the file
            $filePath = $this->file->store('customer-documents/' . $this->customerId, 'public');

            // If editing and replacing file, delete the old one
            if ($this->isEditing && $this->existingFilePath) {
                Storage::disk('public')->delete($this->existingFilePath);
            }
        }

        $documentData = [
            'customer_id' => $this->customerId,
            'title' => $this->title,
            'document_type' => $this->documentType,
            'description' => $this->description,
            'document_date' => $this->documentDate,
            'expiry_date' => $this->expiryDate,
            'is_confidential' => $this->isConfidential,
            'uploaded_by' => Auth::id(),
        ];

        if ($filePath) {
            $documentData['file_path'] = $filePath;
        }

        if ($fileName) {
            $documentData['file_name'] = $fileName;
        }

        if ($fileType) {
            $documentData['file_type'] = $fileType;
        }

        if ($fileSize) {
            $documentData['file_size'] = $fileSize;
        }

        if ($this->isEditing) {
            $document = CustomerDocument::findOrFail($this->documentId);
            $document->update($documentData);
            $message = 'Document updated successfully.';
        } else {
            CustomerDocument::create($documentData);
            $message = 'Document uploaded successfully.';
        }

        $this->showForm = false;
        $this->resetForm();
        $this->dispatch('show-message', ['message' => $message]);
        $this->dispatch('refreshDocuments');
    }

    // Delete document
    public function delete($documentId)
    {
        $document = CustomerDocument::findOrFail($documentId);
        return $this->redirect(route('campaigns.customers.documents.delete', ['customer' => $this->customerId, 'document' => $documentId]), navigate: true);
    }

    // Download document
    public function download($documentId)
    {
        $document = CustomerDocument::findOrFail($documentId);

        return Storage::disk('public')->download(
            $document->file_path,
            $document->file_name
        );
    }

    // Get document types
    public function getDocumentTypesProperty()
    {
        return [
            'contract' => 'Contract',
            'invoice' => 'Invoice',
            'proposal' => 'Proposal',
            'agreement' => 'Agreement',
            'nda' => 'Non-Disclosure Agreement',
            'id' => 'Identification Document',
            'certificate' => 'Certificate',
            'report' => 'Report',
            'other' => 'Other',
        ];
    }

    // Get documents query with filters applied
    public function getDocumentsQueryProperty()
    {
        return CustomerDocument::where('customer_id', $this->customerId)
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('title', 'like', '%' . $this->search . '%')
                        ->orWhere('description', 'like', '%' . $this->search . '%')
                        ->orWhere('file_name', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->filterType, fn($query) => $query->where('document_type', $this->filterType))
            ->when($this->filterConfidential !== '', function ($query) {
                $isConfidential = $this->filterConfidential === 'yes';
                $query->where('is_confidential', $isConfidential);
            })
            ->orderBy('document_date', 'desc');
    }

    // Get documents with pagination
    public function getDocumentsProperty()
    {
        return $this->documentsQuery->paginate($this->perPage);
    }

    // Get customer
    public function getCustomerProperty()
    {
        return Customer::findOrFail($this->customerId);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-customer-documents', [
            'documents' => $this->documents,
            'customer' => $this->customer,
            'documentTypes' => $this->documentTypes,
        ]);
    }
}
