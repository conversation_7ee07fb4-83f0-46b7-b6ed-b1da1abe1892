<?php

namespace App\Livewire\Campaigns;

use App\Models\Customer;
use App\Models\User;
use Livewire\Component;

class CampaignCustomerEdit extends Component
{
    public $customer;
    
    // Customer properties
    public $name;
    public $email;
    public $phone;
    public $address;
    public $city;
    public $country;
    public $postalCode;
    public $website;
    public $industry;
    public $notes;
    public $contactPerson;
    public $contactPosition;
    public $contactPhone;
    public $status = 'active';
    public $customerType;
    public $size;
    public $segment;
    public $annualRevenue;
    public $employeeCount;
    public $taxId;
    public $registrationNumber;
    public $establishedDate;
    public $relationshipSince;
    public $accountManagerId;
    public $customerSatisfaction;
    public $customerTier;
    public $tags = [];
    public $customFields = [];
    public $languagePreference;
    public $timezone;

    public function mount(Customer $customer)
    {
        $this->customer = $customer;
        
        // Fill form with customer data
        $this->name = $customer->name;
        $this->email = $customer->email;
        $this->phone = $customer->phone;
        $this->address = $customer->address;
        $this->city = $customer->city;
        $this->country = $customer->country;
        $this->postalCode = $customer->postal_code;
        $this->website = $customer->website;
        $this->industry = $customer->industry;
        $this->notes = $customer->notes;
        $this->contactPerson = $customer->contact_person;
        $this->contactPosition = $customer->contact_position;
        $this->contactPhone = $customer->contact_phone;
        $this->status = $customer->status;
        $this->customerType = $customer->customer_type;
        $this->size = $customer->size;
        $this->segment = $customer->segment;
        $this->annualRevenue = $customer->annual_revenue;
        $this->employeeCount = $customer->employee_count;
        $this->taxId = $customer->tax_id;
        $this->registrationNumber = $customer->registration_number;
        $this->establishedDate = $customer->established_date ? $customer->established_date->format('Y-m-d') : null;
        $this->relationshipSince = $customer->relationship_since ? $customer->relationship_since->format('Y-m-d') : null;
        $this->accountManagerId = $customer->account_manager_id;
        $this->customerSatisfaction = $customer->customer_satisfaction;
        $this->customerTier = $customer->customer_tier;
        $this->tags = $customer->tags ?? [];
        $this->customFields = $customer->custom_fields ?? [];
        $this->languagePreference = $customer->language_preference;
        $this->timezone = $customer->timezone;
    }

    // Get available account managers
    public function getAccountManagersProperty()
    {
        return User::whereIn('role_id', [1, 2, 3]) // Admin, Director, Manager roles
            ->orderBy('name')
            ->get();
    }

    // Save customer
    public function save()
    {
        $this->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive',
        ]);

        $customerData = [
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'city' => $this->city,
            'country' => $this->country,
            'postal_code' => $this->postalCode,
            'website' => $this->website,
            'industry' => $this->industry,
            'notes' => $this->notes,
            'contact_person' => $this->contactPerson,
            'contact_position' => $this->contactPosition,
            'contact_phone' => $this->contactPhone,
            'status' => $this->status,
            'customer_type' => $this->customerType,
            'size' => $this->size,
            'segment' => $this->segment,
            'annual_revenue' => $this->annualRevenue,
            'employee_count' => $this->employeeCount,
            'tax_id' => $this->taxId,
            'registration_number' => $this->registrationNumber,
            'established_date' => $this->establishedDate,
            'relationship_since' => $this->relationshipSince,
            'account_manager_id' => $this->accountManagerId,
            'customer_satisfaction' => $this->customerSatisfaction,
            'customer_tier' => $this->customerTier,
            'tags' => $this->tags,
            'custom_fields' => $this->customFields,
            'language_preference' => $this->languagePreference,
            'timezone' => $this->timezone,
        ];

        $this->customer->update($customerData);
        
        session()->flash('message', 'Customer updated successfully.');
        
        $this->dispatch('to-customer-show', ['customer' => $this->customer->id]);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-customer-edit', [
            'accountManagers' => $this->accountManagers,
        ]);
    }
}
