<?php

namespace App\Livewire\Campaigns;

use App\Models\Report;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class CampaignReportShow extends Component
{
    public $report;
    public $reportId;
    
    public function mount($report)
    {
        $this->reportId = $report;
        $this->loadReport();
    }
    
    public function loadReport()
    {
        $this->report = Report::with(['creator', 'campaign', 'comments.user', 'comments.replies.user', 'media', 'approver'])
            ->findOrFail($this->reportId);
    }
    
    /**
     * Export report to PDF or DOCX
     */
    public function exportReport($format = 'pdf')
    {
        // Implement export functionality
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => __('Report exported successfully as ' . strtoupper($format))
        ]);
    }
    
    /**
     * Approve report
     */
    public function approveReport()
    {
        if (Auth::user()->role_id <= 3) {
            $this->report->status = 'approved';
            $this->report->approval_status = 'approved';
            $this->report->approver_id = Auth::id();
            $this->report->approved_at = now();
            $this->report->save();
            
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('Report approved successfully')
            ]);
            
            $this->loadReport();
        }
    }
    
    /**
     * Reject report
     */
    public function rejectReport()
    {
        if (Auth::user()->role_id <= 3) {
            $this->report->status = 'rejected';
            $this->report->approval_status = 'rejected';
            $this->report->approver_id = Auth::id();
            $this->report->rejected_at = now();
            $this->report->save();
            
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('Report rejected successfully')
            ]);
            
            $this->loadReport();
        }
    }
    
    /**
     * Navigate back to reports list
     */
    public function backToReports()
    {
        return redirect()->route('campaigns.reports');
    }
    
    public function render()
    {
        return view('livewire.campaigns.campaign-report-show');
    }
}
