<?php

namespace App\Livewire\Campaigns;

use App\Models\Campaign;
use Livewire\Component;
use Livewire\WithPagination;

class CampaignIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'name';
    public string $sortDirection = 'asc';
    public int $perPage = 5;
    public array $selectedCampaigns = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 5, 'as' => 'pp'],
    ];

    public function mount()
    {
        $this->selectedCampaigns = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedCampaigns = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedCampaigns = $value
            ? $this->campaigns->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleCampaignSelection(int $campaignId)
    {
        if (in_array($campaignId, $this->selectedCampaigns)) {
            $this->selectedCampaigns = array_diff($this->selectedCampaigns, [$campaignId]);
        } else {
            $this->selectedCampaigns[] = $campaignId;
        }

        $currentPageIds = $this->campaigns->pluck('id')->all();
        $this->selectAll = !empty($this->selectedCampaigns) && empty(array_diff($currentPageIds, $this->selectedCampaigns));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedCampaigns)) {
            Campaign::whereIn('id', $this->selectedCampaigns)->delete();
            $this->selectedCampaigns = [];
            $this->selectAll = false;
            session()->flash('message', 'Utilisateurs sélectionnés supprimés avec succès !');
            $this->resetPage();
        }
    }

    protected function getCampaignsQuery()
    {
        $user = auth()->user();

        $query = Campaign::query()
            ->with('customer')
            ->orderBy($this->sortField, $this->sortDirection);

        if (in_array($user->role_id, [3, 6])) {
            $query->where('id', '=', $user->campaign_id);
        }

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm);
                //   ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    public function getCampaignsProperty()
    {
        return $this->getCampaignsQuery()->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-index', [
            'campaigns' => $this->campaigns,
        ]);
    }
}

