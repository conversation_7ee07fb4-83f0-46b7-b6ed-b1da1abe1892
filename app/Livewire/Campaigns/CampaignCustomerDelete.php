<?php

namespace App\Livewire\Campaigns;

use App\Models\Customer;
use Livewire\Attributes\On;
use Livewire\Component;

class CampaignCustomerDelete extends Component
{
    public $customer;
    public $confirmationText = '';
    public $confirmationValid = false;

    public function mount(Customer $customer)
    {
        $this->customer = $customer;
    }

    public function updatedConfirmationText()
    {
        $this->confirmationValid = ($this->confirmationText === $this->customer->name);
    }

    #[On('destroy-customer')]
    public function destroyCustomer()
    {
        // Validate that the confirmation text matches the customer name
        if ($this->confirmationText !== $this->customer->name) {
            $this->addError('confirmationText', 'Please type the exact customer name to confirm deletion.');
            return;
        }

        // Check if customer has active campaigns
        if ($this->customer->campaigns()->where('status', 'active')->count() > 0) {
            session()->flash('error', 'Cannot delete customer with active campaigns.');
            return $this->redirect(route('campaigns.customers.show', $this->customer), navigate: true);
        }

        try {
            // Store the name for the success message
            $name = $this->customer->name;

            // Delete the customer
            $this->customer->delete();

            // Flash success message
            session()->flash('message', "Customer '{$name}' has been deleted successfully.");

            // Redirect to the index page
            return $this->redirect(route('campaigns.customers.index'), navigate: true);
        } catch (\Exception $e) {
            // Handle any exceptions that might occur during deletion
            session()->flash('error', 'An error occurred while deleting the customer: ' . $e->getMessage());
            return $this->redirect(route('campaigns.customers.show', $this->customer), navigate: true);
        }
    }

    // Keep the original delete method for backward compatibility
    public function delete()
    {
        return $this->destroyCustomer();
    }

    #[On('to-customer-show')]
    public function navigateToShow($data = null)
    {
        if (isset($data['customer'])) {
            return redirect()->route('campaigns.customers.show', ['customer' => $data['customer']]);
        } else {
            // If no specific customer is provided, use the current one
            return redirect()->route('campaigns.customers.show', ['customer' => $this->customer->id]);
        }
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-customer-delete');
    }
}
