<?php

namespace App\Livewire\Campaigns;

use App\Models\CustomerDocument;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;

class CampaignCustomerDocumentDelete extends Component
{
    public $document;
    public $customerId;
    public $confirmationText = '';
    public $confirmationValid = false;

    public function mount(CustomerDocument $document)
    {
        $this->document = $document;
        $this->customerId = $document->customer_id;
    }

    public function updatedConfirmationText()
    {
        $this->confirmationValid = ($this->confirmationText === $this->document->name);
    }

    #[On('destroy-document')]
    public function destroyDocument()
    {
        // Validate that the confirmation text matches the document name
        if ($this->confirmationText !== $this->document->name) {
            $this->addError('confirmationText', 'Please type the exact document name to confirm deletion.');
            return;
        }

        try {
            // Store the name and file path for deletion and success message
            $name = $this->document->name;
            $filePath = $this->document->file_path;

            // Delete the document from storage if it exists
            if ($filePath && Storage::disk('public')->exists($filePath)) {
                Storage::disk('public')->delete($filePath);
            }

            // Delete the document record
            $this->document->delete();

            // Flash success message
            session()->flash('message', "Document '{$name}' has been deleted successfully.");

            // Redirect back to the customer show page
            return $this->redirect(route('campaigns.customers.show', $this->customerId), navigate: true);
        } catch (\Exception $e) {
            // Handle any exceptions that might occur during deletion
            session()->flash('error', 'An error occurred while deleting the document: ' . $e->getMessage());
            return $this->redirect(route('campaigns.customers.show', $this->customerId), navigate: true);
        }
    }

    // Keep the original delete method for backward compatibility
    public function delete()
    {
        return $this->destroyDocument();
    }

    #[On('to-customer-show')]
    public function navigateToShow($data = null)
    {
        if (isset($data['customer'])) {
            return redirect()->route('campaigns.customers.show', ['customer' => $data['customer']]);
        } else {
            // If no specific customer is provided, use the current one
            return redirect()->route('campaigns.customers.show', ['customer' => $this->customerId]);
        }
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-customer-document-delete');
    }
}
