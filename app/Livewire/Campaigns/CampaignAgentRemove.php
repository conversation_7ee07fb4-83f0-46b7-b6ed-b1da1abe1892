<?php

namespace App\Livewire\Campaigns;

use App\Models\User;
use App\Models\Campaign;
use Livewire\Component;

class CampaignAgentRemove extends Component
{
    public ?User $agent;
    public bool $recycle = false;

    public function mount(User $agent)
    {
        $this->agent = $agent;
    }

    public function submit()
    {
        // If recycle is checked, update status to 'in_training' (directly send to training)
        // Otherwise, update status to 'inactive' (available for training but marked as recycling)
        $newStatus = $this->recycle ? 'in_training' : 'inactive';

        $this->agent->update([
            'status' => $newStatus,
            'campaign_id' => null
        ]);

        // If recycling, create or update training record to reset progress
        if ($this->recycle) {
            \App\Models\Training::updateOrCreate(
                ['user_id' => $this->agent->id],
                [
                    'progress' => 0,
                    'validated_at' => null,
                    'rating' => null
                ]
            );
        }

        $message = $this->recycle
            ? 'Agent removed from campaign and sent to training successfully!'
            : 'Agent removed from campaign successfully!';

        session()->flash('message', $message);
        $this->dispatch('to-campaign-agent');
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-agent-remove', [
            'agent' => $this->agent
        ]);
    }
}
