<?php

namespace App\Livewire\Campaigns;

use App\Models\Campaign;
use App\Models\Certification;
use App\Models\Skill;
use Livewire\Component;
use Livewire\WithPagination;

class CampaignRequirements extends Component
{
    use WithPagination;
    
    public Campaign $campaign;
    
    // Skill requirements
    public $selectedSkill = null;
    public $minimumProficiencyLevel = 1;
    public $isSkillMandatory = true;
    
    // Certification requirements
    public $selectedCertification = null;
    public $isCertificationMandatory = true;
    
    protected $rules = [
        'selectedSkill' => 'nullable|exists:skills,id',
        'minimumProficiencyLevel' => 'required|integer|min:1|max:5',
        'isSkillMandatory' => 'boolean',
        'selectedCertification' => 'nullable|exists:certifications,id',
        'isCertificationMandatory' => 'boolean',
    ];
    
    public function mount(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }
    
    public function addSkillRequirement()
    {
        $this->validate([
            'selectedSkill' => 'required|exists:skills,id',
            'minimumProficiencyLevel' => 'required|integer|min:1|max:5',
            'isSkillMandatory' => 'boolean',
        ]);
        
        // Check if campaign already has this skill requirement
        if ($this->campaign->requiredSkills()->where('skill_id', $this->selectedSkill)->exists()) {
            session()->flash('error', 'This skill is already a requirement for this campaign.');
            return;
        }
        
        $this->campaign->requiredSkills()->attach($this->selectedSkill, [
            'minimum_proficiency_level' => $this->minimumProficiencyLevel,
            'is_mandatory' => $this->isSkillMandatory,
        ]);
        
        session()->flash('message', 'Skill requirement added successfully.');
        
        // Reset form
        $this->selectedSkill = null;
        $this->minimumProficiencyLevel = 1;
        $this->isSkillMandatory = true;
    }
    
    public function updateSkillRequirement($skillId, $proficiencyLevel, $isMandatory)
    {
        $this->campaign->requiredSkills()->updateExistingPivot($skillId, [
            'minimum_proficiency_level' => $proficiencyLevel,
            'is_mandatory' => $isMandatory,
        ]);
        
        session()->flash('message', 'Skill requirement updated successfully.');
    }
    
    public function removeSkillRequirement($skillId)
    {
        $this->campaign->requiredSkills()->detach($skillId);
        
        session()->flash('message', 'Skill requirement removed successfully.');
    }
    
    public function addCertificationRequirement()
    {
        $this->validate([
            'selectedCertification' => 'required|exists:certifications,id',
            'isCertificationMandatory' => 'boolean',
        ]);
        
        // Check if campaign already has this certification requirement
        if ($this->campaign->requiredCertifications()->where('certification_id', $this->selectedCertification)->exists()) {
            session()->flash('error', 'This certification is already a requirement for this campaign.');
            return;
        }
        
        $this->campaign->requiredCertifications()->attach($this->selectedCertification, [
            'is_mandatory' => $this->isCertificationMandatory,
        ]);
        
        session()->flash('message', 'Certification requirement added successfully.');
        
        // Reset form
        $this->selectedCertification = null;
        $this->isCertificationMandatory = true;
    }
    
    public function updateCertificationRequirement($certificationId, $isMandatory)
    {
        $this->campaign->requiredCertifications()->updateExistingPivot($certificationId, [
            'is_mandatory' => $isMandatory,
        ]);
        
        session()->flash('message', 'Certification requirement updated successfully.');
    }
    
    public function removeCertificationRequirement($certificationId)
    {
        $this->campaign->requiredCertifications()->detach($certificationId);
        
        session()->flash('message', 'Certification requirement removed successfully.');
    }
    
    public function render()
    {
        $requiredSkills = $this->campaign->requiredSkills;
        $requiredCertifications = $this->campaign->requiredCertifications;
        
        $availableSkills = Skill::where('is_active', true)
            ->whereNotIn('id', $requiredSkills->pluck('id'))
            ->get();
            
        $availableCertifications = Certification::where('is_active', true)
            ->whereNotIn('id', $requiredCertifications->pluck('id'))
            ->get();
            
        return view('livewire.campaigns.campaign-requirements', [
            'requiredSkills' => $requiredSkills,
            'requiredCertifications' => $requiredCertifications,
            'availableSkills' => $availableSkills,
            'availableCertifications' => $availableCertifications,
        ]);
    }
}
