<?php

namespace App\Livewire\Campaigns;

use App\Models\User;
use App\Models\Campaign;
use Livewire\Component;

class CampaignAgentObservation extends Component
{
    public ?User $agent;

    public $form = [
        'date' => '',
        'subject' => '',
        'observation' => '',
        'performance' => 0,
        'productivity' => 0,
        'rating' => 0,
    ];

    public function mount(User $agent)
    {
        $this->agent = $agent;
        $this->form['date'] = date('Y-m-d');

        // Get current metrics if exists
        $campaign = Campaign::where('user_id', $agent->id)->first();
        if ($campaign) {
            $this->form['performance'] = $campaign->performance ?? 0;
            $this->form['productivity'] = $campaign->productivity ?? 0;
            $this->form['rating'] = $campaign->rating ?? 0;
        }
    }

    public function updatePerformance()
    {
        $this->dispatch('performanceUpdated');
    }

    public function updateProductivity()
    {
        $this->dispatch('productivityUpdated');
    }

    public function updateRating()
    {
        // Ensure rating stays within bounds
        if ($this->form['rating'] < 0) {
            $this->form['rating'] = 0;
        } elseif ($this->form['rating'] > 3) {
            $this->form['rating'] = 3;
        }

        $this->dispatch('ratingUpdated');
    }

    public function rules()
    {
        return [
            'form.date' => 'required|date',
            'form.subject' => 'required|string|min:3',
            'form.observation' => 'required|string|min:10',
            'form.performance' => 'required|integer|min:0|max:100',
            'form.productivity' => 'required|integer|min:0|max:100',
            'form.rating' => 'required|numeric|min:0|max:3',
        ];
    }

    public function submit()
    {
        $this->validate();

        // Prepare notes content
        $notesContent = $this->form['subject'] . ': ' . $this->form['observation'];

        // Update or create campaign record
        Campaign::updateOrCreate(
            ['user_id' => $this->agent->id],
            [
                'start_date' => $this->form['date'],
                'performance' => $this->form['performance'],
                'productivity' => $this->form['productivity'],
                'rating' => $this->form['rating'],
                'notes' => $notesContent,
            ]
        );

        session()->flash('message', 'Observation saved successfully.');

        return redirect()->route('campaigns.agents');
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-agent-observation', [
            'agent' => $this->agent
        ]);
    }
}
