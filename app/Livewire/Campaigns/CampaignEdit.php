<?php

namespace App\Livewire\Campaigns;

use App\Livewire\Forms\CampaignForm;
use App\Models\Customer;
use App\Models\Campaign;
use App\Models\Platform;
use App\Models\User;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class CampaignEdit extends Component
{
    use WithFileUploads;
    public Campaign $campaign;
    public CampaignForm $form;

    public function mount(Campaign $campaign)
    {
        $this->campaign = $campaign;
        $this->form->setCampaign($campaign);
    }

    #[On('update-campaign')]
    public function updateCampaign()
    {
        $this->form->update($this->campaign);
        session()->flash('message', 'Campaign updated successfully!');
        $this->redirect(route('campaigns.show', ['campaign' => $this->campaign->id]), navigate: true);
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-edit', [
            'customers' => Customer::all(),
            'platforms' => Platform::where('status', 'active')->get(),
            'campaignManagers' => User::where('role_id', 5)->get(), // Campaign Supervisor role
        ]);
    }
}
