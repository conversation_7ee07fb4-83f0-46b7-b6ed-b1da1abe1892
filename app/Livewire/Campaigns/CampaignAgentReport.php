<?php

namespace App\Livewire\Campaigns;

use App\Models\User;
use App\Models\Campaign;
use Livewire\Component;

class CampaignAgentReport extends Component
{
    public ?User $agent;
    public ?Campaign $campaign = null;

    public function mount(User $agent)
    {
        $this->agent = $agent;

        // Get the latest campaign record for this agent
        $this->campaign = Campaign::where('user_id', $agent->id)
            ->latest()
            ->first();
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-agent-report');
    }
}
