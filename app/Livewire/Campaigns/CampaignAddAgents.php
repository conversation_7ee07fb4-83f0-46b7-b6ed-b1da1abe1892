<?php

namespace App\Livewire\Campaigns;

use App\Models\User;
use App\Models\Campaign;
use App\Models\Training;
use Livewire\Component;
use Livewire\WithPagination;

class CampaignAddAgents extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'first_name';
    public string $sortDirection = 'asc';
    public int $perPage = 8;
    public array $selectedAgents = [];
    public bool $selectAll = false;
    public $campaign_id;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'first_name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 8, 'as' => 'pp'],
    ];

    public function mount()
    {
        $this->selectedAgents = [];
        $this->selectAll = false;

        // If there's only one campaign, select it by default
        $campaigns = Campaign::all();
        if ($campaigns->count() === 1) {
            $this->campaign_id = $campaigns->first()->id;
        }
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedAgents = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function toggleSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedAgents = $this->agents->pluck('id')->map(fn($id) => (string)$id)->all();
        } else {
            $this->selectedAgents = [];
        }
    }

    public function toggleAgentSelection(int $agentId)
    {
        $agentIdStr = (string)$agentId;

        // This is needed because wire:model with checkboxes uses string values
        if (in_array($agentIdStr, $this->selectedAgents)) {
            $this->selectedAgents = array_values(array_diff($this->selectedAgents, [$agentIdStr]));
        } else {
            $this->selectedAgents[] = $agentIdStr;
        }

        $currentPageIds = $this->agents->pluck('id')->map(fn($id) => (string)$id)->all();
        $this->selectAll = !empty($this->selectedAgents) && count(array_intersect($currentPageIds, $this->selectedAgents)) === count($currentPageIds);
    }

    public function addToCampaign()
    {
        $this->validate([
            'campaign_id' => 'required|exists:campaigns,id',
        ]);

        if (!empty($this->selectedAgents)) {
            // Convert string IDs to integers for the database query
            $agentIds = array_map('intval', $this->selectedAgents);

            // Update agent status and campaign_id
            User::whereIn('id', $agentIds)
                ->update([
                    'status' => 'active',
                    'campaign_id' => $this->campaign_id
                ]);

            $this->selectedAgents = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected agents added to campaign successfully!');
            $this->resetPage();

            // Redirect back to campaigns.agents after successful addition
            if (count($agentIds) > 0) {
                return $this->redirect(route('campaigns.agents'), navigate: true);
            }
        } else {
            session()->flash('error', 'Please select at least one agent to add to the campaign.');
        }
    }

    public function cancel()
    {
        return $this->redirect(route('campaigns.agents'), navigate: true);
    }

    protected function getAgentsQuery()
    {
        $query = User::query()
            ->with(['role', 'training'])
            ->where('role_id', 6)
            ->where(function($query) {
                // Include agents with status 'validated' (validated but not assigned to campaign)
                // or agents with status 'in_training' who have been validated
                $query->where('status', 'validated')
                    ->orWhere(function($q) {
                        $q->where('status', 'in_training')
                            ->whereHas('training', function($tq) {
                                $tq->whereNotNull('validated_at');
                            });
                    });
            })
            ->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    public function getAgentsProperty()
    {
        return $this->getAgentsQuery()->paginate($this->perPage);
    }

    public function getCampaignsProperty()
    {
        return Campaign::all();
    }

    public function render()
    {
        return view('livewire.campaigns.campaign-add-agents', [
            'agents' => $this->agents,
            'campaigns' => $this->campaigns,
        ]);
    }
}
