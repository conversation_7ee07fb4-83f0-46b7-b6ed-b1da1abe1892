<?php

namespace App\Livewire\Campaigns;

use App\Models\Report;
use App\Models\Campaign;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;

class CampaignReportCreate extends Component
{
    use WithFileUploads;
    
    // Form fields
    public $title;
    public $content;
    public $type = 'daily';
    public $category = 'performance';
    public $priority = 'normal';
    public $campaign_id;
    public $performance_score;
    public $quality_score;
    public $compliance_score;
    public $tags = [];
    public $attachments = [];
    
    // Temporary fields
    public $newTag = '';
    public $campaigns = [];
    
    protected $rules = [
        'title' => 'required|string|max:255',
        'content' => 'required|string',
        'type' => 'required|string|in:daily,weekly,monthly,quarterly,annual,ad_hoc',
        'category' => 'required|string|in:performance,quality,compliance,training,operations,financial',
        'priority' => 'required|string|in:low,normal,high,urgent',
        'campaign_id' => 'required|exists:campaigns,id',
        'performance_score' => 'nullable|numeric|min:0|max:100',
        'quality_score' => 'nullable|numeric|min:0|max:100',
        'compliance_score' => 'nullable|numeric|min:0|max:100',
        'attachments.*' => 'nullable|file|max:10240',
    ];
    
    public function mount()
    {
        $this->campaigns = Campaign::where('status', 'active')->get();
        
        // Set default campaign if there's only one
        if ($this->campaigns->count() === 1) {
            $this->campaign_id = $this->campaigns->first()->id;
        }
    }
    
    /**
     * Add a tag to the tags array
     */
    public function addTag()
    {
        if (!empty($this->newTag) && !in_array($this->newTag, $this->tags)) {
            $this->tags[] = $this->newTag;
            $this->newTag = '';
        }
    }
    
    /**
     * Remove a tag from the tags array
     */
    public function removeTag($index)
    {
        if (isset($this->tags[$index])) {
            unset($this->tags[$index]);
            $this->tags = array_values($this->tags);
        }
    }
    
    /**
     * Submit the report
     */
    public function submitReport()
    {
        $this->validate();
        
        $report = new Report();
        $report->title = $this->title;
        $report->content = $this->content;
        $report->type = $this->type;
        $report->category = $this->category;
        $report->priority = $this->priority;
        $report->campaign_id = $this->campaign_id;
        $report->performance_score = $this->performance_score;
        $report->quality_score = $this->quality_score;
        $report->compliance_score = $this->compliance_score;
        $report->tags = $this->tags;
        $report->creator_id = Auth::id();
        $report->status = 'pending';
        $report->approval_status = 'pending';
        $report->date = now();
        $report->save();
        
        // Handle file uploads
        foreach ($this->attachments as $attachment) {
            $report->addMedia($attachment->getRealPath())
                ->usingName($attachment->getClientOriginalName())
                ->usingFileName($attachment->getClientOriginalName())
                ->toMediaCollection('attachments');
        }
        
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => __('Report created successfully')
        ]);
        
        return redirect()->route('campaigns.reports.show', $report->id);
    }
    
    /**
     * Cancel and go back to reports list
     */
    public function cancel()
    {
        return redirect()->route('campaigns.reports');
    }
    
    public function render()
    {
        return view('livewire.campaigns.campaign-report-create');
    }
}
