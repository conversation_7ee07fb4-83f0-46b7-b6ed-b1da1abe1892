<?php

namespace App\Livewire\Campaigns;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class CampaignAgents extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'first_name';
    public string $sortDirection = 'asc';
    public int $perPage = 8;
    public array $selectedAgents = [];
    public bool $selectAll = false;
    public string $status = 'active';
    public string $performanceFilter = '';
    public string $productivityFilter = '';
    public string $ratingFilter = '';

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'first_name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 8, 'as' => 'pp'],
        'status' => ['except' => 'active', 'as' => 'status'],
        'performanceFilter' => ['except' => '', 'as' => 'performance'],
        'productivityFilter' => ['except' => '', 'as' => 'productivity'],
        'ratingFilter' => ['except' => '', 'as' => 'rating'],
    ];

    public function mount()
    {
        $this->selectedAgents = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection', 'status', 'performanceFilter', 'productivityFilter', 'ratingFilter'])) {
            $this->resetPage();
            $this->selectedAgents = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function toggleSelectAll()
    {
        $this->selectAll = !$this->selectAll;

        if ($this->selectAll) {
            $this->selectedAgents = $this->agents->pluck('id')->map(fn($id) => (string)$id)->all();
        } else {
            $this->selectedAgents = [];
        }
    }

    public function clearFilters()
    {
        $this->reset(['performanceFilter', 'productivityFilter', 'ratingFilter']);
        $this->resetPage();
    }

    public function updatedPerformanceFilter()
    {
        $this->resetPage();
    }

    public function updatedProductivityFilter()
    {
        $this->resetPage();
    }

    public function updatedRatingFilter()
    {
        $this->resetPage();
    }

    public function toggleAgentSelection(int $agentId)
    {
        $agentIdStr = (string)$agentId;

        if (in_array($agentIdStr, $this->selectedAgents)) {
            $this->selectedAgents = array_diff($this->selectedAgents, [$agentIdStr]);
        } else {
            $this->selectedAgents[] = $agentIdStr;
        }
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedAgents)) {
            User::whereIn('id', $this->selectedAgents)->delete();
            $this->selectedAgents = [];
            $this->selectAll = false;
            session()->flash('message', 'Utilisateurs sélectionnés supprimés avec succès !');
            $this->resetPage();
        }
    }



    protected function getAgentsQuery()
    {
        $user = auth()->user();

        $query = User::query()
            ->with(['role', 'campaign' => function($query) {
                // Ensure we're getting fresh data
                $query->withoutGlobalScopes();
            }])
            ->where('role_id', 6)
            ->orderBy($this->sortField, $this->sortDirection);

        if ($user->role_id === 6) {
            $query->where('id', '=', $user->id);
        } else if ($user->role_id === 3) {
            $query->where('campaign_id', '=', $user->campaign_id);
        }

        if ($this->status) {
            $query->where('status', $this->status);
        }

        // Apply performance filter if selected
        if (!empty($this->performanceFilter)) {
            $performanceRange = $this->getPerformanceRange($this->performanceFilter);
            if ($performanceRange) {
                $query->whereHas('campaign', function($q) use ($performanceRange) {
                    $q->whereBetween('performance', [$performanceRange['min'], $performanceRange['max']]);
                });
            }
        }

        // Apply productivity filter if selected
        if (!empty($this->productivityFilter)) {
            $productivityRange = $this->getProductivityRange($this->productivityFilter);
            if ($productivityRange) {
                $query->whereHas('campaign', function($q) use ($productivityRange) {
                    $q->whereBetween('productivity', [$productivityRange['min'], $productivityRange['max']]);
                });
            }
        }

        // Apply rating filter if selected
        if (!empty($this->ratingFilter)) {
            $ratingRange = $this->getRatingRange($this->ratingFilter);
            if ($ratingRange) {
                $query->whereHas('campaign', function($q) use ($ratingRange) {
                    $q->whereBetween('rating', [$ratingRange['min'], $ratingRange['max']]);
                });
            }
        }

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    /**
     * Get the performance range based on the filter value
     */
    protected function getPerformanceRange(string $filter): ?array
    {
        return match($filter) {
            'low' => ['min' => 0, 'max' => 60],
            'medium' => ['min' => 61, 'max' => 80],
            'high' => ['min' => 81, 'max' => 100],
            default => null,
        };
    }

    /**
     * Get the productivity range based on the filter value
     */
    protected function getProductivityRange(string $filter): ?array
    {
        return match($filter) {
            'low' => ['min' => 0, 'max' => 60],
            'medium' => ['min' => 61, 'max' => 80],
            'high' => ['min' => 81, 'max' => 100],
            default => null,
        };
    }

    /**
     * Get the rating range based on the filter value
     */
    protected function getRatingRange(string $filter): ?array
    {
        return match($filter) {
            'low' => ['min' => 0, 'max' => 1],
            'medium' => ['min' => 1.1, 'max' => 2],
            'high' => ['min' => 2.1, 'max' => 3],
            default => null,
        };
    }

    public function getAgentsProperty()
    {
        return $this->getAgentsQuery()->paginate($this->perPage);
    }

    public function render()
    {
        $agents = $this->agents;
        return view('livewire.campaigns.campaign-agents', [
            'agents' => $agents,
        ]);
    }
}
