<?php

namespace App\Livewire\CallCenters;

use App\Models\CallCenter;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class CallCenterIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'name';
    public string $sortDirection = 'asc';
    public int $perPage = 10;
    public array $selectedCallCenters = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 10, 'as' => 'pp'],
    ];

    public function mount()
    {
        $this->selectedCallCenters = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedCallCenters = [];
            $this->selectAll = false;
        }
    }

    public function sortBy($field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedCallCenters = $value
            ? $this->callCenters->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleCallCenterSelection(int $callCenterId)
    {
        if (in_array($callCenterId, $this->selectedCallCenters)) {
            $this->selectedCallCenters = array_diff($this->selectedCallCenters, [$callCenterId]);
        } else {
            $this->selectedCallCenters[] = $callCenterId;
        }

        $currentPageIds = $this->callCenters->pluck('id')->all();
        $this->selectAll = !empty($this->selectedCallCenters) && empty(array_diff($currentPageIds, $this->selectedCallCenters));
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedCallCenters)) {
            CallCenter::whereIn('id', $this->selectedCallCenters)->delete();
            $this->selectedCallCenters = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected call centers deleted successfully!');
            $this->resetPage();
        }
    }

    protected function getCallCentersQuery()
    {
        $query = CallCenter::query()
            ->with(['director'])
            ->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                  ->orWhere('description', 'like', $searchTerm)
                  ->orWhere('contact_email', 'like', $searchTerm)
                  ->orWhere('contact_phone', 'like', $searchTerm);
            });
        }

        return $query;
    }

    public function getCallCentersProperty()
    {
        return $this->getCallCentersQuery()->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.call-centers.call-center-index', [
            'callCenters' => $this->callCenters,
        ]);
    }

    public function getListeners()
    {
        return [
            'to-call-center-show' => 'navigateToCallCenterShow',
            'select-call-center-first' => 'showSelectCallCenterMessage',
            'navigate-to' => 'handleNavigation',
        ];
    }

    #[On('navigate-to')]
    public function handleNavigation($data)
    {
        if (isset($data['route'])) {
            return redirect()->route($data['route'], $data['params'] ?? []);
        }
    }

    #[On('select-call-center-first')]
    public function showSelectCallCenterMessage()
    {
        // Show a session flash message as a fallback
        session()->flash('message', 'Please select a call center first to access its sites or departments.');

        // Dispatch the notification event
        $this->dispatch('show-notification', [
            'type' => 'info',
            'title' => 'Action Required',
            'message' => 'Please select a call center first to access its sites or departments.'
        ]);
    }

    public function navigateToCallCenterShow($callCenterId)
    {
        if ($callCenterId) {
            // Ensure we're passing the parameter as an array with the correct key
            return redirect()->route('call-centers.show', ['callCenter' => $callCenterId]);
        }
    }

    public function showCallCenter($callCenterId)
    {
        return redirect()->route('call-centers.show', ['callCenter' => $callCenterId]);
    }
}
