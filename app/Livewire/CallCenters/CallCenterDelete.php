<?php

namespace App\Livewire\CallCenters;

use App\Models\CallCenter;
use Livewire\Attributes\On;
use Livewire\Component;

class CallCenterDelete extends Component
{
    public $callCenter;
    public $confirmationText = '';
    public $confirmationValid = false;

    public function mount(CallCenter $callCenter)
    {
        $this->callCenter = $callCenter;
    }

    public function updatedConfirmationText()
    {
        $this->confirmationValid = ($this->confirmationText === $this->callCenter->name);
    }

    #[On('destroy-call-center')]
    public function destroyCallCenter()
    {
        // Validate that the confirmation text matches the call center name
        if ($this->confirmationText !== $this->callCenter->name) {
            $this->addError('confirmationText', 'Please type the exact call center name to confirm deletion.');
            return;
        }

        try {
            // Store the name for the success message
            $name = $this->callCenter->name;

            // Delete the call center
            $this->callCenter->delete();

            // Flash success message
            session()->flash('message', "Call center '{$name}' has been deleted successfully.");

            // Redirect to the index page
            return $this->redirect(route('call-centers.index'), navigate: true);
        } catch (\Exception $e) {
            // Handle any exceptions that might occur during deletion
            session()->flash('error', 'An error occurred while deleting the call center: ' . $e->getMessage());
            return $this->redirect(route('call-centers.show', $this->callCenter), navigate: true);
        }
    }

    // Keep the original delete method for backward compatibility
    public function delete()
    {
        return $this->destroyCallCenter();
    }

    #[On('to-call-center-show')]
    public function navigateToShow($data = null)
    {
        if (isset($data['callCenter'])) {
            return redirect()->route('call-centers.show', ['callCenter' => $data['callCenter']]);
        } else {
            // If no specific call center is provided, use the current one
            return redirect()->route('call-centers.show', ['callCenter' => $this->callCenter->id]);
        }
    }

    public function render()
    {
        return view('livewire.call-centers.call-center-delete');
    }
}
