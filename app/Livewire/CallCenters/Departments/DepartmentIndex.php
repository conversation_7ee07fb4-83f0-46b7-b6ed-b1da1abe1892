<?php

namespace App\Livewire\CallCenters\Departments;

use App\Models\CallCenter;
use App\Models\Department;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class DepartmentIndex extends Component
{
    use WithPagination;

    public $callCenter;
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $selectedDepartments = [];
    public $selectAll = false;
    public $statusFilter = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
        'statusFilter' => ['except' => ''],
    ];

    public function mount($callCenter = null)
    {
        if ($callCenter instanceof CallCenter) {
            $this->callCenter = $callCenter;
        } elseif (is_numeric($callCenter)) {
            $this->callCenter = CallCenter::find($callCenter);
        } else {
            // Redirect to call centers list if no valid call center is provided
            return redirect()->route('call-centers.list');
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedDepartments = $this->getDepartments()->pluck('id')->map(fn($id) => (string) $id)->toArray();
        } else {
            $this->selectedDepartments = [];
        }
    }

    public function getDepartments()
    {
        $query = Department::query()
            ->where('call_center_id', $this->callCenter->id)
            ->when($this->search, function ($query) {
                return $query->where(function ($query) {
                    $query->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter, function ($query) {
                return $query->where('status', $this->statusFilter);
            });

        return $query->orderBy($this->sortField, $this->sortDirection);
    }

    public function deleteSelected()
    {
        if (count($this->selectedDepartments) > 0) {
            Department::whereIn('id', $this->selectedDepartments)->delete();
            $this->selectedDepartments = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected departments have been deleted.');
        }
    }

    #[On('to-department-show')]
    public function navigateToShow($data)
    {
        if (isset($data['callCenter']) && isset($data['department'])) {
            return redirect()->route('call-centers.departments.show', [
                'callCenter' => $data['callCenter'],
                'department' => $data['department']
            ]);
        }
    }

    #[On('to-department-edit')]
    public function navigateToEdit($data)
    {
        if (isset($data['callCenter']) && isset($data['department'])) {
            return redirect()->route('call-centers.departments.edit', [
                'callCenter' => $data['callCenter'],
                'department' => $data['department']
            ]);
        }
    }

    #[On('to-department-delete')]
    public function navigateToDelete($data)
    {
        if (isset($data['callCenter']) && isset($data['department'])) {
            return redirect()->route('call-centers.departments.delete', [
                'callCenter' => $data['callCenter'],
                'department' => $data['department']
            ]);
        }
    }

    #[On('navigate-to')]
    public function handleNavigation($data)
    {
        if (isset($data['route'])) {
            return redirect()->route($data['route'], $data['params'] ?? []);
        }
    }

    public function render()
    {
        return view('livewire.call-centers.departments.department-index', [
            'departments' => $this->getDepartments()->paginate($this->perPage),
            'callCenter' => $this->callCenter,
        ]);
    }
}
