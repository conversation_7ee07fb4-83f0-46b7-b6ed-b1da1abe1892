<?php

namespace App\Livewire\CallCenters\Departments;

use App\Models\CallCenter;
use App\Models\Department;
use App\Models\User;
use Livewire\Component;

class DepartmentCreate extends Component
{
    public $callCenter;
    public $name;
    public $code;
    public $description;
    public $parent_id;
    public $manager_id;
    public $status = 'active';
    
    public $departments = [];
    public $managers = [];
    
    protected $rules = [
        'name' => 'required|string|max:255',
        'code' => 'required|string|max:50|unique:departments,code',
        'description' => 'nullable|string',
        'parent_id' => 'nullable|exists:departments,id',
        'manager_id' => 'nullable|exists:users,id',
        'status' => 'required|in:active,inactive',
    ];
    
    public function mount(CallCenter $callCenter)
    {
        $this->callCenter = $callCenter;
        $this->loadDepartments();
        $this->loadManagers();
    }
    
    public function loadDepartments()
    {
        // Get departments that belong to this call center
        $this->departments = Department::where('call_center_id', $this->callCenter->id)
            ->orderBy('name')
            ->get()
            ->map(function ($department) {
                return [
                    'id' => $department->id,
                    'name' => $department->name,
                ];
            })
            ->toArray();
    }
    
    public function loadManagers()
    {
        // Get users with manager roles
        $this->managers = User::whereIn('role_id', [2, 3, 4, 12]) // Director, Manager, Supervisor, HR Manager
            ->orderBy('first_name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->full_name,
                    'role' => $user->role->name,
                ];
            })
            ->toArray();
    }
    
    public function save()
    {
        $this->validate();
        
        $department = new Department();
        $department->name = $this->name;
        $department->code = $this->code;
        $department->description = $this->description;
        $department->parent_id = $this->parent_id;
        $department->manager_id = $this->manager_id;
        $department->status = $this->status;
        $department->call_center_id = $this->callCenter->id;
        
        $department->save();
        
        session()->flash('message', 'Department created successfully.');
        
        return redirect()->route('call-centers.departments.index', ['callCenter' => $this->callCenter->id]);
    }
    
    public function render()
    {
        return view('livewire.call-centers.departments.department-create', [
            'callCenter' => $this->callCenter,
            'departments' => $this->departments,
            'managers' => $this->managers,
        ]);
    }
}
