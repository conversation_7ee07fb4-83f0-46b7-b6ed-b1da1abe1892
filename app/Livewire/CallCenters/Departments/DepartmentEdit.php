<?php

namespace App\Livewire\CallCenters\Departments;

use App\Models\CallCenter;
use App\Models\Department;
use App\Models\User;
use Livewire\Component;

class DepartmentEdit extends Component
{
    public $callCenter;
    public $department;
    public $name;
    public $code;
    public $description;
    public $parent_id;
    public $manager_id;
    public $status;
    
    public $departments = [];
    public $managers = [];
    
    protected function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:departments,code,' . $this->department->id,
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:departments,id',
            'manager_id' => 'nullable|exists:users,id',
            'status' => 'required|in:active,inactive',
        ];
    }
    
    public function mount(CallCenter $callCenter, Department $department)
    {
        $this->callCenter = $callCenter;
        $this->department = $department;
        
        $this->name = $department->name;
        $this->code = $department->code;
        $this->description = $department->description;
        $this->parent_id = $department->parent_id;
        $this->manager_id = $department->manager_id;
        $this->status = $department->status;
        
        $this->loadDepartments();
        $this->loadManagers();
    }
    
    public function loadDepartments()
    {
        // Get departments that belong to this call center, excluding the current department and its descendants
        $excludeIds = $this->department->descendants()->pluck('id')->push($this->department->id)->toArray();
        
        $this->departments = Department::where('call_center_id', $this->callCenter->id)
            ->whereNotIn('id', $excludeIds)
            ->orderBy('name')
            ->get()
            ->map(function ($department) {
                return [
                    'id' => $department->id,
                    'name' => $department->name,
                ];
            })
            ->toArray();
    }
    
    public function loadManagers()
    {
        // Get users with manager roles
        $this->managers = User::whereIn('role_id', [2, 3, 4, 12]) // Director, Manager, Supervisor, HR Manager
            ->orderBy('first_name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->full_name,
                    'role' => $user->role->name,
                ];
            })
            ->toArray();
    }
    
    public function save()
    {
        $this->validate();
        
        $this->department->name = $this->name;
        $this->department->code = $this->code;
        $this->department->description = $this->description;
        $this->department->parent_id = $this->parent_id;
        $this->department->manager_id = $this->manager_id;
        $this->department->status = $this->status;
        
        $this->department->save();
        
        session()->flash('message', 'Department updated successfully.');
        
        return redirect()->route('call-centers.departments.show', ['callCenter' => $this->callCenter->id, 'department' => $this->department->id]);
    }
    
    public function render()
    {
        return view('livewire.call-centers.departments.department-edit', [
            'callCenter' => $this->callCenter,
            'department' => $this->department,
            'departments' => $this->departments,
            'managers' => $this->managers,
        ]);
    }
}
