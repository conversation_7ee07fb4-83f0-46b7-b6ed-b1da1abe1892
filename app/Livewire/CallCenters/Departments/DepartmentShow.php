<?php

namespace App\Livewire\CallCenters\Departments;

use App\Models\CallCenter;
use App\Models\Department;
use Livewire\Component;

class DepartmentShow extends Component
{
    public $callCenter;
    public $department;
    
    public function mount(CallCenter $callCenter, Department $department)
    {
        $this->callCenter = $callCenter;
        $this->department = $department;
    }
    
    public function render()
    {
        return view('livewire.call-centers.departments.department-show', [
            'callCenter' => $this->callCenter,
            'department' => $this->department,
            'childDepartments' => $this->department->children()->get(),
            'users' => $this->department->users()->get(),
        ]);
    }
}
