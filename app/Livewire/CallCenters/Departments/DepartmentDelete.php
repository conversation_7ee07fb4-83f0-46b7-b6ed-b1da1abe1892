<?php

namespace App\Livewire\CallCenters\Departments;

use App\Models\CallCenter;
use App\Models\Department;
use Livewire\Component;

class DepartmentDelete extends Component
{
    public $callCenter;
    public $department;
    
    public function mount(CallCenter $callCenter, Department $department)
    {
        $this->callCenter = $callCenter;
        $this->department = $department;
    }
    
    public function delete()
    {
        // Check if the department has children
        if ($this->department->children && $this->department->children->count() > 0) {
            session()->flash('error', 'Cannot delete department. Please remove all sub-departments first.');
            return;
        }
        
        // Check if the department has users
        if ($this->department->users && $this->department->users->count() > 0) {
            session()->flash('error', 'Cannot delete department. Please reassign all employees to other departments first.');
            return;
        }
        
        // Delete the department
        $this->department->delete();
        
        session()->flash('message', 'Department deleted successfully.');
        
        return redirect()->route('call-centers.departments.index', ['callCenter' => $this->callCenter->id]);
    }
    
    public function render()
    {
        return view('livewire.call-centers.departments.department-delete', [
            'callCenter' => $this->callCenter,
            'department' => $this->department,
            'childDepartmentsCount' => $this->department->children->count(),
            'usersCount' => $this->department->users->count(),
        ]);
    }
}
