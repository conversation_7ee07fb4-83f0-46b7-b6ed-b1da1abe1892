<?php

namespace App\Livewire\CallCenters;

use App\Livewire\Forms\CallCenterForm;
use App\Models\User;
use Livewire\Component;

class CallCenterCreate extends Component
{
    public CallCenterForm $form;
    
    public function mount()
    {
        $this->form->status = 'active';
    }
    
    public function save()
    {
        $callCenter = $this->form->store();
        
        session()->flash('message', 'Call center created successfully.');
        
        return $this->redirect(route('call-centers.index'), navigate: true);
    }
    
    public function render()
    {
        $directors = User::where('role_id', 2)->get();
        
        return view('livewire.call-centers.call-center-create', [
            'directors' => $directors
        ]);
    }
}
