<?php

namespace App\Livewire\CallCenters;

use App\Models\CallCenter;
use Livewire\Attributes\On;
use Livewire\Component;

class CallCenterShow extends Component
{
    public $callCenter;

    public function mount(CallCenter $callCenter)
    {
        // Load the call center with its relationships
        $this->callCenter = CallCenter::with(['director', 'sites'])->find($callCenter->id);
    }

    #[On('to-call-center-index')]
    public function navigateToIndex()
    {
        return redirect()->route('call-centers.index');
    }

    #[On('to-call-center-edit')]
    public function navigateToEdit($data = null)
    {
        if (isset($data['callCenter'])) {
            return redirect()->route('call-centers.edit', ['callCenter' => $data['callCenter']]);
        } else {
            // If no specific call center is provided, use the current one
            return redirect()->route('call-centers.edit', ['callCenter' => $this->callCenter->id]);
        }
    }

    #[On('to-call-center-delete')]
    public function navigateToDelete($data = null)
    {
        if (isset($data['callCenter'])) {
            return redirect()->route('call-centers.delete', ['callCenter' => $data['callCenter']]);
        } else {
            // If no specific call center is provided, use the current one
            return redirect()->route('call-centers.delete', ['callCenter' => $this->callCenter->id]);
        }
    }

    #[On('to-call-center-sites')]
    public function navigateToSites()
    {
        return redirect()->route('call-centers.sites.index', ['callCenter' => $this->callCenter->id]);
    }

    #[On('to-call-center-departments')]
    public function navigateToDepartments()
    {
        return redirect()->route('call-centers.departments.index', ['callCenter' => $this->callCenter->id]);
    }

    #[On('navigate-to')]
    public function handleNavigation($data)
    {
        if (isset($data['route'])) {
            return redirect()->route($data['route'], $data['params'] ?? []);
        }
    }

    public function render()
    {
        return view('livewire.call-centers.call-center-show');
    }
}
