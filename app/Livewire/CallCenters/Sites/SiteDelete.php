<?php

namespace App\Livewire\CallCenters\Sites;

use App\Models\CallCenter;
use App\Models\Site;
use Livewire\Component;

class SiteDelete extends Component
{
    public $callCenter;
    public $site;
    
    public function mount(CallCenter $callCenter, Site $site)
    {
        $this->callCenter = $callCenter;
        $this->site = $site;
    }
    
    public function delete()
    {
        // Check if the site has platforms
        if ($this->site->platforms && $this->site->platforms->count() > 0) {
            session()->flash('error', 'Cannot delete site. Please remove all platforms associated with this site first.');
            return;
        }
        
        // Delete the site
        $this->site->delete();
        
        session()->flash('message', 'Site deleted successfully.');
        
        return redirect()->route('call-centers.sites.index', ['callCenter' => $this->callCenter->id]);
    }
    
    public function render()
    {
        return view('livewire.call-centers.sites.site-delete', [
            'callCenter' => $this->callCenter,
            'site' => $this->site,
        ]);
    }
}
