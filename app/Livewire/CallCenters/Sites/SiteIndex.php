<?php

namespace App\Livewire\CallCenters\Sites;

use App\Models\CallCenter;
use App\Models\Site;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class SiteIndex extends Component
{
    use WithPagination;

    public $callCenter;
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $selectedSites = [];
    public $selectAll = false;
    public $statusFilter = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
        'statusFilter' => ['except' => ''],
    ];

    public function mount($callCenter = null)
    {
        if ($callCenter instanceof CallCenter) {
            $this->callCenter = $callCenter;
        } elseif (is_numeric($callCenter)) {
            $this->callCenter = CallCenter::find($callCenter);
        } else {
            // Redirect to call centers list if no valid call center is provided
            return redirect()->route('call-centers.list');
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedSites = $this->getSites()->pluck('id')->map(fn($id) => (string) $id)->toArray();
        } else {
            $this->selectedSites = [];
        }
    }

    public function getSites()
    {
        $query = Site::query()
            ->where('call_center_id', $this->callCenter->id)
            ->when($this->search, function ($query) {
                return $query->where(function ($query) {
                    $query->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('location', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->statusFilter, function ($query) {
                return $query->where('status', $this->statusFilter);
            });

        return $query->orderBy($this->sortField, $this->sortDirection);
    }

    public function deleteSelected()
    {
        if (count($this->selectedSites) > 0) {
            Site::whereIn('id', $this->selectedSites)->delete();
            $this->selectedSites = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected sites have been deleted.');
        }
    }

    #[On('to-site-show')]
    public function navigateToShow($data)
    {
        if (isset($data['callCenter']) && isset($data['site'])) {
            return redirect()->route('call-centers.sites.show', [
                'callCenter' => $data['callCenter'],
                'site' => $data['site']
            ]);
        }
    }

    #[On('to-site-edit')]
    public function navigateToEdit($data)
    {
        if (isset($data['callCenter']) && isset($data['site'])) {
            return redirect()->route('call-centers.sites.edit', [
                'callCenter' => $data['callCenter'],
                'site' => $data['site']
            ]);
        }
    }

    #[On('to-site-delete')]
    public function navigateToDelete($data)
    {
        if (isset($data['callCenter']) && isset($data['site'])) {
            return redirect()->route('call-centers.sites.delete', [
                'callCenter' => $data['callCenter'],
                'site' => $data['site']
            ]);
        }
    }

    #[On('navigate-to')]
    public function handleNavigation($data)
    {
        if (isset($data['route'])) {
            return redirect()->route($data['route'], $data['params'] ?? []);
        }
    }

    public function render()
    {
        return view('livewire.call-centers.sites.site-index', [
            'sites' => $this->getSites()->paginate($this->perPage),
            'callCenter' => $this->callCenter,
        ]);
    }
}
