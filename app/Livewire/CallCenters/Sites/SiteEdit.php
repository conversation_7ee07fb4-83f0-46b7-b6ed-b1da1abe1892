<?php

namespace App\Livewire\CallCenters\Sites;

use App\Models\CallCenter;
use App\Models\Site;
use App\Models\User;
use Livewire\Component;

class SiteEdit extends Component
{
    public $callCenter;
    public $site;
    public $name;
    public $location;
    public $capacity;
    public $description;
    public $status;
    public $manager_id;
    public $it_manager_id;
    public $trainer_id;
    public $accountant_id;
    public $hr_manager_id;
    
    public $managers = [];
    
    protected $rules = [
        'name' => 'required|string|max:255',
        'location' => 'required|string|max:255',
        'capacity' => 'nullable|integer|min:1',
        'description' => 'nullable|string',
        'status' => 'required|in:active,inactive',
        'manager_id' => 'nullable|exists:users,id',
        'it_manager_id' => 'nullable|exists:users,id',
        'trainer_id' => 'nullable|exists:users,id',
        'accountant_id' => 'nullable|exists:users,id',
        'hr_manager_id' => 'nullable|exists:users,id',
    ];
    
    public function mount(CallCenter $callCenter, Site $site)
    {
        $this->callCenter = $callCenter;
        $this->site = $site;
        
        $this->name = $site->name;
        $this->location = $site->location;
        $this->capacity = $site->capacity;
        $this->description = $site->description;
        $this->status = $site->status;
        $this->manager_id = $site->manager_id;
        $this->it_manager_id = $site->it_manager_id;
        $this->trainer_id = $site->trainer_id;
        $this->accountant_id = $site->accountant_id;
        $this->hr_manager_id = $site->hr_manager_id;
        
        $this->loadManagers();
    }
    
    public function loadManagers()
    {
        // Get users with manager roles
        $this->managers = User::whereIn('role_id', [2, 3, 4, 11, 12]) // Director, Manager, Supervisor, Accountant, HR Manager
            ->orderBy('first_name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->full_name,
                    'role' => $user->role->name,
                ];
            })
            ->toArray();
    }
    
    public function save()
    {
        $this->validate();
        
        $this->site->name = $this->name;
        $this->site->location = $this->location;
        $this->site->capacity = $this->capacity;
        $this->site->description = $this->description;
        $this->site->status = $this->status;
        $this->site->manager_id = $this->manager_id;
        $this->site->it_manager_id = $this->it_manager_id;
        $this->site->trainer_id = $this->trainer_id;
        $this->site->accountant_id = $this->accountant_id;
        $this->site->hr_manager_id = $this->hr_manager_id;
        
        $this->site->save();
        
        session()->flash('message', 'Site updated successfully.');
        
        return redirect()->route('call-centers.sites.show', ['callCenter' => $this->callCenter->id, 'site' => $this->site->id]);
    }
    
    public function render()
    {
        return view('livewire.call-centers.sites.site-edit', [
            'callCenter' => $this->callCenter,
            'site' => $this->site,
            'managers' => $this->managers,
        ]);
    }
}
