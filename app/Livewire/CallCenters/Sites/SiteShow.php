<?php

namespace App\Livewire\CallCenters\Sites;

use App\Models\CallCenter;
use App\Models\Site;
use Livewire\Component;

class SiteShow extends Component
{
    public $callCenter;
    public $site;
    
    public function mount(CallCenter $callCenter, Site $site)
    {
        $this->callCenter = $callCenter;
        $this->site = $site;
    }
    
    public function render()
    {
        return view('livewire.call-centers.sites.site-show', [
            'callCenter' => $this->callCenter,
            'site' => $this->site,
        ]);
    }
}
