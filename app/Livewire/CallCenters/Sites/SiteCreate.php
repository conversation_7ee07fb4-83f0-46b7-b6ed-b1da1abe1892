<?php

namespace App\Livewire\CallCenters\Sites;

use App\Models\CallCenter;
use App\Models\Site;
use App\Models\User;
use Livewire\Component;

class SiteCreate extends Component
{
    public $callCenter;
    public $name;
    public $location;
    public $capacity;
    public $description;
    public $status = 'active';
    public $manager_id;
    public $it_manager_id;
    public $trainer_id;
    public $accountant_id;
    public $hr_manager_id;
    
    public $managers = [];
    
    protected $rules = [
        'name' => 'required|string|max:255',
        'location' => 'required|string|max:255',
        'capacity' => 'nullable|integer|min:1',
        'description' => 'nullable|string',
        'status' => 'required|in:active,inactive',
        'manager_id' => 'nullable|exists:users,id',
        'it_manager_id' => 'nullable|exists:users,id',
        'trainer_id' => 'nullable|exists:users,id',
        'accountant_id' => 'nullable|exists:users,id',
        'hr_manager_id' => 'nullable|exists:users,id',
    ];
    
    public function mount(CallCenter $callCenter)
    {
        $this->callCenter = $callCenter;
        $this->loadManagers();
    }
    
    public function loadManagers()
    {
        // Get users with manager roles
        $this->managers = User::whereIn('role_id', [2, 3, 4, 11, 12]) // Director, Manager, Supervisor, Accountant, HR Manager
            ->orderBy('first_name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->full_name,
                    'role' => $user->role->name,
                ];
            })
            ->toArray();
    }
    
    public function save()
    {
        $this->validate();
        
        $site = new Site();
        $site->name = $this->name;
        $site->location = $this->location;
        $site->capacity = $this->capacity;
        $site->description = $this->description;
        $site->status = $this->status;
        $site->manager_id = $this->manager_id;
        $site->it_manager_id = $this->it_manager_id;
        $site->trainer_id = $this->trainer_id;
        $site->accountant_id = $this->accountant_id;
        $site->hr_manager_id = $this->hr_manager_id;
        $site->call_center_id = $this->callCenter->id;
        
        $site->save();
        
        session()->flash('message', 'Site created successfully.');
        
        return redirect()->route('call-centers.sites.index', ['callCenter' => $this->callCenter->id]);
    }
    
    public function render()
    {
        return view('livewire.call-centers.sites.site-create', [
            'callCenter' => $this->callCenter,
            'managers' => $this->managers,
        ]);
    }
}
