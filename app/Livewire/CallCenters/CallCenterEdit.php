<?php

namespace App\Livewire\CallCenters;

use App\Livewire\Forms\CallCenterForm;
use App\Models\CallCenter;
use App\Models\User;
use Livewire\Attributes\On;
use Livewire\Component;

class CallCenterEdit extends Component
{
    public CallCenterForm $form;
    public $callCenter;

    public function mount(CallCenter $callCenter)
    {
        $this->callCenter = $callCenter;
        $this->form->name = $callCenter->name;
        $this->form->description = $callCenter->description;
        $this->form->director_id = $callCenter->director_id;
        $this->form->status = $callCenter->status;
        $this->form->contact_email = $callCenter->contact_email;
        $this->form->contact_phone = $callCenter->contact_phone;
        $this->form->address = $callCenter->address;
    }

    public function save()
    {
        $this->form->update($this->callCenter);

        session()->flash('message', 'Call center updated successfully.');

        return $this->redirect(route('call-centers.show', $this->callCenter), navigate: true);
    }

    #[On('to-call-center-show')]
    public function navigateToShow($data = null)
    {
        if (isset($data['callCenter'])) {
            return redirect()->route('call-centers.show', ['callCenter' => $data['callCenter']]);
        } else {
            // If no specific call center is provided, use the current one
            return redirect()->route('call-centers.show', ['callCenter' => $this->callCenter->id]);
        }
    }

    public function render()
    {
        $directors = User::where('role_id', 2)->get();

        return view('livewire.call-centers.call-center-edit', [
            'directors' => $directors
        ]);
    }
}
