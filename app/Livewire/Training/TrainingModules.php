<?php

namespace App\Livewire\Training;

use Livewire\Component;

class TrainingModules extends Component
{
    public $modules = [
        [
            'id' => 1,
            'title' => 'Introduction to Call Center',
            'description' => 'Basic introduction to call center operations and customer service principles.',
            'duration' => '1 week',
            'status' => 'active',
        ],
        [
            'id' => 2,
            'title' => 'Product Knowledge',
            'description' => 'Detailed information about company products and services.',
            'duration' => '2 weeks',
            'status' => 'active',
        ],
        [
            'id' => 3,
            'title' => 'Call Handling Techniques',
            'description' => 'Advanced techniques for handling different types of calls and customer situations.',
            'duration' => '1 week',
            'status' => 'active',
        ],
        [
            'id' => 4,
            'title' => 'CRM System Training',
            'description' => 'Training on using the customer relationship management system.',
            'duration' => '1 week',
            'status' => 'active',
        ],
    ];

    public function render()
    {
        return view('livewire.training.training-modules', [
            'trainingModules' => $this->modules,
        ]);
    }
}
