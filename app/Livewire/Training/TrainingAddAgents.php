<?php

namespace App\Livewire\Training;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class TrainingAddAgents extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'first_name';
    public string $sortDirection = 'asc';
    public int $perPage = 8;
    public array $selectedAgents = [];
    public bool $selectAll = false;
    public string $statusFilter = ''; // Filter for 'active' (new) or 'inactive' (recycled) agents

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'first_name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 8, 'as' => 'pp'],
        'statusFilter' => ['except' => '', 'as' => 'status'],
    ];

    public function mount()
    {
        $this->selectedAgents = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection', 'statusFilter'])) {
            $this->resetPage();
            $this->selectedAgents = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll()
    {
        if ($this->selectAll) {
            $this->selectedAgents = $this->agents->pluck('id')->map(fn($id) => (string)$id)->all();
        } else {
            $this->selectedAgents = [];
        }
    }

    public function toggleAgentSelection(int $agentId)
    {
        $agentIdStr = (string)$agentId;

        // This is needed because wire:model with checkboxes uses string values
        if (in_array($agentIdStr, $this->selectedAgents)) {
            $this->selectedAgents = array_values(array_diff($this->selectedAgents, [$agentIdStr]));
        } else {
            $this->selectedAgents[] = $agentIdStr;
        }

        $currentPageIds = $this->agents->pluck('id')->map(fn($id) => (string)$id)->all();
        $this->selectAll = !empty($this->selectedAgents) && count(array_intersect($currentPageIds, $this->selectedAgents)) === count($currentPageIds);
    }

    public function addToTraining()
    {
        if (!empty($this->selectedAgents)) {
            // Convert string IDs to integers for the database query
            $agentIds = array_map('intval', $this->selectedAgents);

            User::whereIn('id', $agentIds)
                ->update(['status' => 'in_training']);

            $this->selectedAgents = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected agents added to training successfully!');
            $this->resetPage();
        }
    }

    public function cancel()
    {
        return $this->redirect(route('training.agents'), navigate: true);
    }

    protected function getAgentsQuery()
    {
        $query = User::query()
            ->with('role')
            ->where('role_id', 6)
            ->whereIn('status', ['active', 'inactive']); // Show both active (new hires) and inactive (retrograded) agents

        // Apply status filter if selected
        if (!empty($this->statusFilter)) {
            if ($this->statusFilter === 'new') {
                $query->where('status', 'active');
            } elseif ($this->statusFilter === 'recycled') {
                $query->where('status', 'inactive');
            }
        }

        $query->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    public function getAgentsProperty()
    {
        return $this->getAgentsQuery()->paginate($this->perPage);
    }

    public function render()
    {
        return view('livewire.training.training-add-agents', [
            'agents' => $this->agents,
            'statusOptions' => [
                '' => 'All Agents',
                'new' => 'New Agents',
                'recycled' => 'Recycled Agents',
            ],
        ]);
    }
}
