<?php

namespace App\Livewire\Training;

use App\Models\TrainingModule;
use Livewire\Component;

class TrainingModuleDelete extends Component
{
    public TrainingModule $module;

    public function mount(TrainingModule $module)
    {
        $this->module = $module;
    }

    public function deleteModule()
    {
        // Check if the module is used in any sessions
        if ($this->module->sessions()->count() > 0) {
            session()->flash('error', 'Cannot delete module because it is used in one or more training sessions.');
            return $this->redirect(route('training.modules'), navigate: true);
        }

        $this->module->delete();
        $this->dispatch('moduleDeleted');

        return $this->redirect(route('training.modules'), navigate: true);
    }

    public function render()
    {
        return view('livewire.training.training-module-delete', [
            'module' => $this->module,
            'sessionsCount' => $this->module->sessions()->count(),
        ]);
    }
}
