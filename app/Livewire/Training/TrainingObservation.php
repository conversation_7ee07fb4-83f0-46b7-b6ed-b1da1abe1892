<?php

namespace App\Livewire\Training;

use App\Models\User;
use App\Models\Training;
use Livewire\Component;

class TrainingObservation extends Component
{
    public ?User $agent;
    public $form = [
        'date' => '',
        'subject' => '',
        'observation' => '',
        'progress' => 0,
        'rating' => 0,
    ];

    public function mount(User $agent)
    {
        $this->agent = $agent;
        $this->form['date'] = date('Y-m-d');

        // Get current progress and rating if exists
        $training = Training::where('user_id', $agent->id)->latest()->first();
        if ($training) {
            $this->form['progress'] = $training->progress;
            $this->form['rating'] = $training->rating ?? 0;
        }
    }

    public function rules()
    {
        return [
            'form.date' => 'required|date',
            'form.subject' => 'required|string|min:3',
            'form.observation' => 'required|string|min:10',
            'form.progress' => 'required|integer|min:0|max:100',
            'form.rating' => 'required|numeric|min:0|max:3',
        ];
    }

    // This method will be called when the progress slider is moved
    public function updatedFormProgress()
    {
        // This will trigger a re-render with the new progress value
        $this->dispatch('progressUpdated');
    }

    // This method will be called when the progress is updated via the slider
    public function updateProgress()
    {
        // Ensure the progress is within the valid range (0-100)
        if ($this->form['progress'] < 0) {
            $this->form['progress'] = 0;
        } elseif ($this->form['progress'] > 100) {
            $this->form['progress'] = 100;
        }

        // This will trigger a re-render with the new progress value
        $this->dispatch('progressUpdated');
    }

    // This method will be called when the rating is changed
    public function updatedFormRating()
    {
        // Ensure the rating is within the valid range (0-3)
        if ($this->form['rating'] < 0) {
            $this->form['rating'] = 0;
        } elseif ($this->form['rating'] > 3) {
            $this->form['rating'] = 3;
        }

        // This will trigger a re-render with the new rating value
        $this->dispatch('ratingUpdated');
    }

    // This method will be called when the rating is updated via the slider
    public function updateRating()
    {
        // Ensure the rating is within the valid range (0-3)
        if ($this->form['rating'] < 0) {
            $this->form['rating'] = 0;
        } elseif ($this->form['rating'] > 3) {
            $this->form['rating'] = 3;
        }

        // This will trigger a re-render with the new rating value
        $this->dispatch('ratingUpdated');
    }

    public function submit()
    {
        $this->validate();

        // Prepare notes content
        $notesContent = $this->form['subject'] . ': ' . $this->form['observation'];

        // Update or create training record
        $training = Training::updateOrCreate(
            ['user_id' => $this->agent->id],
            [
                'start_date' => $this->form['date'],
                'progress' => $this->form['progress'],
                'rating' => $this->form['rating'],
                'notes' => $notesContent,
            ]
        );

        // Refresh the agent model to get the updated training relationship
        $this->agent->refresh();

        // Update the agent's training progress in the cache
        cache()->forget('agent_' . $this->agent->id . '_training');

        session()->flash('message', 'Observation saved successfully!');

        // Dispatch an event to refresh the agents list when returning to it
        // This will be caught by the TrainingAgents component
        $this->dispatch('refresh-agents-list')->to('training.training-agents');

        // If we want to stay on the same page after saving
        if ($this->redirectAfterSave) {
            return $this->redirect(route('training.agents'), navigate: true);
        }

        // Otherwise just show a success message
        return null;
    }

    // Property to control whether to redirect after saving
    public $redirectAfterSave = true;

    public function render()
    {
        return view('livewire.training.training-observation', [
            'agent' => $this->agent,
        ]);
    }
}
