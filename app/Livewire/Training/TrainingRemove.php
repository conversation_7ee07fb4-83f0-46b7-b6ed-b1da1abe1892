<?php

namespace App\Livewire\Training;

use App\Models\User;
use App\Models\Training;
use Livewire\Component;

class TrainingRemove extends Component
{
    public ?User $agent;

    public function mount(User $agent)
    {
        $this->agent = $agent;
    }

    public function removeTrainingAgent()
    {
        // Delete the training record
        Training::where('user_id', $this->agent->id)->delete();
        
        // Update the agent status
        $this->agent->update([
            'status' => 'inactive',
        ]);

        session()->flash('message', 'Agent removed from training successfully!');
        $this->dispatch('to-training-index');
    }

    public function render()
    {
        return view('livewire.training.training-remove', [
            'agent' => $this->agent,
        ]);
    }
}
