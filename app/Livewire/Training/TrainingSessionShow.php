<?php

namespace App\Livewire\Training;

use App\Models\TrainingSession;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class TrainingSessionShow extends Component
{
    use WithPagination;

    public TrainingSession $session;
    public $search = '';
    public $perPage = 10;

    protected $listeners = ['refreshSession' => '$refresh'];

    public function mount(TrainingSession $session)
    {
        $this->session = $session;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    protected function getTraineesQuery()
    {
        $query = $this->session->trainees()
            ->when(!empty(trim($this->search)), function ($q) {
                $searchTerm = '%' . trim($this->search) . '%';
                return $q->where(function ($subq) use ($searchTerm) {
                    $subq->where('first_name', 'like', $searchTerm)
                        ->orWhere('last_name', 'like', $searchTerm)
                        ->orWhere('email', 'like', $searchTerm);
                });
            });

        return $query;
    }

    public function addTrainee($userId)
    {
        $user = User::find($userId);
        if ($user) {
            // Check if user is already enrolled
            if (!$this->session->trainees->contains($user->id)) {
                $this->session->trainees()->attach($user->id, [
                    'status' => 'enrolled',
                    'enrollment_date' => now(),
                ]);
                session()->flash('message', 'Trainee added successfully!');
            } else {
                session()->flash('error', 'Trainee is already enrolled in this session.');
            }
        }
    }

    public function removeTrainee($userId)
    {
        $this->session->trainees()->detach($userId);
        session()->flash('message', 'Trainee removed successfully!');
    }

    public function updateTraineeStatus($userId, $status)
    {
        $this->session->trainees()->updateExistingPivot($userId, [
            'status' => $status,
            'completion_date' => in_array($status, ['completed']) ? now() : null,
        ]);
        session()->flash('message', 'Trainee status updated successfully!');
    }

    public function render()
    {
        return view('livewire.training.training-session-show', [
            'trainees' => $this->getTraineesQuery()->paginate($this->perPage),
            'modules' => $this->session->modules()->orderBy('order')->get(),
            'availableAgents' => User::where('role_id', 6)
                ->where('status', 'in_training')
                ->whereNotIn('id', $this->session->trainees->pluck('id'))
                ->get(),
        ]);
    }
}
