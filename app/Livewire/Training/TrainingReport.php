<?php

namespace App\Livewire\Training;

use App\Models\User;
use App\Models\Training;
use Livewire\Component;

class TrainingReport extends Component
{
    public ?User $agent;

    public function mount(User $agent)
    {
        $this->agent = $agent;
    }

    public function render()
    {
        // Get the latest training record for the agent
        $training = Training::where('user_id', $this->agent->id)
            ->latest()
            ->first();

        return view('livewire.training.training-report', [
            'agent' => $this->agent,
            'training' => $training,
        ]);
    }
}
