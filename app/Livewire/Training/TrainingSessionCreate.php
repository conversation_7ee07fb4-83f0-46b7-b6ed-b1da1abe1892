<?php

namespace App\Livewire\Training;

use App\Models\TrainingSession;
use App\Models\TrainingModule;
use App\Models\User;
use Livewire\Component;

class TrainingSessionCreate extends Component
{
    public $name;
    public $description;
    public $start_date;
    public $end_date;
    public $status = 'pending';
    public $capacity = 20;
    public $instructor_id;
    public $location;
    public $selectedModules = [];

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'start_date' => 'required|date',
        'end_date' => 'required|date|after_or_equal:start_date',
        'status' => 'required|in:pending,active,completed,cancelled',
        'capacity' => 'required|integer|min:1',
        'instructor_id' => 'nullable|exists:users,id',
        'location' => 'nullable|string|max:255',
        'selectedModules' => 'array',
    ];

    public function mount()
    {
        $this->start_date = now()->format('Y-m-d');
        $this->end_date = now()->addWeeks(2)->format('Y-m-d');
    }

    public function submit()
    {
        $this->validate();

        $session = TrainingSession::create([
            'name' => $this->name,
            'description' => $this->description,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'status' => $this->status,
            'capacity' => $this->capacity,
            'instructor_id' => $this->instructor_id,
            'location' => $this->location,
        ]);

        // Attach selected modules
        if (!empty($this->selectedModules)) {
            $moduleData = [];
            foreach ($this->selectedModules as $index => $moduleId) {
                $moduleData[$moduleId] = ['order' => $index + 1, 'required' => true];
            }
            $session->modules()->attach($moduleData);
        }

        session()->flash('message', 'Training session created successfully!');
        $this->dispatch('to-training-sessions');
    }

    public function render()
    {
        return view('livewire.training.training-session-create', [
            'instructors' => User::whereIn('role_id', [1, 2, 3, 4])->get(),
            'modules' => TrainingModule::where('status', 'active')->get(),
        ]);
    }
}
