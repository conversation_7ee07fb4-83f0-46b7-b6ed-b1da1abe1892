<?php

namespace App\Livewire\Training;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;

class TrainingIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $sortField = 'first_name';
    public $sortDirection = 'asc';
    public $perPage = 10;
    public $selectedAgents = [];
    public $selectAll = false;

    protected $listeners = ['refreshAgents' => '$refresh'];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    public function selectAllAgents()
    {
        if ($this->selectAll) {
            $this->selectedAgents = $this->getAgentsQuery()->pluck('id')->map(fn($id) => (string) $id)->toArray();
        } else {
            $this->selectedAgents = [];
        }
    }

    public function updatedSelectAll()
    {
        $this->selectAllAgents();
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedAgents)) {
            User::whereIn('id', $this->selectedAgents)->delete();
            $this->selectedAgents = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected users deleted successfully!');
            $this->resetPage();
        }
    }

    protected function getAgentsQuery()
    {
        $query = User::query()
            ->with('role')
            ->where([
                'role_id' => 6,
                'campaign_id' => '',
            ])
            ->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    // No need for mount method with redirect since we're handling it at the route level

    public function render()
    {
        return view('livewire.training.training-index', [
            'agents' => $this->getAgentsQuery()->paginate($this->perPage),
        ]);
    }
}
