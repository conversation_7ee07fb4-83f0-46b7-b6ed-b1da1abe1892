<?php

namespace App\Livewire\Training;

use App\Models\TrainingModule;
use Livewire\Component;

class TrainingModuleCreate extends Component
{
    public $name;
    public $description;
    public $duration;
    public $credits = 0;
    public $status = 'active';
    public $prerequisites = [];
    public $content;

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'duration' => 'required|string|max:50',
        'credits' => 'required|integer|min:0',
        'status' => 'required|in:active,draft,archived',
        'prerequisites' => 'nullable|array',
        'content' => 'nullable|string',
    ];

    public function addPrerequisite()
    {
        $this->prerequisites[] = '';
    }

    public function removePrerequisite($index)
    {
        unset($this->prerequisites[$index]);
        $this->prerequisites = array_values($this->prerequisites);
    }

    public function submit()
    {
        $this->validate();

        // Filter out empty prerequisites
        $this->prerequisites = array_filter($this->prerequisites, fn($item) => !empty($item));

        TrainingModule::create([
            'name' => $this->name,
            'description' => $this->description,
            'duration' => $this->duration,
            'credits' => $this->credits,
            'status' => $this->status,
            'prerequisites' => $this->prerequisites,
            'content' => $this->content,
        ]);

        session()->flash('message', 'Training module created successfully!');
        return $this->redirect(route('training.modules'), navigate: true);
    }

    public function render()
    {
        return view('livewire.training.training-module-create', [
            'existingModules' => TrainingModule::where('status', 'active')->get(),
        ]);
    }
}
