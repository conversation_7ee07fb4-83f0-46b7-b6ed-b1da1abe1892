<?php

namespace App\Livewire\Training;

use App\Models\TrainingSession;
use App\Models\TrainingModule;
use App\Models\User;
use Livewire\Component;

class TrainingSessionEdit extends Component
{
    public TrainingSession $session;
    public $name;
    public $description;
    public $start_date;
    public $end_date;
    public $status;
    public $capacity;
    public $instructor_id;
    public $location;
    public $selectedModules = [];

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'start_date' => 'required|date',
        'end_date' => 'required|date|after_or_equal:start_date',
        'status' => 'required|in:pending,active,completed,cancelled',
        'capacity' => 'required|integer|min:1',
        'instructor_id' => 'nullable|exists:users,id',
        'location' => 'nullable|string|max:255',
        'selectedModules' => 'array',
    ];

    public function mount(TrainingSession $session)
    {
        $this->session = $session;
        $this->name = $session->name;
        $this->description = $session->description;
        $this->start_date = $session->start_date->format('Y-m-d');
        $this->end_date = $session->end_date->format('Y-m-d');
        $this->status = $session->status;
        $this->capacity = $session->capacity;
        $this->instructor_id = $session->instructor_id;
        $this->location = $session->location;
        $this->selectedModules = $session->modules->pluck('id')->toArray();
    }

    public function submit()
    {
        $this->validate();

        $this->session->update([
            'name' => $this->name,
            'description' => $this->description,
            'start_date' => $this->start_date,
            'end_date' => $this->end_date,
            'status' => $this->status,
            'capacity' => $this->capacity,
            'instructor_id' => $this->instructor_id,
            'location' => $this->location,
        ]);

        // Sync selected modules
        if (!empty($this->selectedModules)) {
            $moduleData = [];
            foreach ($this->selectedModules as $index => $moduleId) {
                $moduleData[$moduleId] = ['order' => $index + 1, 'required' => true];
            }
            $this->session->modules()->sync($moduleData);
        } else {
            $this->session->modules()->detach();
        }

        session()->flash('message', 'Training session updated successfully!');
        $this->dispatch('to-training-session-show', ['session' => $this->session]);
    }

    public function render()
    {
        return view('livewire.training.training-session-edit', [
            'instructors' => User::whereIn('role_id', [1, 2, 3, 4])->get(),
            'modules' => TrainingModule::where('status', 'active')->get(),
        ]);
    }
}
