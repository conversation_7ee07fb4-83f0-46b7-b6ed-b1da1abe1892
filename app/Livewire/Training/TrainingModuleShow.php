<?php

namespace App\Livewire\Training;

use App\Models\TrainingModule;
use Livewire\Component;

class TrainingModuleShow extends Component
{
    public TrainingModule $module;

    public function mount(TrainingModule $module)
    {
        $this->module = $module;
    }

    public function render()
    {
        return view('livewire.training.training-module-show', [
            'module' => $this->module,
            'sessions' => $this->module->sessions()->with('instructor')->get(),
        ]);
    }
}
