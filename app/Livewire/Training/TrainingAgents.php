<?php

namespace App\Livewire\Training;

use App\Models\TrainingModule;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;

class TrainingAgents extends Component
{
    use WithPagination;

    public string $search = '';
    public string $sortField = 'first_name';
    public string $sortDirection = 'asc';
    public int $perPage = 8;
    public array $selectedAgents = [];
    public bool $selectAll = false;
    public string $status = 'in_training';
    public string $ratingFilter = '';
    public string $progressFilter = '';
    public string $moduleFilter = '';
    public $trainingModules = [];

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'sortField' => ['except' => 'first_name', 'as' => 'sort'],
        'sortDirection' => ['except' => 'asc', 'as' => 'dir'],
        'perPage' => ['except' => 8, 'as' => 'pp'],
        'status' => ['except' => 'in_training', 'as' => 'status'],
        'ratingFilter' => ['except' => '', 'as' => 'rating'],
        'progressFilter' => ['except' => '', 'as' => 'progress'],
        'moduleFilter' => ['except' => '', 'as' => 'module'],
    ];

    protected $listeners = ['refreshAgents' => '$refresh', 'refresh' => '$refresh'];

    public function mount()
    {
        $this->selectedAgents = [];
        $this->selectAll = false;
        $this->trainingModules = TrainingModule::all();
    }

    public function hydrate()
    {
        // This method is called on every request, ensuring fresh data
        // Clear any cached agent data
        cache()->flush();
    }

    public function boot()
    {
        // This ensures the component is always using fresh data
        // Disable query caching for this component
        DB::connection()->disableQueryLog();
        DB::connection()->unsetEventDispatcher();
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection', 'status', 'ratingFilter', 'progressFilter', 'moduleFilter'])) {
            $this->resetPage();
            $this->selectedAgents = [];
            $this->selectAll = false;
        }
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function toggleSelectAll()
    {
        $this->selectAll = !$this->selectAll;

        if ($this->selectAll) {
            $this->selectedAgents = $this->agents->pluck('id')->map(fn($id) => (string)$id)->all();
        } else {
            $this->selectedAgents = [];
        }
    }

    public function clearFilters()
    {
        $this->reset(['ratingFilter', 'progressFilter', 'moduleFilter']);
        $this->resetPage();
    }

    public function updatedRatingFilter()
    {
        $this->resetPage();
    }

    public function updatedProgressFilter()
    {
        $this->resetPage();
    }

    public function updatedModuleFilter()
    {
        $this->resetPage();
    }

    public function toggleAgentSelection(int $agentId)
    {
        $agentIdStr = (string)$agentId;

        // This is needed because wire:model with checkboxes uses string values
        if (in_array($agentIdStr, $this->selectedAgents)) {
            $this->selectedAgents = array_values(array_diff($this->selectedAgents, [$agentIdStr]));
        } else {
            $this->selectedAgents[] = $agentIdStr;
        }

        $currentPageIds = $this->agents->pluck('id')->map(fn($id) => (string)$id)->all();
        $this->selectAll = !empty($this->selectedAgents) && count(array_intersect($currentPageIds, $this->selectedAgents)) === count($currentPageIds);

        // Force a re-render to update the UI
        $this->dispatch('refresh');
    }

    #[On('removeFromTraining')]
    public function removeFromTraining()
    {
        if (!empty($this->selectedAgents)) {
            // Convert string IDs to integers for the database query
            $agentIds = array_map('intval', $this->selectedAgents);

            User::whereIn('id', $agentIds)
                ->update(['status' => 'active']);

            $this->selectedAgents = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected agents removed from training successfully!');
            $this->resetPage();
        }
    }

    #[On('addToTraining')]
    public function addToTraining()
    {
        return $this->redirect(route('training.add-agents'), navigate: true);
    }

    #[On('to-agent-create')]
    public function toAgentCreate()
    {
        return $this->redirect(route('agents.create'), navigate: true);
    }

    protected function getAgentsQuery()
    {
        $query = User::query()
            ->with(['role', 'training' => function($query) {
                // Ensure we're getting fresh data
                $query->withoutGlobalScopes();
            }])
            ->where('role_id', 6);

        if ($this->status) {
            $query->where('status', $this->status);
        }

        // Apply rating filter if selected
        if (!empty($this->ratingFilter)) {
            $ratingRange = $this->getRatingRange($this->ratingFilter);
            if ($ratingRange) {
                $query->whereHas('training', function($q) use ($ratingRange) {
                    $q->whereBetween('rating', [$ratingRange['min'], $ratingRange['max']]);
                });
            }
        }

        // Apply progress filter if selected
        if (!empty($this->progressFilter)) {
            $progressRange = $this->getProgressRange($this->progressFilter);
            if ($progressRange) {
                $query->whereHas('training', function($q) use ($progressRange) {
                    $q->whereBetween('progress', [$progressRange['min'], $progressRange['max']]);
                });
            }
        }

        // Apply module filter if selected
        if (!empty($this->moduleFilter)) {
            $query->whereHas('training', function($q) {
                $q->where('module_id', $this->moduleFilter);
            });
        }

        $query->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('first_name', 'like', $searchTerm)
                    ->orWhere('last_name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm);
            });
        }

        return $query;
    }

    /**
     * Get the rating range based on the filter value
     */
    protected function getRatingRange(string $filter): ?array
    {
        return match($filter) {
            'low' => ['min' => 0, 'max' => 1],
            'medium' => ['min' => 1.01, 'max' => 2],
            'high' => ['min' => 2.01, 'max' => 3],
            default => null,
        };
    }

    /**
     * Get the progress range based on the filter value
     */
    protected function getProgressRange(string $filter): ?array
    {
        return match($filter) {
            'beginner' => ['min' => 0, 'max' => 33],
            'intermediate' => ['min' => 34, 'max' => 66],
            'advanced' => ['min' => 67, 'max' => 100],
            default => null,
        };
    }

    public function getAgentsProperty()
    {
        // Always get fresh data
        return $this->getAgentsQuery()->paginate($this->perPage);
    }

    // This ensures the component is re-rendered when returning from another page
    #[On('refresh-agents-list')]
    public function refreshAgentsList()
    {
        // Force a complete refresh of the component
        $this->resetPage();
        $this->reset(['selectedAgents', 'selectAll']);

        // Clear any cached data
        cache()->flush();

        // Force a re-render
        $this->dispatch('$refresh');
    }

    public function render()
    {
        return view('livewire.training.training-agents', [
            'agents' => $this->agents,
            'trainingModules' => $this->trainingModules,
        ]);
    }
}
