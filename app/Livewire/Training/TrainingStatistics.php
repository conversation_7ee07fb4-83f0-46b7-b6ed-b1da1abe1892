<?php

namespace App\Livewire\Training;

use App\Models\User;
use App\Models\Training;
use Livewire\Component;

class TrainingStatistics extends Component
{
    public $statistics = [];

    public function mount()
    {
        $this->loadStatistics();
    }

    private function loadStatistics()
    {
        // Count agents in training
        $agentsInTraining = User::where([
            'role_id' => 6,
            'status' => 'in_training',
        ])->count();

        // Count completed trainings
        $completedTrainings = Training::whereNotNull('validated_at')->count();

        // Count ongoing trainings
        $ongoingTrainings = Training::whereNull('validated_at')->count();

        // Calculate average training duration (in days)
        $avgDuration = 14; // Default value, in a real app this would be calculated from data

        // Calculate success rate
        $successRate = $completedTrainings > 0 ? 
            round(($completedTrainings / ($completedTrainings + $ongoingTrainings)) * 100) : 0;

        $this->statistics = [
            'agentsInTraining' => $agentsInTraining,
            'completedTrainings' => $completedTrainings,
            'ongoingTrainings' => $ongoingTrainings,
            'avgDuration' => $avgDuration,
            'successRate' => $successRate,
        ];
    }

    public function render()
    {
        return view('livewire.training.training-statistics', [
            'statistics' => $this->statistics,
        ]);
    }
}
