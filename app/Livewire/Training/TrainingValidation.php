<?php

namespace App\Livewire\Training;

use App\Models\Campaign;
use App\Models\User;
use App\Models\Training;
use Livewire\Component;

class TrainingValidation extends Component
{
    public ?User $agent;
    public $campaign_id;
    public $notes;
    public $progress = 100; // Set to 100% when validating

    public function mount(User $agent)
    {
        $this->agent = $agent;
    }

    public function rules()
    {
        return [
            'campaign_id' => 'nullable|exists:campaigns,id',
            'notes' => 'nullable|string',
        ];
    }

    public function submit()
    {
        $this->validate();

        // If campaign_id is provided, update status to 'active' and assign to campaign
        // Otherwise, update status to 'validated' (ready for production but not assigned)
        $this->agent->update([
            'status' => $this->campaign_id ? 'active' : 'validated',
            'campaign_id' => $this->campaign_id,
        ]);

        // Update or create training record
        Training::updateOrCreate(
            ['user_id' => $this->agent->id],
            [
                'progress' => $this->progress,
                'notes' => $this->notes,
                'validated_at' => now(),
            ]
        );

        session()->flash('message', 'Agent validated successfully!');
        return $this->redirect(route('training.agents'), navigate: true);
    }

    public function render()
    {
        return view('livewire.training.training-validation', [
            'agent' => $this->agent,
            'campaigns' => Campaign::all(),
        ]);
    }
}
