<?php

namespace App\Livewire\Training;

use App\Models\TrainingModule;
use Livewire\Component;

class TrainingModuleEdit extends Component
{
    public TrainingModule $module;
    public $name;
    public $description;
    public $duration;
    public $credits;
    public $status;
    public $prerequisites = [];
    public $content;

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string',
        'duration' => 'required|string|max:50',
        'credits' => 'required|integer|min:0',
        'status' => 'required|in:active,draft,archived',
        'prerequisites' => 'nullable|array',
        'content' => 'nullable|string',
    ];

    public function mount(TrainingModule $module)
    {
        $this->module = $module;
        $this->name = $module->name;
        $this->description = $module->description;
        $this->duration = $module->duration;
        $this->credits = $module->credits;
        $this->status = $module->status;
        $this->prerequisites = $module->prerequisites ?? [];
        $this->content = $module->content;
    }

    public function addPrerequisite()
    {
        $this->prerequisites[] = '';
    }

    public function removePrerequisite($index)
    {
        unset($this->prerequisites[$index]);
        $this->prerequisites = array_values($this->prerequisites);
    }

    public function submit()
    {
        $this->validate();

        // Filter out empty prerequisites
        $this->prerequisites = array_filter($this->prerequisites, fn($item) => !empty($item));

        $this->module->update([
            'name' => $this->name,
            'description' => $this->description,
            'duration' => $this->duration,
            'credits' => $this->credits,
            'status' => $this->status,
            'prerequisites' => $this->prerequisites,
            'content' => $this->content,
        ]);

        session()->flash('message', 'Training module updated successfully!');
        return $this->redirect(route('training.modules'), navigate: true);
    }

    public function render()
    {
        return view('livewire.training.training-module-edit', [
            'existingModules' => TrainingModule::where('id', '!=', $this->module->id)
                ->where('status', 'active')
                ->get(),
        ]);
    }
}
