<?php

namespace App\Livewire\Training;

use App\Models\TrainingSession;
use App\Traits\HandlePageExpiration;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;

class TrainingSessionIndex extends Component
{
    use WithPagination, HandlePageExpiration;

    public $search = '';
    public $status = '';
    public $sortField = 'start_date';
    public $sortDirection = 'desc';
    public $perPage = 10;

    protected $listeners = ['refreshSessions' => '$refresh'];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    #[On('to-training-session-create')]
    public function toTrainingSessionCreate()
    {
        return $this->redirect(route('training.sessions.create'), navigate: true);
    }

    #[On('to-training-session-show')]
    public function toTrainingSessionShow($session)
    {
        return $this->redirect(route('training.sessions.show', ['session' => $session]), navigate: true);
    }

    #[On('to-training-session-edit')]
    public function toTrainingSessionEdit($session)
    {
        return $this->redirect(route('training.sessions.edit', ['session' => $session]), navigate: true);
    }

    #[On('to-training-session-delete')]
    public function toTrainingSessionDelete($session)
    {
        return $this->redirect(route('training.sessions.delete', ['session' => $session]), navigate: true);
    }

    protected function getSessionsQuery()
    {
        $query = TrainingSession::query()
            ->with('instructor')
            ->withCount('trainees')
            ->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('description', 'like', $searchTerm)
                    ->orWhere('location', 'like', $searchTerm);
            });
        }

        if (!empty($this->status)) {
            $query->where('status', $this->status);
        }

        return $query;
    }

    public function render()
    {
        return view('livewire.training.training-session-index', [
            'sessions' => $this->getSessionsQuery()->paginate($this->perPage),
        ]);
    }
}
