<?php

namespace App\Livewire\Training;

use App\Models\TrainingModule;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;

class TrainingModuleIndex extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $perPage = 10;
    public $selectedModules = [];
    public $selectAll = false;

    protected $listeners = ['refreshModules' => '$refresh'];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        $this->sortField = $field;
    }

    #[On('moduleDeleted')]
    public function moduleDeleted()
    {
        session()->flash('message', 'Module deleted successfully!');
        $this->resetPage();
    }

    public function showModule($moduleId)
    {
        $this->dispatch('to-training-module-show', $moduleId);
    }

    public function updatedSelectAll($value)
    {
        $modules = $this->getModulesQuery()->paginate($this->perPage);
        $this->selectedModules = $value
            ? $modules->pluck('id')->map(fn($id) => (string) $id)->toArray()
            : [];
    }

    public function toggleModuleSelection($moduleId)
    {
        $moduleId = (string) $moduleId;

        if (in_array($moduleId, $this->selectedModules)) {
            $this->selectedModules = array_diff($this->selectedModules, [$moduleId]);
        } else {
            $this->selectedModules[] = $moduleId;
        }

        $currentPageIds = $this->getModulesQuery()->paginate($this->perPage)->pluck('id')->map(fn($id) => (string) $id)->toArray();
        $this->selectAll = !empty($this->selectedModules) && count(array_intersect($currentPageIds, $this->selectedModules)) === count($currentPageIds);
    }

    public function deleteSelected()
    {
        // Check if any modules are used in sessions
        $modulesInUse = TrainingModule::whereIn('id', $this->selectedModules)
            ->whereHas('sessions')
            ->pluck('name')
            ->toArray();

        if (count($modulesInUse) > 0) {
            $modulesList = implode(', ', $modulesInUse);
            session()->flash('error', "Cannot delete the following modules because they are used in training sessions: {$modulesList}");
            return;
        }

        // Delete modules that are not in use
        $deletedCount = TrainingModule::whereIn('id', $this->selectedModules)->delete();

        session()->flash('message', "{$deletedCount} module(s) have been deleted successfully.");
        $this->selectedModules = [];
        $this->selectAll = false;
    }

    protected function getModulesQuery()
    {
        $query = TrainingModule::query()
            ->orderBy($this->sortField, $this->sortDirection);

        if (!empty(trim($this->search))) {
            $searchTerm = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('description', 'like', $searchTerm);
            });
        }

        if (!empty($this->status)) {
            $query->where('status', $this->status);
        }

        return $query;
    }

    public function render()
    {
        return view('livewire.training.training-module-index', [
            'modules' => $this->getModulesQuery()->paginate($this->perPage),
        ]);
    }
}
