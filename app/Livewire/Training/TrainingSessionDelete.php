<?php

namespace App\Livewire\Training;

use App\Models\TrainingSession;
use Livewire\Component;

class TrainingSessionDelete extends Component
{
    public TrainingSession $session;

    public function mount(TrainingSession $session)
    {
        $this->session = $session;
    }

    public function deleteSession()
    {
        // Delete the session
        $this->session->delete();

        session()->flash('message', 'Training session deleted successfully!');
        $this->dispatch('to-training-sessions');
    }

    public function render()
    {
        return view('livewire.training.training-session-delete', [
            'session' => $this->session,
        ]);
    }
}
