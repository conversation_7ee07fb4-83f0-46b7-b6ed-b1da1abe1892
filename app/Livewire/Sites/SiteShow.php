<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use Livewire\Component;

class SiteShow extends Component
{
    public Site $site;

    public function mount(Site $site)
    {
        $this->site = $site;
    }

    public function render()
    {
        return view('livewire.sites.site-show', [
            'site' => $this->site->load(['callCenter', 'platforms', 'manager', 'itManager', 'trainer', 'accountant', 'hrManager'])
        ]);
    }
}