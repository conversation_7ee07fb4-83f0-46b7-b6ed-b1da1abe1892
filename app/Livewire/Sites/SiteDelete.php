<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use Livewire\Component;

class SiteDelete extends Component
{
    public Site $site;
    
    public function mount(Site $site)
    {
        $this->site = $site;
    }
    
    public function delete()
    {
        // Check if site has platforms
        if ($this->site->platforms()->count() > 0) {
            session()->flash('error', 'Cannot delete site with associated platforms. Please delete platforms first.');
            return $this->redirect(route('sites.show', $this->site), navigate: true);
        }
        
        $this->site->delete();
        
        session()->flash('message', 'Site deleted successfully!');
        
        return $this->redirect(route('sites.index'), navigate: true);
    }
    
    public function cancel()
    {
        return $this->redirect(route('sites.show', $this->site), navigate: true);
    }
    
    public function render()
    {
        return view('livewire.sites.site-delete');
    }
}