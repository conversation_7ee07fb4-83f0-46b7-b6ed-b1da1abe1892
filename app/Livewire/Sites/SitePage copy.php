<?php

namespace App\Livewire\Sites;

use App\Livewire\Global\Page;
use App\Models\Site;
use App\Models\Platform;
use App\Traits\HandlePageExpiration;
use Livewire\Attributes\On;

class SitePage extends Page
{
    use HandlePageExpiration;

    public ?Site $site = null;
    public ?Platform $platform = null;
    public array $pages = [];
    public array $current_page = [];
    public array $current_page_resume = [];
    public array $current_page_section = [];

    #[On('to-site-index')]
    public function toSiteIndex()
    {
        return $this->redirect(route('sites.index'), navigate: true);
    }

    #[On('to-site-show')]
    public function toSiteShow(Site $site)
    {
        if (!$site) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.show', ['site' => $site]), navigate: true);
    }

    #[On('to-site-edit')]
    public function toSiteEdit(Site $site)
    {
        if (!$site) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.edit', ['site' => $site]), navigate: true);
    }

    #[On('to-site-delete')]
    public function toSiteDelete(Site $site)
    {
        if (!$site) {
            return $this->redirect(route('sites.index'), navigate: true);
        }
        return $this->redirect(route('sites.delete', ['site' => $site]), navigate: true);
    }

    #[On('to-site-create')]
    public function toSiteCreate()
    {
        return $this->redirect(route('sites.create'), navigate: true);
    }

    #[On('to-site-platforms')]
    public function toSitePlatforms()
    {
        return $this->redirect(route('sites.platforms'), navigate: true);
    }

    #[On('to-site-equipments')]
    public function toSiteEquipments()
    {
        return $this->redirect(route('sites.equipments'), navigate: true);
    }

    #[On('to-site-reports')]
    public function toSiteReports()
    {
        return $this->redirect(route('sites.reports'), navigate: true);
    }

    #[On('to-site-personnel')]
    public function toSitePersonnel()
    {
        return $this->redirect(route('sites.personnel'), navigate: true);
    }

    #[On('to-site-statistics')]
    public function toSiteStatistics()
    {
        return $this->redirect(route('sites.statistics'), navigate: true);
    }

    public function setPageResume($route)
    {
        switch ($route) {
            // Site Management
            case 'sites.index':
                $this->current_page_resume['title'] = 'Sites Management';
                $this->current_page_resume['description'] = 'Manage all sites and their details';
                $this->current_page_resume['type'] = 'infos';
                break;
            case 'sites.create':
                $this->current_page_resume['title'] = 'Create New Site';
                $this->current_page_resume['description'] = 'Add a new site to the system';
                $this->current_page_resume['type'] = 'chart';
                break;
            case 'sites.edit':
                $this->current_page_resume['title'] = 'Edit Site';
                $this->current_page_resume['description'] = 'Modify site details';
                $this->current_page_resume['type'] = 'chart';
                break;
            case 'sites.delete':
                $this->current_page_resume['title'] = 'Delete Site';
                $this->current_page_resume['description'] = 'Remove site from the system';
                $this->current_page_resume['type'] = 'infos';
                break;
            case 'sites.show':
                $this->current_page_resume['title'] = 'Site Details';
                $this->current_page_resume['description'] = 'View site information and platforms';
                $this->current_page_resume['type'] = 'infos';
                break;

            // Platform Management
            case 'sites.platforms.index':
                $this->current_page_resume['title'] = 'Site Platforms';
                $this->current_page_resume['description'] = 'Manage platforms for this site';
                $this->current_page_resume['type'] = 'infos';
                break;

            // Equipment Management
            case 'sites.equipments.index':
                $this->current_page_resume['title'] = 'Site Equipment';
                $this->current_page_resume['description'] = 'Manage equipment for this site';
                $this->current_page_resume['type'] = 'infos';
                break;

            // Reports Management
            case 'sites.reports.index':
                $this->current_page_resume['title'] = 'Site Reports';
                $this->current_page_resume['description'] = 'Manage reports for this site';
                $this->current_page_resume['type'] = 'infos';
                break;

            // Personnel Management
            case 'sites.personnels.index':
                $this->current_page_resume['title'] = 'Site Personnel';
                $this->current_page_resume['description'] = 'Manage personnel for this site';
                $this->current_page_resume['type'] = 'infos';
                break;

            // Statistics
            case 'sites.statistics.index':
                $this->current_page_resume['title'] = 'Site Statistics';
                $this->current_page_resume['description'] = 'View statistics for this site';
                $this->current_page_resume['type'] = 'chart';
                break;

            default:
                $this->current_page_resume['title'] = 'Sites';
                $this->current_page_resume['description'] = 'Manage sites';
                $this->current_page_resume['type'] = 'infos';
                break;
        }
    }

    public function mount($component = '')
    {
        parent::mount($component);
        $this->pages = [
            [
                'module_id' => 'site-module',
                'title' => 'Management',
                'description' => 'Site management',
                'route' => 'sites.index',
                'section_routes' => ['sites.create', 'sites.edit', 'sites.delete', 'sites.show'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                    [
                        'title' => 'Create',
                        'description' => 'Create a site',
                        'route' => 'sites.create',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Edit',
                        'description' => 'Edit a site',
                        'route' => 'sites.edit',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Delete',
                        'description' => 'Delete a site',
                        'route' => 'sites.delete',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ],
                    [
                        'title' => 'Show',
                        'description' => 'Show a site',
                        'route' => 'sites.show',
                        'display' => true,
                        'authorized_roles' => [1, 2, 3, 4],
                    ]
                ]
            ],
            [
                'module_id' => 'site-platforms-module',
                'title' => 'Platforms',
                'description' => 'Manage site platforms',
                'route' => 'sites.platforms.index',
                'section_routes' => ['sites.platforms.create', 'sites.platforms.edit', 'sites.platforms.delete', 'sites.platforms.show'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                    
                ]
            ],
            [
                'module_id' => 'site-equipments-module',
                'title' => 'Equipment',
                'description' => 'Manage site equipment',
                'route' => 'sites.equipments.index',
                'section_routes' => ['sites.equipments.create', 'sites.equipments.edit', 'sites.equipments.delete', 'sites.equipments.show'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                   
                ]
            ],
            [
                'module_id' => 'site-reports-module',
                'title' => 'Reports',
                'description' => 'Manage site reports',
                'route' => 'sites.reports.index',
                'section_routes' => ['sites.reports.create', 'sites.reports.edit', 'sites.reports.delete', 'sites.reports.show'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                ]
            ],
            [
                'module_id' => 'site-personnel-module',
                'title' => 'Personnel',
                'description' => 'Manage site personnel',
                'route' => 'sites.personnels.index',
                'section_routes' => ['sites.personnel.assign'],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => [
                ]
            ],
            [
                'module_id' => 'site-statistics-module',
                'title' => 'Statistics',
                'description' => 'Site statistics',
                'route' => 'sites.statistics.index',
                'section_routes' => [],
                'display' => true,
                'authorized_roles' => [1, 2, 3, 4],
                'sections' => []
            ]
        ];
        $this->current_page = $this->getCurrentPage($this->pages);
        $this->current_page_section = $this->getCurrentSection($this->current_page);
        $this->setPageResume($this->current_route);
    }

    public function render()
    {
        // Log the current component for debugging
        \Illuminate\Support\Facades\Log::info('Current component: ' . $this->component);

        return view('livewire.sites.site-page', [
            'site' => $this->site,
        ]);
    }
}
