<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use App\Traits\HandlePageExpiration;
use Livewire\Component;
use Livewire\WithPagination;

class SiteIndex extends Component
{
    use WithPagination, HandlePageExpiration;

    public $search = '';
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $perPage = 10;
    public $selectedSites = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    public function mount()
    {
        $this->selectedSites = [];
        $this->selectAll = false;
    }

    public function updating($name)
    {
        if (in_array($name, ['search', 'perPage', 'sortField', 'sortDirection'])) {
            $this->resetPage();
            $this->selectedSites = [];
            $this->selectAll = false;
        }
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedSites = $this->getSitesQuery()
                ->pluck('id')
                ->map(fn($id) => (string) $id)
                ->toArray();
        } else {
            $this->selectedSites = [];
        }
    }

    public function toggleSiteSelection($siteId)
    {
        $siteId = (string) $siteId;
        if (in_array($siteId, $this->selectedSites)) {
            $this->selectedSites = array_diff($this->selectedSites, [$siteId]);
        } else {
            $this->selectedSites[] = $siteId;
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function deleteSelected()
    {
        if (count($this->selectedSites) > 0) {
            Site::whereIn('id', $this->selectedSites)->delete();
            $this->selectedSites = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected sites have been deleted.');
        }
    }

    protected function getSitesQuery()
    {
        return Site::query()
            ->with('callCenter')
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('location', 'like', '%' . $this->search . '%')
                    ->orWhereHas('callCenter', function ($query) {
                        $query->where('name', 'like', '%' . $this->search . '%');
                    });
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function render()
    {
        return view('livewire.sites.site-index', [
            'sites' => $this->getSitesQuery()->paginate($this->perPage)
        ]);
    }
}
