<?php

namespace App\Livewire\Sites;

use App\Livewire\Forms\SiteForm;
use App\Models\CallCenter;
use App\Models\User;
use Livewire\Component;

class SiteCreate extends Component
{
    public SiteForm $form;

    public $managers = [];
    public $itManagers = [];
    public $trainers = [];
    public $accountants = [];
    public $hrManagers = [];
    public $callCenters = [];

    public function mount()
    {
        // Initialize the form
        $this->form->status = 'active';

        // Get users with appropriate roles using Spatie Permission
        $this->managers = User::role('platform_manager')->get(); // Site Manager role
        $this->itManagers = User::role('it_manager')->get(); // IT Manager role
        $this->trainers = User::role('trainer')->get(); // Trainer role
        $this->accountants = User::role('accountant')->get(); // Accountant role
        $this->hrManagers = User::role('hr_manager')->get(); // HR Manager role

        // Get call centers
        $this->callCenters = CallCenter::where('status', '=', 'active')->get();
    }

    public function submit()
    {
        try {
            // Store the site using the form's store method
            $site = $this->form->store();

            session()->flash('message', 'Site created successfully.');

            return $this->redirect(route('sites.index'), navigate: true);
        } catch (\Exception $e) {
            session()->flash('error', 'Error creating site: ' . $e->getMessage());
        }
    }

    public function updated($propertyName)
    {
        // Validate the field that was just updated
        $this->validateOnly($propertyName);
    }

    public function render()
    {
        return view('livewire.sites.site-create', [
            'callCenters' => $this->callCenters
        ]);
    }
}
