<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use App\Models\Equipment;
use Livewire\Component;
use Livewire\WithPagination;

class SiteEquipments extends Component
{
    use WithPagination;

    public $site;
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $selectedEquipments = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    public function mount(Site $site = null)
    {
        $this->site = $site;
        $this->selectedEquipments = [];
        $this->selectAll = false;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedEquipments = $this->getEquipmentsQuery()
                ->pluck('id')
                ->map(fn($id) => (string) $id)
                ->toArray();
        } else {
            $this->selectedEquipments = [];
        }
    }

    public function toggleEquipmentSelection($equipmentId)
    {
        $equipmentId = (string) $equipmentId;
        if (in_array($equipmentId, $this->selectedEquipments)) {
            $this->selectedEquipments = array_diff($this->selectedEquipments, [$equipmentId]);
        } else {
            $this->selectedEquipments[] = $equipmentId;
        }
    }

    public function deleteSelected()
    {
        if (count($this->selectedEquipments) > 0) {
            Equipment::whereIn('id', $this->selectedEquipments)->delete();
            $this->selectedEquipments = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected equipment have been deleted.');
        }
    }

    protected function getEquipmentsQuery()
    {
        $query = Equipment::query();
        
        if ($this->site) {
            $query->where('site_id', $this->site->id);
        }
        
        return $query->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('serial_number', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function render()
    {
        return view('livewire.sites.site-equipments', [
            'equipments' => $this->getEquipmentsQuery()->paginate($this->perPage)
        ]);
    }
}
