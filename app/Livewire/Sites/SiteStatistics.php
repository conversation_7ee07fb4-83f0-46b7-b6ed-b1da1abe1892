<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use App\Models\Platform;
use App\Models\Equipment;
use Livewire\Component;
use App\Traits\HandlePageExpiration;

class SiteStatistics extends Component
{
    use HandlePageExpiration;

    public $site_id;
    public $dateRange = 'month';
    public $startDate;
    public $endDate;

    public function mount($site = null)
    {
        if ($site) {
            $this->site_id = $site->id;
        }

        // Set default date range (last 30 days)
        $this->startDate = now()->subDays(30)->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function updatedDateRange()
    {
        switch ($this->dateRange) {
            case 'week':
                $this->startDate = now()->subDays(7)->format('Y-m-d');
                break;
            case 'month':
                $this->startDate = now()->subDays(30)->format('Y-m-d');
                break;
            case 'quarter':
                $this->startDate = now()->subMonths(3)->format('Y-m-d');
                break;
            case 'year':
                $this->startDate = now()->subYear()->format('Y-m-d');
                break;
        }
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        $site = Site::find($this->site_id);

        // Get statistics data
        $platformCount = Platform::when($this->site_id, function ($query) {
            return $query->where('site_id', $this->site_id);
        })->count();

        // Check if the Equipment table exists before querying
        try {
            $equipmentCount = Equipment::when($this->site_id, function ($query) {
                return $query->whereHas('platform', function ($q) {
                    $q->where('site_id', $this->site_id);
                });
            })->count();

            // Get equipment status distribution
            $equipmentStatus = Equipment::when($this->site_id, function ($query) {
                return $query->whereHas('platform', function ($q) {
                    $q->where('site_id', $this->site_id);
                });
            })
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status')
            ->toArray();
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), set default values
            $equipmentCount = 0;
            $equipmentStatus = [];
        }

        $personnelCount = $site && method_exists($site, 'users') ? $site->users->count() : 0;

        // Get platform types distribution
        $platformTypes = Platform::when($this->site_id, function ($query) {
            return $query->where('site_id', $this->site_id);
        })
        ->selectRaw('type, COUNT(*) as count')
        ->groupBy('type')
        ->get()
        ->pluck('count', 'type')
        ->toArray();

        return view('livewire.sites.site-statistics', [
            'site' => $site,
            'platformCount' => $platformCount,
            'equipmentCount' => $equipmentCount,
            'personnelCount' => $personnelCount,
            'platformTypes' => $platformTypes,
            'equipmentStatus' => $equipmentStatus,
        ]);
    }
}
