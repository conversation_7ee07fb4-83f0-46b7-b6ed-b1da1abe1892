<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use App\Models\Report;
use Livewire\Component;
use Livewire\WithPagination;
use App\Traits\HandlePageExpiration;

class SiteReports extends Component
{
    use WithPagination;
    use HandlePageExpiration;

    public $site;
    public $search = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedReports = [];
    public $selectAll = false;
    public $dateRange = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'dateRange' => ['except' => ''],
    ];

    public function mount(Site $site = null)
    {
        $this->site = $site;
        $this->selectedReports = [];
        $this->selectAll = false;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedReports = $this->getReportsQuery()
                ->pluck('id')
                ->map(fn($id) => (string) $id)
                ->toArray();
        } else {
            $this->selectedReports = [];
        }
    }

    public function toggleReportSelection($reportId)
    {
        $reportId = (string) $reportId;
        if (in_array($reportId, $this->selectedReports)) {
            $this->selectedReports = array_diff($this->selectedReports, [$reportId]);
        } else {
            $this->selectedReports[] = $reportId;
        }
    }

    public function deleteSelected()
    {
        if (count($this->selectedReports) > 0) {
            Report::whereIn('id', $this->selectedReports)->delete();
            $this->selectedReports = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected reports have been deleted.');
        }
    }

    protected function getReportsQuery()
    {
        $query = Report::query();

        if ($this->site) {
            $query->where(function($q) {
                $q->where('reportable_type', Site::class)
                  ->where('reportable_id', $this->site->id);
            });
        }

        if ($this->dateRange) {
            $dates = explode(' to ', $this->dateRange);
            if (count($dates) == 2) {
                $query->whereDate('created_at', '>=', $dates[0])
                    ->whereDate('created_at', '<=', $dates[1]);
            }
        }

        return $query->when($this->search, function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%')
                    ->orWhere('report_number', 'like', '%' . $this->search . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function render()
    {
        return view('livewire.sites.site-reports', [
            'reports' => $this->getReportsQuery()->paginate($this->perPage)
        ]);
    }
}
