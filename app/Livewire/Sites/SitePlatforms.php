<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use App\Models\Platform;
use Livewire\Component;
use Livewire\WithPagination;

class SitePlatforms extends Component
{
    use WithPagination;

    public ?Platform $paltform = null;
    public $site;
    public $search = '';
    public $perPage = 10;
    public $sortField = 'name';
    public $sortDirection = 'asc';
    public $selectedPlatforms = [];
    public $selectAll = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'perPage' => ['except' => 10],
        'sortField' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    public function mount(Platform $paltform = null)
    {
        $this->paltform = $paltform;
        $this->selectedPlatforms = [];
        $this->selectAll = false;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function updatedSelectAll($value)
    {
        if ($value) {
            $this->selectedPlatforms = $this->getPlatformsQuery()
                ->pluck('id')
                ->map(fn($id) => (string) $id)
                ->toArray();
        } else {
            $this->selectedPlatforms = [];
        }
    }

    public function togglePlatformSelection($platformId)
    {
        $platformId = (string) $platformId;
        if (in_array($platformId, $this->selectedPlatforms)) {
            $this->selectedPlatforms = array_diff($this->selectedPlatforms, [$platformId]);
        } else {
            $this->selectedPlatforms[] = $platformId;
        }
    }

    public function deleteSelected()
    {
        if (count($this->selectedPlatforms) > 0) {
            Platform::whereIn('id', $this->selectedPlatforms)->delete();
            $this->selectedPlatforms = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected platforms have been deleted.');
        }
    }

    protected function getPlatformsQuery()
    {
        $query = Platform::query();
        
        if ($this->site) {
            $query->where('site_id', $this->site->id);
        }
        
        return $query->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->orderBy($this->sortField, $this->sortDirection);
    }

    public function render()
    {
        return view('livewire.sites.site-platforms', [
            'platforms' => $this->getPlatformsQuery()->paginate($this->perPage)
        ]);
    }
}
