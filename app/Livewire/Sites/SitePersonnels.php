<?php

namespace App\Livewire\Sites;

use App\Models\Site;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use App\Traits\HandlePageExpiration;

class SitePersonnels extends Component
{
    use WithPagination;
    use HandlePageExpiration;

    public $site_id;
    public $search = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedRole;

    public function mount($site = null)
    {
        if ($site) {
            $this->site_id = $site->id;
        }
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }

    public function render()
    {
        $query = User::query()
            ->whereHas('sites', function ($query) {
                $query->where('site_id', $this->site_id);
            })
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('first_name', 'like', '%' . $this->search . '%')
                        ->orWhere('last_name', 'like', '%' . $this->search . '%')
                        ->orWhere('email', 'like', '%' . $this->search . '%')
                        ->orWhere('registration_number', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->selectedRole, function ($query) {
                $query->where('role_id', $this->selectedRole);
            })
            ->orderBy($this->sortField, $this->sortDirection);

        $personnel = $query->paginate($this->perPage);
        $site = Site::find($this->site_id);
        $roles = \App\Models\Role::all();

        return view('livewire.sites.site-personnels', [
            'personnel' => $personnel,
            'site' => $site,
            'roles' => $roles,
        ]);
    }
}
