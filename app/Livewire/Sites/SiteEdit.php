<?php

namespace App\Livewire\Sites;

use App\Livewire\Forms\SiteForm;
use App\Models\CallCenter;
use App\Models\Site;
use App\Models\User;
use Livewire\Component;

class SiteEdit extends Component
{
    public SiteForm $form;
    public Site $site;

    public $managers = [];
    public $itManagers = [];
    public $trainers = [];
    public $accountants = [];
    public $hrManagers = [];
    public $callCenters = [];

    public function mount(Site $site)
    {
        $this->site = $site;

        // Set the form data from the site
        $this->form->setSite($site);

        // Get users with appropriate roles
        $this->managers = User::where('role_id', 3)->get(); // Site Manager role
        $this->itManagers = User::where('role_id', 8)->get(); // IT Manager role
        $this->trainers = User::where('role_id', 10)->get(); // Trainer role
        $this->accountants = User::where('role_id', 11)->get(); // Accountant role
        $this->hrManagers = User::where('role_id', 12)->get(); // HR Manager role

        // Get call centers
        $this->callCenters = CallCenter::all();
    }

    public function submit()
    {
        try {
            // Update the site using the form's update method
            $this->form->update($this->site);

            session()->flash('message', 'Site updated successfully!');

            return $this->redirect(route('sites.show', $this->site), navigate: true);
        } catch (\Exception $e) {
            session()->flash('error', 'Error updating site: ' . $e->getMessage());
        }
    }

    public function updated($propertyName)
    {
        // Validate the field that was just updated
        $this->validateOnly($propertyName);
    }

    public function render()
    {
        return view('livewire.sites.site-edit', [
            'callCenters' => $this->callCenters
        ]);
    }
}
