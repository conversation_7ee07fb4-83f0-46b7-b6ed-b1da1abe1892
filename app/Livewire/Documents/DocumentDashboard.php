<?php

namespace App\Livewire\Documents;

use App\Models\Media;
use App\Models\User;
use App\Services\DocumentService;
use Livewire\Component;
use Livewire\WithPagination;
use Carbon\Carbon;

class DocumentDashboard extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'created_at';
    public $sortDirection = 'desc';
    public $selectedCategory = '';
    public $selectedUser = '';
    public $selectedStatus = '';
    public $selectedDepartment = '';
    public $dateRange = '';

    // Document verification
    public $showVerificationModal = false;
    public $documentId;
    public $verificationStatus = 'verified';
    public $rejectionReason = '';

    protected $documentService;

    public function boot(DocumentService $documentService)
    {
        $this->documentService = $documentService;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedCategory()
    {
        $this->resetPage();
    }

    public function updatingSelectedUser()
    {
        $this->resetPage();
    }

    public function updatingSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatingSelectedDepartment()
    {
        $this->resetPage();
    }

    public function updatingDateRange()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }

    public function openVerificationModal($documentId)
    {
        $this->documentId = $documentId;
        $this->verificationStatus = 'verified';
        $this->rejectionReason = '';
        $this->showVerificationModal = true;
    }

    public function closeVerificationModal()
    {
        $this->showVerificationModal = false;
    }

    public function verifyDocument()
    {
        $this->validate([
            'verificationStatus' => 'required|in:verified,rejected',
            'rejectionReason' => 'required_if:verificationStatus,rejected',
        ]);

        $document = Media::findOrFail($this->documentId);

        $this->documentService->verifyDocument(
            $document,
            $this->verificationStatus,
            $this->rejectionReason
        );

        $this->closeVerificationModal();
        session()->flash('message', 'Document verification status updated successfully.');
    }

    public function getDocumentStats()
    {
        return [
            'total' => Media::whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])->count(),
            'pending' => Media::where('verification_status', 'pending')->count(),
            'verified' => Media::where('verification_status', 'verified')->count(),
            'rejected' => Media::where('verification_status', 'rejected')->count(),
            'expired' => Media::whereNotNull('expiry_date')->where('expiry_date', '<', Carbon::today())->count(),
            'expiring_soon' => Media::whereNotNull('expiry_date')
                ->where('expiry_date', '>=', Carbon::today())
                ->where('expiry_date', '<=', Carbon::today()->addDays(30))
                ->count(),
        ];
    }

    public function render()
    {
        $documentsQuery = Media::query()
            ->whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])
            ->when($this->search, function ($query) {
                $query->where(function($q) {
                    $q->where('file_name', 'like', '%' . $this->search . '%')
                      ->orWhere('document_title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->selectedCategory, function ($query) {
                $query->where('category', $this->selectedCategory);
            })
            ->when($this->selectedUser, function ($query) {
                $query->where('mediable_id', $this->selectedUser)
                    ->where('mediable_type', 'App\\Models\\User');
            })
            ->when($this->selectedStatus, function ($query) {
                if ($this->selectedStatus === 'expired') {
                    $query->whereNotNull('expiry_date')->where('expiry_date', '<', Carbon::today());
                } elseif ($this->selectedStatus === 'expiring_soon') {
                    $query->whereNotNull('expiry_date')
                        ->where('expiry_date', '>=', Carbon::today())
                        ->where('expiry_date', '<=', Carbon::today()->addDays(30));
                } else {
                    $query->where('verification_status', $this->selectedStatus);
                }
            })
            ->when($this->selectedDepartment, function ($query) {
                $query->where('department', $this->selectedDepartment);
            })
            ->when($this->dateRange, function ($query) {
                $dates = explode(' to ', $this->dateRange);
                if (count($dates) === 2) {
                    $startDate = Carbon::parse($dates[0])->startOfDay();
                    $endDate = Carbon::parse($dates[1])->endOfDay();
                    $query->whereBetween('created_at', [$startDate, $endDate]);
                }
            });

        $documents = $documentsQuery->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);

        // Get users for the filter
        $users = User::orderBy('first_name')->get();

        // Get categories for the filter
        $categories = Media::select('category')
            ->whereIn('category', ['resume', 'id_card', 'certificate', 'other_document'])
            ->distinct()
            ->pluck('category');

        // Get departments for the filter
        $departments = Media::select('department')
            ->whereNotNull('department')
            ->where('department', '!=', '')
            ->distinct()
            ->pluck('department')
            ->filter(); // Remove any null or empty values that might have slipped through

        // Get document stats
        $stats = $this->getDocumentStats();

        return view('livewire.documents.document-dashboard', [
            'documents' => $documents,
            'users' => $users,
            'categories' => $categories,
            'departments' => $departments,
            'stats' => $stats,
        ]);
    }
}
