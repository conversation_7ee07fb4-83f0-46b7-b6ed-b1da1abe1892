<?php

namespace App\Livewire\Documents;

use App\Models\Media;
use App\Models\User;
use App\Services\DocumentService;
use Livewire\Component;
use Livewire\WithPagination;

class DocumentExpiration extends Component
{
    use WithPagination;

    public $search = '';
    public $perPage = 10;
    public $sortField = 'expiry_date';
    public $sortDirection = 'asc';
    public $selectedCategory = '';
    public $selectedUser = '';
    public $expirationFilter = 'all'; // all, expired, about-to-expire
    public $expirationDays = 30;
    
    // Document update properties
    public $showUpdateModal = false;
    public $documentId;
    public $documentTitle;
    public $documentDescription;
    public $documentExpiryDate;
    
    protected $documentService;
    
    public function boot(DocumentService $documentService)
    {
        $this->documentService = $documentService;
    }
    
    public function updatingSearch()
    {
        $this->resetPage();
    }
    
    public function updatingSelectedCategory()
    {
        $this->resetPage();
    }
    
    public function updatingSelectedUser()
    {
        $this->resetPage();
    }
    
    public function updatingExpirationFilter()
    {
        $this->resetPage();
    }
    
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }
        
        $this->sortField = $field;
    }
    
    public function openUpdateModal($documentId)
    {
        $document = Media::findOrFail($documentId);
        
        $this->documentId = $document->id;
        $this->documentTitle = $document->document_title;
        $this->documentDescription = $document->description;
        $this->documentExpiryDate = $document->expiry_date ? $document->expiry_date->format('Y-m-d') : null;
        
        $this->showUpdateModal = true;
    }
    
    public function closeUpdateModal()
    {
        $this->showUpdateModal = false;
    }
    
    public function updateDocument()
    {
        $this->validate([
            'documentTitle' => 'nullable|string|max:255',
            'documentDescription' => 'nullable|string|max:1000',
            'documentExpiryDate' => 'nullable|date',
        ]);
        
        $document = Media::findOrFail($this->documentId);
        
        $this->documentService->updateDocumentMetadata($document, [
            'document_title' => $this->documentTitle,
            'description' => $this->documentDescription,
            'expiry_date' => $this->documentExpiryDate,
        ]);
        
        $this->closeUpdateModal();
        session()->flash('message', 'Document updated successfully.');
    }
    
    public function render()
    {
        $documentsQuery = Media::query()
            ->whereNotNull('expiry_date')
            ->when($this->search, function ($query) {
                $query->where(function($q) {
                    $q->where('file_name', 'like', '%' . $this->search . '%')
                      ->orWhere('document_title', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->selectedCategory, function ($query) {
                $query->where('category', $this->selectedCategory);
            })
            ->when($this->selectedUser, function ($query) {
                $query->where('mediable_id', $this->selectedUser)
                    ->where('mediable_type', 'App\\Models\\User');
            });
            
        // Apply expiration filter
        if ($this->expirationFilter === 'expired') {
            $documentsQuery->expired();
        } elseif ($this->expirationFilter === 'about-to-expire') {
            $documentsQuery->aboutToExpire($this->expirationDays);
        }
        
        $documents = $documentsQuery->orderBy($this->sortField, $this->sortDirection)
            ->paginate($this->perPage);
        
        $users = User::orderBy('first_name')->get();
        $categories = Media::select('category')->distinct()->pluck('category');
        
        return view('livewire.documents.document-expiration', [
            'documents' => $documents,
            'users' => $users,
            'categories' => $categories,
        ]);
    }
}
