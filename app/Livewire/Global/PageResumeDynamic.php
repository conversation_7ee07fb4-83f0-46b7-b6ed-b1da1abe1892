<?php

namespace App\Livewire\Global;

use Livewire\Component;

class PageResumeDynamic extends Component
{
    /**
     * The type of content to display in the resume
     * Options: 'entity', 'form', 'dashboard', 'default'
     */
    public string $contentType = 'default';
    
    /**
     * Data for the entity being displayed
     */
    public ?array $entityData = null;
    
    /**
     * Default title if not provided in entityData
     */
    public ?string $title = null;
    
    /**
     * Default description if not provided in entityData
     */
    public ?string $description = null;

    /**
     * Mount the component with the provided data
     */
    public function mount($contentType = 'default', $entityData = null, $title = null, $description = null)
    {
        $this->contentType = $contentType;
        $this->entityData = $entityData;
        $this->title = $title;
        $this->description = $description;
    }

    /**
     * Render the component
     */
    public function render()
    {
        return view('livewire.global.page-resume-dynamic');
    }
}
