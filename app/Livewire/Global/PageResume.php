<?php

namespace App\Livewire\Global;

use Livewire\Component;

class PageResume extends Component
{
    public $type;
    public $currentPageResume;

    /**
     * Determine if the page resume should be expanded by default
     */
    public function isExpandedByDefault()
    {
        return true;
    }

    /**
     * Get the content type based on the current page resume data
     */
    public function getContentType()
    {
        if (!isset($this->currentPageResume['type'])) {
            return 'default';
        }

        $typeMap = [
            'infos' => 'entity',
            'chart' => 'dashboard',
            'recent' => 'list',
            'form' => 'form'
        ];

        return $typeMap[$this->currentPageResume['type']] ?? 'default';
    }

    /**
     * Get the entity data in the format expected by the template
     */
    public function getEntityData()
    {
        $data = [
            'title' => $this->currentPageResume['title'] ?? 'Information',
            'description' => $this->currentPageResume['description'] ?? null,
        ];

        // Add stats if available
        if (isset($this->currentPageResume['stats'])) {
            $data['stats'] = $this->currentPageResume['stats'];
        }

        // Add metadata if available
        if (isset($this->currentPageResume['metadata'])) {
            $data['metadata'] = $this->currentPageResume['metadata'];
        }

        // Add metrics for dashboard type
        if (isset($this->currentPageResume['metrics'])) {
            $data['metrics'] = $this->currentPageResume['metrics'];
        }

        // Add form-specific data
        if (isset($this->currentPageResume['steps'])) {
            $data['steps'] = $this->currentPageResume['steps'];
        }

        if (isset($this->currentPageResume['notes'])) {
            $data['notes'] = $this->currentPageResume['notes'];
        }

        // Add value if available (for backward compatibility)
        if (isset($this->currentPageResume['value'])) {
            $data['value'] = $this->currentPageResume['value'];
        }

        return $data;
    }

    public function render()
    {
        return view('livewire.global.page-resume', [
            'contentType' => $this->getContentType(),
            'entityData' => $this->getEntityData(),
            'isExpandedByDefault' => $this->isExpandedByDefault(),
        ]);
    }
}
