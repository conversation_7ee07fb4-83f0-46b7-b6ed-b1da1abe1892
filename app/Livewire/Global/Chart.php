<?php

namespace App\Livewire\Global;

use Livewire\Component;

class Chart extends Component
{
    public $chartId;
    public $title;
    public $subtitle;
    public $type;
    public $data;
    public $period;

    public function mount($chartId = 'chart', $title = null, $subtitle = null, $type = 'line', $data = [], $period = null)
    {
        $this->chartId = $chartId;
        $this->title = $title;
        $this->subtitle = $subtitle;
        $this->type = $type;
        $this->data = $data;
        $this->period = $period;
    }

    public function render()
    {
        return view('livewire.global.chart');
    }
}
