<?php

namespace App\Livewire\Global;

use Livewire\Component;
use App\Livewire\Global\Sidebar;

class PageSidebar extends Component
{
    public $pages = [];
    public $currentRoute;
    public $site = null;

    public function mount($pages, $currentRoute, $site = null)
    {
        $this->pages = $pages;
        $this->currentRoute = $currentRoute;
        $this->site = $site;
    }

    public function render()
    {
        return view('livewire.global.page-sidebar');
    }
}
