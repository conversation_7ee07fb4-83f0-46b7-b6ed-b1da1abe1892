<?php

namespace App\Livewire\Global;

use Livewire\Component;
use Symfony\Component\VarDumper\Cloner\Stub;

class PageHeader extends Component
{
    public array $currentModule = [];
    public array $pages = [];
    public array $currentPage = [];
    public array $currentPageSection = [];
    public string $currentRoute = '';


    /**
     * Generate a URL for a page route, handling routes that require parameters
     *
     * @param string $route The route name
     * @return string The generated URL or # if the route cannot be generated
     */
    public function generatePageUrl($route)
    {
        if (empty($route)) {
            return '#';
        }

        try {
            // Handle call-centers routes
            if (strpos($route, 'call-centers.') === 0) {
                $parts = explode('.', $route);

                // Get the callCenter parameter from the route
                $callCenter = request()->route('callCenter');

                // Main call center routes
                if (count($parts) === 2) {
                    if ($parts[1] === 'index' || $parts[1] === 'create') {
                        return route($route);
                    } else if ($callCenter) {
                        return route($route, ['callCenter' => $callCenter->id]);
                    }
                }
                // Submodule routes (sites, departments)
                else if (count($parts) === 3 && $callCenter) {
                    if ($parts[2] === 'index' || $parts[2] === 'create') {
                        return route($route, ['callCenter' => $callCenter->id]);
                    } else {
                        $submoduleParam = request()->route($parts[1] === 'sites' ? 'site' : 'department');
                        if ($submoduleParam) {
                            return route($route, [
                                'callCenter' => $callCenter->id,
                                $parts[1] === 'sites' ? 'site' : 'department' => $submoduleParam->id
                            ]);
                        }
                    }
                }
            }

            // Default case: try to generate the route without parameters
            return route($route);
        } catch (\Exception $e) {
            // Log the error for debugging
            \Illuminate\Support\Facades\Log::warning('Error generating URL for route: ' . $route, [
                'error' => $e->getMessage(),
                'route' => $route
            ]);

            return '#';
        }
    }

    public function render()
    {
        return view('livewire.global.page-header');
    }
}
