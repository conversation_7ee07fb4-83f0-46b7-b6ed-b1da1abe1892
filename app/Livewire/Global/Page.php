<?php

namespace App\Livewire\Global;

use App\Traits\HandlePageExpiration;
use Illuminate\Support\Facades\Route;
use Livewire\Component;

class Page extends Component
{
    use HandlePageExpiration;
    public array $modules = [];
    public array $current_module = [];
    public $current_route = '';
    public $component = '';

    // Dynamic page resume properties
    public string $resumeContentType = 'default';
    public ?array $resumeData = null;
    public ?string $resumeTitle = null;
    public ?string $resumeDescription = null;

    private function getCurrentModule(array $modules): array
    {
        if (empty($this->current_route)) {
            return [];
        }

        foreach ($modules as $module) {
            if (in_array($this->current_route, $module['routes'])) {
                return $module;
            }
        }
        return [];
    }

    public function getCurrentPage(array $pages): array
    {
        if (empty($this->current_route)) {
            return [];
        }

        foreach ($pages as $page) {
            if (strpos($this->current_route, $page['route']) === 0) {
                return $page;
            } else {
                $sectionPage = $this->getSectionPage($page);
                if (!empty($sectionPage)) {
                    return $sectionPage;
                }
            }
        }
        return [];
    }

    public function getCurrentSection($page)
    {
        if (empty($this->current_route)) {
            return [];
        }

        foreach ($page['sections'] as $section) {
            if ($this->current_route === $section['route']) {
                return $section;
            }
        }
        return [];
    }

    private function getSectionPage($page)
    {
        foreach ($page['section_routes'] as $route) {
            if (strpos($this->current_route, $route) === 0) {
                return $page;
            }
        }
        return [];
    }

    /**
     * Enhance page resume data with additional context-specific information
     * This method can be overridden in child classes to provide more specific data
     */
    protected function enhancePageResumeData(array $resumeData): array
    {
        // Default implementation just returns the original data
        // Child classes should override this to provide enhanced data
        return $resumeData;
    }

    /**
     * Set the page resume data based on the current route and context
     * This method should be implemented in child classes
     */
    public function setPageResume($routeName)
    {
        // Default implementation - should be overridden in child classes
        $this->resumeTitle = 'Information';
        $this->resumeDescription = 'No specific information available for this page.';
        $this->resumeContentType = 'default';
        $this->resumeData = [
            'title' => 'Information',
            'description' => 'No specific information available for this page.'
        ];

        // For backward compatibility
        return [
            'title' => 'Information',
            'type' => 'infos',
            'description' => 'No specific information available for this page.'
        ];
    }

    public function mount($component = '')
    {
        // Ensure component is a string
        $this->component = is_string($component) ? $component : '';

        $this->modules = config('modules');

        // Ensure current_route is a string
        $routeName = Route::currentRouteName();
        $this->current_route = is_string($routeName) ? $routeName : '';

        $this->current_module = $this->getCurrentModule($this->modules);

        // Set the page resume data
        $this->setPageResume($this->current_route);

        // Log for debugging
        \Illuminate\Support\Facades\Log::info('Page mounted', [
            'component' => $this->component,
            'component_type' => gettype($component),
            'component_class' => is_object($component) ? get_class($component) : 'not an object',
            'current_route' => $this->current_route,
            'current_route_type' => gettype($this->current_route),
            'modules_count' => count($this->modules),
            'current_module' => $this->current_module ? array_keys($this->current_module) : 'No current module'
        ]);
    }

    /**
     * Generate a URL for a page route, handling routes that require parameters
     *
     * @param string $route The route name
     * @return string The generated URL or # if the route cannot be generated
     */
    public function generatePageUrl($route)
    {
        if (empty($route)) {
            return '#';
        }

        try {
            // Handle call-centers routes
            if (strpos($route, 'call-centers.') === 0) {
                $parts = explode('.', $route);

                // Get the callCenter parameter from the route
                $callCenter = request()->route('callCenter');

                // Main call center routes
                if (count($parts) === 2) {
                    if ($parts[1] === 'index' || $parts[1] === 'create') {
                        return route($route);
                    } else if ($callCenter) {
                        return route($route, ['callCenter' => $callCenter->id]);
                    }
                }
                // Submodule routes (sites, departments)
                else if (count($parts) === 3 && $callCenter) {
                    if ($parts[2] === 'index' || $parts[2] === 'create') {
                        return route($route, ['callCenter' => $callCenter->id]);
                    } else {
                        $submoduleParam = request()->route($parts[1] === 'sites' ? 'site' : 'department');
                        if ($submoduleParam) {
                            return route($route, [
                                'callCenter' => $callCenter->id,
                                $parts[1] === 'sites' ? 'site' : 'department' => $submoduleParam->id
                            ]);
                        }
                    }
                }
            }

            // Default case: try to generate the route without parameters
            return route($route);
        } catch (\Exception $e) {
            // Log the error for debugging
            \Illuminate\Support\Facades\Log::warning('Error generating URL for route: ' . $route, [
                'error' => $e->getMessage(),
                'route' => $route
            ]);

            return '#';
        }
    }

    public function render()
    {
        return view('livewire.global.page');
    }
}
