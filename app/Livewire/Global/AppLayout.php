<?php

namespace App\Livewire\Global;

use Livewire\Component;

class AppLayout extends Component
{
    public $pageComponent;
    public $model; // Generic model instance
    public $modelType; // e.g., 'users', 'posts'
    public $action;

    public function mount($pageComponent, $model, $modelType, $action)
    {
        $this->pageComponent = $pageComponent;
        $this->model = $model;
        $this->modelType = $modelType;
        $this->action = $action;
    }
    public function render()
    {
        return view('livewire.global.app-layout');
    }
}
