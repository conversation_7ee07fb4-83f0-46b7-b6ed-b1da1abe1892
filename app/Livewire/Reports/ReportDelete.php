<?php

namespace App\Livewire\Reports;

use App\Models\Report;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\On;
use Livewire\Component;

class ReportDelete extends Component
{
    public Report $report;
    public $created_by;
    #[On('destroy-report')]
    public function destroyReport()
    {
        $this->report->delete();

        session()->flash('message', 'Selected reports deleted successfully!');

        return $this->redirect(route('reports.index'), navigate: true);
    }

    public function mount(Report $report)
    {
        $this->created_by = $report->created_by;
    }
    public function render()
    {
        return view('livewire.reports.report-delete');
    }
}
