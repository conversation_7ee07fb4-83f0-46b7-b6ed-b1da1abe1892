<?php

namespace App\Livewire\Reports;

use App\Models\Report;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithPagination;

class ReportIndex extends Component
{
    use WithPagination;

    public string $search = '';
    public string $managerSearch = '';
    public ?int $selectedManagerId = null;
    public ?int $selectedCampaignId = null;
    public string $campaignSearch = '';
    public string $statusFilter = '';
    public string $typeFilter = '';
    public string $priorityFilter = '';
    public string $dateFrom = '';
    public string $dateTo = '';

    public string $sortField = 'date';
    public string $sortDirection = 'desc';
    public int $perPage = 5;

    public array $selectedReports = [];
    public bool $selectAll = false;

    protected $queryString = [
        'search' => ['except' => '', 'as' => 'q'],
        'managerSearch' => ['except' => '', 'as' => 'manager'],
        'selectedManagerId' => ['except' => null, 'as' => 'mid'],
        'selectedCampaignId' => ['except' => null, 'as' => 'cid'],
        'campaignSearch' => ['except' => '', 'as' => 'campaign'],
        'statusFilter' => ['except' => '', 'as' => 'status'],
        'typeFilter' => ['except' => '', 'as' => 'type'],
        'priorityFilter' => ['except' => '', 'as' => 'priority'],
        'dateFrom' => ['except' => '', 'as' => 'from'],
        'dateTo' => ['except' => '', 'as' => 'to'],
        'sortField' => ['except' => 'date', 'as' => 'sort'],
        'sortDirection' => ['except' => 'desc', 'as' => 'dir'],
        'perPage' => ['except' => 5, 'as' => 'pp'],
    ];

    public function updating($field): void
    {
        if (in_array($field, [
            'search',
            'managerSearch',
            'selectedManagerId',
            'campaignSearch',
            'selectedCampaignId',
            'statusFilter',
            'typeFilter',
            'priorityFilter',
            'dateFrom',
            'dateTo',
            'perPage',
            'sortField',
            'sortDirection'
        ])) {
            $this->resetPage();
            $this->resetSelection();
        }
    }

    public function mount(): void
    {
        $this->resetSelection();
    }

    public function resetSelection(): void
    {
        $this->selectedReports = [];
        $this->selectAll = false;
    }

    public function sortBy(string $field)
    {
        $this->sortDirection = $this->sortField === $field && $this->sortDirection === 'asc' ? 'desc' : 'asc';
        $this->sortField = $field;
    }

    public function updatedSelectAll(bool $value)
    {
        $this->selectedReports = $value
            ? $this->reports->pluck('id')->map('intval')->all()
            : [];
    }

    public function toggleReportSelection(int $reportId)
    {
        if (in_array($reportId, $this->selectedReports)) {
            $this->selectedReports = array_diff($this->selectedReports, [$reportId]);
        } else {
            $this->selectedReports[] = $reportId;
        }

        $currentPageIds = $this->reports->pluck('id')->all();
        $this->selectAll = !empty($this->selectedReports) && empty(array_diff($currentPageIds, $this->selectedReports));
    }

    // Method moved to the bottom of the class

    protected function getReportsQuery()
    {
        $query = Report::with(['campaign', 'creator'])
            ->orderBy($this->sortField, $this->sortDirection);

        // Filter by creator/manager
        if ($this->selectedManagerId) {
            $query->where('created_by', '=', $this->selectedManagerId);
        } else {
            if (auth()->user()->id === 6) {
                $query->where('created_by', '=', Auth::id());
            }
        }

        // Filter by campaign
        if ($this->selectedCampaignId) {
            $query->where('campaign_id', '=', $this->selectedCampaignId);
        }

        // Filter by status
        if ($this->statusFilter) {
            $query->where('status', '=', $this->statusFilter);
        }

        // Filter by type
        if ($this->typeFilter) {
            $query->where('type', '=', $this->typeFilter);
        }

        // Filter by priority
        if ($this->priorityFilter) {
            $query->where('priority', '=', $this->priorityFilter);
        }

        // Filter by date range
        if ($this->dateFrom) {
            $query->whereDate('date', '>=', $this->dateFrom);
        }

        if ($this->dateTo) {
            $query->whereDate('date', '<=', $this->dateTo);
        }

        // Search in title, content, and description
        if (trim($this->search)) {
            $term = '%' . trim($this->search) . '%';
            $query->where(function ($q) use ($term) {
                $q->where('title', 'like', $term)
                  ->orWhere('content', 'like', $term)
                  ->orWhere('description', 'like', $term);
            });
        }

        return $query;
    }

    public function getReportsProperty()
    {
        return $this->getReportsQuery()->paginate($this->perPage);
    }


    public function getFilteredManagersProperty()
    {
        $term = trim($this->managerSearch);

        return User::query()
            ->when($term, fn($q) => $q->where('first_name', 'like', '%' . $term . '%')->orWhere('last_name', 'like', '%' . $term . '%'))
            ->orderBy('first_name')
            ->limit(20)
            ->get();
    }

    public function selectManager(int $id)
    {
        $this->selectedManagerId = $id;
        $user = User::find($id);
        $this->managerSearch = $user?->getFullNameAttribute() ?? '';
    }

    public function updatedManagerSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedManagerId = null;
        }
    }

    public function getFilteredCampaignsProperty()
    {
        $term = trim($this->campaignSearch);

        return \App\Models\Campaign::query()
            ->when($term, fn($q) => $q->where('name', 'like', '%' . $term . '%'))
            ->orderBy('name')
            ->limit(20)
            ->get();
    }

    public function selectCampaign(int $id)
    {
        $this->selectedCampaignId = $id;
        $campaign = \App\Models\Campaign::find($id);
        $this->campaignSearch = $campaign?->name ?? '';
    }

    public function updatedCampaignSearch(string $value)
    {
        if (trim($value) === '') {
            $this->selectedCampaignId = null;
        }
    }

    public function getStatusOptionsProperty()
    {
        return [
            'pending' => 'Pending',
            'submitted' => 'Submitted',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'in_review' => 'In Review'
        ];
    }

    public function getPriorityOptionsProperty()
    {
        return [
            'low' => 'Low',
            'normal' => 'Normal',
            'high' => 'High',
            'urgent' => 'Urgent'
        ];
    }

    public function getTypeOptionsProperty()
    {
        return \App\Models\Report::select('type')
            ->distinct()
            ->whereNotNull('type')
            ->pluck('type')
            ->toArray();
    }

    public function exportReports()
    {
        // Implement export functionality
        session()->flash('message', 'Reports exported successfully!');
    }

    public function deleteSelected()
    {
        if (!empty($this->selectedReports)) {
            Report::whereIn('id', $this->selectedReports)->delete();
            $this->selectedReports = [];
            $this->selectAll = false;
            session()->flash('message', 'Selected reports deleted successfully!');
            $this->resetPage();
        }
    }

    public function render()
    {
        return view('livewire.reports.report-index', [
            'reports' => $this->reports,
            'filteredManagers' => $this->filteredManagers,
            'filteredCampaigns' => $this->filteredCampaigns,
            'statusOptions' => $this->statusOptions,
            'priorityOptions' => $this->priorityOptions,
            'typeOptions' => $this->typeOptions,
        ]);
    }
}

