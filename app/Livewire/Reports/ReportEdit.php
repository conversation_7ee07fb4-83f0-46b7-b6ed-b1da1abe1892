<?php

namespace App\Livewire\Reports;

use App\Livewire\Forms\ReportForm;
use App\Models\Campaign;
use App\Models\Report;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class ReportEdit extends Component
{
    use WithFileUploads;
    public Report $report;
    public ReportForm $form;

    public function mount(Report $report)
    {
        $this->report = $report;
        $this->form->setReport($report);
    }

    #[On('update-report')]
    public function updateReport()
    {
        $this->form->update($this->report);
        session()->flash('message', 'Report updated successfully!');
        $this->redirect(route('reports.show', ['report' => $this->report->id]), navigate: true);
    }

    public function render()
    {
        return view('livewire.reports.report-edit', [
            'campaigns' => Campaign::all(),
        ]);
    }
}
