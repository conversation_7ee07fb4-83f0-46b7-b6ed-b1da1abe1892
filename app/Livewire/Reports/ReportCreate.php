<?php

namespace App\Livewire\Reports;

use App\Livewire\Forms\ReportForm;
use App\Models\Campaign;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;

class ReportCreate extends Component
{
    use WithFileUploads;

    public ReportForm $form;

    #[On('create-report')]
    public function storeReport()
    {
        $this->form->store();
        return $this->redirect(route('reports.index'), navigate: true);
    }

    public function render()
    {
        return view('livewire.reports.report-create', [
            'campaigns' => Campaign::all(),
        ]);
    }
}
