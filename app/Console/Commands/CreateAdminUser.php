<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create an admin user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating admin user...');

        // Check if admin user already exists
        $adminUser = User::where('email', '<EMAIL>')->first();
        
        if ($adminUser) {
            $this->info('Admin user already exists. Updating password...');
            $adminUser->update([
                'password' => Hash::make('admin123'),
            ]);
            $this->info('Admin user password updated!');
        } else {
            // Create a new admin user with minimal required fields
            $adminUser = new User();
            $adminUser->first_name = 'Admin';
            $adminUser->last_name = 'User';
            $adminUser->email = '<EMAIL>';
            $adminUser->password = Hash::make('admin123');
            $adminUser->role_id = 1; // Admin role
            $adminUser->registration_number = 'EMP-ADMIN';
            $adminUser->hire_date = now();
            $adminUser->status = 'actif';
            $adminUser->email_verified_at = now();
            
            try {
                $adminUser->save();
                $this->info('Admin user created successfully!');
            } catch (\Exception $e) {
                $this->error('Failed to create admin user: ' . $e->getMessage());
                return 1;
            }
        }
        
        $this->info('Admin credentials:');
        $this->info('Email: <EMAIL>');
        $this->info('Password: admin123');
        
        return 0;
    }
}
