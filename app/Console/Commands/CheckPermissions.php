<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionCacheService;

class CheckPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:check {user? : The ID of the user to check}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check permissions in the database and for a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking permissions...');

        // List all permissions
        $permissions = Permission::all();
        $this->info('Total permissions in database: ' . $permissions->count());
        
        $this->table(
            ['ID', 'Name'],
            $permissions->map(function ($permission) {
                return [
                    'id' => $permission->id,
                    'name' => $permission->name,
                ];
            })
        );

        // Check if a user ID was provided
        $userId = $this->argument('user');
        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("User with ID {$userId} not found.");
                return Command::FAILURE;
            }

            $this->info("Checking permissions for user: {$user->first_name} {$user->last_name} (ID: {$user->id})");
            $this->info("Role: " . ($user->role ? $user->role->name : 'No role assigned'));

            // Get role permissions
            $rolePermissions = $user->role ? $user->role->permissions->pluck('name')->toArray() : [];
            $this->info("Role permissions: " . implode(', ', $rolePermissions));

            // Get direct permissions
            $directPermissions = $user->directPermissions->pluck('name')->toArray();
            $this->info("Direct permissions: " . implode(', ', $directPermissions));

            // Get cached permissions
            $cachedPermissions = PermissionCacheService::getUserPermissions($user);
            $this->info("Cached permissions: " . implode(', ', $cachedPermissions));

            // Check specific permissions
            $this->info("Has 'view_users' permission: " . ($user->hasPermission('view_users') ? 'Yes' : 'No'));
            $this->info("Has 'create_users' permission: " . ($user->hasPermission('create_users') ? 'Yes' : 'No'));
            $this->info("Has 'manage_users' permission: " . ($user->hasPermission('manage_users') ? 'Yes' : 'No'));
        }

        // Clear permission cache
        if ($this->confirm('Do you want to clear the permission cache?')) {
            PermissionCacheService::clearAllUserPermissionsCache();
            $this->info('Permission cache cleared for all users.');
        }

        return Command::SUCCESS;
    }
}
