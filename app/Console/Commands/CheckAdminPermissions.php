<?php

namespace App\Console\Commands;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class CheckAdminPermissions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'admin:check-permissions {--fix : Automatically fix missing permissions}';

    /**
     * The console command description.
     */
    protected $description = 'Check if admin user has all required permissions and optionally fix them';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Checking admin permissions...');

        // Find admin user
        $admin = User::where('email', '<EMAIL>')->first();
        
        if (!$admin) {
            $this->error('❌ Admin user not found!');
            
            if ($this->option('fix')) {
                $this->info('🔧 Running migration and seeding to create admin user...');
                return $this->runMigrationAndSeeding();
            }
            
            return 1;
        }

        $this->info("✅ Admin user found: {$admin->email}");

        // Check if admin has admin role
        if (!$admin->hasRole(Role::ADMIN)) {
            $this->error('❌ Admin user does not have admin role!');
            
            if ($this->option('fix')) {
                $this->fixAdminRole($admin);
            }
        } else {
            $this->info('✅ Admin user has admin role');
        }

        // Get all required permissions from modules config
        $modules = config('modules', []);
        $requiredPermissions = $this->extractAllPermissions($modules);
        
        $this->info("📋 Found " . count($requiredPermissions) . " required permissions");

        // Check admin permissions
        $missingPermissions = [];
        $hasPermissions = 0;

        foreach ($requiredPermissions as $permission) {
            if ($admin->hasPermissionTo($permission)) {
                $hasPermissions++;
            } else {
                $missingPermissions[] = $permission;
            }
        }

        // Display results
        $this->displayResults($hasPermissions, count($requiredPermissions), $missingPermissions);

        // Fix if requested
        if (!empty($missingPermissions) && $this->option('fix')) {
            return $this->fixMissingPermissions($admin, $missingPermissions);
        }

        // Check module access
        $this->checkModuleAccess($admin, $modules);

        return empty($missingPermissions) ? 0 : 1;
    }

    /**
     * Extract all permissions from modules config
     */
    private function extractAllPermissions(array $modules): array
    {
        $permissions = [];
        
        foreach ($modules as $module) {
            if (isset($module['permissions']) && is_array($module['permissions'])) {
                foreach ($module['permissions'] as $permission) {
                    if (!in_array($permission, $permissions)) {
                        $permissions[] = $permission;
                    }
                }
            }
        }
        
        return $permissions;
    }

    /**
     * Display permission check results
     */
    private function displayResults(int $hasPermissions, int $totalPermissions, array $missingPermissions): void
    {
        $percentage = $totalPermissions > 0 ? round(($hasPermissions / $totalPermissions) * 100, 1) : 0;
        
        $this->info("📊 Permission Status: {$hasPermissions}/{$totalPermissions} ({$percentage}%)");
        
        if (empty($missingPermissions)) {
            $this->info('✅ Admin has all required permissions!');
        } else {
            $this->error("❌ Admin is missing " . count($missingPermissions) . " permissions:");
            
            if (count($missingPermissions) <= 10) {
                foreach ($missingPermissions as $permission) {
                    $this->line("   - {$permission}");
                }
            } else {
                // Show first 10 and count
                for ($i = 0; $i < 10; $i++) {
                    $this->line("   - {$missingPermissions[$i]}");
                }
                $remaining = count($missingPermissions) - 10;
                $this->line("   ... and {$remaining} more");
            }
        }
    }

    /**
     * Fix admin role assignment
     */
    private function fixAdminRole(User $admin): void
    {
        $this->info('🔧 Fixing admin role assignment...');
        
        $adminRole = Role::where('name', Role::ADMIN)->first();
        
        if (!$adminRole) {
            $this->error('❌ Admin role not found in database!');
            return;
        }
        
        $admin->assignRole($adminRole);
        $this->info('✅ Admin role assigned successfully');
    }

    /**
     * Fix missing permissions
     */
    private function fixMissingPermissions(User $admin, array $missingPermissions): int
    {
        $this->info('🔧 Fixing missing permissions...');
        
        $fixed = 0;
        $failed = 0;
        
        foreach ($missingPermissions as $permissionName) {
            try {
                // Create permission if it doesn't exist
                $permission = Permission::firstOrCreate(
                    ['name' => $permissionName],
                    ['guard_name' => 'web']
                );
                
                // Give permission to admin
                if (!$admin->hasPermissionTo($permission)) {
                    $admin->givePermissionTo($permission);
                    $fixed++;
                    $this->line("  ✓ Fixed: {$permissionName}");
                }
            } catch (\Exception $e) {
                $failed++;
                $this->error("  ❌ Failed: {$permissionName} - " . $e->getMessage());
            }
        }
        
        $this->info("✅ Fixed {$fixed} permissions, {$failed} failed");
        
        return $failed > 0 ? 1 : 0;
    }

    /**
     * Check module access
     */
    private function checkModuleAccess(User $admin, array $modules): void
    {
        $this->info('🏢 Checking module access...');
        
        $accessibleModules = 0;
        $totalModules = count($modules);
        
        foreach ($modules as $module) {
            $moduleAccessPermission = 'access_' . $module['id'];
            
            if ($admin->hasPermissionTo($moduleAccessPermission)) {
                $accessibleModules++;
            } else {
                $this->warn("  ⚠️  Cannot access: {$module['title']}");
            }
        }
        
        if ($accessibleModules === $totalModules) {
            $this->info("✅ Admin can access all {$totalModules} modules!");
        } else {
            $this->warn("⚠️  Admin can access {$accessibleModules}/{$totalModules} modules");
        }
    }

    /**
     * Run migration and seeding process
     */
    private function runMigrationAndSeeding(): int
    {
        $this->info('🔄 Running fresh migration and seeding...');
        
        try {
            // Fresh migration
            $this->call('migrate:fresh', ['--force' => true]);
            
            // Run seeders
            $this->call('db:seed', ['--force' => true]);
            
            $this->info('✅ Migration and seeding completed successfully!');
            
            // Re-check admin permissions
            return $this->handle();
            
        } catch (\Exception $e) {
            $this->error('❌ Migration/seeding failed: ' . $e->getMessage());
            return 1;
        }
    }
}
