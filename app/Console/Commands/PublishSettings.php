<?php

namespace App\Console\Commands;

use App\Models\Setting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class PublishSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settings:publish {--group= : Specific settings group to publish}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish settings to the cache for faster access';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $group = $this->option('group');
        
        if ($group) {
            $this->publishGroup($group);
        } else {
            $this->publishAllGroups();
        }
        
        $this->info('Settings published successfully!');
        
        return Command::SUCCESS;
    }
    
    /**
     * Publish a specific settings group
     */
    private function publishGroup(string $group): void
    {
        $settings = Setting::where('group', $group)->get();
        
        if ($settings->isEmpty()) {
            $this->warn("No settings found for group: {$group}");
            return;
        }
        
        $this->info("Publishing settings for group: {$group}");
        
        $groupSettings = [];
        foreach ($settings as $setting) {
            // Cast the value based on the type
            switch ($setting->type) {
                case 'boolean':
                    $groupSettings[$setting->key] = (bool) $setting->value;
                    break;
                case 'integer':
                    $groupSettings[$setting->key] = (int) $setting->value;
                    break;
                case 'float':
                    $groupSettings[$setting->key] = (float) $setting->value;
                    break;
                case 'json':
                    $groupSettings[$setting->key] = json_decode($setting->value, true);
                    break;
                default:
                    $groupSettings[$setting->key] = $setting->value;
            }
        }
        
        // Store in cache
        Cache::put("settings.{$group}", $groupSettings, now()->addDay());
        
        $this->info("Published " . count($groupSettings) . " settings for group: {$group}");
    }
    
    /**
     * Publish all settings groups
     */
    private function publishAllGroups(): void
    {
        $groups = Setting::select('group')->distinct()->pluck('group');
        
        $this->info("Publishing settings for " . count($groups) . " groups");
        
        foreach ($groups as $group) {
            $this->publishGroup($group);
        }
    }
}
