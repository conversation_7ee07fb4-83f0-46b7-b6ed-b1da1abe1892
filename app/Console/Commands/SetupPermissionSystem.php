<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use App\Services\PermissionCacheService;

class SetupPermissionSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:setup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up the permission system by running migrations and seeders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up the permission system...');

        // Run the migration for user_permissions table
        $this->info('Running migrations...');
        Artisan::call('migrate', ['--path' => 'database/migrations/2024_07_01_000001_create_user_permissions_table.php']);
        $this->info(Artisan::output());

        // Run the permission seeder
        $this->info('Running permission seeder...');
        Artisan::call('db:seed', ['--class' => 'PermissionSeeder']);
        $this->info(Artisan::output());

        // Run the role-permission seeder
        $this->info('Running role-permission seeder...');
        Artisan::call('db:seed', ['--class' => 'RolePermissionSeeder']);
        $this->info(Artisan::output());

        // Clear permission cache
        $this->info('Clearing permission cache...');
        PermissionCacheService::clearAllUserPermissionsCache();

        // Clear route cache
        $this->info('Clearing route cache...');
        Artisan::call('route:clear');
        $this->info(Artisan::output());

        // Clear view cache
        $this->info('Clearing view cache...');
        Artisan::call('view:clear');
        $this->info(Artisan::output());

        $this->info('Permission system setup complete!');
        $this->info('You can now use the new permission system in your application.');

        return Command::SUCCESS;
    }
}
