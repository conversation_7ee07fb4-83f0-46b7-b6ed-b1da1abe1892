<?php

namespace App\Traits;

use App\Helpers\RoleHelper;

trait HasPermissionChecks
{
    /**
     * Filter menu items based on user's roles and permissions
     * This method works with the modules config structure
     */
    public function filterMenuItems(array $modules): array
    {
        $user = auth()->user();
        
        if (!$user) {
            return [];
        }

        return array_filter($modules, function ($module) use ($user) {
            // Check if module should be displayed
            if (!isset($module['display']) || !$module['display']) {
                return false;
            }

            // Check role-based access using authorized_roles
            if (isset($module['authorized_roles']) && !empty($module['authorized_roles'])) {
                if (!RoleHelper::userHasAnyRoleId($user, $module['authorized_roles'])) {
                    return false;
                }
            }

            // Check permission-based access for module access
            $moduleAccessPermission = 'access_' . $module['id'];
            if (!$user->hasPermissionTo($moduleAccessPermission)) {
                return false;
            }

            return true;
        });
    }

    /**
     * Check if user can access a specific module
     */
    public function canAccessModule(array $module): bool
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        // Check role-based access
        if (isset($module['authorized_roles']) && !empty($module['authorized_roles'])) {
            if (!RoleHelper::userHasAnyRoleId($user, $module['authorized_roles'])) {
                return false;
            }
        }

        // Check permission-based access
        $moduleAccessPermission = 'access_' . $module['id'];
        return $user->hasPermissionTo($moduleAccessPermission);
    }

    /**
     * Check if user has any of the specified role IDs (from modules config)
     */
    public function hasAnyRoleId(array $roleIds): bool
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        return RoleHelper::userHasAnyRoleId($user, $roleIds);
    }

    /**
     * Check if user has permission to perform action on module
     */
    public function hasModulePermission(string $moduleId, string $action): bool
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        $permission = $action . '_' . $moduleId;
        return $user->hasPermissionTo($permission);
    }

    /**
     * Get modules that user can access
     */
    public function getAccessibleModules(array $modules): array
    {
        return $this->filterMenuItems($modules);
    }

    /**
     * Check if user can access route based on modules config
     */
    public function canAccessRoute(string $routeName, array $modules): bool
    {
        $user = auth()->user();
        
        if (!$user) {
            return false;
        }

        // Find module that contains this route
        foreach ($modules as $module) {
            if (in_array($routeName, $module['routes'] ?? [])) {
                return $this->canAccessModule($module);
            }
        }

        // If route not found in modules, allow access (fallback)
        return true;
    }
}
