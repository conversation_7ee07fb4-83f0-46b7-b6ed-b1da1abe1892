<?php

namespace App\Traits;

use Illuminate\Support\Facades\Session;
use Livewire\Attributes\On;

trait HandlePageExpiration
{
    /**
     * Boot the trait.
     *
     * @return void
     */
    public function bootHandlePageExpiration()
    {
        // Refresh the session on component mount
        if (Session::isStarted()) {
            Session::regenerate(false);
        }
    }

    /**
     * Handle a page expiration error.
     *
     * @return mixed
     */
    #[On('pageExpired')]
    public function handlePageExpiration()
    {
        // Refresh the CSRF token
        if (Session::isStarted()) {
            Session::regenerateToken();
        }

        // Refresh the current page
        return $this->redirect(request()->fullUrl());
    }

    /**
     * Handle Livewire errors.
     *
     * @param \Throwable $e
     * @return mixed
     */
    public function handleError($e)
    {
        // Check if it's a page expiration error
        if (strpos($e->getMessage(), 'page has expired') !== false ||
            strpos($e->getMessage(), 'CSRF token mismatch') !== false) {
            return $this->handlePageExpiration();
        }

        // Re-throw other errors
        throw $e;
    }
}
