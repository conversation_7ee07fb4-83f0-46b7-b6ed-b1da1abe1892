<?php

use Illuminate\Support\Str;

if (!function_exists('formatDuration')) {
    function formatDuration($minutes)
    {
        $hours = intdiv($minutes, 60);
        $remainingMinutes = $minutes % 60;
        return $hours > 0
            ? "{$hours}h {$remainingMinutes}m"
            : "{$remainingMinutes}m";
    }
}

if (!function_exists('str_limit')) {
    /**
     * Limit the number of characters in a string.
     *
     * @param  string  $value
     * @param  int  $limit
     * @param  string  $end
     * @return string
     */
    function str_limit($value, $limit = 100, $end = '...')
    {
        return Str::limit($value, $limit, $end);
    }
}

