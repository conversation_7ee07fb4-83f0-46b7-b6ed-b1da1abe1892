<?php

namespace App\Helpers;

use App\Models\Role;

class <PERSON><PERSON>elper
{
    /**
     * Role ID to name mapping based on the seeder
     */
    private static array $roleMapping = [
        1 => Role::ADMIN,              // administrator
        2 => Role::DIRECTOR,           // director
        3 => Role::MANAGER,            // platform_manager
        4 => Role::SUPERVISOR,         // supervisor
        5 => Role::QUALITY_CONTROLLER, // quality_control
        6 => Role::AGENT,              // agent
        7 => Role::IT_MANAGER,         // it_manager
        8 => Role::IT_SUPPORT,         // it_support
        9 => Role::TRAINER,            // trainer
        10 => Role::ACCOUNTANT,        // accountant
        11 => Role::HR_MANAGER,        // hr_manager
        12 => Role::API,               // api
        13 => Role::MOBILE,            // mobile
        14 => Role::ADMIN_API,         // admin_api
        15 => Role::REMOTE_AGENT,      // remote_agent
        16 => Role::REMOTE_SUPERVISOR, // remote_supervisor
        17 => Role::REMOTE_MANAGER,    // remote_manager
    ];

    /**
     * Convert role ID to role name
     */
    public static function getRoleName(int $roleId): ?string
    {
        return self::$roleMapping[$roleId] ?? null;
    }

    /**
     * Convert multiple role IDs to role names
     */
    public static function getRoleNames(array $roleIds): array
    {
        return array_filter(array_map(fn($id) => self::getRoleName($id), $roleIds));
    }

    /**
     * Check if user has any of the role IDs from modules config
     */
    public static function userHasAnyRoleId($user, array $roleIds): bool
    {
        if (!$user) {
            return false;
        }

        $roleNames = self::getRoleNames($roleIds);
        return $user->hasAnyRole($roleNames);
    }

    /**
     * Check if user can access module based on authorized_roles
     */
    public static function userCanAccessModule($user, array $module): bool
    {
        if (!$user || !isset($module['authorized_roles'])) {
            return false;
        }

        return self::userHasAnyRoleId($user, $module['authorized_roles']);
    }

    /**
     * Filter modules based on user's roles
     */
    public static function filterModulesForUser($user, array $modules): array
    {
        if (!$user) {
            return [];
        }

        return array_filter($modules, function ($module) use ($user) {
            return self::userCanAccessModule($user, $module);
        });
    }

    /**
     * Get all role names that correspond to the given role IDs
     */
    public static function getAllRoleNames(): array
    {
        return array_values(self::$roleMapping);
    }

    /**
     * Get role ID by role name (reverse lookup)
     */
    public static function getRoleId(string $roleName): ?int
    {
        return array_search($roleName, self::$roleMapping) ?: null;
    }
}
