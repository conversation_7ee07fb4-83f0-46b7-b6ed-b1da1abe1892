<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('performance_reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('reviewer_id')->constrained('users')->onDelete('cascade');
            $table->date('review_date');
            $table->date('review_period_start');
            $table->date('review_period_end');
            $table->float('performance_score', 3, 2)->nullable();
            $table->float('attendance_score', 3, 2)->nullable();
            $table->float('communication_score', 3, 2)->nullable();
            $table->float('teamwork_score', 3, 2)->nullable();
            $table->float('initiative_score', 3, 2)->nullable();
            $table->float('overall_score', 3, 2)->nullable();
            $table->text('strengths')->nullable();
            $table->text('areas_for_improvement')->nullable();
            $table->text('goals')->nullable();
            $table->text('comments')->nullable();
            $table->string('status')->default('draft'); // draft, completed, acknowledged
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('performance_reviews');
    }
};
