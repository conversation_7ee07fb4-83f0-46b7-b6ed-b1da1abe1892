<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reports', function (Blueprint $table) {
            if (!Schema::hasColumn('reports', 'reportable_type')) {
                $table->string('reportable_type')->nullable()->after('campaign_id');
            }
            
            if (!Schema::hasColumn('reports', 'reportable_id')) {
                $table->unsignedBigInteger('reportable_id')->nullable()->after('reportable_type');
            }
            
            if (!Schema::hasColumn('reports', 'title')) {
                $table->string('title')->nullable()->after('reportable_id');
            }
            
            if (!Schema::hasColumn('reports', 'description')) {
                $table->text('description')->nullable()->after('title');
            }
            
            if (!Schema::hasColumn('reports', 'type')) {
                $table->string('type')->nullable()->after('description');
            }
            
            if (!Schema::hasColumn('reports', 'report_number')) {
                $table->string('report_number')->nullable()->after('type');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reports', function (Blueprint $table) {
            $table->dropColumn([
                'reportable_type',
                'reportable_id',
                'title',
                'description',
                'type',
                'report_number',
            ]);
        });
    }
};
