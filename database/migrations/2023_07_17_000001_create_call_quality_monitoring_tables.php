<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create call records table
        Schema::create('calls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Agent who handled the call
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->string('call_id')->nullable(); // External call ID from telephony system
            $table->string('customer_phone')->nullable();
            $table->string('customer_name')->nullable();
            $table->dateTime('call_time');
            $table->integer('duration')->default(0); // Duration in seconds
            $table->string('direction')->default('inbound'); // inbound or outbound
            $table->string('status')->default('completed'); // completed, abandoned, transferred, etc.
            $table->string('recording_url')->nullable();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional call data
            $table->timestamps();

            $table->index(['user_id', 'call_time']);
            $table->index(['campaign_id', 'call_time']);
        });

        // Create call quality evaluations table
        Schema::create('call_evaluations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('call_id')->constrained()->onDelete('cascade');
            $table->foreignId('evaluator_id')->constrained('users')->onDelete('cascade'); // User who performed the evaluation
            $table->date('evaluation_date');

            // Evaluation scores (1-5 scale)
            $table->decimal('greeting_score', 3, 1)->nullable();
            $table->decimal('communication_score', 3, 1)->nullable();
            $table->decimal('knowledge_score', 3, 1)->nullable();
            $table->decimal('problem_solving_score', 3, 1)->nullable();
            $table->decimal('closing_score', 3, 1)->nullable();
            $table->decimal('compliance_score', 3, 1)->nullable();
            $table->decimal('overall_score', 3, 1)->nullable();

            // Feedback and comments
            $table->text('strengths')->nullable();
            $table->text('areas_for_improvement')->nullable();
            $table->text('action_items')->nullable();
            $table->text('notes')->nullable();

            // Status
            $table->string('status')->default('completed'); // draft, completed, reviewed
            $table->timestamps();
        });

        // Create evaluation criteria table
        Schema::create('evaluation_criterias', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('category'); // greeting, communication, knowledge, etc.
            $table->text('description')->nullable();
            $table->integer('weight')->default(1); // Weight for calculating overall score
            $table->boolean('is_active')->default(true);
            $table->boolean('is_required')->default(false);
            $table->foreignId('campaign_id')->nullable()->constrained()->onDelete('cascade'); // NULL means global criteria
            $table->timestamps();
        });

        // Create evaluation scores table (for individual criteria scores)
        Schema::create('evaluation_scores', function (Blueprint $table) {
            $table->id();
            $table->foreignId('call_evaluation_id')->constrained()->onDelete('cascade');
            $table->foreignId('evaluation_criteria_id')->constrained('evaluation_criterias')->onDelete('cascade');
            $table->decimal('score', 3, 1); // 1-5 scale with half points
            $table->text('comment')->nullable();
            $table->timestamps();

            $table->unique(['call_evaluation_id', 'evaluation_criteria_id']);
        });

        // Create performance metrics table
        Schema::create('performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->date('date');

            // Call volume metrics
            $table->integer('calls_handled')->default(0);
            $table->integer('calls_missed')->default(0);
            $table->integer('outbound_calls')->default(0);
            $table->integer('total_talk_time')->default(0); // in seconds
            $table->integer('total_hold_time')->default(0); // in seconds
            $table->integer('total_wrap_up_time')->default(0); // in seconds

            // Efficiency metrics
            $table->decimal('average_handle_time', 8, 2)->default(0); // in seconds
            $table->decimal('average_hold_time', 8, 2)->default(0); // in seconds
            $table->decimal('average_wrap_up_time', 8, 2)->default(0); // in seconds
            $table->decimal('occupancy_rate', 5, 2)->default(0); // percentage

            // Quality metrics
            $table->decimal('quality_score', 5, 2)->default(0); // percentage
            $table->integer('evaluations_count')->default(0);

            // Outcome metrics
            $table->integer('appointments_set')->default(0);
            $table->integer('appointments_kept')->default(0);
            $table->decimal('conversion_rate', 5, 2)->default(0); // percentage

            // Adherence metrics
            $table->integer('scheduled_time')->default(0); // in minutes
            $table->integer('logged_time')->default(0); // in minutes
            $table->decimal('adherence_rate', 5, 2)->default(0); // percentage

            // Custom metrics (can be extended via JSON)
            $table->json('custom_metrics')->nullable();

            $table->timestamps();

            $table->unique(['user_id', 'campaign_id', 'date']);
        });

        // Create KPI targets table
        Schema::create('kpi_targets', function (Blueprint $table) {
            $table->id();
            $table->string('metric_name');
            $table->string('target_type'); // individual, team, campaign
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade'); // NULL for team/campaign targets
            $table->foreignId('campaign_id')->nullable()->constrained()->onDelete('cascade'); // NULL for global targets
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->decimal('target_value', 10, 2);
            $table->decimal('min_value', 10, 2)->nullable(); // Minimum acceptable value
            $table->decimal('max_value', 10, 2)->nullable(); // Maximum value (for capping)
            $table->string('unit')->default('count'); // count, percentage, seconds, etc.
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('kpi_targets');
        Schema::dropIfExists('performance_metrics');
        Schema::dropIfExists('evaluation_scores');
        Schema::dropIfExists('evaluation_criterias');
        Schema::dropIfExists('call_evaluations');
        Schema::dropIfExists('calls');
    }
};
