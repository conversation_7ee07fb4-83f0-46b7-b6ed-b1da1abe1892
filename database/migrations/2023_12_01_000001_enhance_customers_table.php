<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Customer categorization
            $table->string('customer_type')->nullable()->after('industry'); // enterprise, government, non-profit, etc.
            $table->string('size')->nullable()->after('customer_type'); // small, medium, large, enterprise
            $table->string('segment')->nullable()->after('size'); // premium, standard, basic
            $table->decimal('annual_revenue', 15, 2)->nullable()->after('segment');
            $table->integer('employee_count')->nullable()->after('annual_revenue');
            
            // Business details
            $table->string('tax_id')->nullable()->after('employee_count');
            $table->string('registration_number')->nullable()->after('tax_id');
            $table->date('established_date')->nullable()->after('registration_number');
            
            // Relationship management
            $table->date('relationship_since')->nullable()->after('established_date');
            $table->string('account_manager_id')->nullable()->after('relationship_since');
            $table->string('customer_satisfaction')->nullable()->after('account_manager_id'); // rating 1-5
            $table->string('customer_tier')->nullable()->after('customer_satisfaction'); // gold, silver, bronze
            
            // Additional fields
            $table->json('tags')->nullable()->after('customer_tier');
            $table->json('custom_fields')->nullable()->after('tags');
            $table->string('language_preference')->nullable()->after('custom_fields');
            $table->string('timezone')->nullable()->after('language_preference');
            
            // Add foreign key for account manager
            $table->foreign('account_manager_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Drop foreign key first
            $table->dropForeign(['account_manager_id']);
            
            // Drop columns
            $table->dropColumn([
                'customer_type',
                'size',
                'segment',
                'annual_revenue',
                'employee_count',
                'tax_id',
                'registration_number',
                'established_date',
                'relationship_since',
                'account_manager_id',
                'customer_satisfaction',
                'customer_tier',
                'tags',
                'custom_fields',
                'language_preference',
                'timezone'
            ]);
        });
    }
};
