<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appointments', function (Blueprint $table) {
            $table->id();

            // Liens vers les autres entités
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('campaign_id');
            $table->unsignedBigInteger('customer_id');

            // Date et heure du rendez‑vous
            $table->dateTime('scheduled_at');

            // Statut : pending / validated / rejected
            $table->string('status')->default('pending');

            // Notes saisies par l'agent lors du RDV
            $table->text('agent_notes')->nullable();

            // Enregistrement audio (chemin dans le disque configuré)
            $table->string('audio_path')->nullable();

            // Notes du contrôle qualité
            $table->text('cq_notes')->nullable();

            // Qui a validé et quand
            $table->unsignedBigInteger('validated_by')->nullable();
            $table->dateTime('validated_at')->nullable();

            // Qui a rejeté et quand
            $table->unsignedBigInteger('rejected_by')->nullable();
            $table->dateTime('rejected_at')->nullable();

            $table->timestamps();

            // Clés étrangères
            $table->foreign('user_id')
                  ->references('id')->on('users')
                  ->onDelete('cascade');

            $table->foreign('campaign_id')
                  ->references('id')->on('campaigns')
                  ->onDelete('cascade');

            $table->foreign('customer_id')
                  ->references('id')->on('customers')
                  ->onDelete('cascade');

            // Les utilisateurs CQ validant/rejetant
            $table->foreign('validated_by')
                  ->references('id')->on('users')
                  ->onDelete('set null');

            $table->foreign('rejected_by')
                  ->references('id')->on('users')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appointments');
    }
};
