<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create onboarding templates table
        Schema::create('onboarding_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unsignedBigInteger('role_id')->nullable(); // For role-specific templates
            $table->unsignedBigInteger('department_id')->nullable(); // For department-specific templates
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by');
            $table->timestamps();
            
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('set null');
            $table->foreign('department_id')->references('id')->on('departments')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
        
        // Create onboarding template tasks table
        Schema::create('onboarding_template_tasks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('template_id');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('category'); // e.g., 'documentation', 'training', 'equipment', 'system_access'
            $table->integer('due_days')->default(1); // Days from onboarding start to complete
            $table->string('responsible_role')->nullable(); // Role responsible for completing this task
            $table->integer('order')->default(0); // Order in the template
            $table->boolean('is_required')->default(true);
            $table->timestamps();
            
            $table->foreign('template_id')->references('id')->on('onboarding_templates')->onDelete('cascade');
        });
        
        // Create employee onboarding table
        Schema::create('employee_onboardings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // The employee being onboarded
            $table->unsignedBigInteger('template_id')->nullable(); // The template used
            $table->date('start_date');
            $table->date('target_completion_date')->nullable();
            $table->date('actual_completion_date')->nullable();
            $table->string('status')->default('pending'); // pending, in_progress, completed, cancelled
            $table->integer('progress_percentage')->default(0);
            $table->unsignedBigInteger('assigned_to')->nullable(); // HR person responsible
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('template_id')->references('id')->on('onboarding_templates')->onDelete('set null');
            $table->foreign('assigned_to')->references('id')->on('users')->onDelete('set null');
        });
        
        // Create onboarding tasks table
        Schema::create('onboarding_tasks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('onboarding_id');
            $table->unsignedBigInteger('template_task_id')->nullable();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('category');
            $table->date('due_date')->nullable();
            $table->string('status')->default('pending'); // pending, in_progress, completed, skipped
            $table->unsignedBigInteger('assigned_to')->nullable();
            $table->unsignedBigInteger('completed_by')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->text('completion_notes')->nullable();
            $table->boolean('is_required')->default(true);
            $table->integer('order')->default(0);
            $table->timestamps();
            
            $table->foreign('onboarding_id')->references('id')->on('employee_onboardings')->onDelete('cascade');
            $table->foreign('template_task_id')->references('id')->on('onboarding_template_tasks')->onDelete('set null');
            $table->foreign('assigned_to')->references('id')->on('users')->onDelete('set null');
            $table->foreign('completed_by')->references('id')->on('users')->onDelete('set null');
        });
        
        // Create onboarding documents table
        Schema::create('onboarding_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('onboarding_id');
            $table->unsignedBigInteger('task_id')->nullable();
            $table->string('name');
            $table->string('file_path');
            $table->string('file_type');
            $table->unsignedBigInteger('uploaded_by');
            $table->timestamps();
            
            $table->foreign('onboarding_id')->references('id')->on('employee_onboardings')->onDelete('cascade');
            $table->foreign('task_id')->references('id')->on('onboarding_tasks')->onDelete('set null');
            $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('onboarding_documents');
        Schema::dropIfExists('onboarding_tasks');
        Schema::dropIfExists('employee_onboardings');
        Schema::dropIfExists('onboarding_template_tasks');
        Schema::dropIfExists('onboarding_templates');
    }
};
