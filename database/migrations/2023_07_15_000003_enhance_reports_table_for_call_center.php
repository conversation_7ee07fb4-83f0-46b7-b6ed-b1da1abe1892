<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reports', function (Blueprint $table) {
            // Report categorization
            $table->string('category')->nullable()->after('type');
            $table->json('tags')->nullable()->after('category');
            $table->string('priority')->default('normal')->after('tags');
            
            // Report metrics
            $table->json('metrics')->nullable()->after('content');
            $table->decimal('performance_score', 5, 2)->nullable()->after('metrics');
            $table->decimal('quality_score', 5, 2)->nullable()->after('performance_score');
            $table->decimal('compliance_score', 5, 2)->nullable()->after('quality_score');
            
            // Report workflow
            $table->unsignedBigInteger('approved_by')->nullable()->after('created_by');
            $table->dateTime('approved_at')->nullable()->after('approved_by');
            $table->string('approval_status')->default('pending')->after('approved_at');
            $table->text('approval_notes')->nullable()->after('approval_status');
            
            // Report visibility and access
            $table->json('visibility')->nullable()->after('approval_notes');
            $table->boolean('is_public')->default(false)->after('visibility');
            $table->boolean('is_template')->default(false)->after('is_public');
            
            // Report scheduling
            $table->string('recurrence')->nullable()->after('is_template');
            $table->dateTime('next_scheduled_at')->nullable()->after('recurrence');
            $table->dateTime('last_generated_at')->nullable()->after('next_scheduled_at');
            
            // Foreign keys
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reports', function (Blueprint $table) {
            // Drop foreign keys first
            $table->dropForeign(['approved_by']);
            
            // Drop columns
            $table->dropColumn([
                'category',
                'tags',
                'priority',
                'metrics',
                'performance_score',
                'quality_score',
                'compliance_score',
                'approved_by',
                'approved_at',
                'approval_status',
                'approval_notes',
                'visibility',
                'is_public',
                'is_template',
                'recurrence',
                'next_scheduled_at',
                'last_generated_at'
            ]);
        });
    }
};
