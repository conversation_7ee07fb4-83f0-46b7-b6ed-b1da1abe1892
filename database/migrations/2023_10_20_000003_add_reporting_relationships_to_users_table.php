<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('manager_id')->nullable()->after('department_id');
            $table->string('job_title')->nullable()->after('manager_id');
            $table->string('employment_type')->nullable()->after('job_title'); // full-time, part-time, contractor, etc.
            $table->integer('hierarchy_level')->nullable()->after('employment_type');
            $table->foreign('manager_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['manager_id']);
            $table->dropColumn(['manager_id', 'job_title', 'employment_type', 'hierarchy_level']);
        });
    }
};
