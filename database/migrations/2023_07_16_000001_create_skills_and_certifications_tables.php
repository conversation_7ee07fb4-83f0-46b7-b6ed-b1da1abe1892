<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create skills table
        Schema::create('skills', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('category')->nullable(); // e.g., 'technical', 'language', 'soft_skill'
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Create certifications table
        Schema::create('certifications', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('issuer')->nullable();
            $table->text('description')->nullable();
            $table->integer('validity_period')->nullable(); // in months
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Create user_skills pivot table
        Schema::create('user_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('skill_id')->constrained()->onDelete('cascade');
            $table->integer('proficiency_level')->default(1); // 1-5 scale
            $table->text('notes')->nullable();
            $table->date('acquired_at')->nullable();
            $table->date('last_verified_at')->nullable();
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            $table->unique(['user_id', 'skill_id']);
        });

        // Create user_certifications pivot table
        Schema::create('user_certifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('certification_id')->constrained()->onDelete('cascade');
            $table->string('certificate_number')->nullable();
            $table->date('issued_at');
            $table->date('expires_at')->nullable();
            $table->string('status')->default('active'); // active, expired, revoked
            $table->text('notes')->nullable();
            $table->string('document_path')->nullable(); // path to certificate document
            $table->foreignId('verified_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            $table->index(['user_id', 'certification_id']);
            $table->index(['expires_at', 'status']);
        });

        // Create campaign_required_skills pivot table
        Schema::create('campaign_required_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->foreignId('skill_id')->constrained()->onDelete('cascade');
            $table->integer('minimum_proficiency_level')->default(1);
            $table->boolean('is_mandatory')->default(true);
            $table->timestamps();
            
            $table->unique(['campaign_id', 'skill_id']);
        });

        // Create campaign_required_certifications pivot table
        Schema::create('campaign_required_certifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campaign_id')->constrained()->onDelete('cascade');
            $table->foreignId('certification_id')->constrained()->onDelete('cascade');
            $table->boolean('is_mandatory')->default(true);
            $table->timestamps();
            
            $table->unique(['campaign_id', 'certification_id']);
        });

        // Add skill-related fields to training_modules table
        Schema::table('training_modules', function (Blueprint $table) {
            $table->json('skills_taught')->nullable(); // IDs of skills taught in this module
            $table->json('certifications_provided')->nullable(); // IDs of certifications this module prepares for
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove skill-related fields from training_modules table
        Schema::table('training_modules', function (Blueprint $table) {
            $table->dropColumn('skills_taught');
            $table->dropColumn('certifications_provided');
        });

        // Drop tables in reverse order to avoid foreign key constraints
        Schema::dropIfExists('campaign_required_certifications');
        Schema::dropIfExists('campaign_required_skills');
        Schema::dropIfExists('user_certifications');
        Schema::dropIfExists('user_skills');
        Schema::dropIfExists('certifications');
        Schema::dropIfExists('skills');
    }
};
