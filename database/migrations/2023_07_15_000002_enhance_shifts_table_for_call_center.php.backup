<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shifts', function (Blueprint $table) {
            // Schedule adherence fields
            $table->dateTime('scheduled_start_time')->nullable()->after('date');
            $table->dateTime('scheduled_end_time')->nullable()->after('scheduled_start_time');
            $table->integer('scheduled_duration_minutes')->nullable()->after('scheduled_end_time');
            $table->decimal('adherence_percentage', 5, 2)->nullable()->after('hours_worked');
            
            // Break management fields
            $table->json('breaks')->nullable()->after('break_duration');
            $table->integer('lunch_duration')->default(0)->after('breaks');
            $table->integer('training_duration')->default(0)->after('lunch_duration');
            $table->integer('personal_duration')->default(0)->after('training_duration');
            
            // Status and shift type fields
            $table->enum('status', ['pending', 'approved', 'rejected', 'exception'])->default('pending')->after('is_validated');
            $table->enum('shift_type', ['regular', 'overtime', 'training', 'meeting'])->default('regular')->after('status');
            
            // Supervisor fields
            $table->unsignedBigInteger('approved_by')->nullable()->after('shift_type');
            $table->text('notes')->nullable()->after('approved_by');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            
            // Rename is_validated to avoid confusion with new status field
            $table->renameColumn('is_validated', 'is_completed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shifts', function (Blueprint $table) {
            // Remove new fields
            $table->dropColumn([
                'scheduled_start_time',
                'scheduled_end_time',
                'scheduled_duration_minutes',
                'adherence_percentage',
                'breaks',
                'lunch_duration',
                'training_duration',
                'personal_duration',
                'status',
                'shift_type',
                'notes'
            ]);
            
            // Drop foreign key first
            $table->dropForeign(['approved_by']);
            $table->dropColumn('approved_by');
            
            // Rename back to original
            $table->renameColumn('is_completed', 'is_validated');
        });
    }
};
