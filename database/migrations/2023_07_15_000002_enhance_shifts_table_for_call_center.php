<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First add the new column
        Schema::table('shifts', function (Blueprint $table) {
            $table->boolean('is_completed')->default(false)->after('status');
        });

        // First create a temporary table
        Schema::create('shifts_temp', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->dateTime('login_time');
            $table->dateTime('logout_time')->nullable();
            $table->integer('hours_worked')->nullable();
            $table->integer('break_duration')->default(0);
            $table->boolean('on_break')->default(false);
            $table->date('date');
            $table->boolean('is_completed')->default(false);
            $table->timestamps();
        });

        // Copy data to temporary table
        DB::statement("INSERT INTO shifts_temp SELECT id, user_id, login_time, logout_time, hours_worked, break_duration, on_break, date, is_validated as is_completed, created_at, updated_at FROM shifts");

        // Drop the original table
        Schema::dropIfExists('shifts');

        // Rename temporary table
        Schema::rename('shifts_temp', 'shifts');

        // Now add the rest of the columns
        Schema::table('shifts', function (Blueprint $table) {
            // Schedule adherence fields
            $table->dateTime('scheduled_start_time')->nullable()->after('date');
            $table->dateTime('scheduled_end_time')->nullable()->after('scheduled_start_time');
            $table->integer('scheduled_duration_minutes')->nullable()->after('scheduled_end_time');
            $table->decimal('adherence_percentage', 5, 2)->nullable()->after('hours_worked');
            
            // Break management fields
            $table->json('breaks')->nullable()->after('break_duration');
            $table->integer('lunch_duration')->default(0)->after('breaks');
            $table->integer('training_duration')->default(0)->after('lunch_duration');
            $table->integer('personal_duration')->default(0)->after('training_duration');
            
            // Status and shift type fields
            $table->enum('status', ['pending', 'approved', 'rejected', 'exception'])->default('pending')->after('is_completed');
            $table->enum('shift_type', ['regular', 'overtime', 'training', 'meeting'])->default('regular')->after('status');
            
            // Supervisor fields
            $table->unsignedBigInteger('approved_by')->nullable()->after('shift_type');
            $table->text('notes')->nullable()->after('approved_by');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First create a temporary table
        Schema::create('shifts_temp', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->dateTime('login_time');
            $table->dateTime('logout_time')->nullable();
            $table->integer('hours_worked')->nullable();
            $table->integer('break_duration')->default(0);
            $table->boolean('on_break')->default(false);
            $table->date('date');
            $table->boolean('is_validated')->default(false);
            $table->timestamps();
        });

        // Copy data to temporary table
        DB::statement("INSERT INTO shifts_temp SELECT id, user_id, login_time, logout_time, hours_worked, break_duration, on_break, date, is_completed as is_validated, created_at, updated_at FROM shifts");

        // Drop the original table
        Schema::dropIfExists('shifts');

        // Rename temporary table
        Schema::rename('shifts_temp', 'shifts');

        // Add back the dropped columns
        Schema::table('shifts', function (Blueprint $table) {
            $table->dateTime('scheduled_start_time')->nullable()->after('date');
            $table->dateTime('scheduled_end_time')->nullable()->after('scheduled_start_time');
            $table->integer('scheduled_duration_minutes')->nullable()->after('scheduled_end_time');
            $table->decimal('adherence_percentage', 5, 2)->nullable()->after('hours_worked');
            
            // Break management fields
            $table->json('breaks')->nullable()->after('break_duration');
            $table->integer('lunch_duration')->default(0)->after('breaks');
            $table->integer('training_duration')->default(0)->after('lunch_duration');
            $table->integer('personal_duration')->default(0)->after('training_duration');
            
            // Status and shift type fields
            $table->enum('status', ['pending', 'approved', 'rejected', 'exception'])->default('pending')->after('is_completed');
            $table->enum('shift_type', ['regular', 'overtime', 'training', 'meeting'])->default('regular')->after('status');
            
            // Supervisor fields
            $table->unsignedBigInteger('approved_by')->nullable()->after('shift_type');
            $table->text('notes')->nullable()->after('approved_by');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
        });
    }
};
