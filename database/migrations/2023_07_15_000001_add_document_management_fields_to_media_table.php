<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('media', function (Blueprint $table) {
            // Document expiration tracking
            $table->date('expiry_date')->nullable()->after('uploaded_by');
            
            // Document verification status
            $table->enum('verification_status', ['pending', 'verified', 'rejected'])->default('pending')->after('expiry_date');
            $table->unsignedBigInteger('verified_by')->nullable()->after('verification_status');
            $table->timestamp('verified_at')->nullable()->after('verified_by');
            
            // Document description and notes
            $table->string('document_title')->nullable()->after('file_name');
            $table->text('description')->nullable()->after('document_title');
            $table->text('rejection_reason')->nullable()->after('description');
            
            // Document tags for better categorization
            $table->json('tags')->nullable()->after('category');
            
            // Document department association
            $table->string('department')->nullable()->after('tags');
            
            // Foreign key for verified_by
            $table->foreign('verified_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('media', function (Blueprint $table) {
            $table->dropForeign(['verified_by']);
            $table->dropColumn([
                'expiry_date',
                'verification_status',
                'verified_by',
                'verified_at',
                'document_title',
                'description',
                'rejection_reason',
                'tags',
                'department'
            ]);
        });
    }
};
