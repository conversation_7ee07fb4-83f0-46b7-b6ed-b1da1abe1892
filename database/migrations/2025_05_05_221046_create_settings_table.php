<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('group')->index(); // e.g., 'general', 'calls', 'campaigns'
            $table->string('key')->index();   // e.g., 'company_name', 'timezone'
            $table->text('value')->nullable();
            $table->text('default_value')->nullable();
            $table->string('type')->default('string'); // string, boolean, integer, json, etc.
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false); // Whether this setting can be exposed to frontend
            $table->timestamps();

            // Ensure each key is unique within its group
            $table->unique(['group', 'key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
