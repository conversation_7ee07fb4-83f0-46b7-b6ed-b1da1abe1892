<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('platforms', function (Blueprint $table) {
            $table->string('type')->default('inbound')->after('site_id');
            $table->integer('capacity')->default(0)->after('status');
            $table->string('ip_address')->nullable()->after('capacity');
            $table->string('server_location')->nullable()->after('ip_address');
            $table->string('software_version')->nullable()->after('server_location');
            $table->timestamp('last_maintenance')->nullable()->after('software_version');
        });
    }

    public function down(): void
    {
        Schema::table('platforms', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('capacity');
            $table->dropColumn('ip_address');
            $table->dropColumn('server_location');
            $table->dropColumn('software_version');
            $table->dropColumn('last_maintenance');
        });
    }
};
