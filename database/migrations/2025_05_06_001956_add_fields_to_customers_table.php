<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('address')->nullable()->after('phone');
            $table->string('city')->nullable()->after('address');
            $table->string('country')->nullable()->after('city');
            $table->string('postal_code')->nullable()->after('country');
            $table->string('website')->nullable()->after('postal_code');
            $table->string('industry')->nullable()->after('website');
            $table->text('notes')->nullable()->after('industry');
            $table->string('contact_person')->nullable()->after('notes');
            $table->string('contact_position')->nullable()->after('contact_person');
            $table->string('contact_phone')->nullable()->after('contact_position');
            $table->string('status')->default('active')->after('contact_phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'address',
                'city',
                'country',
                'postal_code',
                'website',
                'industry',
                'notes',
                'contact_person',
                'contact_position',
                'contact_phone',
                'status'
            ]);
        });
    }
};
