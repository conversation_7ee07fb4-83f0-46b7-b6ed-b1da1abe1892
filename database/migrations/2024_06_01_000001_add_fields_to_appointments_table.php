<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // Add new fields for appointment timing
            $table->dateTime('end_time')->nullable()->after('scheduled_at');
            $table->integer('duration_minutes')->nullable()->after('end_time');
            
            // Add fields for appointment categorization
            $table->string('type')->nullable()->after('duration_minutes');
            $table->string('category')->nullable()->after('type');
            
            // Add fields for appointment outcome
            $table->string('outcome')->nullable()->after('rejected_at');
            $table->text('outcome_notes')->nullable()->after('outcome');
            
            // Add fields for follow-up
            $table->boolean('follow_up_required')->default(false)->after('outcome_notes');
            $table->dateTime('follow_up_date')->nullable()->after('follow_up_required');
            
            // Add fields for recurring appointments
            $table->boolean('is_recurring')->default(false)->after('follow_up_date');
            $table->string('recurrence_pattern')->nullable()->after('is_recurring');
            $table->dateTime('recurrence_end_date')->nullable()->after('recurrence_pattern');
            
            // Add fields for location
            $table->string('location')->nullable()->after('recurrence_end_date');
            $table->string('meeting_link')->nullable()->after('location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropColumn([
                'end_time',
                'duration_minutes',
                'type',
                'category',
                'outcome',
                'outcome_notes',
                'follow_up_required',
                'follow_up_date',
                'is_recurring',
                'recurrence_pattern',
                'recurrence_end_date',
                'location',
                'meeting_link',
            ]);
        });
    }
};
