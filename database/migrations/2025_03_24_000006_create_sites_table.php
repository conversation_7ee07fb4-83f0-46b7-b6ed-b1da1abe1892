<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sites', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('location');
            $table->unsignedBigInteger('call_center_id')->nullable();
            $table->unsignedBigInteger('manager_id')->nullable();
            $table->unsignedBigInteger('it_manager_id')->nullable();
            $table->unsignedBigInteger('trainer_id')->nullable();
            $table->unsignedBigInteger('accountant_id')->nullable();
            $table->unsignedBigInteger('hr_manager_id')->nullable();
            $table->text('description')->nullable();
            $table->string('status')->default('active');
            $table->integer('capacity')->default(0);
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->timestamps();

            $table->foreign('call_center_id')->references('id')->on('call_centers')->onDelete('set null');
            $table->foreign('manager_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('it_manager_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('trainer_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('accountant_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('hr_manager_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sites');
    }
};