<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('performance_metrics', function (Blueprint $table) {
            // Add new efficiency metrics
            $table->float('first_call_resolution')->nullable()->after('occupancy_rate');
            
            // Add new quality metrics
            $table->float('customer_satisfaction')->nullable()->after('quality_score');
            $table->float('nps_score')->nullable()->after('customer_satisfaction');
            
            // Add new outcome metrics
            $table->float('sales_value')->nullable()->after('conversion_rate');
            
            // Add new adherence metrics
            $table->float('punctuality_rate')->nullable()->after('adherence_rate');
            
            // Add new compliance metrics
            $table->float('compliance_score')->nullable()->after('punctuality_rate');
            $table->float('script_adherence')->nullable()->after('compliance_score');
            $table->float('regulatory_compliance')->nullable()->after('script_adherence');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('performance_metrics', function (Blueprint $table) {
            // Remove new efficiency metrics
            $table->dropColumn('first_call_resolution');
            
            // Remove new quality metrics
            $table->dropColumn('customer_satisfaction');
            $table->dropColumn('nps_score');
            
            // Remove new outcome metrics
            $table->dropColumn('sales_value');
            
            // Remove new adherence metrics
            $table->dropColumn('punctuality_rate');
            
            // Remove new compliance metrics
            $table->dropColumn('compliance_score');
            $table->dropColumn('script_adherence');
            $table->dropColumn('regulatory_compliance');
        });
    }
};
