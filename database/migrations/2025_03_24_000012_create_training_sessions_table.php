<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('training_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->date('start_date');
            $table->date('end_date');
            $table->string('status')->default('pending'); // pending, active, completed, cancelled
            $table->integer('capacity')->default(20);
            $table->unsignedBigInteger('instructor_id')->nullable();
            $table->string('location')->nullable();
            $table->timestamps();

            $table->foreign('instructor_id')->references('id')->on('users')->onDelete('set null');
        });

        Schema::create('training_session_user', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('training_session_id');
            $table->unsignedBigInteger('user_id');
            $table->string('status')->default('enrolled'); // enrolled, in_progress, completed, dropped
            $table->date('enrollment_date')->nullable();
            $table->date('completion_date')->nullable();
            $table->string('grade')->nullable();
            $table->timestamps();

            $table->foreign('training_session_id')->references('id')->on('training_sessions')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->unique(['training_session_id', 'user_id']);
        });

        Schema::create('training_session_module', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('training_session_id');
            $table->unsignedBigInteger('training_module_id');
            $table->integer('order')->default(0);
            $table->boolean('required')->default(true);
            $table->timestamps();

            $table->foreign('training_session_id')->references('id')->on('training_sessions')->onDelete('cascade');
            $table->foreign('training_module_id')->references('id')->on('training_modules')->onDelete('cascade');
            
            $table->unique(['training_session_id', 'training_module_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('training_session_module');
        Schema::dropIfExists('training_session_user');
        Schema::dropIfExists('training_sessions');
    }
};
