<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // Add prospect relationship
            $table->foreignId('prospect_id')->nullable()->after('customer_id');
            
            // Add service relationship
            $table->foreignId('service_id')->nullable()->after('meeting_link');
            
            // Add customer notification fields
            $table->boolean('sent_to_customer')->default(false)->after('service_id');
            $table->timestamp('sent_to_customer_at')->nullable()->after('sent_to_customer');
            $table->boolean('customer_confirmed')->default(false)->after('sent_to_customer_at');
            $table->timestamp('customer_confirmed_at')->nullable()->after('customer_confirmed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropColumn([
                'prospect_id',
                'service_id',
                'sent_to_customer',
                'sent_to_customer_at',
                'customer_confirmed',
                'customer_confirmed_at',
            ]);
        });
    }
};
