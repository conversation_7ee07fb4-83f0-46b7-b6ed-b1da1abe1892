<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('activity_type'); // call, email, meeting, etc.
            $table->text('description');
            $table->dateTime('activity_date');
            $table->string('status')->default('completed'); // scheduled, completed, cancelled
            $table->unsignedBigInteger('contact_id')->nullable();
            $table->unsignedBigInteger('performed_by');
            $table->text('outcome')->nullable();
            $table->text('follow_up_notes')->nullable();
            $table->dateTime('follow_up_date')->nullable();
            $table->timestamps();
            
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
            $table->foreign('contact_id')->references('id')->on('customer_contacts')->onDelete('set null');
            $table->foreign('performed_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_activities');
    }
};
