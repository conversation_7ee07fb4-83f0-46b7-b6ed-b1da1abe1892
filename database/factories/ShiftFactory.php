<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shift>
 */
class ShiftFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $login = $this->faker->dateTimeThisMonth();
        $logout = $this->faker->dateTimeBetween($login, $login->modify('+8 hours'));

        return [
            'user_id' => $this->faker->numberBetween(1, 10),
            'login_time' => $login,
            'logout_time' => $logout,
            'hours_worked' => round($this->faker->numberBetween(1, 90)),
            'break_duration' => $this->faker->numberBetween(0, 60),
            'on_break' => $this->faker->boolean,
            'date' => $login->format('Y-m-d'),
            'is_completed' => $this->faker->boolean,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
