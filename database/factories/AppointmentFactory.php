<?php

namespace Database\Factories;

use App\Models\Appointment;
use App\Models\Campaign;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class AppointmentFactory extends Factory
{
    protected $model = Appointment::class;

    public function definition()
    {
        // Statut aléatoire
        $status = $this->faker->randomElement(['pending', 'validated', 'rejected']);

        // Dates de validation / rejet si nécessaire
        $validatedAt = $status === 'validated'
            ? $this->faker->dateTimeBetween('-1 week', 'now')
            : null;

        $rejectedAt = $status === 'rejected'
            ? $this->faker->dateTimeBetween('-1 week', 'now')
            : null;

        // Utilisateur CQ qui valide/rejette
        $validatedBy = $status === 'validated'
            ? User::factory()
                  ->state(fn(array $attributes) => ['role_id' => 5]) // QC role
                  ->create()->id
            : null;

        $rejectedBy = $status === 'rejected'
            ? User::factory()
                  ->state(fn(array $attributes) => ['role_id' => 5])
                  ->create()->id
            : null;

        return [
            'user_id'      => User::factory()->state(fn() => ['role_id' => $this->faker->randomElement([3,6])])->create()->id,
            'campaign_id'  => Campaign::factory(),
            'customer_id'  => Customer::factory(),
            'scheduled_at' => $this->faker->dateTimeBetween('+0 days', '+1 week'),
            'status'       => $status,
            'agent_notes'  => $this->faker->optional()->paragraph(),
            'audio_path'   => $this->faker->optional(0.7)->regexify('appointments/[A-Za-z0-9\-]{36}\.mp3'),
            'cq_notes'     => $this->faker->optional()->sentence(),
            'validated_by' => $validatedBy,
            'validated_at' => $validatedAt,
            'rejected_by'  => $rejectedBy,
            'rejected_at'  => $rejectedAt,
        ];
    }
}
