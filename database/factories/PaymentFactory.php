<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => $this->faker->numberBetween(1, 10),
            'period' => $this->faker->date('Y-m'),
            'hours_worked' => $this->faker->numberBetween(100, 200),
            'amount' => $this->faker->randomFloat(2, 1000, 5000),
            'paid_at' => $this->faker->optional()->dateTimeThisMonth(),
            'status' => $this->faker->randomElement(['on_hold', 'paid']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
