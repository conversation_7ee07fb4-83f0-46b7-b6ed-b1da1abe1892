<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Report>
 */
class ReportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'campaign_id' => $this->faker->numberBetween(1, 5),
            'date' => $this->faker->date('Y-m-d', 'now'),
            'content' => $this->faker->paragraphs(3, true),
            'response' => $this->faker->paragraphs(3, true),
            'created_by' => $this->faker->numberBetween(1, 10),
            'sent_at' => $this->faker->optional()->dateTimeThisMonth(),
            'responded_at' => $this->faker->optional()->dateTimeThisMonth(),
            'file_path' => $this->faker->optional()->filePath(),
            'status' => $this->faker->randomElement(['pending', 'sent', 'received']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
