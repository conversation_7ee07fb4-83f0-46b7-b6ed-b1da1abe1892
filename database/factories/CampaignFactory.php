<?php

namespace Database\Factories;

use App\Models\Platform;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Campaign>
 */
class CampaignFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $platforms = Platform::pluck('id')->toArray();
        $platformId = !empty($platforms) ? $this->faker->randomElement($platforms) : null;

        return [
            'name' => $this->faker->company . ' Campaign',
            'customer_id' => $this->faker->numberBetween(1, 3),
            'platform_id' => $platformId,
            'manager_id' => $this->faker->optional()->numberBetween(1, 5),
            'start_date' => $this->faker->date('Y-m-d', 'now'),
            'end_date' => $this->faker->optional()->date('Y-m-d', '+1 month'),
            'status' => $this->faker->randomElement(['active', 'finished']),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
