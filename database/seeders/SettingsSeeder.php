<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // General Settings
        $this->createSetting('general', 'company_name', 'My Call Center', 'string', 'Company name', true);
        $this->createSetting('general', 'timezone', 'UTC', 'string', 'Timezone', true);
        $this->createSetting('general', 'language', 'en', 'string', 'Default language', true);
        $this->createSetting('general', 'business_hours', '9-5', 'string', 'Business hours', true);
        
        // Call Settings
        $this->createSetting('calls', 'recording', 'all', 'string', 'Call recording setting', true);
        $this->createSetting('calls', 'queue_timeout', '60', 'integer', 'Queue timeout in seconds', true);
        $this->createSetting('calls', 'max_hold_time', '10', 'integer', 'Maximum hold time in minutes', true);
        $this->createSetting('calls', 'voicemail_enabled', '1', 'boolean', 'Whether voicemail is enabled', true);
        
        // Campaign Settings
        $this->createSetting('campaigns', 'default_campaign_type', 'outbound', 'string', 'Default campaign type', true);
        $this->createSetting('campaigns', 'auto_assign_agents', '0', 'boolean', 'Auto-assign agents to campaigns', true);
        $this->createSetting('campaigns', 'max_agents_per_campaign', '20', 'integer', 'Maximum agents per campaign', true);
        $this->createSetting('campaigns', 'enable_campaign_analytics', '1', 'boolean', 'Enable campaign analytics', true);
        
        // Integration Settings
        $this->createSetting('integrations', 'enable_crm_integration', '0', 'boolean', 'Enable CRM integration', true);
        $this->createSetting('integrations', 'crm_api_key', '', 'string', 'CRM API key', false);
        $this->createSetting('integrations', 'crm_endpoint', 'https://api.example.com/crm', 'string', 'CRM API endpoint', true);
        $this->createSetting('integrations', 'enable_sms_integration', '0', 'boolean', 'Enable SMS integration', true);
        $this->createSetting('integrations', 'sms_api_key', '', 'string', 'SMS API key', false);
        $this->createSetting('integrations', 'sms_sender_id', 'CallCenter', 'string', 'SMS sender ID', true);
    }
    
    /**
     * Create a setting if it doesn't exist
     */
    private function createSetting(string $group, string $key, string $value, string $type, string $description, bool $isPublic): void
    {
        Setting::updateOrCreate(
            ['group' => $group, 'key' => $key],
            [
                'value' => $value,
                'default_value' => $value,
                'type' => $type,
                'description' => $description,
                'is_public' => $isPublic,
            ]
        );
    }
}
