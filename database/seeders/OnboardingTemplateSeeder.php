<?php

namespace Database\Seeders;

use App\Models\OnboardingTemplate;
use App\Models\OnboardingTemplateTask;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class OnboardingTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        try {
            // First, temporarily disable foreign key checks
            DB::statement('PRAGMA foreign_keys = OFF');

            // Create templates without foreign keys initially
            $agentTemplate = OnboardingTemplate::create([
                'name' => 'Standard Agent Onboarding',
                'description' => 'Standard onboarding process for new call center agents',
                'is_active' => true,
                'created_by' => 1, // Will update later
                'role_id' => null, // Set to null initially
            ]);

            $supervisorTemplate = OnboardingTemplate::create([
                'name' => 'Supervisor Onboarding',
                'description' => 'Onboarding process for new call center supervisors',
                'is_active' => true,
                'created_by' => 1, // Will update later
                'role_id' => null, // Set to null initially
            ]);

            $remoteAgentTemplate = OnboardingTemplate::create([
                'name' => 'Remote Agent Onboarding',
                'description' => 'Onboarding process for remote call center agents',
                'is_active' => true,
                'created_by' => 1, // Will update later
                'role_id' => null, // Set to null initially
            ]);

            // Wait for roles to be created
            while (Role::count() < 17) {
                \Illuminate\Support\Facades\Log::info('Waiting for roles to be created...');
                sleep(1);
            }

            // Get admin ID from config
            $adminId = config('miamboo.admin_user_id');
            if (!$adminId) {
                \Illuminate\Support\Facades\Log::error('Admin user ID not found in config');
                throw new \Exception('Admin user ID not found in config');
            }

            // Get admin user for created_by
            $admin = \App\Models\User::find($adminId);
            if (!$admin) {
                \Illuminate\Support\Facades\Log::error('Admin user not found with ID: ' . $adminId);
                throw new \Exception('Admin user not found with ID: ' . $adminId);
            }
            
            // Debug admin user
            \Illuminate\Support\Facades\Log::info('Admin user info: ' . json_encode($admin->toArray()));
            
            // Get roles dynamically by name
            $roles = Role::all();
            \Illuminate\Support\Facades\Log::info('All roles: ' . json_encode($roles->pluck('name', 'id')->toArray()));
            
            $agentRole = $roles->firstWhere('name', Role::AGENT);
            $supervisorRole = $roles->firstWhere('name', Role::SUPERVISOR);
            $remoteAgentRole = $roles->firstWhere('name', Role::REMOTE_AGENT);
            
            if (!$agentRole || !$supervisorRole || !$remoteAgentRole) {
                \Illuminate\Support\Facades\Log::error('Could not find required roles in database');
                throw new \Exception('Required roles not found in database');
            }
            
            // Debug role IDs
            \Illuminate\Support\Facades\Log::info('Agent role ID: ' . $agentRole->id);
            \Illuminate\Support\Facades\Log::info('Supervisor role ID: ' . $supervisorRole->id);
            \Illuminate\Support\Facades\Log::info('Remote Agent role ID: ' . $remoteAgentRole->id);
            \Illuminate\Support\Facades\Log::info('Admin user ID: ' . $adminId);

            // Update templates with role IDs and admin ID
            $agentTemplate->update([
                'role_id' => $agentRole->id,
                'created_by' => $adminId,
            ]);

            $supervisorTemplate->update([
                'role_id' => $supervisorRole->id,
                'created_by' => $adminId,
            ]);

            $remoteAgentTemplate->update([
                'role_id' => $remoteAgentRole->id,
                'created_by' => $adminId,
            ]);

            // Re-enable foreign key checks
            DB::statement('PRAGMA foreign_keys = ON');
            // Remove incorrect statement
            // DB::statement('SET FOREIGN_KEY_CHECKS = 1;');

            // Debug template creation
            \Illuminate\Support\Facades\Log::info('Created templates: ' . json_encode([
                'agent' => $agentTemplate->id,
                'supervisor' => $supervisorTemplate->id,
                'remote_agent' => $remoteAgentTemplate->id
            ]));

            // Create tasks for each template
            $this->createAgentOnboardingTasks($agentTemplate->id);
            $this->createSupervisorOnboardingTasks($supervisorTemplate->id);
            $this->createRemoteAgentOnboardingTasks($remoteAgentTemplate->id);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Template creation error: ' . $e->getMessage());
            throw $e;
        } finally {
            // Ensure foreign key checks are always re-enabled
            DB::statement('PRAGMA foreign_keys = ON');
        }
    }
    
    /**
     * Create tasks for agent onboarding template.
     */
    private function createAgentOnboardingTasks($templateId): void
    {
        $tasks = [
            // HR Administration tasks
            [
                'name' => 'Complete employment paperwork',
                'description' => 'Complete all required employment forms including tax forms, direct deposit, and emergency contact information',
                'category' => 'hr_admin',
                'due_days' => 1,
                'responsible_role' => 'hr_manager',
                'order' => 1,
                'is_required' => true,
            ],
            [
                'name' => 'Review and sign employee handbook',
                'description' => 'Review the employee handbook and sign acknowledgment form',
                'category' => 'hr_admin',
                'due_days' => 1,
                'responsible_role' => 'hr_manager',
                'order' => 2,
                'is_required' => true,
            ],
            [
                'name' => 'Collect ID and work eligibility documents',
                'description' => 'Collect copies of ID, work permits, and other eligibility documents',
                'category' => 'documentation',
                'due_days' => 1,
                'responsible_role' => 'hr_manager',
                'order' => 3,
                'is_required' => true,
            ],
            
            // IT Setup tasks
            [
                'name' => 'Create system accounts',
                'description' => 'Create accounts for all required systems including email, CRM, and call center software',
                'category' => 'it_setup',
                'due_days' => 2,
                'responsible_role' => 'it_manager',
                'order' => 4,
                'is_required' => true,
            ],
            [
                'name' => 'Set up workstation',
                'description' => 'Prepare workstation with computer, headset, and necessary peripherals',
                'category' => 'equipment',
                'due_days' => 2,
                'responsible_role' => 'it_manager',
                'order' => 5,
                'is_required' => true,
            ],
            [
                'name' => 'Configure phone system',
                'description' => 'Set up phone system with appropriate extensions and call routing',
                'category' => 'it_setup',
                'due_days' => 2,
                'responsible_role' => 'it_manager',
                'order' => 6,
                'is_required' => true,
            ],
            
            // Orientation tasks
            [
                'name' => 'Company orientation',
                'description' => 'Attend company orientation session covering mission, values, and policies',
                'category' => 'orientation',
                'due_days' => 3,
                'responsible_role' => 'hr_manager',
                'order' => 7,
                'is_required' => true,
            ],
            [
                'name' => 'Facility tour',
                'description' => 'Tour of the facility including break rooms, emergency exits, and other important areas',
                'category' => 'orientation',
                'due_days' => 3,
                'responsible_role' => 'supervisor',
                'order' => 8,
                'is_required' => true,
            ],
            [
                'name' => 'Team introduction',
                'description' => 'Introduction to team members and key personnel',
                'category' => 'team_introduction',
                'due_days' => 3,
                'responsible_role' => 'supervisor',
                'order' => 9,
                'is_required' => true,
            ],
            
            // Training tasks
            [
                'name' => 'Basic call handling training',
                'description' => 'Training on basic call handling procedures and etiquette',
                'category' => 'training',
                'due_days' => 5,
                'responsible_role' => 'trainer',
                'order' => 10,
                'is_required' => true,
            ],
            [
                'name' => 'CRM system training',
                'description' => 'Training on customer relationship management system',
                'category' => 'training',
                'due_days' => 5,
                'responsible_role' => 'trainer',
                'order' => 11,
                'is_required' => true,
            ],
            [
                'name' => 'Product/service knowledge training',
                'description' => 'Training on products or services the agent will be supporting',
                'category' => 'training',
                'due_days' => 7,
                'responsible_role' => 'trainer',
                'order' => 12,
                'is_required' => true,
            ],
            [
                'name' => 'Call scripts and procedures training',
                'description' => 'Training on call scripts and standard operating procedures',
                'category' => 'training',
                'due_days' => 7,
                'responsible_role' => 'trainer',
                'order' => 13,
                'is_required' => true,
            ],
            
            // Final steps
            [
                'name' => 'Shadow experienced agent',
                'description' => 'Shadow an experienced agent to observe real calls and procedures',
                'category' => 'training',
                'due_days' => 10,
                'responsible_role' => 'supervisor',
                'order' => 14,
                'is_required' => true,
            ],
            [
                'name' => 'Initial performance review',
                'description' => 'First performance review to provide feedback and set expectations',
                'category' => 'hr_admin',
                'due_days' => 14,
                'responsible_role' => 'supervisor',
                'order' => 15,
                'is_required' => true,
            ],
        ];
        
        foreach ($tasks as $task) {
            OnboardingTemplateTask::create(array_merge($task, ['template_id' => $templateId]));
        }
    }
    
    /**
     * Create tasks for supervisor onboarding template.
     */
    private function createSupervisorOnboardingTasks($templateId): void
    {
        // Base tasks similar to agent onboarding
        $this->createAgentOnboardingTasks($templateId);
        
        // Add supervisor-specific tasks
        $supervisorTasks = [
            [
                'name' => 'Leadership training',
                'description' => 'Training on leadership skills and team management',
                'category' => 'training',
                'due_days' => 8,
                'responsible_role' => 'manager',
                'order' => 16,
                'is_required' => true,
                'template_id' => $templateId,
            ],
            [
                'name' => 'Performance management training',
                'description' => 'Training on performance evaluation and coaching techniques',
                'category' => 'training',
                'due_days' => 9,
                'responsible_role' => 'manager',
                'order' => 17,
                'is_required' => true,
                'template_id' => $templateId,
            ],
            [
                'name' => 'Call center metrics and reporting',
                'description' => 'Training on call center KPIs, metrics, and reporting tools',
                'category' => 'training',
                'due_days' => 10,
                'responsible_role' => 'manager',
                'order' => 18,
                'is_required' => true,
                'template_id' => $templateId,
            ],
            [
                'name' => 'Scheduling and workforce management',
                'description' => 'Training on scheduling tools and workforce management principles',
                'category' => 'training',
                'due_days' => 11,
                'responsible_role' => 'manager',
                'order' => 19,
                'is_required' => true,
                'template_id' => $templateId,
            ],
        ];
        
        foreach ($supervisorTasks as $task) {
            OnboardingTemplateTask::create($task);
        }
    }
    
    /**
     * Create tasks for remote agent onboarding template.
     */
    private function createRemoteAgentOnboardingTasks($templateId): void
    {
        $tasks = [
            // HR Administration tasks
            [
                'name' => 'Complete employment paperwork (remote)',
                'description' => 'Complete all required employment forms electronically',
                'category' => 'hr_admin',
                'due_days' => 1,
                'responsible_role' => 'hr_manager',
                'order' => 1,
                'is_required' => true,
            ],
            [
                'name' => 'Review and sign employee handbook',
                'description' => 'Review the employee handbook and sign acknowledgment form electronically',
                'category' => 'hr_admin',
                'due_days' => 1,
                'responsible_role' => 'hr_manager',
                'order' => 2,
                'is_required' => true,
            ],
            [
                'name' => 'Submit ID and work eligibility documents',
                'description' => 'Submit electronic copies of ID, work permits, and other eligibility documents',
                'category' => 'documentation',
                'due_days' => 1,
                'responsible_role' => 'hr_manager',
                'order' => 3,
                'is_required' => true,
            ],
            
            // IT Setup tasks
            [
                'name' => 'Create system accounts',
                'description' => 'Create accounts for all required systems including email, CRM, and call center software',
                'category' => 'it_setup',
                'due_days' => 2,
                'responsible_role' => 'it_manager',
                'order' => 4,
                'is_required' => true,
            ],
            [
                'name' => 'Remote workstation setup instructions',
                'description' => 'Provide instructions for setting up home office workstation',
                'category' => 'equipment',
                'due_days' => 2,
                'responsible_role' => 'it_manager',
                'order' => 5,
                'is_required' => true,
            ],
            [
                'name' => 'VPN and remote access setup',
                'description' => 'Set up VPN and remote access to company systems',
                'category' => 'it_setup',
                'due_days' => 2,
                'responsible_role' => 'it_manager',
                'order' => 6,
                'is_required' => true,
            ],
            [
                'name' => 'Ship equipment package',
                'description' => 'Ship headset, security token, and any other required equipment',
                'category' => 'equipment',
                'due_days' => 3,
                'responsible_role' => 'it_manager',
                'order' => 7,
                'is_required' => true,
            ],
            
            // Orientation tasks
            [
                'name' => 'Virtual company orientation',
                'description' => 'Attend virtual company orientation session',
                'category' => 'orientation',
                'due_days' => 4,
                'responsible_role' => 'hr_manager',
                'order' => 8,
                'is_required' => true,
            ],
            [
                'name' => 'Virtual team introduction',
                'description' => 'Virtual introduction to team members and key personnel',
                'category' => 'team_introduction',
                'due_days' => 4,
                'responsible_role' => 'supervisor',
                'order' => 9,
                'is_required' => true,
            ],
            
            // Training tasks
            [
                'name' => 'Online basic call handling training',
                'description' => 'Complete online training modules for basic call handling',
                'category' => 'training',
                'due_days' => 6,
                'responsible_role' => 'trainer',
                'order' => 10,
                'is_required' => true,
            ],
            [
                'name' => 'Virtual CRM system training',
                'description' => 'Attend virtual training session for CRM system',
                'category' => 'training',
                'due_days' => 6,
                'responsible_role' => 'trainer',
                'order' => 11,
                'is_required' => true,
            ],
            [
                'name' => 'Online product/service knowledge training',
                'description' => 'Complete online training modules for products/services',
                'category' => 'training',
                'due_days' => 8,
                'responsible_role' => 'trainer',
                'order' => 12,
                'is_required' => true,
            ],
            [
                'name' => 'Remote work policies and procedures',
                'description' => 'Training on remote work policies, communication protocols, and expectations',
                'category' => 'training',
                'due_days' => 5,
                'responsible_role' => 'hr_manager',
                'order' => 13,
                'is_required' => true,
            ],
            
            // Final steps
            [
                'name' => 'Virtual shadowing session',
                'description' => 'Virtual session to observe experienced agent handling calls',
                'category' => 'training',
                'due_days' => 10,
                'responsible_role' => 'supervisor',
                'order' => 14,
                'is_required' => true,
            ],
            [
                'name' => 'Initial performance review (virtual)',
                'description' => 'Virtual performance review to provide feedback and set expectations',
                'category' => 'hr_admin',
                'due_days' => 14,
                'responsible_role' => 'supervisor',
                'order' => 15,
                'is_required' => true,
            ],
        ];
        
        foreach ($tasks as $task) {
            OnboardingTemplateTask::create(array_merge($task, ['template_id' => $templateId]));
        }
    }
}
