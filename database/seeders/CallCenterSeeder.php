<?php

namespace Database\Seeders;

use App\Models\CallCenter;
use App\Models\User;
use Illuminate\Database\Seeder;

class CallCenterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get a director user
        $director = User::where('role_id', 2)->first();
        
        // Create a main call center
        CallCenter::create([
            'name' => 'Main Call Center',
            'description' => 'The main call center headquarters',
            'director_id' => $director?->id,
            'status' => 'active',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+1234567890',
            'address' => '123 Main Street, City, Country',
        ]);
        
        // Create additional call centers
        CallCenter::create([
            'name' => 'East Region Call Center',
            'description' => 'Call center for the eastern region',
            'director_id' => $director?->id,
            'status' => 'active',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+1234567891',
            'address' => '456 East Street, City, Country',
        ]);
        
        CallCenter::create([
            'name' => 'West Region Call Center',
            'description' => 'Call center for the western region',
            'director_id' => $director?->id,
            'status' => 'active',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+1234567892',
            'address' => '789 West Street, City, Country',
        ]);
    }
}
