<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use Illuminate\Support\Facades\Schema;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        // First clear any existing roles
        Role::truncate();
        
        // Disable foreign key checks temporarily
        Schema::disableForeignKeyConstraints();

        // Create roles in a specific order with specific IDs
        $roles = [
            ['name' => Role::ADMIN, 'guard_name' => 'web', 'id' => 1],
            ['name' => Role::DIRECTOR, 'guard_name' => 'web', 'id' => 2],
            ['name' => Role::MANAGER, 'guard_name' => 'web', 'id' => 3],
            ['name' => Role::SUPERVISOR, 'guard_name' => 'web', 'id' => 4],
            ['name' => Role::QUALITY_CONTROLLER, 'guard_name' => 'web', 'id' => 5],
            ['name' => Role::AGENT, 'guard_name' => 'web', 'id' => 6],
            ['name' => Role::IT_MANAGER, 'guard_name' => 'web', 'id' => 7],
            ['name' => Role::IT_SUPPORT, 'guard_name' => 'web', 'id' => 8],
            ['name' => Role::TRAINER, 'guard_name' => 'web', 'id' => 9],
            ['name' => Role::ACCOUNTANT, 'guard_name' => 'web', 'id' => 10],
            ['name' => Role::HR_MANAGER, 'guard_name' => 'web', 'id' => 11],
            ['name' => Role::API, 'guard_name' => 'web', 'id' => 12],
            ['name' => Role::MOBILE, 'guard_name' => 'web', 'id' => 13],
            ['name' => Role::ADMIN_API, 'guard_name' => 'web', 'id' => 14],
            ['name' => Role::REMOTE_AGENT, 'guard_name' => 'web', 'id' => 15],
            ['name' => Role::REMOTE_SUPERVISOR, 'guard_name' => 'web', 'id' => 16],
            ['name' => Role::REMOTE_MANAGER, 'guard_name' => 'web', 'id' => 17],
        ];

        // Create roles in a specific order
        foreach ($roles as $role) {
            // First try to find existing role
            $existingRole = Role::where('id', $role['id'])->first();
            
            if ($existingRole) {
                // Update existing role
                $existingRole->update($role);
                $createdRole = $existingRole;
            } else {
                // Create new role
                $createdRole = Role::create($role);
            }
        }

        // Verify all roles were created
        $createdRoles = Role::all();
        if ($createdRoles->count() !== count($roles)) {
            throw new \Exception('Not all roles were created successfully');
        }

        // Re-enable foreign key checks
        Schema::enableForeignKeyConstraints();

        // Add display names if column exists
        if (Schema::hasColumn('roles', 'display_name')) {
            $displayNames = [
                Role::ADMIN => 'Administrator',
                Role::DIRECTOR => 'Director',
                Role::MANAGER => 'Platform Manager',
                Role::SUPERVISOR => 'Campaign Supervisor',
                Role::QUALITY_CONTROLLER => 'Quality Control',
                Role::AGENT => 'Agent',
                Role::IT_MANAGER => 'IT Manager',
                Role::IT_SUPPORT => 'IT Support',
                Role::TRAINER => 'Trainer',
                Role::ACCOUNTANT => 'Accountant',
                Role::HR_MANAGER => 'HR Manager',
                Role::API => 'API User',
                Role::MOBILE => 'Mobile User',
                Role::ADMIN_API => 'Admin API',
                Role::REMOTE_AGENT => 'Remote Agent',
                Role::REMOTE_SUPERVISOR => 'Remote Supervisor',
                Role::REMOTE_MANAGER => 'Remote Manager',
            ];

            foreach ($displayNames as $name => $displayName) {
                Role::where('name', $name)->update(['display_name' => $displayName]);
            }
        }
    }
}
