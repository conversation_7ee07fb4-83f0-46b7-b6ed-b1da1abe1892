<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Permission;
use App\Models\Role;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        // 1) Clear Spatie’s cache
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // 2) Load your module definitions
        $modules = config('modules');

        if (empty($modules)) {
            $this->command->error('No modules found in config/modules.php');
            return;
        }

        // 3) Loop modules and build permissions
        foreach ($modules as $module) {
            $moduleId   = $module['id'];
            $moduleName = $module['title'];

            // 3a) Always create an “access_{module}” permission first
            $accessName = "access_{$moduleId}";
            Permission::updateOrCreate(
                ['name' => $accessName, 'guard_name' => 'web'],
                ['description' => "Access {$moduleName} module"]
            );

            // 3b) Then create each of your module’s action permissions
            foreach ($module['permissions'] as $perm) {
                Permission::updateOrCreate(
                    ['name' => $perm, 'guard_name' => 'web'],
                    ['description' => ucfirst(str_replace('_', ' ', $perm)) . " in {$moduleName}"]
                );
            }
        }

        // 4) Assign them to roles
        foreach ($modules as $module) {
            $moduleId = $module['id'];
            $accessName = "access_{$moduleId}";

            // get the Permission instances once
            $accessPerm = Permission::findByName($accessName, 'web');
            $others     = Permission::whereIn('name', $module['permissions'])->get();

            foreach ($module['authorized_roles'] as $roleId) {
                if ($role = Role::find($roleId)) {
                    // give both module access + each action
                    $role->givePermissionTo($accessPerm);
                    $role->givePermissionTo($others);
                }
            }
        }

        // 5) Ensure a “super_admin” role exists and grant *everything* to it
        $super = Role::firstOrCreate(['name' => Role::ADMIN], ['guard_name' => 'web']);
        $super->syncPermissions(Permission::all());

        $this->command->info('✅ Permissions seeded and assigned successfully.');
    }
}
