<?php

namespace Database\Seeders;

use App\Models\Campaign;
use App\Models\Customer;
use App\Models\Platform;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;

class CampaignSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have platforms
        $platforms = Platform::all();
        if ($platforms->isEmpty()) {
            // If no platforms, check if we have sites to create platforms
            if (\App\Models\Site::count() > 0) {
                $this->call(PlatformSeeder::class);
                $platforms = Platform::all();
            } else {
                // Create a default platform if no sites
                $platform = Platform::create([
                    'name' => 'Default Platform',
                    'description' => 'Default platform created by seeder',
                    'status' => 'active',
                ]);
                $platforms = collect([$platform]);
            }
        }

        // Ensure we have customers
        $customers = Customer::all();
        if ($customers->isEmpty()) {
            $this->call(CustomerSeeder::class);
            $customers = Customer::all();
        }

        // Find campaign supervisor role
        $supervisorRole = Role::where('name', 'supervisor')->first();
        
        // Get campaign managers
        $campaignManagers = $supervisorRole 
            ? User::where('role_id', $supervisorRole->id)->get() 
            : collect([]);
        
        // If no campaign managers, create some
        if ($campaignManagers->isEmpty() && $supervisorRole) {
            $campaignManagers = collect([
                User::factory()->create([
                    'role_id' => $supervisorRole->id,
                    'first_name' => 'Campaign',
                    'last_name' => 'Manager',
                ])
            ]);
        }
        
        // Create campaigns for each platform
        foreach ($platforms as $platform) {
            $campaignCount = rand(1, 3); // Each platform has 1-3 campaigns
            
            for ($i = 0; $i < $campaignCount; $i++) {
                Campaign::create([
                    'name' => "Campaign " . ($i + 1) . " - " . $platform->name,
                    'customer_id' => $customers->isNotEmpty() ? $customers->random()->id : 1,
                    'platform_id' => $platform->id,
                    'manager_id' => $campaignManagers->isNotEmpty() ? $campaignManagers->random()->id : null,
                    'start_date' => now(),
                    'end_date' => now()->addMonths(rand(3, 12)),
                    'status' => 'active',
                ]);
            }
        }
    }
}
