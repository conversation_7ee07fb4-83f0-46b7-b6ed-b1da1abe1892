<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting database seeding...');

        // Step 1: Create roles first
        $this->command->info('📝 Creating roles...');
        $this->call(RoleSeeder::class);

        // Step 2: Create all permissions from modules config
        $this->command->info('🔐 Creating permissions...');
        $this->call(OptimizedPermissionSeeder::class);

        // Step 3: Create admin user with all permissions
        $this->command->info('👤 Creating admin user...');
        $this->call(OptimizedAdminSeeder::class);

        // Step 4: Create other users
        $this->command->info('👥 Creating other users...');
        $this->call(UserSeeder::class);

        // Step 5: Create business data
        $this->command->info('🏢 Creating business data...');
        $this->call(CustomerSeeder::class);
        $this->call(CallCenterSeeder::class);
        $this->call(SiteSeeder::class);
        $this->call(PlatformSeeder::class);
        $this->call(DepartmentSeeder::class);
        $this->call(TeamSeeder::class);
        $this->call(OnboardingTemplateSeeder::class);
        $this->call(CampaignSeeder::class);
        $this->call(TrainingSeeder::class);
        $this->call(ShiftSeeder::class);
        $this->call(AppointmentSeeder::class);
        $this->call(ReportSeeder::class);
        $this->call(PaymentSeeder::class);
        $this->call(SettingsSeeder::class);

        $this->command->info('✅ Database seeding completed successfully!');
    }
}
