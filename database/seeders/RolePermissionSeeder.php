<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'view_roles',
            'create_roles',
            'edit_roles',
            'delete_roles',
            'view_permissions',
            'create_permissions',
            'edit_permissions',
            'delete_permissions',
            'view_campaigns',
            'create_campaigns',
            'edit_campaigns',
            'delete_campaigns',
            'view_appointments',
            'create_appointments',
            'edit_appointments',
            'delete_appointments',
            'view_reports',
            'create_reports',
            'edit_reports',
            'delete_reports',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::firstOrCreate(['name' => 'administrator']);
        $adminRole->givePermissionTo(Permission::all());

        $directorRole = Role::firstOrCreate(['name' => 'director']);
        $directorRole->givePermissionTo([
            'view_users', 'create_users', 'edit_users',
            'view_campaigns', 'create_campaigns', 'edit_campaigns',
            'view_appointments', 'create_appointments', 'edit_appointments',
            'view_reports', 'create_reports', 'edit_reports',
        ]);

        $managerRole = Role::firstOrCreate(['name' => 'platform_manager']);
        $managerRole->givePermissionTo([
            'view_users', 'edit_users',
            'view_campaigns', 'edit_campaigns',
            'view_appointments', 'create_appointments', 'edit_appointments',
            'view_reports', 'create_reports',
        ]);

        $supervisorRole = Role::firstOrCreate(['name' => 'supervisor']);
        $supervisorRole->givePermissionTo([
            'view_users',
            'view_campaigns',
            'view_appointments', 'create_appointments', 'edit_appointments',
            'view_reports', 'create_reports',
        ]);

        $agentRole = Role::firstOrCreate(['name' => 'agent']);
        $agentRole->givePermissionTo([
            'view_appointments', 'create_appointments', 'edit_appointments',
        ]);

        // Assign admin role to the first user if exists
        $firstUser = User::first();
        if ($firstUser) {
            $firstUser->assignRole('administrator');
            $this->command->info("Assigned administrator role to user: {$firstUser->email}");
        }

        $this->command->info('Roles and permissions seeded successfully!');
    }
}
