<?php

namespace Database\Seeders;

use App\Models\Department;
use App\Models\Team;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get departments
        $departments = Department::all();
        
        // Get potential team leaders (supervisors, managers)
        $potentialLeaders = User::whereIn('role_id', [3, 4, 5, 8, 10, 12])->get();
        
        // Get potential team members
        $potentialMembers = User::all();
        
        // Create teams
        $teams = [
            [
                'name' => 'Executive Leadership Team',
                'code' => 'ELT',
                'description' => 'Executive leadership team responsible for strategic direction',
                'department_id' => $departments->where('code', 'EXEC')->first()?->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subYears(2),
            ],
            [
                'name' => 'HR Recruitment Team',
                'code' => 'HRT',
                'description' => 'Team responsible for recruiting new talent',
                'department_id' => $departments->where('code', 'HR')->first()?->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subMonths(6),
            ],
            [
                'name' => 'IT Support Team',
                'code' => 'IST',
                'description' => 'Technical support team for all IT issues',
                'department_id' => $departments->where('code', 'IT')->first()?->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subYears(1),
            ],
            [
                'name' => 'Sales Team Alpha',
                'code' => 'STA',
                'description' => 'Sales team focused on new client acquisition',
                'department_id' => $departments->where('code', 'SALES')->first()?->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subMonths(8),
            ],
            [
                'name' => 'Customer Support Team',
                'code' => 'CST',
                'description' => 'Team handling customer inquiries and support',
                'department_id' => $departments->where('code', 'CS')->first()?->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subMonths(10),
            ],
            [
                'name' => 'Quality Assurance Team',
                'code' => 'QAT',
                'description' => 'Team responsible for quality control and assurance',
                'department_id' => $departments->where('code', 'QA')->first()?->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subMonths(9),
            ],
            [
                'name' => 'New Agent Training Team',
                'code' => 'NATT',
                'description' => 'Team focused on training new agents',
                'department_id' => $departments->where('code', 'TRN')->first()?->id,
                'status' => 'active',
                'start_date' => Carbon::now()->subMonths(4),
            ],
        ];
        
        foreach ($teams as $teamData) {
            // Assign a random leader if available
            if ($potentialLeaders->isNotEmpty()) {
                $leader = $potentialLeaders->random();
                $teamData['leader_id'] = $leader->id;
            }
            
            // Create the team
            $team = Team::updateOrCreate(
                ['code' => $teamData['code']],
                $teamData
            );
            
            // Add team members (3-8 random members)
            $memberCount = rand(3, 8);
            $members = $potentialMembers->random($memberCount);
            
            foreach ($members as $member) {
                // Don't add the leader as a member again
                if ($team->leader_id === $member->id) {
                    continue;
                }
                
                // Add as team member with random role
                $roles = ['member', 'coordinator', 'specialist', 'advisor'];
                $team->members()->attach($member->id, [
                    'role' => $roles[array_rand($roles)],
                    'joined_at' => Carbon::now()->subMonths(rand(1, 6)),
                ]);
            }
        }
    }
}
