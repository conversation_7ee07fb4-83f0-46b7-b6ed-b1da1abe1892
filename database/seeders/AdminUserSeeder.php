<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user with a known password
        $adminRole = Role::where('name', 'Admin')->first();
        
        if (!$adminRole) {
            $this->command->info('Admin role not found. Creating it...');
            $adminRole = Role::create([
                'id' => 1,
                'name' => 'Admin',
                'display_name' => 'Administrator',
            ]);
        }
        
        // Check if admin user already exists
        $adminUser = User::where('email', '<EMAIL>')->first();
        
        if (!$adminUser) {
            $this->command->info('Creating admin user...');
            $adminUser = User::create([
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'), // Set a known password
                'role_id' => $adminRole->id,
                'status' => 'actif',
                'registration_number' => 'EMP-ADMIN',
                'hire_date' => now()->subYear(),
                'email_verified_at' => now(),
            ]);
            
            $this->command->info('Admin user created successfully!');
            $this->command->info('Email: <EMAIL>');
            $this->command->info('Password: admin123');
        } else {
            // Update the admin password
            $adminUser->update([
                'password' => Hash::make('admin123'),
            ]);
            
            $this->command->info('Admin user password updated!');
            $this->command->info('Email: <EMAIL>');
            $this->command->info('Password: admin123');
        }
        
        // Assign admin role using Spatie if available
        if (class_exists('\Spatie\Permission\Models\Role')) {
            $spatieAdminRole = \Spatie\Permission\Models\Role::where('name', 'Admin')->first();
            if ($spatieAdminRole && method_exists($adminUser, 'assignRole')) {
                $adminUser->assignRole($spatieAdminRole);
                $this->command->info('Spatie admin role assigned!');
            }
        }
    }
}
