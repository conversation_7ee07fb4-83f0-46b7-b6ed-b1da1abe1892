<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class OptimizedAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('👤 Creating optimized admin user...');

        // Ensure admin role exists
        $adminRole = Role::where('name', Role::ADMIN)->first();
        
        if (!$adminRole) {
            $this->command->error('❌ Admin role not found! Please run RoleSeeder first.');
            return;
        }

        // Create or update admin user
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'status' => 'actif',
                'registration_number' => 'EMP-ADMIN-001',
                'hire_date' => now()->subYear(),
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
            ]
        );

        if ($adminUser->wasRecentlyCreated) {
            $this->command->info('✅ Admin user created successfully!');
        } else {
            $this->command->info('✅ Admin user updated successfully!');
        }

        // Assign admin role
        if (!$adminUser->hasRole(Role::ADMIN)) {
            $adminUser->assignRole(Role::ADMIN);
            $this->command->info('✅ Admin role assigned to user');
        } else {
            $this->command->info('→ Admin role already assigned');
        }

        // Ensure admin has ALL permissions
        $this->ensureAdminHasAllPermissions($adminUser);

        // Verify admin permissions
        $this->verifyAdminPermissions($adminUser);

        $this->command->info('📧 Admin credentials:');
        $this->command->info('   Email: <EMAIL>');
        $this->command->info('   Password: admin123');
        $this->command->info('✅ Admin user setup completed!');
    }

    /**
     * Ensure admin user has all permissions directly
     */
    private function ensureAdminHasAllPermissions(User $adminUser): void
    {
        $this->command->info('🔐 Ensuring admin has all permissions...');

        $allPermissions = Permission::all();
        
        if ($allPermissions->isEmpty()) {
            $this->command->warn('⚠️  No permissions found in database');
            return;
        }

        // Give all permissions directly to admin user
        $adminUser->syncPermissions($allPermissions);

        $this->command->info("✅ Admin user granted {$allPermissions->count()} permissions directly");
    }

    /**
     * Verify admin has all required permissions
     */
    private function verifyAdminPermissions(User $adminUser): void
    {
        $this->command->info('🔍 Verifying admin permissions...');

        // Get all modules from config
        $modules = config('modules', []);
        $missingPermissions = [];
        $totalChecked = 0;

        foreach ($modules as $module) {
            if (!isset($module['permissions'])) {
                continue;
            }

            foreach ($module['permissions'] as $permission) {
                $totalChecked++;
                
                if (!$adminUser->hasPermissionTo($permission)) {
                    $missingPermissions[] = $permission;
                }
            }
        }

        if (empty($missingPermissions)) {
            $this->command->info("✅ Admin has all {$totalChecked} required permissions!");
        } else {
            $this->command->error("❌ Admin is missing " . count($missingPermissions) . " permissions:");
            foreach ($missingPermissions as $permission) {
                $this->command->error("   - {$permission}");
            }
            
            // Try to fix missing permissions
            $this->fixMissingPermissions($adminUser, $missingPermissions);
        }

        // Additional verification - check if admin can access all modules
        $this->verifyModuleAccess($adminUser, $modules);
    }

    /**
     * Fix missing permissions for admin
     */
    private function fixMissingPermissions(User $adminUser, array $missingPermissions): void
    {
        $this->command->info('🔧 Attempting to fix missing permissions...');

        foreach ($missingPermissions as $permissionName) {
            try {
                // Create permission if it doesn't exist
                $permission = Permission::firstOrCreate(
                    ['name' => $permissionName],
                    ['guard_name' => 'web']
                );

                // Give permission to admin
                $adminUser->givePermissionTo($permission);
                $this->command->info("  ✓ Fixed: {$permissionName}");
            } catch (\Exception $e) {
                $this->command->error("  ❌ Failed to fix: {$permissionName} - " . $e->getMessage());
            }
        }
    }

    /**
     * Verify admin can access all modules
     */
    private function verifyModuleAccess(User $adminUser, array $modules): void
    {
        $this->command->info('🏢 Verifying module access...');

        $accessibleModules = 0;
        $totalModules = count($modules);

        foreach ($modules as $module) {
            $moduleAccessPermission = 'access_' . $module['id'];
            
            if ($adminUser->hasPermissionTo($moduleAccessPermission)) {
                $accessibleModules++;
            } else {
                $this->command->warn("  ⚠️  Cannot access module: {$module['title']} (missing: {$moduleAccessPermission})");
                
                // Try to create and assign the missing access permission
                try {
                    $permission = Permission::firstOrCreate(
                        ['name' => $moduleAccessPermission],
                        ['guard_name' => 'web']
                    );
                    $adminUser->givePermissionTo($permission);
                    $accessibleModules++;
                    $this->command->info("  ✓ Fixed access to: {$module['title']}");
                } catch (\Exception $e) {
                    $this->command->error("  ❌ Failed to fix access to: {$module['title']}");
                }
            }
        }

        if ($accessibleModules === $totalModules) {
            $this->command->info("✅ Admin can access all {$totalModules} modules!");
        } else {
            $this->command->warn("⚠️  Admin can access {$accessibleModules}/{$totalModules} modules");
        }
    }
}
