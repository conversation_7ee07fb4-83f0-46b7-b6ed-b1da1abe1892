<?php

namespace Database\Seeders;

use App\Models\Department;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get manager role
        $managerRole = Role::where('name', 'site_manager')->first();
        $hrManagerRole = Role::where('name', 'hr_manager')->first();
        $itManagerRole = Role::where('name', 'it_manager')->first();
        $accountantRole = Role::where('name', 'accountant')->first();
        
        // Get potential department managers
        $managers = User::whereIn('role_id', [
            $managerRole?->id, 
            $hrManagerRole?->id, 
            $itManagerRole?->id, 
            $accountantRole?->id
        ])->get();
        
        // Create main departments
        $departments = [
            [
                'name' => 'Executive',
                'code' => 'EXEC',
                'description' => 'Executive leadership team',
                'parent_id' => null,
            ],
            [
                'name' => 'Human Resources',
                'code' => 'HR',
                'description' => 'Human Resources department',
                'parent_id' => 1, // Executive
            ],
            [
                'name' => 'Information Technology',
                'code' => 'IT',
                'description' => 'Information Technology department',
                'parent_id' => 1, // Executive
            ],
            [
                'name' => 'Finance',
                'code' => 'FIN',
                'description' => 'Finance department',
                'parent_id' => 1, // Executive
            ],
            [
                'name' => 'Operations',
                'code' => 'OPS',
                'description' => 'Operations department',
                'parent_id' => 1, // Executive
            ],
            [
                'name' => 'Sales',
                'code' => 'SALES',
                'description' => 'Sales department',
                'parent_id' => 5, // Operations
            ],
            [
                'name' => 'Marketing',
                'code' => 'MKT',
                'description' => 'Marketing department',
                'parent_id' => 5, // Operations
            ],
            [
                'name' => 'Customer Service',
                'code' => 'CS',
                'description' => 'Customer Service department',
                'parent_id' => 5, // Operations
            ],
            [
                'name' => 'Quality Assurance',
                'code' => 'QA',
                'description' => 'Quality Assurance department',
                'parent_id' => 5, // Operations
            ],
            [
                'name' => 'Training',
                'code' => 'TRN',
                'description' => 'Training department',
                'parent_id' => 2, // HR
            ],
        ];
        
        foreach ($departments as $index => $departmentData) {
            // Assign a random manager if available
            if ($managers->isNotEmpty()) {
                $manager = $managers->random();
                $departmentData['manager_id'] = $manager->id;
            }
            
            Department::updateOrCreate(
                ['code' => $departmentData['code']],
                $departmentData
            );
        }
    }
}
