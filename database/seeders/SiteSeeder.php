<?php

namespace Database\Seeders;

use App\Models\CallCenter;
use App\Models\Role;
use App\Models\Site;
use App\Models\User;
use Illuminate\Database\Seeder;

class SiteSeeder extends Seeder
{
    public function run(): void
    {
        // Get role IDs by name instead of hardcoded IDs
        $siteManagerRole = Role::where('name', 'site_manager')->first();
        $itManagerRole = Role::where('name', 'it_manager')->first();
        $trainerRole = Role::where('name', 'agent')->first(); // Assuming 'agent' can be trainers
        $accountantRole = Role::where('name', 'administrator')->first(); // Fallback if no accountant role
        $hrManagerRole = Role::where('name', 'administrator')->first(); // Fallback if no HR role

        // Get users with specific roles
        $siteManagers = $siteManagerRole ? User::where('role_id', $siteManagerRole->id)->get() : collect([]);
        $itManagers = $itManagerRole ? User::where('role_id', $itManagerRole->id)->get() : collect([]);
        $trainers = $trainerRole ? User::where('role_id', $trainerRole->id)->get() : collect([]);
        $accountants = $accountantRole ? User::where('role_id', $accountantRole->id)->get() : collect([]);
        $hrManagers = $hrManagerRole ? User::where('role_id', $hrManagerRole->id)->get() : collect([]);

        // Get call centers
        $callCenters = CallCenter::all();

        // Create default sites
        $sites = [
            [
                'name' => 'Headquarters',
                'location' => 'Paris, France',
                'description' => 'Main headquarters',
                'status' => 'active',
                'capacity' => 500,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+***********',
            ],
            [
                'name' => 'Regional Office',
                'location' => 'Lyon, France',
                'description' => 'Regional office for southern France',
                'status' => 'active',
                'capacity' => 250,
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+***********',
            ],
            // Add more sites as needed
        ];

        foreach ($sites as $siteData) {
            Site::create([
                'name' => $siteData['name'],
                'location' => $siteData['location'],
                'call_center_id' => $callCenters->isNotEmpty() ? $callCenters->random()->id : null,
                'manager_id' => $siteManagers->isNotEmpty() ? $siteManagers->random()->id : null,
                'it_manager_id' => $itManagers->isNotEmpty() ? $itManagers->random()->id : null,
                'trainer_id' => $trainers->isNotEmpty() ? $trainers->random()->id : null,
                'accountant_id' => $accountants->isNotEmpty() ? $accountants->random()->id : null,
                'hr_manager_id' => $hrManagers->isNotEmpty() ? $hrManagers->random()->id : null,
                'description' => $siteData['description'],
                'status' => $siteData['status'],
                'capacity' => $siteData['capacity'],
                'contact_email' => $siteData['contact_email'],
                'contact_phone' => $siteData['contact_phone'],
            ]);
        }
    }
}
