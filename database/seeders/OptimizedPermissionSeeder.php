<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Spatie\Permission\PermissionRegistrar;

class OptimizedPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $this->command->info('🔐 Creating permissions from modules configuration...');

        // Get all modules from config
        $modules = config('modules', []);
        
        if (empty($modules)) {
            $this->command->error('❌ No modules found in config/modules.php');
            return;
        }

        $allPermissions = [];
        $permissionCount = 0;

        // Extract all permissions from modules
        foreach ($modules as $module) {
            if (isset($module['permissions']) && is_array($module['permissions'])) {
                foreach ($module['permissions'] as $permission) {
                    if (!in_array($permission, $allPermissions)) {
                        $allPermissions[] = $permission;
                    }
                }
            }
        }

        $this->command->info("📋 Found " . count($allPermissions) . " unique permissions to create");

        // Create permissions
        foreach ($allPermissions as $permissionName) {
            try {
                $permission = Permission::firstOrCreate(
                    ['name' => $permissionName],
                    ['guard_name' => 'web']
                );
                
                if ($permission->wasRecentlyCreated) {
                    $permissionCount++;
                    $this->command->line("  ✓ Created: {$permissionName}");
                } else {
                    $this->command->line("  → Exists: {$permissionName}");
                }
            } catch (\Exception $e) {
                $this->command->error("  ❌ Failed to create permission: {$permissionName} - " . $e->getMessage());
            }
        }

        $this->command->info("✅ Created {$permissionCount} new permissions");

        // Assign permissions to roles based on modules configuration
        $this->assignPermissionsToRoles($modules);

        $this->command->info('✅ Permission seeding completed successfully!');
    }

    /**
     * Assign permissions to roles based on modules configuration
     */
    private function assignPermissionsToRoles(array $modules): void
    {
        $this->command->info('🔗 Assigning permissions to roles...');

        // Get role mappings (role ID to role name)
        $roleMapping = [
            1 => Role::ADMIN,
            2 => Role::DIRECTOR,
            3 => Role::MANAGER,
            4 => Role::SUPERVISOR,
            5 => Role::QUALITY_CONTROLLER,
            6 => Role::AGENT,
            7 => Role::IT_MANAGER,
            8 => Role::IT_SUPPORT,
            9 => Role::TRAINER,
            10 => Role::ACCOUNTANT,
            11 => Role::HR_MANAGER,
            12 => Role::API,
            13 => Role::MOBILE,
            14 => Role::ADMIN_API,
            15 => Role::REMOTE_AGENT,
            16 => Role::REMOTE_SUPERVISOR,
            17 => Role::REMOTE_MANAGER,
        ];

        foreach ($modules as $module) {
            $moduleId = $module['id'];
            $modulePermissions = $module['permissions'] ?? [];
            $authorizedRoles = $module['authorized_roles'] ?? [];

            if (empty($modulePermissions) || empty($authorizedRoles)) {
                continue;
            }

            $this->command->line("  📦 Processing module: {$module['title']}");

            foreach ($authorizedRoles as $roleId) {
                if (!isset($roleMapping[$roleId])) {
                    $this->command->warn("    ⚠️  Unknown role ID: {$roleId}");
                    continue;
                }

                $roleName = $roleMapping[$roleId];
                $role = Role::where('name', $roleName)->first();

                if (!$role) {
                    $this->command->warn("    ⚠️  Role not found: {$roleName}");
                    continue;
                }

                // Give permissions to role
                foreach ($modulePermissions as $permissionName) {
                    try {
                        if (!$role->hasPermissionTo($permissionName)) {
                            $role->givePermissionTo($permissionName);
                            $this->command->line("    ✓ {$roleName} → {$permissionName}");
                        }
                    } catch (\Exception $e) {
                        $this->command->error("    ❌ Failed to assign {$permissionName} to {$roleName}: " . $e->getMessage());
                    }
                }
            }
        }

        // Ensure admin role has ALL permissions
        $this->ensureAdminHasAllPermissions();
    }

    /**
     * Ensure admin role has all permissions
     */
    private function ensureAdminHasAllPermissions(): void
    {
        $this->command->info('👑 Ensuring admin role has all permissions...');

        $adminRole = Role::where('name', Role::ADMIN)->first();
        
        if (!$adminRole) {
            $this->command->error('❌ Admin role not found!');
            return;
        }

        $allPermissions = Permission::all();
        $adminRole->syncPermissions($allPermissions);

        $this->command->info("✅ Admin role now has {$allPermissions->count()} permissions");
    }
}
