<?php

namespace Database\Seeders;

use App\Models\Platform;
use App\Models\Site;
use App\Models\User;
use Illuminate\Database\Seeder;

class PlatformSeeder extends Seeder
{
    public function run(): void
    {
        $sites = Site::all();
        $platformManagers = User::where('role_id', 4)->get();
        $itSupports = User::where('role_id', 9)->get();
        
        foreach ($sites as $index => $site) {
            $platformCount = rand(1, 3); // Each site has 1-3 platforms
            
            for ($i = 0; $i < $platformCount; $i++) {
                Platform::create([
                    'name' => "Platform " . ($i + 1) . " - " . $site->name,
                    'site_id' => $site->id,
                    'manager_id' => $platformManagers->isNotEmpty() ? $platformManagers->random()->id : null,
                    'it_support_id' => $itSupports->isNotEmpty() ? $itSupports->random()->id : null,
                    'description' => "Platform for " . $site->name,
                    'status' => 'active',
                ]);
            }
        }
    }
}