<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

use App\Models\Customer;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = [
            [
                'name' => 'Acme Corporation',
                'email' => '<EMAIL>',
                'phone' => '******-123-4567',
                'address' => '123 Main St',
                'city' => 'New York',
                'country' => 'USA',
                'postal_code' => '10001',
                'website' => 'https://www.acme.com',
                'industry' => 'Technology',
                'notes' => 'Major technology company with global presence.',
                'contact_person' => '<PERSON>',
                'contact_position' => 'CEO',
                'contact_phone' => '******-987-6543',
                'status' => 'active',
            ],
            [
                'name' => 'Globex Corporation',
                'email' => '<EMAIL>',
                'phone' => '******-234-5678',
                'address' => '456 Park Ave',
                'city' => 'Los Angeles',
                'country' => 'USA',
                'postal_code' => '90001',
                'website' => 'https://www.globex.com',
                'industry' => 'Manufacturing',
                'notes' => 'Leading manufacturer of industrial equipment.',
                'contact_person' => 'Jane Doe',
                'contact_position' => 'COO',
                'contact_phone' => '******-876-5432',
                'status' => 'active',
            ],
            [
                'name' => 'Soylent Corp',
                'email' => '<EMAIL>',
                'phone' => '******-345-6789',
                'address' => '789 Broadway',
                'city' => 'Chicago',
                'country' => 'USA',
                'postal_code' => '60007',
                'website' => 'https://www.soylent.com',
                'industry' => 'Food & Beverage',
                'notes' => 'Innovative food technology company.',
                'contact_person' => 'Robert Johnson',
                'contact_position' => 'CTO',
                'contact_phone' => '******-765-4321',
                'status' => 'active',
            ],
            [
                'name' => 'Initech',
                'email' => '<EMAIL>',
                'phone' => '******-456-7890',
                'address' => '101 Tech Blvd',
                'city' => 'Austin',
                'country' => 'USA',
                'postal_code' => '73301',
                'website' => 'https://www.initech.com',
                'industry' => 'Software',
                'notes' => 'Enterprise software solutions provider.',
                'contact_person' => 'Michael Bolton',
                'contact_position' => 'VP Sales',
                'contact_phone' => '******-654-3210',
                'status' => 'active',
            ],
            [
                'name' => 'Umbrella Corporation',
                'email' => '<EMAIL>',
                'phone' => '******-567-8901',
                'address' => '202 Science Way',
                'city' => 'Boston',
                'country' => 'USA',
                'postal_code' => '02108',
                'website' => 'https://www.umbrella.com',
                'industry' => 'Pharmaceuticals',
                'notes' => 'Pharmaceutical research and development company.',
                'contact_person' => 'Albert Wesker',
                'contact_position' => 'Research Director',
                'contact_phone' => '******-543-2109',
                'status' => 'active',
            ],
        ];

        foreach ($customers as $customer) {
            Customer::create($customer);
        }
    }
}
