<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Media;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role as SpatieRole;
use App\Models\Permission as SpatiePermission;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('👥 Creating additional users...');

        // Skip admin user creation as it's handled by OptimizedAdminSeeder
        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            $this->command->warn('⚠️  Admin user not found. Please run OptimizedAdminSeeder first.');
            return;
        }

        Media::create([
            'mediable_id' => $admin->id,
            'mediable_type' => User::class,
            'file_name' => 'admin_profile.jpg',
            'file_path' => 'media/profile_pictures/admin_profile.jpg',
            'mime_type' => 'image/jpeg',
            'category' => 'profile_picture',
            'uploaded_by' => $admin->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Get all roles except admin for creating other users
        $roles = Role::where('name', '!=', Role::ADMIN)->get();

        if ($roles->isEmpty()) {
            $this->command->warn('⚠️  No non-admin roles found for creating users');
            return;
        }

        $this->command->info("📋 Found {$roles->count()} roles for user creation");

        // Store admin ID in config for other seeders
        config(['miamboo.admin_user_id' => $admin->id]);

        $createdUsers = 0;

        // Create 2 users per role (reduced from 3 for efficiency)
        foreach ($roles as $role) {
            $this->command->line("  👤 Creating users for role: {$role->name}");

            User::factory(2)->create()->each(function ($user) use ($role) {
                $user->assignRole($role);
            });

            $createdUsers += 2;
        }

        // Create 5 additional users with random roles (reduced from 10)
        $this->command->line("  🎲 Creating users with random roles...");
        User::factory(5)->create()->each(function ($user) use ($roles) {
            $role = $roles->random();
            $user->assignRole($role);
        });

        $createdUsers += 5;

        $this->command->info("✅ Created {$createdUsers} additional users");
        $this->command->info("📊 Total users in system: " . User::count());
    }
}
