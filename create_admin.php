<?php

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

try {
    DB::beginTransaction();
    
    // Check if admin user already exists
    $adminUser = User::where('email', '<EMAIL>')->first();
    
    if ($adminUser) {
        echo "Admin user already exists. Updating password...\n";
        $adminUser->update([
            'password' => Hash::make('admin123'),
        ]);
        echo "Admin user password updated!\n";
    } else {
        // Create a new admin user with minimal required fields
        $adminUser = new User();
        $adminUser->first_name = 'Admin';
        $adminUser->last_name = 'User';
        $adminUser->email = '<EMAIL>';
        $adminUser->password = Hash::make('admin123');
        $adminUser->role_id = 1; // Admin role
        $adminUser->status = 'active';
        $adminUser->email_verified_at = now();
        $adminUser->registration_number = 'EMP-ADMIN';
        $adminUser->hire_date = now();
        
        $adminUser->save();
        
        echo "Admin user created successfully!\n";
    }
    
    DB::commit();
    
    echo "Admin credentials:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    
} catch (\Exception $e) {
    DB::rollBack();
    echo "Failed to create admin user: " . $e->getMessage() . "\n";
    echo "Exception type: " . get_class($e) . "\n";
    echo "File: " . $e->getFile() . ':' . $e->getLine() . "\n";
}
