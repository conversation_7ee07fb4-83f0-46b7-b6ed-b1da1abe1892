<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Campaign;

// Get the first campaign
$campaign = Campaign::first();

if ($campaign) {
    echo "Deleting campaign: " . $campaign->name . " (ID: " . $campaign->id . ")\n";
    
    try {
        $result = $campaign->delete();
        echo "Delete result: " . ($result ? "Success" : "Failed") . "\n";
    } catch (\Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
    }
} else {
    echo "No campaigns found.\n";
}
