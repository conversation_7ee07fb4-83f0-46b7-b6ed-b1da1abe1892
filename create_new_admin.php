<?php

require __DIR__ . '/vendor/autoload.php';

$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

try {
    // Create a completely new admin user with a different email
    $email = '<EMAIL>';
    $password = 'password123';
    
    // Check if this admin already exists
    $existingAdmin = DB::table('users')->where('email', $email)->first();
    
    if ($existingAdmin) {
        echo "Admin user with email {$email} already exists. Updating password...\n";
        DB::table('users')->where('email', $email)->update([
            'password' => Hash::make($password)
        ]);
        echo "Password updated!\n";
    } else {
        // Insert a new admin user directly into the database
        DB::table('users')->insert([
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => $email,
            'password' => Hash::make($password),
            'role_id' => 1,
            'status' => 'active',
            'email_verified_at' => now(),
            'registration_number' => 'EMP-ADMIN2',
            'hire_date' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "New admin user created successfully!\n";
    }
    
    echo "Admin credentials:\n";
    echo "Email: {$email}\n";
    echo "Password: {$password}\n";
    
} catch (\Exception $e) {
    echo "Failed to create admin user: " . $e->getMessage() . "\n";
    echo "Exception type: " . get_class($e) . "\n";
    echo "File: " . $e->getFile() . ':' . $e->getLine() . "\n";
}
