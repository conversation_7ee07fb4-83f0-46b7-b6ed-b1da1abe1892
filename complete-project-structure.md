Laravel Call Center Management System (Complete Structure)
│
├── app/                              # Application code
│   ├── Console/                      # Console commands
│   │   └── Commands/
│   │       ├── GenerateReports.php
│   │       └── SyncAttendance.php
│   │
│   ├── Exceptions/                   # Exception handlers
│   │   └── Handler.php
│   │
│   ├── Helpers/                      # Helper functions
│   │   └── helpers.php               # Global helper functions
│   │
│   ├── Http/                         # HTTP layer
│   │   ├── Controllers/              # Controllers
│   │   │   ├── Auth/
│   │   │   │   └── LoginController.php
│   │   │   ├── API/
│   │   │   │   └── ShiftController.php
│   │   │   ├── AppointmentController.php
│   │   │   ├── CampaignController.php
│   │   │   ├── CustomerController.php
│   │   │   ├── DashboardController.php
│   │   │   ├── ExportController.php
│   │   │   ├── PaymentController.php
│   │   │   ├── ReportController.php
│   │   │   ├── ShiftController.php
│   │   │   ├── SiteController.php
│   │   │   ├── TrainingController.php
│   │   │   └── UserController.php
│   │   │
│   │   ├── Middleware/               # Middleware
│   │   │   ├── Authenticate.php
│   │   │   ├── CheckRole.php
│   │   │   ├── RedirectIfAuthenticated.php
│   │   │   └── RedirectIfRole.php
│   │   │
│   │   └── Requests/                 # Form requests
│   │       ├── AppointmentRequest.php
│   │       ├── CampaignRequest.php
│   │       ├── PaymentRequest.php
│   │       ├── ReportRequest.php
│   │       ├── ShiftRequest.php
│   │       ├── SiteRequest.php
│   │       └── UserRequest.php
│   │
│   ├── Livewire/                     # Livewire components
│   │   ├── Accountant/               # Accountant module components
│   │   │   ├── AccountingDashboard.php
│   │   │   ├── AgentAttendance.php
│   │   │   ├── PaymentHistory.php
│   │   │   ├── PaymentManagement.php
│   │   │   └── SalaryCalculation.php
│   │   │
│   │   ├── Accounting/               # Accounting module components
│   │   │   ├── AccountingDashboard.php
│   │   │   ├── ExpenseCreate.php
│   │   │   ├── ExpenseEdit.php
│   │   │   ├── ExpenseIndex.php
│   │   │   ├── InvoiceCreate.php
│   │   │   ├── InvoiceEdit.php
│   │   │   ├── InvoiceIndex.php
│   │   │   ├── ReportCreate.php
│   │   │   └── ReportIndex.php
│   │   │
│   │   ├── Admin/                    # Admin module components
│   │   │   ├── AdminDashboard.php
│   │   │   ├── PermissionManager.php
│   │   │   ├── RoleManager.php
│   │   │   └── SystemSettings.php
│   │   │
│   │   ├── Agents/                   # Agent module components
│   │   │   ├── AgentCreate.php
│   │   │   ├── AgentEdit.php
│   │   │   ├── AgentIndex.php
│   │   │   ├── AgentPage.php
│   │   │   └── AgentShow.php
│   │   │
│   │   ├── Appointments/             # Appointment module components
│   │   │   ├── AppointmentCreate.php
│   │   │   ├── AppointmentEdit.php
│   │   │   ├── AppointmentIndex.php
│   │   │   ├── AppointmentPage.php
│   │   │   ├── AppointmentShow.php
│   │   │   └── AppointmentValidate.php
│   │   │
│   │   ├── Auth/                     # Authentication components
│   │   │   ├── Login.php
│   │   │   ├── Register.php
│   │   │   └── ResetPassword.php
│   │   │
│   │   ├── Campaigns/                # Campaign module components
│   │   │   ├── CampaignCreate.php
│   │   │   ├── CampaignEdit.php
│   │   │   ├── CampaignIndex.php
│   │   │   ├── CampaignPage.php
│   │   │   └── CampaignShow.php
│   │   │
│   │   ├── Customers/                # Customer module components
│   │   │   ├── CustomerCreate.php
│   │   │   ├── CustomerEdit.php
│   │   │   ├── CustomerIndex.php
│   │   │   └── CustomerShow.php
│   │   │
│   │   ├── Forms/                    # Form components
│   │   │   ├── AppointmentForm.php
│   │   │   ├── CampaignForm.php
│   │   │   ├── CustomerForm.php
│   │   │   ├── PaymentForm.php
│   │   │   ├── ReportForm.php
│   │   │   ├── ShiftForm.php
│   │   │   ├── SiteForm.php
│   │   │   ├── TrainingForm.php
│   │   │   └── UserForm.php
│   │   │
│   │   ├── Global/                   # Global components
│   │   │   ├── App.php
│   │   │   ├── AppLayout.php
│   │   │   ├── Chart.php
│   │   │   ├── Dashboard.php
│   │   │   ├── DataTable.php
│   │   │   ├── Footer.php
│   │   │   ├── Header.php
│   │   │   ├── Notification.php
│   │   │   ├── Page.php
│   │   │   ├── PageHeader.php
│   │   │   ├── PageSidebar.php
│   │   │   ├── SearchBar.php
│   │   │   └── Sidebar.php
│   │   │
│   │   ├── HR/                       # HR module components
│   │   │   ├── EmployeeCreate.php
│   │   │   ├── EmployeeEdit.php
│   │   │   ├── EmployeeIndex.php
│   │   │   ├── EmployeeShow.php
│   │   │   ├── LeaveCreate.php
│   │   │   ├── LeaveEdit.php
│   │   │   ├── LeaveIndex.php
│   │   │   ├── PerformanceCreate.php
│   │   │   ├── PerformanceIndex.php
│   │   │   ├── RecruitmentCreate.php
│   │   │   └── RecruitmentIndex.php
│   │   │
│   │   ├── Platforms/                # Platform module components
│   │   │   ├── PlatformCreate.php
│   │   │   ├── PlatformEdit.php
│   │   │   ├── PlatformIndex.php
│   │   │   └── PlatformShow.php
│   │   │
│   │   ├── Reports/                  # Reports module components
│   │   │   ├── DayReports.php
│   │   │   ├── ReportCreate.php
│   │   │   ├── ReportEdit.php
│   │   │   ├── ReportExport.php
│   │   │   ├── ReportIndex.php
│   │   │   ├── ReportPage.php
│   │   │   └── ReportShow.php
│   │   │
│   │   ├── Settings/                 # Settings module components
│   │   │   ├── ProfileSettings.php
│   │   │   ├── SecuritySettings.php
│   │   │   └── SettingPage.php
│   │   │
│   │   ├── Shifts/                   # Shift module components
│   │   │   ├── ShiftCreate.php
│   │   │   ├── ShiftEdit.php
│   │   │   ├── ShiftIndex.php
│   │   │   └── ShiftShow.php
│   │   │
│   │   ├── Sites/                    # Sites module components
│   │   │   ├── SiteCreate.php
│   │   │   ├── SiteEdit.php
│   │   │   ├── SiteIndex.php
│   │   │   └── SiteShow.php
│   │   │
│   │   ├── Statistics/               # Statistics module components
│   │   │   ├── AgentStatistics.php
│   │   │   ├── CampaignStatistics.php
│   │   │   ├── DayStatistics.php
│   │   │   ├── GeneralStatistics.php
│   │   │   ├── KpiCharts.php
│   │   │   ├── PerformanceMonitoring.php
│   │   │   └── StatisticPage.php
│   │   │
│   │   ├── Training/                 # Training module components
│   │   │   ├── TrainingCreate.php
│   │   │   ├── TrainingEdit.php
│   │   │   ├── TrainingIndex.php
│   │   │   ├── TrainingPage.php
│   │   │   └── TrainingShow.php
│   │   │
│   │   └── Users/                    # User module components
│   │       ├── UserCreate.php
│   │       ├── UserEdit.php
│   │       ├── UserIndex.php
│   │       ├── UserPage.php
│   │       └── UserShow.php
│   │
│   ├── Models/                       # Database models
│   │   ├── Activity.php
│   │   ├── Appointment.php
│   │   ├── Campaign.php
│   │   ├── Customer.php
│   │   ├── Employee.php
│   │   ├── Expense.php
│   │   ├── Invoice.php
│   │   ├── Leave.php
│   │   ├── Payment.php
│   │   ├── Performance.php
│   │   ├── Permission.php
│   │   ├── Platform.php
│   │   ├── Recruitment.php
│   │   ├── Report.php
│   │   ├── Role.php
│   │   ├── Shift.php
│   │   ├── Site.php
│   │   ├── Training.php
│   │   └── User.php
│   │
│   ├── Providers/                    # Service providers
│   │   ├── AppServiceProvider.php
│   │   ├── AuthServiceProvider.php
│   │   ├── EventServiceProvider.php
│   │   └── RouteServiceProvider.php
│   │
│   └── Services/                     # Service classes
│       ├── AppointmentService.php
│       ├── ChartService.php
│       ├── ExportService.php
│       ├── PaymentService.php
│       ├── ReportService.php
│       ├── ShiftService.php
│       └── StatisticsService.php
│
├── bootstrap/                        # Bootstrap files
│   └── app.php                       # Application bootstrap
│
├── config/                           # Configuration files
│   ├── app.php
│   ├── auth.php
│   ├── database.php
│   ├── filesystems.php
│   ├── modules.php                   # Module configuration
│   ├── permission.php                # Spatie permissions config
│   └── services.php
│
├── database/                         # Database files
│   ├── factories/                    # Model factories
│   │   ├── AppointmentFactory.php
│   │   ├── CampaignFactory.php
│   │   ├── CustomerFactory.php
│   │   ├── PaymentFactory.php
│   │   ├── ReportFactory.php
│   │   ├── ShiftFactory.php
│   │   ├── SiteFactory.php
│   │   ├── TrainingFactory.php
│   │   └── UserFactory.php
│   │
│   ├── migrations/                   # Database migrations
│   │   ├── 2014_10_12_000000_create_users_table.php
│   │   ├── 2014_10_12_100000_create_password_reset_tokens_table.php
│   │   ├── 2019_08_19_000000_create_failed_jobs_table.php
│   │   ├── 2019_12_14_000001_create_personal_access_tokens_table.php
│   │   ├── 2023_01_01_000001_create_permission_tables.php
│   │   ├── 2023_01_01_000002_create_sites_table.php
│   │   ├── 2023_01_01_000003_create_platforms_table.php
│   │   ├── 2023_01_01_000004_create_campaigns_table.php
│   │   ├── 2023_01_01_000005_create_customers_table.php
│   │   ├── 2023_01_01_000006_create_appointments_table.php
│   │   ├── 2023_01_01_000007_create_reports_table.php
│   │   ├── 2023_01_01_000008_create_trainings_table.php
│   │   ├── 2023_01_01_000009_create_payments_table.php
│   │   ├── 2023_01_01_000010_create_activities_table.php
│   │   ├── 2024_01_01_000001_create_expenses_table.php
│   │   ├── 2024_01_01_000002_create_invoices_table.php
│   │   ├── 2024_01_01_000003_create_employees_table.php
│   │   ├── 2024_01_01_000004_create_leaves_table.php
│   │   ├── 2024_01_01_000005_create_performances_table.php
│   │   ├── 2024_01_01_000006_create_recruitments_table.php
│   │   ├── 2025_03_24_220400_create_shifts_table.php
│   │   └── 2025_06_03_000001_add_accountant_and_hr_to_sites_table.php
│   │
│   └── seeders/                      # Database seeders
│       ├── AppointmentSeeder.php
│       ├── CampaignSeeder.php
│       ├── CustomerSeeder.php
│       ├── DatabaseSeeder.php
│       ├── PaymentSeeder.php
│       ├── PermissionSeeder.php
│       ├── ReportSeeder.php
│       ├── RolePermissionSeeder.php
│       ├── RoleSeeder.php
│       ├── ShiftSeeder.php
│       ├── SiteSeeder.php
│       ├── TrainingSeeder.php
│       └── UserSeeder.php
│
├── public/                           # Public assets
│   ├── build/                        # Compiled assets
│   ├── css/                          # CSS files
│   ├── js/                           # JavaScript files
│   ├── images/                       # Image files
│   ├── fonts/                        # Font files
│   ├── favicon.ico                   # Favicon
│   └── index.php                     # Entry point
│
├── resources/                        # Frontend resources
│   ├── css/                          # CSS files
│   │   └── app.css                   # Main CSS file
│   │
│   ├── js/                           # JavaScript files
│   │   ├── app.js                    # Main JS file
│   │   ├── bootstrap.js             
│   │   └── components/               # JS components
│   │       ├── charts.js
│   │       └── datatable.js
│   │
│   └── views/                        # Blade templates
│       ├── auth/                     # Authentication views
│       │   ├── login.blade.php
│       │   ├── register.blade.php
│       │   └── reset-password.blade.php
│       │
│       ├── components/               # Blade components
│       │   ├── alerts/
│       │   │   ├── error.blade.php
│       │   │   └── success.blade.php
│       │   ├── buttons/
│       │   │   ├── primary.blade.php
│       │   │   └── secondary.blade.php
│       │   ├── forms/
│       │   │   ├── input.blade.php
│       │   │   └── select.blade.php
│       │   ├── layouts/
│       │   │   └── app.blade.php     # Main layout
│       │   └── modals/
│       │       ├── confirm.blade.php
│       │       └── form.blade.php
│       │
│       ├── errors/                   # Error views
│       │   ├── 403.blade.php
│       │   ├── 404.blade.php
│       │   └── 500.blade.php
│       │
│       ├── exports/                  # Export templates
│       │   ├── appointments.blade.php
│       │   ├── reports.blade.php
│       │   └── shifts.blade.php
│       │
│       ├── livewire/                 # Livewire views
│       │   ├── accountant/           # Accountant views
│       │   │   ├── accounting-dashboard.blade.php
│       │   │   ├── agent-attendance.blade.php
│       │   │   ├── payment-history.blade.php
│       │   │   ├── payment-management.blade.php
│       │   │   └── salary-calculation.blade.php
│       │   │
│       │   ├── accounting/           # Accounting views
│       │   │   ├── accounting-dashboard.blade.php
│       │   │   ├── expense-create.blade.php
│       │   │   ├── expense-edit.blade.php
│       │   │   ├── expense-index.blade.php
│       │   │   ├── invoice-create.blade