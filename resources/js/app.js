import { initFlowbite } from "flowbite";

// Initialize Flowbite on DOMContentLoaded
document.addEventListener("DOMContentLoaded", function() {
    initFlowbite();

    // Handle CSRF token errors - make sure Livewire is defined before using it
    if (typeof window.Livewire !== 'undefined') {
        // Livewire is already available, set up the hook immediately
        setupLivewireHooks();
    } else {
        // Wait for Livewire to initialize
        window.addEventListener('livewire:init', () => {
            setupLivewireHooks();
        });
    }

    // Function to set up Livewire hooks
    function setupLivewireHooks() {
        if (typeof window.Livewire === 'undefined') {
            console.warn('Livewire is not defined, cannot set up hooks');
            return;
        }

        // Add a custom CSRF token refresher
        window.Livewire.hook('request', ({ fail }) => {
            // Handle failed requests
            fail(({ status }) => {
                if (status === 419) {
                    console.log('CSRF token mismatch detected. Refreshing token...');

                    // Fetch a new CSRF token
                    fetch('/csrf-token')
                        .then(response => response.json())
                        .then(data => {
                            // Update the CSRF token meta tag
                            const tokenElement = document.querySelector('meta[name="csrf-token"]');
                            if (tokenElement) {
                                tokenElement.setAttribute('content', data.token);
                            }

                            // Also update any CSRF token form fields
                            document.querySelectorAll('input[name="_token"]').forEach(input => {
                                input.value = data.token;
                            });

                            console.log('CSRF token refreshed successfully');

                            // Reload the page to apply the new token
                            window.location.reload();
                        })
                        .catch(error => {
                            console.error('Failed to refresh CSRF token:', error);
                            window.location.reload();
                        });
                }
            });
        });
    }
});

// Reinitialize Flowbite after Livewire navigation
document.addEventListener("livewire:navigated", function () {
    console.log("Livewire navigation detected, reinitializing Flowbite");

    // Flowbite components need to be reinitialized after navigation
    setTimeout(function() {
        initFlowbite();
    }, 100);

    // Refresh CSRF token on navigation
    (function() {
        fetch('/csrf-token')
            .then(response => response.json())
            .then(data => {
                if (data && data.token) {
                    // Update the CSRF token meta tag
                    const tokenElement = document.querySelector('meta[name="csrf-token"]');
                    if (tokenElement) {
                        tokenElement.setAttribute('content', data.token);
                    }

                    // Also update any CSRF token form fields
                    document.querySelectorAll('input[name="_token"]').forEach(input => {
                        input.value = data.token;
                    });

                    console.log('CSRF token refreshed during navigation');
                }
            })
            .catch(error => {
                console.error('Failed to refresh CSRF token during navigation:', error);
            });
    })();

    // On page load or when changing themes, best to add inline in `head` to avoid FOUC
    if (
        localStorage.getItem("color-theme") === "dark" ||
        (!("color-theme" in localStorage) &&
            window.matchMedia("(prefers-color-scheme: dark)").matches)
    ) {
        document.documentElement.classList.add("dark");
    } else {
        document.documentElement.classList.remove("dark");
    }

    var themeToggleDarkIcon = document.getElementById("theme-toggle-dark-icon");
    var themeToggleLightIcon = document.getElementById(
        "theme-toggle-light-icon"
    );

    // Change the icons inside the button based on previous settings
    if (
        localStorage.getItem("color-theme") === "dark" ||
        (!("color-theme" in localStorage) &&
            window.matchMedia("(prefers-color-scheme: dark)").matches)
    ) {
        themeToggleLightIcon.classList.remove("hidden");
    } else {
        themeToggleDarkIcon.classList.remove("hidden");
    }

    var themeToggleBtn = document.getElementById("theme-toggle");

    themeToggleBtn.addEventListener("click", function () {
        // toggle icons inside button
        themeToggleDarkIcon.classList.toggle("hidden");
        themeToggleLightIcon.classList.toggle("hidden");

        // if set via local storage previously
        if (localStorage.getItem("color-theme")) {
            if (localStorage.getItem("color-theme") === "light") {
                document.documentElement.classList.add("dark");
                localStorage.setItem("color-theme", "dark");
            } else {
                document.documentElement.classList.remove("dark");
                localStorage.setItem("color-theme", "light");
            }

            // if NOT set via local storage previously
        } else {
            if (document.documentElement.classList.contains("dark")) {
                document.documentElement.classList.remove("dark");
                localStorage.setItem("color-theme", "light");
            } else {
                document.documentElement.classList.add("dark");
                localStorage.setItem("color-theme", "dark");
            }
        }
    });
});
