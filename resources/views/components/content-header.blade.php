@props(['title' => '', 'breadcrumb' => null, 'actions' => null])

<x-card key="content-header" class="p-4">
    @if($breadcrumb)
        {{ $breadcrumb }}
    @endif

    <div class="sm:flex">
        <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
            <h3 class="text-xl font-semibold dark:text-white">
                {{ $title }}
            </h3>
        </div>
        <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
            @if($actions)
                {{ $actions }}
            @else
                {{ $slot }}
            @endif
        </div>
    </div>
</x-card>
