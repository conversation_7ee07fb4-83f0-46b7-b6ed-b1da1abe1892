@props([
    'id',
    'name',
    'label',
    'model',
    'placeholder' => 'Select date',
    'format' => 'yyyy-mm-dd',
    'error' => null
])

<div {{ $attributes->class(['relative']) }}>
    <label for="{{ $id }}" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ $label }}</label>
    <div class="relative" wire:ignore>
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path clip-rule="evenodd" fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"></path>
            </svg>
        </div>
        <input 
            type="text" 
            id="{{ $id }}" 
            name="{{ $name }}"
            x-data
            x-init="
                flatpickr($el, {
                    dateFormat: '{{ $format }}',
                    allowInput: true,
                    onChange: function(selectedDates, dateStr) {
                        @this.set('{{ $model }}', dateStr);
                    }
                });
            "
            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 cursor-pointer"
            placeholder="{{ $placeholder }}"
            value="{{ @$this->{str_replace('form.', 'form->', $model)} }}"
        >
        <input type="hidden" wire:model="{{ $model }}">
    </div>
    @if($error)
        <span class="text-sm text-red-500">{{ $error }}</span>
    @endif
</div>
