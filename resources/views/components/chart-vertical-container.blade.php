    {{-- card header --}}
    <div class="flex items-center justify-between mb-4">
        <div class="flex-shrink-0">
            <h3 class="text-base font-light text-gray-500 dark:text-gray-400">{{ $data['title'] ?? 'Chart' }}</h3>
            <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white">{{ $data['count'] ?? 0 }}</span>
            <h3 class="text-base font-light text-gray-500 dark:text-gray-400">{{ $data['current_period'] ?? 'This period' }} this period</h3>
        </div>
        <div class="flex items-center justify-end flex-1 text-base font-medium {{ isset($data['growth_percentage']) && $data['growth_percentage'] >= 0 ? 'text-green-500 dark:text-green-400' : 'text-red-500 dark:text-red-400' }}">
            {{ isset($data['growth_percentage']) ? abs($data['growth_percentage']) : 0 }}%
            @if(isset($data['growth_percentage']) && $data['growth_percentage'] >= 0)
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                        d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                        clip-rule="evenodd"></path>
                </svg>
            @else
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                        d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"></path>
                </svg>
            @endif
        </div>
    </div>
    <!-- Card content -->
    {{ $slot }}
    <!-- Card Footer -->
    <div class="flex items-center justify-between pt-3 mt-4 border-t border-gray-200 sm:pt-6 dark:border-gray-700">
        <div>
            <button
                class="inline-flex items-center p-2 text-sm font-medium text-center text-gray-500 rounded-lg hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
                type="button"
                data-dropdown-toggle="{{ $toggleId }}"
            >
                @if(request()->query('period') == 'day')
                    Today
                @elseif(request()->query('period') == 'month')
                    This Month
                @elseif(request()->query('period') == 'year')
                    This Year
                @else
                    Last 7 days
                @endif
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
            </button>
            <!-- Dropdown menu -->
            <div
                class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600"
                id="{{ $toggleId }}"
            >
                <div class="px-4 py-3" role="none">
                <p class="text-sm font-medium text-gray-900 truncate dark:text-white" role="none">
                    Select Time Period
                </p>
                </div>
                <ul class="py-1" role="none">
                <li>
                    <a href="{{ request()->fullUrlWithQuery(['period' => 'day']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">Today</a>
                </li>
                <li>
                    <a href="{{ request()->fullUrlWithQuery(['period' => 'week']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">Last 7 days</a>
                </li>
                <li>
                    <a href="{{ request()->fullUrlWithQuery(['period' => 'month']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">This Month</a>
                </li>
                <li>
                    <a href="{{ request()->fullUrlWithQuery(['period' => 'year']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">This Year</a>
                </li>
                </ul>
            </div>
        </div>
        <div class="flex-shrink-0">
            <a href="#" class="inline-flex items-center p-2 text-xs font-medium uppercase rounded-lg text-primary-700 sm:text-sm hover:bg-gray-100 dark:text-primary-500 dark:hover:bg-gray-700">
            Access {{ $data['title'] ?? 'Chart' }}
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
            </a>
        </div>
    </div>
