@props(['title' => 'Filters', 'content' => null, 'pages' => [], 'current_page' => [], 'section' => []])

<aside class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform -translate-x-full bg-white border-r border-gray-200 md:translate-x-0 dark:bg-gray-800 dark:border-gray-700" aria-label="Sidenav" id="drawer-navigation">
    <div class="overflow-y-auto py-5 px-3 h-full bg-white dark:bg-gray-800">
        <div class="flex items-center mb-4 space-x-2">
            @if(isset($section['icon']))
                <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" aria-hidden="true" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    {!! $section['icon'] !!}
                </svg>
            @else
                <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" aria-hidden="true" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"></path>
                </svg>
            @endif
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $section['title'] ?? $title ?? 'Filters' }}</h2>
        </div>
        @if($content)
            {{ $content }}
        @else
            <ul class="space-y-2">
                @if(isset($pages) && is_array($pages))
                    @foreach ($pages as $page)
                    <li>
                        <a href="{{ isset($page['route']) ? route($page['route']) : '#' }}" class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group {{ isset($current_page['component']) && isset($page['component']) && $current_page['component'] === $page['component'] ? 'bg-gray-100 dark:bg-gray-700' : '' }}">
                            <svg class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white {{ isset($current_page['component']) && isset($page['component']) && $current_page['component'] === $page['component'] ? 'text-gray-900 dark:text-white' : '' }}" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                @if(isset($page['icon']))
                                    {!! $page['icon'] !!}
                                @else
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"></path>
                                @endif
                            </svg>
                            <span class="ml-3">{{ $page['name'] ?? 'Page' }}</span>
                        </a>
                    </li>
                    @endforeach
                @endif
            </ul>
        @endif
    </div>
</aside>
