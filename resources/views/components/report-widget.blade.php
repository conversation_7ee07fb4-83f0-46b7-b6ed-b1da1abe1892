    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Latest Reports</h3>
        <a href="{{ route('reports.index') }}" class="inline-flex items-center p-2 text-sm font-medium rounded-lg text-primary-700 hover:bg-gray-100 dark:text-primary-500 dark:hover:bg-gray-700">
            View all
        </a>
    </div>
    <!-- Reports List -->
    <div class="overflow-y-auto lg:max-h-[60rem] 2xl:max-h-fit p-4">
        @if(isset($reportData['latest_reports']) && $reportData['latest_reports']->count() > 0)
            @foreach($reportData['latest_reports'] as $report)
                <article class="mb-5 pb-5 border-b border-gray-200 dark:border-gray-700">
                    <footer class="flex items-center justify-between mb-2">
                        <div class="flex items-center">
                            <p class="inline-flex items-center mr-3 text-sm font-semibold text-gray-900 dark:text-white">
                                @if($report->creator)
                                    <img class="w-6 h-6 mr-2 rounded-full" src="https://avatar.iran.liara.run/public/{{$report->creator->id}}" alt="{{$report->creator->getFullNameAttribute()}} avatar">
                                    {{$report->creator->getFullNameAttribute()}}
                                @else
                                    <span class="w-6 h-6 mr-2 rounded-full bg-gray-200 dark:bg-gray-700"></span>
                                    Unknown User
                                @endif
                            </p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                <time pubdate datetime="{{ $report->created_at }}" title="{{ $report->created_at->format('F j, Y') }}">
                                    {{ $report->created_at->diffForHumans() }}
                                </time>
                            </p>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{
                                $report->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                ($report->status === 'sent' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300')
                            }}">
                                {{ ucfirst($report->status) }}
                            </span>
                        </div>
                    </footer>

                    <div class="mb-2">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                            Campaign: <span class="font-normal">{{ $report->campaign ? $report->campaign->name : 'Unknown Campaign' }}</span>
                        </h4>
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                            Date: <span class="font-normal">{{ $report->date ? $report->date->format('M d, Y') : 'Not specified' }}</span>
                        </h4>
                    </div>

                    <p class="mb-2 text-gray-900 dark:text-white line-clamp-2">
                        {{ $report->content }}
                    </p>

                    <div class="flex justify-end">
                        <a href="{{ route('reports.show', $report->id) }}" class="inline-flex items-center text-xs font-medium text-primary-700 dark:text-primary-500 hover:underline">
                            View Report
                            <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </article>
            @endforeach
        @else
            <div class="flex items-center justify-center h-40">
                <p class="text-gray-500 dark:text-gray-400">No reports available</p>
            </div>
        @endif
    </div>

    <!-- Report Stats -->
    <div class="mt-4 grid grid-cols-2 gap-4">
        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Reports</h5>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $reportData['count'] ?? 0 }}</p>
        </div>
        <div class="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">This Period</h5>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $reportData['current_period'] ?? 0 }}</p>
        </div>
    </div>
