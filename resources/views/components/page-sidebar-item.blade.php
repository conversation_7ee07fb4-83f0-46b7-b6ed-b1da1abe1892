<li>
    @php
        $url = '#';
        $useWireNavigate = true;
        $wireClick = null;

        try {
            // Check if the route is a sites or departments route
            if (strpos($route, 'call-centers.sites') !== false || strpos($route, 'call-centers.departments') !== false) {
                // Get the current call center ID from the route parameters
                $callCenter = request()->route('callCenter');

                // If we have a call center ID, generate the route with it
                if ($callCenter) {
                    // Handle both object and numeric ID cases
                    if (is_object($callCenter) && method_exists($callCenter, 'getKey')) {
                        $callCenterId = $callCenter->getKey();
                        $url = route($route, ['callCenter' => $callCenterId]);
                    } elseif (is_numeric($callCenter)) {
                        $callCenterId = $callCenter;
                        $url = route($route, ['callCenter' => $callCenterId]);
                    } else {
                        // Fallback to call centers index
                        $url = route('call-centers.index');
                    }
                } else {
                    // Check the current route
                    $currentRoute = request()->route()->getName();

                    if ($currentRoute === 'call-centers.show') {
                        // If we're on a call center show page, we can use a wire:click event
                        $url = '#';
                        $useWireNavigate = false;

                        // Determine which event to dispatch based on the route
                        if (strpos($route, 'call-centers.sites') !== false) {
                            $wireClick = '$dispatch(\'to-call-center-sites\')';
                        } elseif (strpos($route, 'call-centers.departments') !== false) {
                            $wireClick = '$dispatch(\'to-call-center-departments\')';
                        }
                    } elseif ($currentRoute === 'call-centers.index') {
                        // If we're on the call centers index page, use a special event to prompt user to select a call center first
                        $url = 'javascript:void(0)';
                        $useWireNavigate = false;
                        $wireClick = 'console.log(\'Dispatching select-call-center-first event\'); $dispatch(\'select-call-center-first\')';
                    } else {
                        // For other routes without a call center ID, return to the call centers index
                        $url = route('call-centers.index');
                    }
                }
            }
            // For routes that require edit, show, or delete parameters, return a hash
            elseif (strpos($route, 'edit') !== false || strpos($route, 'show') !== false || strpos($route, 'delete') !== false) {
                $url = '#';
                $useWireNavigate = false;
            }
            // For all other routes, generate the route normally
            else {
                $url = route($route);
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            \Illuminate\Support\Facades\Log::error('Error generating URL for route: ' . $route . ' - ' . $e->getMessage());
            $url = '#';
            $useWireNavigate = false;
        }
    @endphp

    @php
        // Ensure currentRoute is a string for comparison
        $currentRouteStr = is_string($currentRoute) ? $currentRoute : '';
        $routeStr = is_string($route) ? $route : '';
        $isActive = $currentRouteStr == $routeStr || (is_array($routes) && in_array($currentRouteStr, $routes));
    @endphp

    <a
        href="{{ $url }}"
        class="flex items-start p-2 text-base font-medium rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group {{ $isActive ? 'bg-gray-100 dark:bg-gray-700' : 'text-gray-900' }}"
        @if($useWireNavigate)
        wire:navigate
        @endif
        @if($wireClick)
        wire:click.prevent="{!! $wireClick !!}"
        @endif
        @if($currentRoute === 'call-centers.index' && (strpos($route, 'call-centers.sites') !== false || strpos($route, 'call-centers.departments') !== false))
        onclick="event.preventDefault(); console.log('Direct click handler'); window.dispatchEvent(new CustomEvent('show-notification', { detail: { type: 'info', title: 'Action Required', message: 'Please select a call center first to access its sites or departments.' } })); alert('Please select a call center first to access its sites or departments.');"
        @endif
    >
        <span class="flex-1 ml-1 text-sm whitespace-nowrap">{{ __($title) }}</span>
        @if(isset($bage))
            @if($bage != '')
                <span
                    class="inline-flex justify-center items-center w-6 h-6 text-xs font-semibold rounded-full text-primary-800 bg-primary-100 dark:bg-primary-200 dark:text-primary-800"
                >
                    {{ $bage }}
                </span>
            @endif
        @endif
    </a>
</li>
