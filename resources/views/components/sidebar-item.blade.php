@if($display)
<li>
    <a
        href="{{ route($routes[0]) }}"
        class="flex items-center p-2 text-base font-medium rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group {{ in_array(request()->route()->getName(), $routes) ? 'bg-gray-100 dark:bg-gray-700' : 'text-gray-900' }}"
        wire:navigate
    >
        <svg
            aria-hidden="true"
            class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
        >
            {!! $icon !!}
        </svg>
        <span class="flex-1 ml-3 whitespace-nowrap">{{ __($title) }}</span>
        @if(isset($badge))
            @if($badge != '' && $badge != 0)
                <span
                    class="inline-flex justify-center items-center w-6 h-6 text-xs font-semibold rounded-full text-primary-800 bg-primary-100 dark:bg-primary-200 dark:text-primary-800"
                >
                    {{ $badge }}
                </span>
            @endif
        @endif
    </a>
</li>
@endif
