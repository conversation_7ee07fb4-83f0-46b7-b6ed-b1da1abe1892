<div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Latest Activity</h3>
    <a href="{{ route('statistics.general') }}" class="inline-flex items-center p-2 text-sm font-medium rounded-lg text-primary-700 hover:bg-gray-100 dark:text-primary-500 dark:hover:bg-gray-700">
        View all
    </a>
</div>
<ol class="relative border-l border-gray-200 dark:border-gray-700">
    @forelse($activities ?? [] as $activity)
    <li class="mb-6 ml-4">
        <div class="absolute w-3 h-3 bg-primary-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-primary-900"></div>
        <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">{{ $activity['date'] }}</time>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $activity['title'] }}</h3>
        <p class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">{{ $activity['description'] }}</p>
        <a href="{{ $activity['link'] }}" class="inline-flex items-center text-xs font-medium hover:underline text-primary-700 sm:text-sm dark:text-primary-500">
            {{ $activity['link_text'] }}
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
        </a>
    </li>
    @empty
    <li class="mb-6 ml-4">
        <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
        <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">{{ now()->format('M d, Y') }}</time>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">No recent activity</h3>
        <p class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">There has been no recent activity in the system.</p>
    </li>
    @endforelse
</ol>
