<x-content>
    <x-content-header title="Site Statistics">
        <div class="flex space-x-2">
            <x-page-button type="export" label="Export Data" action="exportData" />
        </div>
    </x-content-header>

    <x-content-body>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Total Platforms</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ $platformCount }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5 0H1a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V1a1 1 0 0 0-1-1Zm14 0h-4a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V1a1 1 0 0 0-1-1ZM5 14H1a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1Zm14 0h-4a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1ZM12 2H8a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1Zm0 14H8a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1ZM3 8a1 1 0 0 0-1-1H1a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V8Zm18 0a1 1 0 0 0-1-1h-1a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V8Zm-12 0a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V8Zm6 0a1 1 0 0 0-1-1h-1a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1V8Z"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Total Equipment</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ $equipmentCount }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 dark:bg-green-900">
                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M19.472 9.541a.5.5 0 0 0-.472-.541H3.1a.5.5 0 0 0-.39.812L5 12.764V18a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-5.236l2.286-2.952a.5.5 0 0 0 .186-.271ZM14.5 16h-7a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1Zm0-3h-7a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1ZM16.525 5.9l-1.623-4.064A2 2 0 0 0 13.073 0H6.927a2 2 0 0 0-1.829 1.836L3.475 5.9A1.5 1.5 0 0 0 3.1 7h15.8a1.5 1.5 0 0 0-2.375-1.1Z"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Personnel</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ $personnelCount }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900">
                        <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 18">
                            <path d="M14 2a3.963 3.963 0 0 0-1.4.267 6.439 6.439 0 0 1-1.331 6.638A4 4 0 1 0 14 2Zm1 9h-1.264A6.957 6.957 0 0 1 15 15v2a2.97 2.97 0 0 1-.184 1H19a1 1 0 0 0 1-1v-1a5.006 5.006 0 0 0-5-5ZM6.5 9a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9ZM8 10H5a5.006 5.006 0 0 0-5 5v2a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-2a5.006 5.006 0 0 0-5-5Z"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Reports</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ $site && method_exists($site, 'reports') ? $site->reports->count() : 0 }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-orange-100 dark:bg-orange-900">
                        <svg class="w-6 h-6 text-orange-600 dark:text-orange-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 20">
                            <path d="M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM10.5 6a1.5 1.5 0 1 1 0 2.999A1.5 1.5 0 0 1 10.5 6Zm2.221 10.515a1 1 0 0 1-.858.485h-8a1 1 0 0 1-.9-1.43L5.6 10.039a.978.978 0 0 1 .936-.57 1 1 0 0 1 .9.632l1.181 2.981.541-1a.945.945 0 0 1 .883-.522 1 1 0 0 1 .879.529l1.832 3.438a1 1 0 0 1-.031.988Z"/>
                            <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Platform Distribution</h3>
                </div>
                <div id="platform-chart" class="h-80"></div>
            </div>
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Equipment Status</h3>
                </div>
                <div id="equipment-chart" class="h-80"></div>
            </div>
        </div>
    </x-content-body>

    @push('scripts')
    <script>
        document.addEventListener('livewire:initialized', function () {
            // Platform Distribution Chart
            const platformTypes = @json($platformTypes ?? []);
            const platformData = {
                labels: Object.keys(platformTypes).length > 0 ? Object.keys(platformTypes) : ['No Data'],
                values: Object.keys(platformTypes).length > 0 ? Object.values(platformTypes) : [1]
            };

            if (document.getElementById('platform-chart')) {
                const platformChart = new ApexCharts(document.getElementById('platform-chart'), {
                    series: platformData.values,
                    chart: {
                        type: 'donut',
                        height: 320,
                    },
                    labels: platformData.labels,
                    colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#9061F9', '#E74694', '#34D399'],
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Inter, sans-serif',
                        fontSize: '14px',
                        fontWeight: 500,
                        labels: {
                            colors: window.theme === 'dark' ? '#FFFFFF' : '#111827',
                        },
                        itemMargin: {
                            horizontal: 10,
                            vertical: 5
                        },
                    },
                    dataLabels: {
                        enabled: false
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '65%',
                                background: 'transparent',
                            }
                        }
                    },
                    stroke: {
                        width: 2,
                        colors: window.theme === 'dark' ? ['#1F2937'] : ['#FFFFFF'],
                    },
                    tooltip: {
                        shared: true,
                        followCursor: false,
                        theme: window.theme === 'dark' ? 'dark' : 'light',
                    },
                });
                platformChart.render();
            }

            // Equipment Status Chart
            const equipmentStatus = @json($equipmentStatus ?? []);
            const equipmentData = {
                labels: Object.keys(equipmentStatus).length > 0 ? Object.keys(equipmentStatus) : ['No Data'],
                values: Object.keys(equipmentStatus).length > 0 ? Object.values(equipmentStatus) : [1]
            };

            if (document.getElementById('equipment-chart')) {
                const equipmentChart = new ApexCharts(document.getElementById('equipment-chart'), {
                    series: equipmentData.values,
                    chart: {
                        type: 'donut',
                        height: 320,
                    },
                    labels: equipmentData.labels,
                    colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#9061F9', '#E74694'],
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Inter, sans-serif',
                        fontSize: '14px',
                        fontWeight: 500,
                        labels: {
                            colors: window.theme === 'dark' ? '#FFFFFF' : '#111827',
                        },
                        itemMargin: {
                            horizontal: 10,
                            vertical: 5
                        },
                    },
                    dataLabels: {
                        enabled: false
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '65%',
                                background: 'transparent',
                            }
                        }
                    },
                    stroke: {
                        width: 2,
                        colors: window.theme === 'dark' ? ['#1F2937'] : ['#FFFFFF'],
                    },
                    tooltip: {
                        shared: true,
                        followCursor: false,
                        theme: window.theme === 'dark' ? 'dark' : 'light',
                    },
                });
                equipmentChart.render();
            }
        });
    </script>
    @endpush
</x-content>
