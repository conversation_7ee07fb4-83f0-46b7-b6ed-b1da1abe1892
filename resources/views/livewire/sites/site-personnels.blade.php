<x-content>
    <x-content-header title="Site Personnel">
        <div class="flex space-x-2">
            <x-page-button type="add" label="Assign Personnel" action="$dispatch('to-site-personnel-assign', { site: {{ $site ? $site->id : 'null' }} })" />
        </div>
    </x-content-header>

    <x-content-body>
        <div class="grid grid-cols-1 gap-4 mb-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h2 class="text-xl font-semibold mb-4">Personnel Management</h2>
                <p class="text-gray-500 dark:text-gray-400 mb-4">This module allows you to manage personnel assigned to this site, including assigning new personnel and viewing personnel details.</p>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-4 py-3">Name</th>
                                <th scope="col" class="px-4 py-3">Position</th>
                                <th scope="col" class="px-4 py-3">Department</th>
                                <th scope="col" class="px-4 py-3">Status</th>
                                <th scope="col" class="px-4 py-3">Assigned Date</th>
                                <th scope="col" class="px-4 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if($site)
                                @forelse($site->personnel as $person)
                                <tr class="border-b dark:border-gray-700">
                                    <td class="px-4 py-3">{{ $person->name }}</td>
                                    <td class="px-4 py-3">{{ $person->position }}</td>
                                    <td class="px-4 py-3">{{ $person->department }}</td>
                                    <td class="px-4 py-3">
                                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                            {{ ucfirst($person->status) }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3">{{ $person->pivot->created_at->format('M d, Y') }}</td>
                                    <td class="px-4 py-3">
                                        <div class="flex items-center space-x-2">
                                            <button class="text-blue-600 dark:text-blue-500 hover:underline">View</button>
                                            <button class="text-red-600 dark:text-red-500 hover:underline">Remove</button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="px-4 py-3 text-center">No personnel assigned to this site.</td>
                                </tr>
                                @endforelse
                            @else
                                <tr>
                                    <td colspan="6" class="px-4 py-3 text-center">Please select a site to view personnel.</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
