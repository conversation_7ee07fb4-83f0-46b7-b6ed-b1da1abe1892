<x-content>
    <x-content-header title="{{ $site->name }}">
        <x-page-button type="edit" label="Edit" action="$dispatch('to-site-edit', { site: {{ $site->id }} })"/>
        <x-page-button type="delete" label="Delete" action="$dispatch('to-site-delete', { site: {{ $site->id }} })"/>
        <x-page-button type="add" label="Add Platform" action="$dispatch('to-platform-create', { site: {{ $site->id }} })"/>
        <x-page-button type="back" label="Back" action="$dispatch('to-site-index')"/>
    </x-content-header>
    <x-content-body>
        {{-- Site Information --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Site Information</h3>
            <div class="grid grid-cols-6 gap-6">
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Site Name</label>
                    <p class="text-gray-900 dark:text-white">{{ $site->name }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Location</label>
                    <p class="text-gray-900 dark:text-white">{{ $site->location }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Call Center</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($site->callCenter)
                            <a href="{{ route('call-centers.show', $site->callCenter) }}" class="text-primary-600 hover:underline dark:text-primary-500">
                                {{ $site->callCenter->name }}
                            </a>
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                    <span class="bg-{{ $site->status === 'active' ? 'green' : 'red' }}-100 text-{{ $site->status === 'active' ? 'green' : 'red' }}-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-{{ $site->status === 'active' ? 'green' : 'red' }}-900 dark:text-{{ $site->status === 'active' ? 'green' : 'red' }}-300">
                        {{ ucfirst($site->status) }}
                    </span>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Capacity</label>
                    <p class="text-gray-900 dark:text-white">{{ $site->capacity ?? '0' }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contact Information</label>
                    <p class="text-gray-900 dark:text-white">
                        {{ $site->contact_email ?? 'No email provided' }}<br>
                        {{ $site->contact_phone ?? 'No phone provided' }}
                    </p>
                </div>
                <div class="col-span-6">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                    <p class="text-gray-900 dark:text-white">{{ $site->description ?? 'No description provided' }}</p>
                </div>
            </div>
        </div>

        {{-- Site Personnel --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Site Personnel</h3>
            <div class="grid grid-cols-6 gap-6">
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Site Manager</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($site->manager)
                            {{ $site->manager->first_name }} {{ $site->manager->last_name }}
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IT Manager</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($site->itManager)
                            {{ $site->itManager->first_name }} {{ $site->itManager->last_name }}
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Trainer</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($site->trainer)
                            {{ $site->trainer->first_name }} {{ $site->trainer->last_name }}
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Accountant</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($site->accountant)
                            {{ $site->accountant->first_name }} {{ $site->accountant->last_name }}
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">HR Manager</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($site->hrManager)
                            {{ $site->hrManager->first_name }} {{ $site->hrManager->last_name }}
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
            </div>
        </div>

        {{-- Platforms List --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="mb-4 flex justify-between items-center">
                <h3 class="text-xl font-semibold dark:text-white">Platforms</h3>
                <x-page-button type="add" label="Add Platform" action="$dispatch('to-platform-create', { site: {{ $site->id }} })"/>
            </div>

            @if($site->platforms->count() > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-4 py-3">Name</th>
                                <th scope="col" class="px-4 py-3">Type</th>
                                <th scope="col" class="px-4 py-3">Status</th>
                                <th scope="col" class="px-4 py-3">Manager</th>
                                <th scope="col" class="px-4 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($site->platforms as $platform)
                                <tr class="border-b dark:border-gray-700">
                                    <td class="px-4 py-3">{{ $platform->name }}</td>
                                    <td class="px-4 py-3 capitalize">{{ $platform->type }}</td>
                                    <td class="px-4 py-3">
                                        <span class="bg-{{ $platform->status === 'active' ? 'green' : 'red' }}-100 text-{{ $platform->status === 'active' ? 'green' : 'red' }}-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-{{ $platform->status === 'active' ? 'green' : 'red' }}-900 dark:text-{{ $platform->status === 'active' ? 'green' : 'red' }}-300">
                                            {{ ucfirst($platform->status) }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3">
                                        {{ $platform->manager ? $platform->manager->first_name . ' ' . $platform->manager->last_name : 'Not assigned' }}
                                    </td>
                                    <td class="px-4 py-3">
                                        <x-dropdown label="Actions" align="right">
                                            <x-dropdown-item wire:click="$dispatch('to-platform-show', { platform: {{ $platform->id }} })">
                                                View
                                            </x-dropdown-item>
                                            <x-dropdown-item wire:click="$dispatch('to-platform-edit', { platform: {{ $platform->id }} })">
                                                Edit
                                            </x-dropdown-item>
                                            <x-dropdown-item wire:click="$dispatch('to-platform-delete', { platform: {{ $platform->id }} })">
                                                Delete
                                            </x-dropdown-item>
                                        </x-dropdown>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="p-4 text-center">
                    <p class="text-gray-500 dark:text-gray-400">No platforms found for this site.</p>
                    <button wire:click="$dispatch('to-platform-create', { site: {{ $site->id }} })" class="mt-4 text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                        Add Platform
                    </button>
                </div>
            @endif
        </div>
    </x-content-body>
</x-content>
