<x-content>
    <form wire:submit="submit" class="space-y-8">
        <x-content-header title="Create Site">
            <x-page-button type="save" label="Save" action="submit"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-site-index')"/>
        </x-content-header>
        <x-content-body>
            {{-- Site Information --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Site Information</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Site Name</label>
                        <input type="text" wire:model="form.name" id="form.name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter site name" required>
                        @error('form.name') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.location" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Location</label>
                        <input type="text" wire:model="form.location" id="form.location" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter site location" required>
                        @error('form.location') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.call_center_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Call Center</label>
                        <select wire:model="form.call_center_id" id="form.call_center_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select call center</option>
                            @foreach($callCenters as $callCenter)
                                <option value="{{ $callCenter->id }}">{{ $callCenter->name }}</option>
                            @endforeach
                        </select>
                        @error('form.call_center_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                        <select wire:model="form.status" id="form.status" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                        @error('form.status') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.capacity" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Capacity</label>
                        <input type="number" wire:model="form.capacity" id="form.capacity" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter site capacity">
                        @error('form.capacity') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.contact_email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contact Email</label>
                        <input type="email" wire:model="form.contact_email" id="form.contact_email" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter contact email">
                        @error('form.contact_email') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.contact_phone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contact Phone</label>
                        <input type="text" wire:model="form.contact_phone" id="form.contact_phone" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter contact phone">
                        @error('form.contact_phone') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6">
                        <label for="form.description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                        <textarea wire:model="form.description" id="form.description" rows="4" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Site description"></textarea>
                        @error('form.description') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                </div>
            </div>

            {{-- Site Personnel --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Site Personnel</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.manager_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Site Manager</label>
                        <select wire:model="form.manager_id" id="form.manager_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select manager</option>
                            @foreach($managers as $manager)
                                <option value="{{ $manager->id }}">{{ $manager->first_name }} {{ $manager->last_name }}</option>
                            @endforeach
                        </select>
                        @error('form.manager_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.it_manager_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IT Manager</label>
                        <select wire:model="form.it_manager_id" id="form.it_manager_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select IT manager</option>
                            @foreach($itManagers as $itManager)
                                <option value="{{ $itManager->id }}">{{ $itManager->first_name }} {{ $itManager->last_name }}</option>
                            @endforeach
                        </select>
                        @error('form.it_manager_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.trainer_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Trainer</label>
                        <select wire:model="form.trainer_id" id="form.trainer_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select trainer</option>
                            @foreach($trainers as $trainer)
                                <option value="{{ $trainer->id }}">{{ $trainer->first_name }} {{ $trainer->last_name }}</option>
                            @endforeach
                        </select>
                        @error('form.trainer_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.accountant_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Accountant</label>
                        <select wire:model="form.accountant_id" id="form.accountant_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select accountant</option>
                            @foreach($accountants as $accountant)
                                <option value="{{ $accountant->id }}">{{ $accountant->first_name }} {{ $accountant->last_name }}</option>
                            @endforeach
                        </select>
                        @error('form.accountant_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="form.hr_manager_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">HR Manager</label>
                        <select wire:model="form.hr_manager_id" id="form.hr_manager_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select HR manager</option>
                            @foreach($hrManagers as $hrManager)
                                <option value="{{ $hrManager->id }}">{{ $hrManager->first_name }} {{ $hrManager->last_name }}</option>
                            @endforeach
                        </select>
                        @error('form.hr_manager_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
