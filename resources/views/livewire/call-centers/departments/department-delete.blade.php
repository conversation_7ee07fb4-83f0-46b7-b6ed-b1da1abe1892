<div>
    <div class="mb-4 flex flex-col md:flex-row items-start md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Delete Department</h2>
            <p class="mt-2 text-base text-gray-500 dark:text-gray-400">Confirm deletion of {{ $department->name }}</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{{ route('call-centers.departments.show', ['callCenter' => $callCenter->id, 'department' => $department->id]) }}" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                Cancel
            </a>
        </div>
    </div>

    @if(session()->has('error'))
    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
        {{ session('error') }}
    </div>
    @endif

    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <div class="flex items-center p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
            <svg class="flex-shrink-0 inline w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
            </svg>
            <span class="sr-only">Warning</span>
            <div>
                <span class="font-medium">Warning!</span> This action cannot be undone. All data associated with this department will be permanently deleted.
            </div>
        </div>

        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Department Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                    <p class="text-base text-gray-900 dark:text-white">{{ $department->name }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Code</p>
                    <p class="text-base text-gray-900 dark:text-white">{{ $department->code }}</p>
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</p>
                    @if ($department->status === 'active')
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Active</span>
                    @else
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Inactive</span>
                    @endif
                </div>
                
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Manager</p>
                    <p class="text-base text-gray-900 dark:text-white">{{ $department->manager ? $department->manager->full_name : 'Not assigned' }}</p>
                </div>
            </div>
            
            <div class="flex items-center p-4 mb-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300" role="alert">
                <svg class="flex-shrink-0 inline w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                <span class="sr-only">Info</span>
                <div>
                    <span class="font-medium">Note:</span> You cannot delete a department that has sub-departments or employees. Please remove all sub-departments and reassign all employees first.
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 dark:bg-gray-800 dark:border-gray-700">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Sub-Departments</h4>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $childDepartmentsCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        @if($childDepartmentsCount > 0)
                            <span class="text-red-500">Must be 0 to delete</span>
                        @else
                            <span class="text-green-500">Ready for deletion</span>
                        @endif
                    </p>
                </div>
                
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 dark:bg-gray-800 dark:border-gray-700">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Employees</h4>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $usersCount }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        @if($usersCount > 0)
                            <span class="text-red-500">Must be 0 to delete</span>
                        @else
                            <span class="text-green-500">Ready for deletion</span>
                        @endif
                    </p>
                </div>
            </div>
        </div>
        
        <div class="flex justify-end">
            <button wire:click="delete" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 focus:outline-none dark:focus:ring-red-800">
                Delete Department
            </button>
        </div>
    </div>
</div>
