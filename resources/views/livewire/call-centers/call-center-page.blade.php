{{-- Page --}}
<div>
    {{-- Page header --}}
    <livewire:global.page-header :currentRoute="$current_route" :currentPage="$current_page" :currentModule="$current_module" :currentPageSection="$current_page_section"/>
    <div class="flex items-start max-md:flex-col w-full">
        {{-- Page sidebar --}}
        <livewire:global.page-sidebar :pages="$pages" :currentRoute="$current_route"/>
        <hr class="md:hidden w-full mb-4 border-t border-gray-200 dark:border-gray-700" />
        <div class="flex-1 self-stretch">
            <div class="w-full md:px-0">
                <div class="grid grid-cols-1 xl:grid-cols-12 xl:gap-4 dark:bg-gray-900">
                    <div class="xl:col-span-3 flex flex-col gap-4">
                        {{-- Dynamic Page Resume --}}
                        <livewire:global.page-resume-dynamic
                            :contentType="$resumeContentType"
                            :entityData="$resumeData"
                            :title="$resumeTitle"
                            :description="$resumeDescription"
                        />
                    </div>
                    <div class="xl:col-span-9">
                        {{-- Page content --}}
                        <x-content>
                            <x-content-body>
                                {{-- Call Center Components --}}
                                {{-- Try to match component with route first --}}
                                @if($current_route === 'call-centers.index')
                                    <livewire:call-centers.dashboard />
                                @elseif($current_route === 'call-centers.list')
                                    <livewire:call-centers.call-center-index />
                                @elseif($current_route === 'call-centers.create')
                                    <livewire:call-centers.call-center-create />
                                @elseif($current_route === 'call-centers.edit' && isset($callCenter))
                                    <livewire:call-centers.call-center-edit :callCenter="$callCenter" />
                                @elseif($current_route === 'call-centers.show' && isset($callCenter) && is_object($callCenter) && $callCenter instanceof \App\Models\CallCenter)
                                    <livewire:call-centers.call-center-show :callCenter="$callCenter" />
                                @elseif($current_route === 'call-centers.delete' && isset($callCenter))
                                    <livewire:call-centers.call-center-delete :callCenter="$callCenter" />
                                @elseif($current_route === 'call-centers.sites.index' && isset($callCenter))
                                    <livewire:call-centers.sites.site-index :callCenter="$callCenter" />
                                @elseif($current_route === 'call-centers.sites.create' && isset($callCenter))
                                    <livewire:call-centers.sites.site-create :callCenter="$callCenter" />
                                @elseif($current_route === 'call-centers.sites.edit' && isset($callCenter) && isset($site))
                                    <livewire:call-centers.sites.site-edit :callCenter="$callCenter" :site="$site" />
                                @elseif($current_route === 'call-centers.sites.show' && isset($callCenter) && isset($site))
                                    <livewire:call-centers.sites.site-show :callCenter="$callCenter" :site="$site" />
                                @elseif($current_route === 'call-centers.sites.delete' && isset($callCenter) && isset($site))
                                    <livewire:call-centers.sites.site-delete :callCenter="$callCenter" :site="$site" />
                                @elseif($current_route === 'call-centers.departments.index' && isset($callCenter))
                                    <livewire:call-centers.departments.department-index :callCenter="$callCenter" />
                                @elseif($current_route === 'call-centers.departments.create' && isset($callCenter))
                                    <livewire:call-centers.departments.department-create :callCenter="$callCenter" />
                                @elseif($current_route === 'call-centers.departments.edit' && isset($callCenter) && isset($department))
                                    <livewire:call-centers.departments.department-edit :callCenter="$callCenter" :department="$department" />
                                @elseif($current_route === 'call-centers.departments.show' && isset($callCenter) && isset($department))
                                    <livewire:call-centers.departments.department-show :callCenter="$callCenter" :department="$department" />
                                @elseif($current_route === 'call-centers.departments.delete' && isset($callCenter) && isset($department))
                                    <livewire:call-centers.departments.department-delete :callCenter="$callCenter" :department="$department" />

                                {{-- If route matching fails, try component matching --}}
                                @elseif($component === 'call-centers.dashboard')
                                    <livewire:call-centers.dashboard />
                                @elseif($component === 'call-centers.call-center-index')
                                    <livewire:call-centers.call-center-index />
                                @elseif($component === 'call-centers.call-center-create')
                                    <livewire:call-centers.call-center-create />
                                @elseif($component === 'call-centers.call-center-edit')
                                    <livewire:call-centers.call-center-edit :callCenter="$callCenter" />
                                @elseif($component === 'call-centers.call-center-show' && isset($callCenter) && is_object($callCenter) && $callCenter instanceof \App\Models\CallCenter)
                                    <livewire:call-centers.call-center-show :callCenter="$callCenter" />
                                @elseif($component === 'call-centers.call-center-delete')
                                    <livewire:call-centers.call-center-delete :callCenter="$callCenter" />
                                @elseif($component === 'call-centers.sites.site-index')
                                    <livewire:call-centers.sites.site-index :callCenter="$callCenter" />
                                @elseif($component === 'call-centers.sites.site-create')
                                    <livewire:call-centers.sites.site-create :callCenter="$callCenter" />
                                @elseif($component === 'call-centers.sites.site-edit')
                                    <livewire:call-centers.sites.site-edit :callCenter="$callCenter" :site="$site" />
                                @elseif($component === 'call-centers.sites.site-show')
                                    <livewire:call-centers.sites.site-show :callCenter="$callCenter" :site="$site" />
                                @elseif($component === 'call-centers.sites.site-delete')
                                    <livewire:call-centers.sites.site-delete :callCenter="$callCenter" :site="$site" />
                                @elseif($component === 'call-centers.departments.department-index')
                                    <livewire:call-centers.departments.department-index :callCenter="$callCenter" />
                                @elseif($component === 'call-centers.departments.department-create')
                                    <livewire:call-centers.departments.department-create :callCenter="$callCenter" />
                                @elseif($component === 'call-centers.departments.department-edit')
                                    <livewire:call-centers.departments.department-edit :callCenter="$callCenter" :department="$department" />
                                @elseif($component === 'call-centers.departments.department-show')
                                    <livewire:call-centers.departments.department-show :callCenter="$callCenter" :department="$department" />
                                @elseif($component === 'call-centers.departments.department-delete')
                                    <livewire:call-centers.departments.department-delete :callCenter="$callCenter" :department="$department" />
                                @else
                                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                                        <span class="font-medium">Error!</span> Unknown component:
                                        @if(is_string($component))
                                            {{ $component }}
                                        @elseif(is_object($component))
                                            [Object of type {{ get_class($component) }}]
                                        @else
                                            [Type: {{ gettype($component) }}]
                                        @endif
                                    </div>

                                    <div class="mt-4">
                                        <h3 class="text-lg font-semibold mb-4">Component Loading Error</h3>
                                        <div class="p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-400" role="alert">
                                            <p class="mb-2"><strong>Could not determine appropriate component to load.</strong></p>
                                            <p><strong>Route:</strong> {{ $current_route }}</p>
                                            <p><strong>Component:</strong>
                                                @if(is_string($component))
                                                    {{ $component }}
                                                @elseif(is_object($component))
                                                    [Object of type {{ get_class($component) }}]
                                                @else
                                                    [Type: {{ gettype($component) }}]
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                @endif
                            </x-content-body>
                        </x-content>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
