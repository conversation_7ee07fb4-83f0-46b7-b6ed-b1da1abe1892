<div>
    <div class="mb-4 flex flex-col md:flex-row items-start md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">{{ $site->name }}</h2>
            <p class="mt-2 text-base text-gray-500 dark:text-gray-400">Site Details</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
            <a href="{{ route('call-centers.sites.edit', ['callCenter' => $callCenter->id, 'site' => $site->id]) }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Edit
            </a>
            <a href="{{ route('call-centers.sites.delete', ['callCenter' => $callCenter->id, 'site' => $site->id]) }}" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 focus:outline-none dark:focus:ring-red-800">
                Delete
            </a>
            <a href="{{ route('call-centers.sites.index', ['callCenter' => $callCenter->id]) }}" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                Back to Sites
            </a>
        </div>
    </div>

    <hr class="my-4 border-t border-gray-200 dark:border-gray-700" />

    <!-- Tabs -->
    <div class="border-b border-gray-200 dark:border-gray-700">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400">
            <li class="mr-2">
                <button id="tab-overview" onclick="showTab('overview')" class="inline-flex items-center justify-center p-4 border-b-2 border-primary-600 text-primary-600 rounded-t-lg active dark:text-primary-500 dark:border-primary-500 group" aria-current="page">
                    <svg class="w-4 h-4 mr-2 text-primary-600 dark:text-primary-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"></path>
                    </svg>
                    Overview
                </button>
            </li>
            <li class="mr-2">
                <button id="tab-platforms" onclick="showTab('platforms')" class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 group">
                    <svg class="w-4 h-4 mr-2 text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 6a4 4 0 1 0 0 8 4 4 0 0 0 0-8zm-6 4a6 6 0 1 1 12 0 6 6 0 0 1-12 0zm14-1h-1.5a.5.5 0 0 0 0 1H18a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-1.5a.5.5 0 0 0-1 0V15a2 2 0 0 0 2 2h4a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2z"/>
                    </svg>
                    Platforms
                </button>
            </li>
            <li class="mr-2">
                <button id="tab-personnel" onclick="showTab('personnel')" class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 group">
                    <svg class="w-4 h-4 mr-2 text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"></path>
                    </svg>
                    Personnel
                </button>
            </li>
            <li class="mr-2">
                <button id="tab-equipment" onclick="showTab('equipment')" class="inline-flex items-center justify-center p-4 border-b-2 border-transparent rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 group">
                    <svg class="w-4 h-4 mr-2 text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"></path>
                    </svg>
                    Equipment
                </button>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div id="tab-content-overview" class="tab-content mt-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6 dark:bg-gray-800 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Site Information</h3>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->name }}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->location }}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Capacity</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->capacity ?: 'Not specified' }}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h4>
                        @if ($site->status === 'active')
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Active</span>
                        @else
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Inactive</span>
                        @endif
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->description ?: 'No description provided' }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6 dark:bg-gray-800 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Management</h3>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Site Manager</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->manager ? $site->manager->full_name : 'Not assigned' }}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">IT Manager</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->itManager ? $site->itManager->full_name : 'Not assigned' }}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Trainer</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->trainer ? $site->trainer->full_name : 'Not assigned' }}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Accountant</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->accountant ? $site->accountant->full_name : 'Not assigned' }}</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">HR Manager</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $site->hrManager ? $site->hrManager->full_name : 'Not assigned' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="tab-content-platforms" class="tab-content mt-4 hidden">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Platforms</h3>
            <a href="{{ route('call-centers.sites.platforms.create', ['callCenter' => $callCenter->id, 'site' => $site->id]) }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Add Platform
            </a>
        </div>
        
        @if($site->platforms && $site->platforms->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Name</th>
                            <th scope="col" class="px-4 py-3">Type</th>
                            <th scope="col" class="px-4 py-3">Manager</th>
                            <th scope="col" class="px-4 py-3">Status</th>
                            <th scope="col" class="px-4 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($site->platforms as $platform)
                            <tr class="border-b dark:border-gray-700">
                                <th scope="row" class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    {{ $platform->name }}
                                </th>
                                <td class="px-4 py-3">{{ $platform->type }}</td>
                                <td class="px-4 py-3">{{ $platform->manager ? $platform->manager->full_name : 'Not assigned' }}</td>
                                <td class="px-4 py-3">
                                    @if($platform->status === 'active')
                                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Active</span>
                                    @else
                                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Inactive</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    <a href="{{ route('call-centers.sites.platforms.show', ['callCenter' => $callCenter->id, 'site' => $site->id, 'platform' => $platform->id]) }}" class="font-medium text-primary-600 dark:text-primary-500 hover:underline">View</a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="p-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
                No platforms found for this site.
            </div>
        @endif
    </div>

    <div id="tab-content-personnel" class="tab-content mt-4 hidden">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Personnel</h3>
            <a href="{{ route('call-centers.sites.personnels.assign', ['callCenter' => $callCenter->id, 'site' => $site->id]) }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Assign Personnel
            </a>
        </div>
        
        <div class="p-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
            No personnel assigned to this site.
        </div>
    </div>

    <div id="tab-content-equipment" class="tab-content mt-4 hidden">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Equipment</h3>
            <a href="{{ route('call-centers.sites.equipments.create', ['callCenter' => $callCenter->id, 'site' => $site->id]) }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Add Equipment
            </a>
        </div>
        
        <div class="p-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
            No equipment found for this site.
        </div>
    </div>

    <!-- Tab Switching Script -->
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Show the selected tab content
            document.getElementById('tab-content-' + tabName).classList.remove('hidden');
            
            // Update tab button styles
            document.querySelectorAll('button[id^="tab-"]').forEach(button => {
                button.classList.remove('border-primary-600', 'text-primary-600', 'dark:text-primary-500', 'dark:border-primary-500');
                button.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300', 'dark:hover:text-gray-300');
                
                // Update SVG colors
                const svg = button.querySelector('svg');
                if (svg) {
                    svg.classList.remove('text-primary-600', 'dark:text-primary-500');
                    svg.classList.add('text-gray-400', 'group-hover:text-gray-500', 'dark:text-gray-500', 'dark:group-hover:text-gray-300');
                }
            });
            
            // Set active tab button style
            const activeButton = document.getElementById('tab-' + tabName);
            activeButton.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300', 'dark:hover:text-gray-300');
            activeButton.classList.add('border-primary-600', 'text-primary-600', 'dark:text-primary-500', 'dark:border-primary-500');
            
            // Update active SVG color
            const activeSvg = activeButton.querySelector('svg');
            if (activeSvg) {
                activeSvg.classList.remove('text-gray-400', 'group-hover:text-gray-500', 'dark:text-gray-500', 'dark:group-hover:text-gray-300');
                activeSvg.classList.add('text-primary-600', 'dark:text-primary-500');
            }
        }
    </script>
</div>
