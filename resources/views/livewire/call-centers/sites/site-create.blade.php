<div>
    <div class="mb-4 flex flex-col md:flex-row items-start md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Create Site</h2>
            <p class="mt-2 text-base text-gray-500 dark:text-gray-400">Add a new site to {{ $callCenter->name }}</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="{{ route('call-centers.sites.index', ['callCenter' => $callCenter->id]) }}" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                Back to Sites
            </a>
        </div>
    </div>

    @if(session()->has('message'))
    <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
        {{ session('message') }}
    </div>
    @endif

    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
        <form wire:submit.prevent="save">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="col-span-1">
                    <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Site Name <span class="text-red-500">*</span></label>
                    <input type="text" wire:model="name" id="name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter site name" required>
                    @error('name') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-1">
                    <label for="location" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Location <span class="text-red-500">*</span></label>
                    <input type="text" wire:model="location" id="location" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter site location" required>
                    @error('location') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-1">
                    <label for="capacity" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Capacity</label>
                    <input type="number" wire:model="capacity" id="capacity" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter site capacity">
                    @error('capacity') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-1">
                    <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status <span class="text-red-500">*</span></label>
                    <select wire:model="status" id="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    @error('status') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-2">
                    <label for="description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                    <textarea wire:model="description" id="description" rows="4" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter site description"></textarea>
                    @error('description') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-2">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Management</h3>
                </div>
                
                <div class="col-span-1">
                    <label for="manager_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Site Manager</label>
                    <select wire:model="manager_id" id="manager_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select Manager</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager['id'] }}">{{ $manager['name'] }} ({{ $manager['role'] }})</option>
                        @endforeach
                    </select>
                    @error('manager_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-1">
                    <label for="it_manager_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IT Manager</label>
                    <select wire:model="it_manager_id" id="it_manager_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select IT Manager</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager['id'] }}">{{ $manager['name'] }} ({{ $manager['role'] }})</option>
                        @endforeach
                    </select>
                    @error('it_manager_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-1">
                    <label for="trainer_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Trainer</label>
                    <select wire:model="trainer_id" id="trainer_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select Trainer</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager['id'] }}">{{ $manager['name'] }} ({{ $manager['role'] }})</option>
                        @endforeach
                    </select>
                    @error('trainer_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-1">
                    <label for="accountant_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Accountant</label>
                    <select wire:model="accountant_id" id="accountant_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select Accountant</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager['id'] }}">{{ $manager['name'] }} ({{ $manager['role'] }})</option>
                        @endforeach
                    </select>
                    @error('accountant_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
                
                <div class="col-span-1">
                    <label for="hr_manager_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">HR Manager</label>
                    <select wire:model="hr_manager_id" id="hr_manager_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select HR Manager</option>
                        @foreach($managers as $manager)
                            <option value="{{ $manager['id'] }}">{{ $manager['name'] }} ({{ $manager['role'] }})</option>
                        @endforeach
                    </select>
                    @error('hr_manager_id') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                </div>
            </div>
            
            <div class="mt-6 flex justify-end">
                <button type="submit" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                    Create Site
                </button>
            </div>
        </form>
    </div>
</div>
