<x-content>
    <x-content-header title="{{ $callCenter->name }}">
        <div class="flex items-center space-x-2">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-call-center-edit', { callCenter: {{ $callCenter->id }} })"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-call-center-delete', { callCenter: {{ $callCenter->id }} })"/>
            <x-page-button type="back" label="Back to List" action="$dispatch('navigate-to', { route: 'call-centers.list' })"/>
            <x-page-button type="secondary" label="Dashboard" action="$dispatch('navigate-to', { route: 'call-centers.index' })"/>
        </div>
    </x-content-header>
    <x-content-body>

        <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
            <!-- Right Content -->
            <div class="col-span-full xl:col-auto">
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <div class="flow-root">
                        <h3 class="text-xl font-semibold dark:text-white">History</h3>
                        <ol class="relative mt-4 border-l border-gray-200 dark:border-gray-700">
                            <li class="mb-10 ml-4">
                                <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Creation Date</time>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $callCenter->created_at->format('d M Y, H:i') }}</h3>
                            </li>
                            <li class="mb-10 ml-4">
                                <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Status</time>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    @if ($callCenter->status === 'active')
                                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Active</span>
                                    @else
                                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Inactive</span>
                                    @endif
                                </h3>
                            </li>
                            <li class="mb-10 ml-4">
                                <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Last Update</time>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $callCenter->updated_at->format('d M Y, H:i') }}</h3>
                            </li>
                        </ol>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <h3 class="mb-4 text-xl font-semibold dark:text-white">Statistics</h3>
                    <div class="grid grid-cols-1 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 dark:bg-gray-800 dark:border-gray-700">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Sites</h4>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $callCenter->sites->count() }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Total sites managed</p>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 dark:bg-gray-800 dark:border-gray-700">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Platforms</h4>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $callCenter->sites->flatMap->platforms->count() }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Active platforms</p>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 dark:bg-gray-800 dark:border-gray-700">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Departments</h4>
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">0</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Total departments</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Left Content -->
            <div class="col-span-2">
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <h3 class="mb-4 text-xl font-semibold dark:text-white">General information</h3>
                    <div class="grid grid-cols-6 gap-6">
                        <div class="col-span-6 sm:col-span-3">
                            <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Name</label>
                            <input readonly type="text" value="{{ $callCenter->name }}" name="name" id="name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <label for="director" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Director</label>
                            <input readonly type="text" value="{{ $callCenter->director ? $callCenter->director->full_name : 'Not assigned' }}" name="director" id="director" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <label for="contact-email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contact Email</label>
                            <input readonly type="email" value="{{ $callCenter->contact_email ?: 'No email provided' }}" name="contact-email" id="contact-email" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <label for="contact-phone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Contact Phone</label>
                            <input readonly type="text" value="{{ $callCenter->contact_phone ?: 'No phone provided' }}" name="contact-phone" id="contact-phone" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <label for="address" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Address</label>
                            <input readonly type="text" value="{{ $callCenter->address ?: 'No address provided' }}" name="address" id="address" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        </div>
                        <div class="col-span-6 sm:col-span-3">
                            <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                            <input readonly type="text" value="{{ ucfirst($callCenter->status) }}" name="status" id="status" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        </div>
                        <div class="col-span-6">
                            <label for="description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                            <textarea readonly id="description" rows="3" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">{{ $callCenter->description ?: 'No description provided' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Sites Section -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold dark:text-white">Sites</h3>
                        <div class="flex space-x-3">
                            <a href="{{ route('call-centers.sites.create', ['callCenter' => $callCenter->id]) }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                                Add Site
                            </a>
                            <a href="{{ route('call-centers.sites.index', ['callCenter' => $callCenter->id]) }}" class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                                View All
                            </a>
                        </div>
                    </div>

                    @if($callCenter->sites->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                    <tr>
                                        <th scope="col" class="px-4 py-3">Name</th>
                                        <th scope="col" class="px-4 py-3">Location</th>
                                        <th scope="col" class="px-4 py-3">Manager</th>
                                        <th scope="col" class="px-4 py-3">Status</th>
                                        <th scope="col" class="px-4 py-3">
                                            <span class="sr-only">Actions</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($callCenter->sites->take(5) as $site)
                                        <tr class="border-b dark:border-gray-700">
                                            <th scope="row" class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                {{ $site->name }}
                                            </th>
                                            <td class="px-4 py-3">{{ $site->location }}</td>
                                            <td class="px-4 py-3">{{ $site->manager ? $site->manager->full_name : 'Not assigned' }}</td>
                                            <td class="px-4 py-3">
                                                @if($site->status === 'active')
                                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Active</span>
                                                @else
                                                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Inactive</span>
                                                @endif
                                            </td>
                                            <td class="px-4 py-3">
                                                <a href="{{ route('call-centers.sites.show', ['callCenter' => $callCenter->id, 'site' => $site->id]) }}" class="font-medium text-primary-600 dark:text-primary-500 hover:underline">View</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($callCenter->sites->count() > 5)
                            <div class="p-4 text-center">
                                <a href="{{ route('call-centers.sites.index', ['callCenter' => $callCenter->id]) }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                    View all {{ $callCenter->sites->count() }} sites
                                </a>
                            </div>
                        @endif
                    @else
                        <div class="p-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            No sites found for this call center.
                        </div>
                    @endif
                </div>

                <!-- Departments Section -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold dark:text-white">Departments</h3>
                        <div class="flex space-x-3">
                            <a href="{{ route('call-centers.departments.create', ['callCenter' => $callCenter->id]) }}" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                                Add Department
                            </a>
                            <a href="{{ route('call-centers.departments.index', ['callCenter' => $callCenter->id]) }}" class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                                View All
                            </a>
                        </div>
                    </div>

                    <div class="p-4 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p>Manage departments for this call center by clicking the "View All" button.</p>
                    </div>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
