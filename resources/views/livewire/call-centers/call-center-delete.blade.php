<x-content>
    <form action="#">
        <x-content-header title="Delete Call Center">
            <x-page-button type="delete" label="Delete" action="$dispatch('destroy-call-center')" :disabled="!$confirmationValid"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-call-center-show', { callCenter: {{ $callCenter->id }} })"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Warning and confirmation section -->
                <div class="md:col-span-2">
                    <div class="p-4 text-center bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800 mb-6">
                        <svg class="w-16 h-16 mx-auto text-red-600 dark:text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <h3 class="mt-5 mb-2 text-xl font-semibold text-gray-900 dark:text-white">Delete "{{ $callCenter->name }}"</h3>
                        <p class="mb-5 text-base text-gray-500 dark:text-gray-400">Are you sure you want to delete this call center?</p>

                        <div class="flex flex-col space-y-4">
                            <div class="p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-gray-700 dark:border-red-700">
                                <div class="flex items-center mb-2">
                                    <svg class="w-5 h-5 text-red-700 dark:text-red-500 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                    </svg>
                                    <h3 class="text-base font-semibold text-red-700 dark:text-red-500">This action cannot be undone</h3>
                                </div>
                                <p class="text-sm text-red-600 dark:text-red-400">
                                    Deleting this call center will permanently remove it from the system along with all associated data.
                                </p>
                            </div>

                            @if($callCenter->sites->count() > 0)
                            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-gray-700 dark:border-yellow-700">
                                <div class="flex items-center mb-2">
                                    <svg class="w-5 h-5 text-yellow-700 dark:text-yellow-500 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                    </svg>
                                    <h3 class="text-base font-semibold text-yellow-700 dark:text-yellow-500">Associated sites will be affected</h3>
                                </div>
                                <p class="text-sm text-yellow-600 dark:text-yellow-400">
                                    <strong>This call center has {{ $callCenter->sites->count() }} site(s) associated with it.</strong>
                                    If you delete this call center, these sites will be disassociated from it.
                                </p>
                            </div>
                            @endif

                            <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg dark:bg-gray-700 dark:border-blue-700">
                                <div class="flex items-center mb-2">
                                    <svg class="w-5 h-5 text-blue-700 dark:text-blue-500 mr-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                    </svg>
                                    <h3 class="text-base font-semibold text-blue-700 dark:text-blue-500">Alternative options</h3>
                                </div>
                                <p class="text-sm text-blue-600 dark:text-blue-400">
                                    Instead of deleting, consider setting the call center status to "inactive" if you may need it again in the future.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Call Center Information -->
                <div class="md:col-span-1">
                    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Call Center Details</h3>

                        <div class="space-y-4">
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</h4>
                                <p class="text-base font-medium text-gray-900 dark:text-white">{{ $callCenter->name }}</p>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Director</h4>
                                <p class="text-base font-medium text-gray-900 dark:text-white">{{ $callCenter->director ? $callCenter->director->full_name : 'Not assigned' }}</p>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h4>
                                <p class="text-base font-medium">
                                    @if($callCenter->status === 'active')
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                            Active
                                        </span>
                                    @else
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                            Inactive
                                        </span>
                                    @endif
                                </p>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Sites</h4>
                                <p class="text-base font-medium text-gray-900 dark:text-white">{{ $callCenter->sites->count() }}</p>
                            </div>

                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</h4>
                                <p class="text-base font-medium text-gray-900 dark:text-white">{{ $callCenter->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Type confirmation -->
            <div class="mt-6 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div>
                    <label for="confirmation" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Type "{{ $callCenter->name }}" to confirm deletion</label>
                    <input wire:model="confirmationText" type="text" id="confirmation" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Type call center name here">
                    @error('confirmationText') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
