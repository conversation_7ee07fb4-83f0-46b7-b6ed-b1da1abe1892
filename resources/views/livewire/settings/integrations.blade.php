<div>
    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <h3 class="mb-4 text-xl font-semibold dark:text-white">Integration Settings</h3>
        
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg dark:bg-green-200 dark:text-green-800" role="alert">
                {{ session('message') }}
            </div>
        @endif
        
        <form wire:submit.prevent="saveSettings">
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">CRM Integration</h4>
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                        <label for="enable_crm_integration" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Enable CRM Integration</label>
                        <select id="enable_crm_integration" wire:model="enable_crm_integration" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="1">Yes</option>
                            <option value="0">No</option>
                        </select>
                        @error('enable_crm_integration') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                    
                    <div>
                        <label for="crm_api_key" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">CRM API Key</label>
                        <input type="password" id="crm_api_key" wire:model="crm_api_key" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter API key" @if(!$enable_crm_integration) disabled @endif>
                        @error('crm_api_key') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                    
                    <div class="md:col-span-2">
                        <label for="crm_endpoint" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">CRM API Endpoint</label>
                        <input type="url" id="crm_endpoint" wire:model="crm_endpoint" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="https://api.example.com/crm" @if(!$enable_crm_integration) disabled @endif>
                        @error('crm_endpoint') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">SMS Integration</h4>
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                        <label for="enable_sms_integration" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Enable SMS Integration</label>
                        <select id="enable_sms_integration" wire:model="enable_sms_integration" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="1">Yes</option>
                            <option value="0">No</option>
                        </select>
                        @error('enable_sms_integration') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                    
                    <div>
                        <label for="sms_api_key" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">SMS API Key</label>
                        <input type="password" id="sms_api_key" wire:model="sms_api_key" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter SMS API key" @if(!$enable_sms_integration) disabled @endif>
                        @error('sms_api_key') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                    
                    <div>
                        <label for="sms_sender_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">SMS Sender ID</label>
                        <input type="text" id="sms_sender_id" wire:model="sms_sender_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="CallCenter" @if(!$enable_sms_integration) disabled @endif>
                        @error('sms_sender_id') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>
            
            <div class="mt-6">
                <button type="submit" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                    <span wire:loading.remove wire:target="saveSettings">Save Settings</span>
                    <span wire:loading wire:target="saveSettings">Saving...</span>
                </button>
            </div>
        </form>
    </div>
</div>
