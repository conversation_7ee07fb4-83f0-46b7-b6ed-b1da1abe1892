<div>
    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <h3 class="mb-4 text-xl font-semibold dark:text-white">Call Settings</h3>

        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg dark:bg-green-200 dark:text-green-800" role="alert">
                {{ session('message') }}
            </div>
        @endif

        <form wire:submit.prevent="saveSettings">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                    <label for="recording" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Call Recording</label>
                    <select id="recording" wire:model="recording" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="all">Record All Calls</option>
                        <option value="outbound">Record Outbound Only</option>
                        <option value="inbound">Record Inbound Only</option>
                        <option value="none">No Recording</option>
                    </select>
                    @error('recording') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="queue_timeout" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Queue Timeout (seconds)</label>
                    <input type="number" id="queue_timeout" wire:model="queue_timeout" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="60">
                    @error('queue_timeout') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="max_hold_time" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Maximum Hold Time (minutes)</label>
                    <input type="number" id="max_hold_time" wire:model="max_hold_time" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="10">
                    @error('max_hold_time') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="voicemail_enabled" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Voicemail Enabled</label>
                    <select id="voicemail_enabled" wire:model="voicemail_enabled" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                    @error('voicemail_enabled') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
            </div>

            <div class="mt-6">
                <button type="submit" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                    <span wire:loading.remove wire:target="saveSettings">Save Settings</span>
                    <span wire:loading wire:target="saveSettings">Saving...</span>
                </button>
            </div>
        </form>
    </div>
</div>