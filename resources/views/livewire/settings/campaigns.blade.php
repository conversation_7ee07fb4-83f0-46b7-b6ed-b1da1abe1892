<div>
    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <h3 class="mb-4 text-xl font-semibold dark:text-white">Campaign Settings</h3>
        
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-700 bg-green-100 rounded-lg dark:bg-green-200 dark:text-green-800" role="alert">
                {{ session('message') }}
            </div>
        @endif
        
        <form wire:submit.prevent="saveSettings">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                    <label for="default_campaign_type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Default Campaign Type</label>
                    <select id="default_campaign_type" wire:model="default_campaign_type" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="outbound">Outbound</option>
                        <option value="inbound">Inbound</option>
                        <option value="mixed">Mixed</option>
                    </select>
                    @error('default_campaign_type') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                
                <div>
                    <label for="auto_assign_agents" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Auto-assign Agents</label>
                    <select id="auto_assign_agents" wire:model="auto_assign_agents" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                    @error('auto_assign_agents') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                
                <div>
                    <label for="max_agents_per_campaign" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Maximum Agents per Campaign</label>
                    <input type="number" id="max_agents_per_campaign" wire:model="max_agents_per_campaign" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="20">
                    @error('max_agents_per_campaign') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
                
                <div>
                    <label for="enable_campaign_analytics" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Enable Campaign Analytics</label>
                    <select id="enable_campaign_analytics" wire:model="enable_campaign_analytics" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                    @error('enable_campaign_analytics') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                </div>
            </div>
            
            <div class="mt-6">
                <button type="submit" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                    <span wire:loading.remove wire:target="saveSettings">Save Settings</span>
                    <span wire:loading wire:target="saveSettings">Saving...</span>
                </button>
            </div>
        </form>
    </div>
</div>
