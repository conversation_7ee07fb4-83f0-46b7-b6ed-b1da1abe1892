<x-content>
    <x-content-header title="Delete Department: {{ $department->name }}">
        <x-page-button type="back" label="Back" action="$dispatch('to-settings-departments')"/>
    </x-content-header>

    <x-content-body>
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="text-center">
                <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                </svg>
                <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">Are you sure you want to delete this department?</h3>
                
                @if($hasUsers || $hasChildren)
                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        @if($hasUsers)
                            <p class="mb-2"><span class="font-medium">Warning!</span> This department has users assigned to it.</p>
                        @endif
                        @if($hasChildren)
                            <p><span class="font-medium">Warning!</span> This department has child departments.</p>
                        @endif
                        <p class="mt-2">You must reassign or remove these dependencies before deleting this department.</p>
                    </div>
                @else
                    <div class="p-4 mb-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300" role="alert">
                        <span class="font-medium">Warning!</span> This action cannot be undone. All associated data will be permanently removed.
                    </div>
                @endif
                
                <div class="mt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Department Details</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                <div class="mb-2">
                                    <span class="font-medium text-gray-900 dark:text-white">Name:</span>
                                    <span class="text-gray-700 dark:text-gray-300">{{ $department->name }}</span>
                                </div>
                                <div class="mb-2">
                                    <span class="font-medium text-gray-900 dark:text-white">Code:</span>
                                    <span class="text-gray-700 dark:text-gray-300">{{ $department->code }}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-900 dark:text-white">Status:</span>
                                    <span class="text-gray-700 dark:text-gray-300">{{ ucfirst($department->status) }}</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Dependencies</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                <div class="mb-2">
                                    <span class="font-medium text-gray-900 dark:text-white">Users:</span>
                                    <span class="text-gray-700 dark:text-gray-300">{{ $department->users()->count() }}</span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-900 dark:text-white">Child Departments:</span>
                                    <span class="text-gray-700 dark:text-gray-300">{{ $department->children()->count() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-center mt-6 space-x-4">
                    <button wire:click="$dispatch('to-settings-departments')" type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                        No, cancel
                    </button>
                    <button wire:click="deleteDepartment" type="button" class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center" {{ ($hasUsers || $hasChildren) ? 'disabled' : '' }}>
                        Yes, I'm sure
                    </button>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
