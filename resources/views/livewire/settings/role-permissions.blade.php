<div>
    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <!-- Card header -->
        <div class="items-center justify-between lg:flex">
            <div class="mb-4 lg:mb-0">
                <h3 class="mb-2 text-xl font-bold text-gray-900 dark:text-white">Role Permissions</h3>
                <span class="text-base font-normal text-gray-500 dark:text-gray-400">Manage permissions for each role in the system</span>
            </div>
        </div>
        
        <!-- Role selection -->
        <div class="mt-4">
            <label for="role-select" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Select Role</label>
            <select id="role-select" wire:model.live="selectedRole" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option value="">Select a role</option>
                @foreach($roles as $role)
                    <option value="{{ $role->id }}">{{ $role->display_name ?? ucfirst($role->name) }}</option>
                @endforeach
            </select>
        </div>
        
        <!-- Permissions list -->
        @if($selectedRole)
            <div class="mt-6">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">Permissions for {{ $roles->firstWhere('id', $selectedRole)->display_name ?? ucfirst($roles->firstWhere('id', $selectedRole)->name) }}</h4>
                    <button wire:click="savePermissions" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                        Save Permissions
                    </button>
                </div>
                
                <!-- Flash messages -->
                @if (session()->has('success'))
                    <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                        {{ session('success') }}
                    </div>
                @endif
                
                @if (session()->has('error'))
                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        {{ session('error') }}
                    </div>
                @endif
                
                <!-- Permission groups -->
                <div class="space-y-4">
                    @foreach($permissionGroups as $group)
                        <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="text-base font-medium text-gray-900 dark:text-white">{{ $group }}</h5>
                                <button type="button" wire:click="toggleAllInGroup('{{ $group }}')" class="text-sm text-blue-600 hover:underline dark:text-blue-500">
                                    Toggle All
                                </button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                @foreach($groupedPermissions[$group] as $permission)
                                    <div class="flex items-center">
                                        <input id="permission-{{ $permission->id }}" type="checkbox" 
                                            wire:click="togglePermission({{ $permission->id }})"
                                            wire:checked="{{ $selectedPermissions && in_array($permission->id, $selectedPermissions) }}"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="permission-{{ $permission->id }}" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                            {{ ucfirst(str_replace('_', ' ', $permission->name)) }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <!-- Save button (bottom) -->
                <div class="mt-6 flex justify-end">
                    <button wire:click="savePermissions" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                        Save Permissions
                    </button>
                </div>
            </div>
        @endif
    </div>
</div>
