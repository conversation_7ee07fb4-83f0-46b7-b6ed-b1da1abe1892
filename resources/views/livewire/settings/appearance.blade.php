<x-content>
    <x-content-body>
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">Appearance Settings</h3>
            <button type="button" class="py-2 px-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700" wire:click="resetToDefaults">
                <svg class="mr-2 h-4 w-4 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                </svg>
                Reset to Defaults
            </button>
        </div>
        <p class="mb-6 text-sm text-gray-500 dark:text-gray-400">Customize how the application appears and behaves</p>

        <div class="space-y-6">
            <!-- Theme Preference -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h4 class="mb-4 text-base font-medium text-gray-900 dark:text-white">Theme Preference</h4>

                <div class="flex flex-col space-y-4">
                    <div class="inline-flex rounded-md shadow-sm" role="group">
                        <button type="button" wire:click="$set('theme', 'light')" class="px-4 py-2 text-sm font-medium {{ $theme === 'light' ? 'text-white bg-primary-700 border-primary-700 hover:bg-primary-800' : 'text-gray-900 bg-white border-gray-200 hover:bg-gray-100 hover:text-primary-700' }} border rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary-700 focus:text-white dark:{{ $theme === 'light' ? 'bg-primary-600 border-primary-600 hover:bg-primary-700' : 'bg-gray-700 border-gray-600 text-white hover:text-white hover:bg-gray-600' }}">
                            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Light') }}
                        </button>
                        <button type="button" wire:click="$set('theme', 'dark')" class="px-4 py-2 text-sm font-medium {{ $theme === 'dark' ? 'text-white bg-primary-700 border-primary-700 hover:bg-primary-800' : 'text-gray-900 bg-white border-gray-200 hover:bg-gray-100 hover:text-primary-700' }} border-t border-b border-r focus:z-10 focus:ring-2 focus:ring-primary-700 focus:text-white dark:{{ $theme === 'dark' ? 'bg-primary-600 border-primary-600 hover:bg-primary-700' : 'bg-gray-700 border-gray-600 text-white hover:text-white hover:bg-gray-600' }}">
                            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                            </svg>
                            {{ __('Dark') }}
                        </button>
                        <button type="button" wire:click="$set('theme', 'system')" class="px-4 py-2 text-sm font-medium {{ $theme === 'system' ? 'text-white bg-primary-700 border-primary-700 hover:bg-primary-800' : 'text-gray-900 bg-white border-gray-200 hover:bg-gray-100 hover:text-primary-700' }} border rounded-r-lg focus:z-10 focus:ring-2 focus:ring-primary-700 focus:text-white dark:{{ $theme === 'system' ? 'bg-primary-600 border-primary-600 hover:bg-primary-700' : 'bg-gray-700 border-gray-600 text-white hover:text-white hover:bg-gray-600' }}">
                            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('System') }}
                        </button>
                    </div>

                    <div class="mt-4 text-sm text-gray-500 dark:text-gray-400">
                        <p>
                            <strong>Light:</strong> Always use light mode regardless of system settings.
                        </p>
                        <p>
                            <strong>Dark:</strong> Always use dark mode regardless of system settings.
                        </p>
                        <p>
                            <strong>System:</strong> Automatically switch between light and dark mode based on your system preferences.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Font Size -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h4 class="mb-4 text-base font-medium text-gray-900 dark:text-white">Font Size</h4>

                <div class="flex flex-col space-y-4">
                    <div class="inline-flex rounded-md shadow-sm" role="group">
                        <button type="button" wire:click="$set('fontSize', 'small')" class="px-4 py-2 text-sm font-medium {{ $fontSize === 'small' ? 'text-white bg-primary-700 border-primary-700 hover:bg-primary-800' : 'text-gray-900 bg-white border-gray-200 hover:bg-gray-100 hover:text-primary-700' }} border rounded-l-lg focus:z-10 focus:ring-2 focus:ring-primary-700 focus:text-white dark:{{ $fontSize === 'small' ? 'bg-primary-600 border-primary-600 hover:bg-primary-700' : 'bg-gray-700 border-gray-600 text-white hover:text-white hover:bg-gray-600' }}">
                            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Small') }}
                        </button>
                        <button type="button" wire:click="$set('fontSize', 'medium')" class="px-4 py-2 text-sm font-medium {{ $fontSize === 'medium' ? 'text-white bg-primary-700 border-primary-700 hover:bg-primary-800' : 'text-gray-900 bg-white border-gray-200 hover:bg-gray-100 hover:text-primary-700' }} border-t border-b border-r focus:z-10 focus:ring-2 focus:ring-primary-700 focus:text-white dark:{{ $fontSize === 'medium' ? 'bg-primary-600 border-primary-600 hover:bg-primary-700' : 'bg-gray-700 border-gray-600 text-white hover:text-white hover:bg-gray-600' }}">
                            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Medium') }}
                        </button>
                        <button type="button" wire:click="$set('fontSize', 'large')" class="px-4 py-2 text-sm font-medium {{ $fontSize === 'large' ? 'text-white bg-primary-700 border-primary-700 hover:bg-primary-800' : 'text-gray-900 bg-white border-gray-200 hover:bg-gray-100 hover:text-primary-700' }} border rounded-r-lg focus:z-10 focus:ring-2 focus:ring-primary-700 focus:text-white dark:{{ $fontSize === 'large' ? 'bg-primary-600 border-primary-600 hover:bg-primary-700' : 'bg-gray-700 border-gray-600 text-white hover:text-white hover:bg-gray-600' }}">
                            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            {{ __('Large') }}
                        </button>
                    </div>

                    <div class="mt-4 text-sm text-gray-500 dark:text-gray-400">
                        <p>
                            Adjust the text size throughout the application to improve readability.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Accessibility Options -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h4 class="mb-4 text-base font-medium text-gray-900 dark:text-white">Accessibility Options</h4>

                <div class="flex flex-col space-y-4">
                    <div class="flex items-center">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" wire:model.live="reducedMotion" id="reduced-motion" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                        <label for="reduced-motion" class="ml-3 text-sm font-medium text-gray-900 dark:text-white">
                            Reduced Motion
                        </label>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 ml-12">
                        Minimize animations and transitions throughout the application.
                    </p>

                    <div class="flex items-center pt-3">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" wire:model.live="highContrast" id="high-contrast" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600"></div>
                        </label>
                        <label for="high-contrast" class="ml-3 text-sm font-medium text-gray-900 dark:text-white">
                            High Contrast Mode
                        </label>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400 ml-12">
                        Increase contrast between elements for better visibility.
                    </p>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
