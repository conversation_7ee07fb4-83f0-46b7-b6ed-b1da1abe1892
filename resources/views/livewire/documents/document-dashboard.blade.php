<x-content>
    <x-content-header title="Document Management Dashboard">
        <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
            <button type="button" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path>
                </svg>
                Export
            </button>
        </div>
    </x-content-header>

    <x-content-body>
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        <!-- Document Stats -->
        <div class="grid w-full grid-cols-1 gap-4 mb-4 xl:grid-cols-3">
            <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="w-full">
                    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Total Documents</h3>
                    <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $stats['total'] }}</span>
                    <div class="flex items-center mt-4 space-x-2">
                        <div class="flex-shrink-0">
                            <span class="flex items-center justify-center w-8 h-8 text-blue-500 bg-blue-100 rounded-full dark:text-blue-300 dark:bg-blue-900">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        </div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            All document types
                        </div>
                    </div>
                </div>
            </div>
            <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="w-full">
                    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Pending Verification</h3>
                    <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $stats['pending'] }}</span>
                    <div class="flex items-center mt-4 space-x-2">
                        <div class="flex-shrink-0">
                            <span class="flex items-center justify-center w-8 h-8 text-yellow-500 bg-yellow-100 rounded-full dark:text-yellow-300 dark:bg-yellow-900">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        </div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            <button wire:click="$set('selectedStatus', 'pending')" class="text-yellow-600 hover:underline dark:text-yellow-500">
                                View all pending documents
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="w-full">
                    <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Expired Documents</h3>
                    <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $stats['expired'] }}</span>
                    <div class="flex items-center mt-4 space-x-2">
                        <div class="flex-shrink-0">
                            <span class="flex items-center justify-center w-8 h-8 text-red-500 bg-red-100 rounded-full dark:text-red-300 dark:bg-red-900">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </span>
                        </div>
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            <button wire:click="$set('selectedStatus', 'expired')" class="text-red-600 hover:underline dark:text-red-500">
                                View all expired documents
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Filters</h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div>
                    <label for="search" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Search</label>
                    <input type="text" wire:model.debounce.300ms="search" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search documents...">
                </div>
                <div>
                    <label for="selectedCategory" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Category</label>
                    <select wire:model="selectedCategory" id="selectedCategory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Categories</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}">{{ ucfirst(str_replace('_', ' ', $category)) }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="selectedUser" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">User</label>
                    <select wire:model="selectedUser" id="selectedUser" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Users</option>
                        @foreach($users as $user)
                            <option value="{{ $user->id }}">{{ $user->first_name }} {{ $user->last_name }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="selectedStatus" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                    <select wire:model="selectedStatus" id="selectedStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Statuses</option>
                        <option value="pending">Pending Verification</option>
                        <option value="verified">Verified</option>
                        <option value="rejected">Rejected</option>
                        <option value="expired">Expired</option>
                        <option value="expiring_soon">Expiring Soon</option>
                    </select>
                </div>
                <div>
                    <label for="selectedDepartment" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Department</label>
                    <select wire:model="selectedDepartment" id="selectedDepartment" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Departments</option>
                        @foreach($departments as $department)
                            <option value="{{ $department }}">{{ $department }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="dateRange" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Date Range</label>
                    <input type="text" wire:model="dateRange" id="dateRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Select date range">
                </div>
                <div>
                    <label for="perPage" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Per Page</label>
                    <select wire:model="perPage" id="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button wire:click="$refresh" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                        Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Documents Table -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-semibold dark:text-white">Documents</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Showing {{ $documents->firstItem() ?? 0 }} - {{ $documents->lastItem() ?? 0 }} of {{ $documents->total() }}
                    </span>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Document</th>
                            <th scope="col" class="px-4 py-3">User</th>
                            <th scope="col" class="px-4 py-3">Category</th>
                            <th scope="col" class="px-4 py-3">Status</th>
                            <th scope="col" class="px-4 py-3">Expiry Date</th>
                            <th scope="col" class="px-4 py-3">Department</th>
                            <th scope="col" class="px-4 py-3">Uploaded</th>
                            <th scope="col" class="px-4 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($documents as $document)
                            <tr class="border-b dark:border-gray-700">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">{{ $document->document_title ?: $document->file_name }}</p>
                                            <p class="text-xs">{{ $document->file_name }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    @if($document->mediable_type === 'App\\Models\\User' && $document->mediable)
                                        {{ $document->mediable->first_name }} {{ $document->mediable->last_name }}
                                    @else
                                        Unknown
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    {{ ucfirst(str_replace('_', ' ', $document->category)) }}
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {{ $document->verification_status === 'verified' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' :
                                           ($document->verification_status === 'rejected' ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' :
                                           'text-yellow-800 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                        {{ ucfirst($document->verification_status) }}
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    @if($document->expiry_date)
                                        <span class="px-2 py-1 text-xs font-medium {{ $document->expiry_date->isPast() ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' : 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' }} rounded-full">
                                            {{ $document->expiry_date->format('M d, Y') }}
                                        </span>
                                    @else
                                        <span class="text-gray-500">No expiry</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    {{ isset($document->department) && !empty($document->department) ? $document->department : 'Not specified' }}
                                </td>
                                <td class="px-4 py-3">
                                    {{ $document->created_at->format('M d, Y') }}
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('documents.view', ['id' => $document->id]) }}" target="_blank" class="text-blue-600 hover:underline dark:text-blue-500">
                                            View
                                        </a>
                                        @if($document->verification_status === 'pending')
                                            <button wire:click="openVerificationModal({{ $document->id }})" class="text-green-600 hover:underline dark:text-green-500">
                                                Verify
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-4 py-6 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <p class="mt-2 text-gray-500 dark:text-gray-400">No documents found</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="mt-4">
                {{ $documents->links() }}
            </div>
        </div>
    </x-content-body>

    <!-- Document Verification Modal -->
    @if($showVerificationModal)
        <div class="fixed inset-0 z-50 flex items-center justify-center overflow-x-hidden overflow-y-auto outline-none focus:outline-none bg-gray-900 bg-opacity-50">
            <div class="relative w-full max-w-md mx-auto my-6">
                <div class="relative flex flex-col w-full bg-white border-0 rounded-lg shadow-lg outline-none focus:outline-none dark:bg-gray-800">
                    <div class="flex items-start justify-between p-5 border-b border-gray-200 rounded-t dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Verify Document
                        </h3>
                        <button wire:click="closeVerificationModal" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-700 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="mb-4">
                            <label for="verification_status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Verification Status</label>
                            <select wire:model="verificationStatus" id="verification_status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                <option value="verified">Verified</option>
                                <option value="rejected">Rejected</option>
                            </select>
                        </div>

                        @if($verificationStatus === 'rejected')
                            <div class="mb-4">
                                <label for="rejection_reason" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Rejection Reason</label>
                                <textarea wire:model="rejectionReason" id="rejection_reason" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Provide a reason for rejection"></textarea>
                                @error('rejectionReason') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                        @endif
                    </div>
                    <div class="flex items-center justify-end p-6 border-t border-gray-200 rounded-b dark:border-gray-700">
                        <button wire:click="closeVerificationModal" type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:ring-gray-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-700">
                            Cancel
                        </button>
                        <button wire:click="verifyDocument" type="button" class="ml-3 text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</x-content>
