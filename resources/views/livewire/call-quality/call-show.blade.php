<x-content>
    <x-content-header title="Call Details">
        <div class="flex items-center space-x-2">
            <a href="{{ route('call-quality.calls') }}" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                Back to Calls
            </a>
            <button type="button" wire:click="openEvaluationModal" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                Evaluate Call
            </button>
        </div>
    </x-content-header>

    <x-content-body>
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        <!-- Call Details -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
            <!-- Basic Info -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Call Information</h3>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2">
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Call ID</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->call_id ?? 'N/A' }}</dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Date & Time</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->call_time->format('M d, Y H:i:s') }}</dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->formatted_duration }}</dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Direction</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <span class="px-2 py-1 text-xs font-medium {{ $call->direction === 'inbound' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' }} rounded-full">
                                {{ ucfirst($call->direction) }}
                            </span>
                        </dd>
                    </div>
                    <div class="sm:col-span-1">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            <span class="px-2 py-1 text-xs font-medium {{ $call->status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' }} rounded-full">
                                {{ ucfirst($call->status) }}
                            </span>
                        </dd>
                    </div>
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Recording</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            @if ($call->recording_url)
                                <audio controls class="w-full">
                                    <source src="{{ $call->recording_url }}" type="audio/mpeg">
                                    Your browser does not support the audio element.
                                </audio>
                            @else
                                <span class="text-gray-500 dark:text-gray-400">No recording available</span>
                            @endif
                        </dd>
                    </div>
                </dl>
            </div>

            <!-- Agent Info -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Agent Information</h3>
                <div class="flex items-center mb-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-600 dark:text-primary-300 font-bold text-lg">
                            {{ substr($call->agent->first_name, 0, 1) }}{{ substr($call->agent->last_name, 0, 1) }}
                        </div>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-base font-semibold text-gray-900 dark:text-white">
                            {{ $call->agent->first_name }} {{ $call->agent->last_name }}
                        </h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {{ $call->agent->email }}
                        </p>
                    </div>
                </div>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Campaign</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->campaign->name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Agent ID</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->agent->id }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->agent->phone ?? 'N/A' }}</dd>
                    </div>
                </dl>
            </div>

            <!-- Customer Info -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Information</h3>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->customer_name ?? 'N/A' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ $call->customer_phone ?? 'N/A' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</dt>
                        <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                            @if ($call->notes)
                                <p class="whitespace-pre-line">{{ $call->notes }}</p>
                            @else
                                <span class="text-gray-500 dark:text-gray-400">No notes available</span>
                            @endif
                        </dd>
                    </div>
                    @if ($call->metadata)
                        <div>
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Additional Data</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                <div class="overflow-x-auto">
                                    <pre class="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded">{{ json_encode($call->metadata, JSON_PRETTY_PRINT) }}</pre>
                                </div>
                            </dd>
                        </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- Evaluations -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 mb-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Call Evaluations</h3>
            </div>
            
            @if ($evaluations->isEmpty())
                <div class="py-4 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">No evaluations yet</p>
                    <button type="button" wire:click="openEvaluationModal" class="mt-3 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600">
                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Evaluate Call
                    </button>
                </div>
            @else
                <div class="space-y-4">
                    @foreach ($evaluations as $evaluation)
                        <div class="p-4 border border-gray-200 rounded-lg dark:border-gray-700 {{ $loop->first ? 'bg-blue-50 dark:bg-blue-900' : 'bg-gray-50 dark:bg-gray-700' }}">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="text-base font-semibold text-gray-900 dark:text-white">
                                        Evaluation by {{ $evaluation->evaluator->first_name }} {{ $evaluation->evaluator->last_name }}
                                    </h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $evaluation->evaluation_date->format('M d, Y') }}
                                        @if ($loop->first)
                                            <span class="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300 rounded-full">Latest</span>
                                        @endif
                                    </p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                        {{ number_format($evaluation->overall_score, 1) }}/5
                                    </div>
                                    <p class="text-sm font-medium 
                                        @if ($evaluation->overall_score >= 4.5)
                                            text-green-600 dark:text-green-400
                                        @elseif ($evaluation->overall_score >= 3.5)
                                            text-blue-600 dark:text-blue-400
                                        @elseif ($evaluation->overall_score >= 2.5)
                                            text-yellow-600 dark:text-yellow-400
                                        @elseif ($evaluation->overall_score >= 1.5)
                                            text-orange-600 dark:text-orange-400
                                        @else
                                            text-red-600 dark:text-red-400
                                        @endif
                                    ">
                                        {{ $evaluation->getRatingDescription() }}
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Category Scores -->
                            <div class="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                                @if ($evaluation->greeting_score)
                                    <div>
                                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Greeting</p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mr-2">
                                                <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ ($evaluation->greeting_score / 5) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $evaluation->greeting_score }}</span>
                                        </div>
                                    </div>
                                @endif
                                
                                @if ($evaluation->communication_score)
                                    <div>
                                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Communication</p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mr-2">
                                                <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ ($evaluation->communication_score / 5) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $evaluation->communication_score }}</span>
                                        </div>
                                    </div>
                                @endif
                                
                                @if ($evaluation->knowledge_score)
                                    <div>
                                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Knowledge</p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mr-2">
                                                <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ ($evaluation->knowledge_score / 5) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $evaluation->knowledge_score }}</span>
                                        </div>
                                    </div>
                                @endif
                                
                                @if ($evaluation->problem_solving_score)
                                    <div>
                                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Problem Solving</p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mr-2">
                                                <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ ($evaluation->problem_solving_score / 5) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $evaluation->problem_solving_score }}</span>
                                        </div>
                                    </div>
                                @endif
                                
                                @if ($evaluation->closing_score)
                                    <div>
                                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Closing</p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mr-2">
                                                <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ ($evaluation->closing_score / 5) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $evaluation->closing_score }}</span>
                                        </div>
                                    </div>
                                @endif
                                
                                @if ($evaluation->compliance_score)
                                    <div>
                                        <p class="text-xs font-medium text-gray-500 dark:text-gray-400">Compliance</p>
                                        <div class="flex items-center mt-1">
                                            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700 mr-2">
                                                <div class="bg-primary-600 h-1.5 rounded-full" style="width: {{ ($evaluation->compliance_score / 5) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $evaluation->compliance_score }}</span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Feedback -->
                            <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                                @if ($evaluation->strengths)
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-900 dark:text-white">Strengths</h5>
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 whitespace-pre-line">{{ $evaluation->strengths }}</p>
                                    </div>
                                @endif
                                
                                @if ($evaluation->areas_for_improvement)
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-900 dark:text-white">Areas for Improvement</h5>
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 whitespace-pre-line">{{ $evaluation->areas_for_improvement }}</p>
                                    </div>
                                @endif
                                
                                @if ($evaluation->action_items)
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-900 dark:text-white">Action Items</h5>
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 whitespace-pre-line">{{ $evaluation->action_items }}</p>
                                    </div>
                                @endif
                            </div>
                            
                            @if ($evaluation->notes)
                                <div class="mt-4">
                                    <h5 class="text-sm font-medium text-gray-900 dark:text-white">Notes</h5>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 whitespace-pre-line">{{ $evaluation->notes }}</p>
                                </div>
                            @endif
                            
                            <!-- Actions -->
                            <div class="mt-4 flex justify-end">
                                <a href="{{ route('call-quality.evaluation-edit', $evaluation->id) }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                    Edit Evaluation
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
        
        <!-- Evaluation Modal -->
        <div wire:ignore>
            <livewire:call-quality.evaluation-form wire:key="evaluation-form-{{ $call->id }}" />
        </div>
    </x-content-body>
</x-content>
