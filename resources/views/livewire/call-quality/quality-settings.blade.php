<x-content>
    <x-content-header>
        <x-slot name="title">Quality Settings</x-slot>
        <x-slot name="description">Configure call quality monitoring settings and KPI targets</x-slot>
        <x-slot name="actions">
            <button wire:click="openCreateModal" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add KPI Target
            </button>
        </x-slot>
    </x-content-header>

    <x-content-body>
        <!-- Settings Tabs -->
        <div class="mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700">
                <nav class="-mb-px flex space-x-8">
                    <button wire:click="setActiveTab('kpi_targets')" class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'kpi_targets' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}">
                        KPI Targets
                    </button>
                    <button wire:click="setActiveTab('evaluation_settings')" class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'evaluation_settings' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}">
                        Evaluation Settings
                    </button>
                    <button wire:click="setActiveTab('notification_settings')" class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'notification_settings' ? 'border-primary-500 text-primary-600 dark:text-primary-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }}">
                        Notifications
                    </button>
                </nav>
            </div>
        </div>

        @if($activeTab === 'kpi_targets')
            <!-- KPI Targets -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-semibold dark:text-white">KPI Targets</h3>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            {{ $kpiTargets->total() }} targets
                        </span>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-4 py-3">Metric</th>
                                <th scope="col" class="px-4 py-3">Type</th>
                                <th scope="col" class="px-4 py-3">Target</th>
                                <th scope="col" class="px-4 py-3">Period</th>
                                <th scope="col" class="px-4 py-3">Status</th>
                                <th scope="col" class="px-4 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($kpiTargets as $target)
                                <tr class="border-b dark:border-gray-700">
                                    <td class="px-4 py-3">
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">{{ $metricOptions[$target->metric_name] ?? $target->metric_name }}</p>
                                            @if($target->description)
                                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ \Illuminate\Support\Str::limit($target->description, 50) }}</p>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                            {{ ucfirst($target->target_type) }}
                                        </span>
                                        @if($target->user)
                                            <div class="text-xs text-gray-500 mt-1">{{ $target->user->first_name }} {{ $target->user->last_name }}</div>
                                        @endif
                                        @if($target->campaign)
                                            <div class="text-xs text-gray-500 mt-1">{{ $target->campaign->name }}</div>
                                        @endif
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="text-sm">
                                            <div class="font-medium">{{ $target->target_value }} {{ $unitOptions[$target->unit] ?? $target->unit }}</div>
                                            @if($target->min_value || $target->max_value)
                                                <div class="text-xs text-gray-500">
                                                    Range: {{ $target->min_value ?? 'N/A' }} - {{ $target->max_value ?? 'N/A' }}
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="text-sm">
                                            <div>{{ $target->start_date->format('M d, Y') }}</div>
                                            @if($target->end_date)
                                                <div class="text-xs text-gray-500">to {{ $target->end_date->format('M d, Y') }}</div>
                                            @else
                                                <div class="text-xs text-gray-500">Ongoing</div>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <button wire:click="toggleActive({{ $target->id }})" class="px-2 py-1 text-xs font-medium rounded-full {{ $target->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                            {{ $target->is_active ? 'Active' : 'Inactive' }}
                                        </button>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="flex items-center space-x-2">
                                            <button wire:click="openEditModal({{ $target->id }})" class="text-blue-600 hover:underline dark:text-blue-500">
                                                Edit
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-4 py-6 text-center">
                                        <div class="flex flex-col items-center justify-center">
                                            <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                            </svg>
                                            <p class="mt-2 text-gray-500 dark:text-gray-400">No KPI targets configured</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4">
                    {{ $kpiTargets->links() }}
                </div>
            </div>
        @elseif($activeTab === 'evaluation_settings')
            <!-- Evaluation Settings -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="text-xl font-semibold dark:text-white mb-4">Evaluation Settings</h3>
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Default Evaluation Scale</label>
                            <select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                                <option value="1-5">1-5 Scale</option>
                                <option value="1-10">1-10 Scale</option>
                                <option value="percentage">Percentage (0-100%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Minimum Score for Pass</label>
                            <input type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white" value="3.0" step="0.1">
                        </div>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Require comments for scores below passing threshold</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Enable peer evaluations</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Auto-assign evaluations to supervisors</label>
                    </div>
                </div>
            </div>
        @elseif($activeTab === 'notification_settings')
            <!-- Notification Settings -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="text-xl font-semibold dark:text-white mb-4">Notification Settings</h3>
                <div class="space-y-6">
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Email Notifications</h4>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                <label class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Notify agents when evaluations are completed</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                <label class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Notify supervisors of low-scoring evaluations</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                <label class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Send weekly quality reports to managers</label>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Alert Thresholds</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Low Score Alert Threshold</label>
                                <input type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white" value="2.5" step="0.1">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Consecutive Low Scores Alert</label>
                                <input type="number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white" value="3" min="1">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </x-content-body>
</x-content>
