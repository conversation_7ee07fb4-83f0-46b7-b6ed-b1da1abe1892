<x-content>
    <x-content-header title="Call Quality Dashboard">
        <div class="flex items-center space-x-4">
            <select wire:model.live="period" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option value="day">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
            </select>
            
            <select wire:model.live="selectedCampaignId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option value="">All Campaigns</option>
                @foreach($campaigns as $campaign)
                    <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                @endforeach
            </select>
        </div>
    </x-content-header>

    <x-content-body>
        <!-- Call Stats Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Total Calls</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ number_format($callStats['total']) }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-8 h-8 text-blue-500 bg-blue-100 rounded-lg dark:text-blue-300 dark:bg-blue-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Inbound Calls</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ number_format($callStats['inbound']) }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:text-green-300 dark:bg-green-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.414 7l3.293-3.293a1 1 0 00-1.414-1.414L13 5.586V4a1 1 0 10-2 0v4.003a.996.996 0 00.617.921A.997.997 0 0012 9h4a1 1 0 100-2h-1.586z"></path>
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Outbound Calls</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ number_format($callStats['outbound']) }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-8 h-8 text-purple-500 bg-purple-100 rounded-lg dark:text-purple-300 dark:bg-purple-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.414 7l3.293-3.293a1 1 0 00-1.414-1.414L13 5.586V4a1 1 0 10-2 0v4.003a.996.996 0 00.617.921A.997.997 0 0012 9h4a1 1 0 100-2h-1.586z"></path>
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Evaluated Calls</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ number_format($callStats['evaluated']) }}</h3>
                    </div>
                    <div class="flex items-center justify-center w-8 h-8 text-yellow-500 bg-yellow-100 rounded-lg dark:text-yellow-300 dark:bg-yellow-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Evaluation Rate</p>
                        <h3 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">{{ $callStats['evaluation_rate'] }}%</h3>
                    </div>
                    <div class="flex items-center justify-center w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:text-red-300 dark:bg-red-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Quality Score Trend -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Quality Score Trend</h3>
                </div>
                <div id="quality-trend-chart" class="w-full h-80" data-chart="{{ json_encode($qualityTrend) }}"></div>
            </div>
            
            <!-- Evaluation Score Distribution -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Evaluation Score Distribution</h3>
                    <span class="text-base font-bold text-gray-500 dark:text-gray-400">Avg: {{ number_format($evaluationStats['average_score'], 2) }}</span>
                </div>
                <div id="score-distribution-chart" class="w-full h-80" data-chart="{{ json_encode($evaluationStats['score_distribution']) }}"></div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Top Performers -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Top Performers</h3>
                </div>
                <div class="flow-root">
                    <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($topPerformers as $performer)
                            <li class="py-3 sm:py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-600 dark:text-primary-300 font-bold">
                                            {{ substr($performer['name'], 0, 1) }}
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                            {{ $performer['name'] }}
                                        </p>
                                        <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                            {{ $performer['evaluations_count'] }} evaluations
                                        </p>
                                    </div>
                                    <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                        {{ number_format($performer['average_score'], 2) }}
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="py-3 sm:py-4">
                                <p class="text-sm text-gray-500 dark:text-gray-400 text-center">No data available</p>
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>
            
            <!-- Areas for Improvement -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Areas for Improvement</h3>
                </div>
                <div class="flow-root">
                    <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($improvementAreas as $area)
                            <li class="py-3 sm:py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                            {{ $area['name'] }}
                                        </p>
                                        <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                            {{ ucfirst($area['category']) }}
                                        </p>
                                    </div>
                                    <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                        {{ number_format($area['average_score'], 2) }}
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="py-3 sm:py-4">
                                <p class="text-sm text-gray-500 dark:text-gray-400 text-center">No data available</p>
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 gap-4 mb-4">
            <!-- Campaign Performance -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Campaign Performance</h3>
                </div>
                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Campaign</th>
                                <th scope="col" class="px-6 py-3">Calls</th>
                                <th scope="col" class="px-6 py-3">Evaluated</th>
                                <th scope="col" class="px-6 py-3">Evaluation Rate</th>
                                <th scope="col" class="px-6 py-3">Avg. Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($campaignPerformance as $campaign)
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {{ $campaign['name'] }}
                                    </th>
                                    <td class="px-6 py-4">{{ number_format($campaign['calls_count']) }}</td>
                                    <td class="px-6 py-4">{{ number_format($campaign['evaluations_count']) }}</td>
                                    <td class="px-6 py-4">{{ $campaign['evaluation_rate'] }}%</td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-700 mr-2">
                                                <div class="h-2.5 rounded-full bg-primary-600" style="width: {{ min(100, $campaign['average_score'] * 20) }}%"></div>
                                            </div>
                                            <span>{{ number_format($campaign['average_score'], 2) }}</span>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td colspan="5" class="px-6 py-4 text-center">No data available</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 gap-4">
            <!-- Recent Evaluations -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Recent Evaluations</h3>
                    <a href="{{ route('call-quality.evaluations') }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                        View all
                    </a>
                </div>
                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Agent</th>
                                <th scope="col" class="px-6 py-3">Evaluator</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Score</th>
                                <th scope="col" class="px-6 py-3">Rating</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($recentEvaluations as $evaluation)
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {{ $evaluation['agent_name'] }}
                                    </th>
                                    <td class="px-6 py-4">{{ $evaluation['evaluator_name'] }}</td>
                                    <td class="px-6 py-4">{{ $evaluation['date'] }}</td>
                                    <td class="px-6 py-4">{{ number_format($evaluation['score'], 2) }}</td>
                                    <td class="px-6 py-4">
                                        @php
                                            $ratingClass = match($evaluation['rating']) {
                                                'Exceptional' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                                'Exceeds Expectations' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                                'Meets Expectations' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                                'Needs Improvement' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
                                                'Unsatisfactory' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                                default => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                                            };
                                        @endphp
                                        <span class="px-2 py-1 text-xs font-medium {{ $ratingClass }} rounded-full">
                                            {{ $evaluation['rating'] }}
                                        </span>
                                    </td>
                                </tr>
                            @empty
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td colspan="5" class="px-6 py-4 text-center">No recent evaluations</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </x-content-body>

    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeQualityTrendChart();
            initializeScoreDistributionChart();
            
            // Listen for Livewire updates
            document.addEventListener('livewire:initialized', function() {
                Livewire.hook('message.processed', (message, component) => {
                    if (component.fingerprint.name === 'call-quality.dashboard') {
                        initializeQualityTrendChart();
                        initializeScoreDistributionChart();
                    }
                });
            });
        });
        
        function initializeQualityTrendChart() {
            const chartElement = document.getElementById('quality-trend-chart');
            if (!chartElement) return;
            
            const chartData = JSON.parse(chartElement.getAttribute('data-chart'));
            
            if (window.qualityTrendChart) {
                window.qualityTrendChart.destroy();
            }
            
            window.qualityTrendChart = new ApexCharts(chartElement, {
                series: [{
                    name: 'Quality Score',
                    data: chartData.scores
                }],
                chart: {
                    height: 320,
                    type: 'line',
                    zoom: {
                        enabled: false
                    },
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                grid: {
                    borderColor: '#e0e0e0',
                    row: {
                        colors: ['transparent', 'transparent'],
                        opacity: 0.5
                    },
                },
                xaxis: {
                    categories: chartData.labels,
                    labels: {
                        style: {
                            colors: '#9ca3af'
                        }
                    }
                },
                yaxis: {
                    min: 0,
                    max: 5,
                    labels: {
                        style: {
                            colors: '#9ca3af'
                        }
                    }
                },
                colors: ['#3b82f6'],
                tooltip: {
                    theme: 'dark'
                }
            });
            
            window.qualityTrendChart.render();
        }
        
        function initializeScoreDistributionChart() {
            const chartElement = document.getElementById('score-distribution-chart');
            if (!chartElement) return;
            
            const chartData = JSON.parse(chartElement.getAttribute('data-chart'));
            
            if (window.scoreDistributionChart) {
                window.scoreDistributionChart.destroy();
            }
            
            window.scoreDistributionChart = new ApexCharts(chartElement, {
                series: [
                    chartData.exceptional || 0,
                    chartData.exceeds || 0,
                    chartData.meets || 0,
                    chartData.needs_improvement || 0,
                    chartData.unsatisfactory || 0
                ],
                chart: {
                    height: 320,
                    type: 'donut',
                },
                labels: [
                    'Exceptional',
                    'Exceeds Expectations',
                    'Meets Expectations',
                    'Needs Improvement',
                    'Unsatisfactory'
                ],
                colors: ['#10b981', '#3b82f6', '#f59e0b', '#f97316', '#ef4444'],
                legend: {
                    position: 'bottom',
                    labels: {
                        colors: '#9ca3af'
                    }
                },
                dataLabels: {
                    enabled: true,
                    formatter: function (val) {
                        return val.toFixed(1) + "%";
                    }
                },
                tooltip: {
                    theme: 'dark'
                }
            });
            
            window.scoreDistributionChart.render();
        }
    </script>
    @endpush
</x-content>
