<x-content>
    <x-content-header>
        <x-slot name="title">Quality Criteria</x-slot>
        <x-slot name="description">Manage evaluation criteria and scoring rules for call quality assessments</x-slot>
        <x-slot name="actions">
            <button wire:click="openCreateModal" class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Criteria
            </button>
        </x-slot>
    </x-content-header>

    <x-content-body>
        <!-- Filters -->
        <div class="mb-6 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Filters</h3>
                <button wire:click="toggleFilters" class="text-primary-600 hover:text-primary-700 dark:text-primary-400">
                    {{ $showFilters ? 'Hide Filters' : 'Show Filters' }}
                </button>
            </div>
            
            @if($showFilters)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Search</label>
                        <input type="text" wire:model.debounce.300ms="search" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search criteria...">
                    </div>
                    <div>
                        <label for="selectedCampaign" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign</label>
                        <select wire:model="selectedCampaign" id="selectedCampaign" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">All Campaigns</option>
                            <option value="global">Global Criteria</option>
                            @foreach($campaigns as $campaign)
                                <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="selectedCategory" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Category</label>
                        <select wire:model="selectedCategory" id="selectedCategory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category }}">{{ ucfirst($category) }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex items-center">
                        <div class="flex items-center h-5">
                            <input wire:model="showInactive" id="showInactive" type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        </div>
                        <div class="ml-2 text-sm">
                            <label for="showInactive" class="font-medium text-gray-900 dark:text-gray-300">Show Inactive</label>
                        </div>
                    </div>
                </div>
                <div class="mt-4 flex justify-end">
                    <button wire:click="resetFilters" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        Reset Filters
                    </button>
                </div>
            @endif
        </div>

        <!-- Criteria Table -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-semibold dark:text-white">Evaluation Criteria</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Showing {{ $criteria->firstItem() ?? 0 }} - {{ $criteria->lastItem() ?? 0 }} of {{ $criteria->total() }}
                    </span>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3 cursor-pointer" wire:click="sortBy('name')">
                                Name
                                @if($sortField === 'name')
                                    <span class="ml-1">{{ $sortDirection === 'asc' ? '↑' : '↓' }}</span>
                                @endif
                            </th>
                            <th scope="col" class="px-4 py-3">Category</th>
                            <th scope="col" class="px-4 py-3">Campaign</th>
                            <th scope="col" class="px-4 py-3 cursor-pointer" wire:click="sortBy('weight')">
                                Weight
                                @if($sortField === 'weight')
                                    <span class="ml-1">{{ $sortDirection === 'asc' ? '↑' : '↓' }}</span>
                                @endif
                            </th>
                            <th scope="col" class="px-4 py-3">Status</th>
                            <th scope="col" class="px-4 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($criteria as $criterion)
                            <tr class="border-b dark:border-gray-700">
                                <td class="px-4 py-3">
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $criterion->name }}</p>
                                        @if($criterion->description)
                                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ \Illuminate\Support\Str::limit($criterion->description, 50) }}</p>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                        {{ ucfirst($criterion->category) }}
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    @if($criterion->campaign)
                                        {{ $criterion->campaign->name }}
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">Global</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    <span class="font-medium">{{ $criterion->weight }}</span>
                                    @if($criterion->is_required)
                                        <span class="ml-1 text-xs text-red-600 dark:text-red-400">*</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    <button wire:click="toggleActive({{ $criterion->id }})" class="px-2 py-1 text-xs font-medium rounded-full {{ $criterion->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                        {{ $criterion->is_active ? 'Active' : 'Inactive' }}
                                    </button>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center space-x-2">
                                        <button wire:click="openEditModal({{ $criterion->id }})" class="text-blue-600 hover:underline dark:text-blue-500">
                                            Edit
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-4 py-6 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        </svg>
                                        <p class="mt-2 text-gray-500 dark:text-gray-400">No criteria found</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="mt-4">
                {{ $criteria->links() }}
            </div>
        </div>
    </x-content-body>
</x-content>
