<div>
    <!-- Evaluation Modal -->
    <div x-data="{ open: false }" 
         x-show="open" 
         x-on:open-evaluation-modal.window="open = true; $wire.loadCall($event.detail.callId)" 
         x-on:evaluation-completed.window="open = false; $wire.dispatch('evaluation-completed')"
         x-on:keydown.escape.window="open = false"
         class="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50"
         style="display: none;">
        <div x-on:click.away="open = false" class="relative w-full max-w-4xl p-4 mx-auto bg-white rounded-lg shadow dark:bg-gray-800 sm:p-5 max-h-[90vh] overflow-y-auto">
            <div class="flex items-center justify-between pb-4 mb-4 border-b rounded-t border-gray-200 dark:border-gray-600">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $isEditMode ? 'Edit Evaluation' : 'New Evaluation' }}
                </h3>
                <button type="button" x-on:click="open = false" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            
            @if ($call)
                <div class="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Agent</p>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $call->agent->first_name }} {{ $call->agent->last_name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Campaign</p>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $call->campaign->name }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Call Time</p>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $call->call_time->format('M d, Y H:i') }}</p>
                        </div>
                    </div>
                </div>
                
                <form wire:submit.prevent="saveEvaluation">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="evaluationDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Evaluation Date</label>
                            <input type="date" id="evaluationDate" wire:model="evaluationDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            @error('evaluationDate') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>
                        <div>
                            <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                            <select id="status" wire:model="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                <option value="draft">Draft</option>
                                <option value="completed">Completed</option>
                                <option value="reviewed">Reviewed</option>
                            </select>
                            @error('status') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>
                    </div>
                    
                    <!-- Standard Evaluation Categories -->
                    <div class="mb-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                        <h4 class="text-base font-semibold text-gray-900 dark:text-white mb-4">Standard Evaluation Categories</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label for="greetingScore" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Greeting</label>
                                <select id="greetingScore" wire:model="greetingScore" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Applicable</option>
                                    <option value="1">1 - Unsatisfactory</option>
                                    <option value="1.5">1.5</option>
                                    <option value="2">2 - Needs Improvement</option>
                                    <option value="2.5">2.5</option>
                                    <option value="3">3 - Meets Expectations</option>
                                    <option value="3.5">3.5</option>
                                    <option value="4">4 - Exceeds Expectations</option>
                                    <option value="4.5">4.5</option>
                                    <option value="5">5 - Exceptional</option>
                                </select>
                                @error('greetingScore') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="communicationScore" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Communication</label>
                                <select id="communicationScore" wire:model="communicationScore" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Applicable</option>
                                    <option value="1">1 - Unsatisfactory</option>
                                    <option value="1.5">1.5</option>
                                    <option value="2">2 - Needs Improvement</option>
                                    <option value="2.5">2.5</option>
                                    <option value="3">3 - Meets Expectations</option>
                                    <option value="3.5">3.5</option>
                                    <option value="4">4 - Exceeds Expectations</option>
                                    <option value="4.5">4.5</option>
                                    <option value="5">5 - Exceptional</option>
                                </select>
                                @error('communicationScore') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="knowledgeScore" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Knowledge</label>
                                <select id="knowledgeScore" wire:model="knowledgeScore" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Applicable</option>
                                    <option value="1">1 - Unsatisfactory</option>
                                    <option value="1.5">1.5</option>
                                    <option value="2">2 - Needs Improvement</option>
                                    <option value="2.5">2.5</option>
                                    <option value="3">3 - Meets Expectations</option>
                                    <option value="3.5">3.5</option>
                                    <option value="4">4 - Exceeds Expectations</option>
                                    <option value="4.5">4.5</option>
                                    <option value="5">5 - Exceptional</option>
                                </select>
                                @error('knowledgeScore') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="problemSolvingScore" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Problem Solving</label>
                                <select id="problemSolvingScore" wire:model="problemSolvingScore" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Applicable</option>
                                    <option value="1">1 - Unsatisfactory</option>
                                    <option value="1.5">1.5</option>
                                    <option value="2">2 - Needs Improvement</option>
                                    <option value="2.5">2.5</option>
                                    <option value="3">3 - Meets Expectations</option>
                                    <option value="3.5">3.5</option>
                                    <option value="4">4 - Exceeds Expectations</option>
                                    <option value="4.5">4.5</option>
                                    <option value="5">5 - Exceptional</option>
                                </select>
                                @error('problemSolvingScore') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="closingScore" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Closing</label>
                                <select id="closingScore" wire:model="closingScore" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Applicable</option>
                                    <option value="1">1 - Unsatisfactory</option>
                                    <option value="1.5">1.5</option>
                                    <option value="2">2 - Needs Improvement</option>
                                    <option value="2.5">2.5</option>
                                    <option value="3">3 - Meets Expectations</option>
                                    <option value="3.5">3.5</option>
                                    <option value="4">4 - Exceeds Expectations</option>
                                    <option value="4.5">4.5</option>
                                    <option value="5">5 - Exceptional</option>
                                </select>
                                @error('closingScore') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="complianceScore" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Compliance</label>
                                <select id="complianceScore" wire:model="complianceScore" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Applicable</option>
                                    <option value="1">1 - Unsatisfactory</option>
                                    <option value="1.5">1.5</option>
                                    <option value="2">2 - Needs Improvement</option>
                                    <option value="2.5">2.5</option>
                                    <option value="3">3 - Meets Expectations</option>
                                    <option value="3.5">3.5</option>
                                    <option value="4">4 - Exceeds Expectations</option>
                                    <option value="4.5">4.5</option>
                                    <option value="5">5 - Exceptional</option>
                                </select>
                                @error('complianceScore') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                        </div>
                    </div>
                    
                    <!-- Specific Criteria -->
                    @if ($criteria && count($criteria) > 0)
                        <div class="mb-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                            <h4 class="text-base font-semibold text-gray-900 dark:text-white mb-4">Specific Evaluation Criteria</h4>
                            
                            @foreach ($criteria as $category => $items)
                                <div class="mb-4">
                                    <h5 class="text-sm font-medium text-gray-900 dark:text-white mb-2">{{ ucfirst($category) }}</h5>
                                    <div class="space-y-3">
                                        @foreach ($items as $item)
                                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-start">
                                                <div class="md:col-span-1">
                                                    <label for="criteria_{{ $item->id }}" class="block text-sm font-medium text-gray-900 dark:text-white">
                                                        {{ $item->name }}
                                                        @if ($item->is_required)
                                                            <span class="text-red-500">*</span>
                                                        @endif
                                                    </label>
                                                    @if ($item->description)
                                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ $item->description }}</p>
                                                    @endif
                                                </div>
                                                <div class="md:col-span-1">
                                                    <select id="criteria_{{ $item->id }}" wire:model="criteriaScores.{{ $item->id }}" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                                        <option value="">Not Applicable</option>
                                                        <option value="1">1 - Unsatisfactory</option>
                                                        <option value="1.5">1.5</option>
                                                        <option value="2">2 - Needs Improvement</option>
                                                        <option value="2.5">2.5</option>
                                                        <option value="3">3 - Meets Expectations</option>
                                                        <option value="3.5">3.5</option>
                                                        <option value="4">4 - Exceeds Expectations</option>
                                                        <option value="4.5">4.5</option>
                                                        <option value="5">5 - Exceptional</option>
                                                    </select>
                                                    @error('criteriaScores.'.$item->id) <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                                                </div>
                                                <div class="md:col-span-2">
                                                    <input type="text" id="comment_{{ $item->id }}" wire:model="criteriaComments.{{ $item->id }}" placeholder="Comment (optional)" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                    
                    <!-- Overall Score -->
                    <div class="mb-4 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-base font-semibold text-gray-900 dark:text-white">Overall Score</h4>
                            <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $overallScore ? number_format($overallScore, 1) : 'N/A' }}
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="strengths" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Strengths</label>
                                <textarea id="strengths" wire:model="strengths" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="What did the agent do well?"></textarea>
                                @error('strengths') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="areasForImprovement" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Areas for Improvement</label>
                                <textarea id="areasForImprovement" wire:model="areasForImprovement" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="What could the agent improve?"></textarea>
                                @error('areasForImprovement') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div>
                                <label for="actionItems" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Action Items</label>
                                <textarea id="actionItems" wire:model="actionItems" rows="4" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Specific actions for the agent to take"></textarea>
                                @error('actionItems') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <label for="notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Additional Notes</label>
                            <textarea id="notes" wire:model="notes" rows="2" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Any additional notes or context"></textarea>
                            @error('notes') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-end space-x-4">
                        <button type="button" x-on:click="open = false" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                            Cancel
                        </button>
                        <button type="submit" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                            {{ $isEditMode ? 'Update Evaluation' : 'Save Evaluation' }}
                        </button>
                    </div>
                </form>
            @else
                <div class="py-4 text-center">
                    <p class="text-gray-500 dark:text-gray-400">Loading call information...</p>
                </div>
            @endif
        </div>
    </div>
</div>
