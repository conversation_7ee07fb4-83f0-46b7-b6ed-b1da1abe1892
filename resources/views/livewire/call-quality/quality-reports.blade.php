<x-content>
    <x-content-header>
        <x-slot name="title">Quality Reports</x-slot>
        <x-slot name="description">View and analyze call quality reports and performance trends</x-slot>
    </x-content-header>

    <x-content-body>
        <!-- Report Configuration -->
        <div class="mb-6 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Report Configuration</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="reportType" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Report Type</label>
                    <select wire:model="reportType" id="reportType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="agent_performance">Agent Performance</option>
                        <option value="campaign_performance">Campaign Performance</option>
                        <option value="evaluation_criteria">Evaluation Criteria</option>
                        <option value="quality_trend">Quality Trend</option>
                        <option value="evaluation_volume">Evaluation Volume</option>
                    </select>
                </div>
                
                <div>
                    <label for="startDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Date</label>
                    <input type="date" wire:model="startDate" id="startDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                </div>
                
                <div>
                    <label for="endDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End Date</label>
                    <input type="date" wire:model="endDate" id="endDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                </div>
                
                @if(in_array($reportType, ['quality_trend', 'evaluation_volume']))
                <div>
                    <label for="groupBy" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Group By</label>
                    <select wire:model="groupBy" id="groupBy" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="day">Day</option>
                        <option value="week">Week</option>
                        <option value="month">Month</option>
                    </select>
                </div>
                @endif
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                    <label for="selectedCampaign" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign</label>
                    <select wire:model="selectedCampaign" id="selectedCampaign" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Campaigns</option>
                        @foreach($campaigns as $campaign)
                            <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                        @endforeach
                    </select>
                </div>
                
                @if(in_array($reportType, ['agent_performance', 'quality_trend', 'evaluation_volume']))
                <div>
                    <label for="selectedAgent" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Agent</label>
                    <select wire:model="selectedAgent" id="selectedAgent" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Agents</option>
                        @foreach($agents as $agent)
                            <option value="{{ $agent->id }}">{{ $agent->first_name }} {{ $agent->last_name }}</option>
                        @endforeach
                    </select>
                </div>
                @endif
                
                @if($reportType === 'evaluation_criteria')
                <div>
                    <label for="selectedCriteria" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Criteria</label>
                    <select wire:model="selectedCriteria" id="selectedCriteria" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Criteria</option>
                        @foreach($criteria as $criterion)
                            <option value="{{ $criterion->id }}">{{ $criterion->name }}</option>
                        @endforeach
                    </select>
                </div>
                @endif
            </div>
        </div>

        <!-- Report Results -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-semibold dark:text-white">
                    @switch($reportType)
                        @case('agent_performance')
                            Agent Performance Report
                            @break
                        @case('campaign_performance')
                            Campaign Performance Report
                            @break
                        @case('evaluation_criteria')
                            Evaluation Criteria Report
                            @break
                        @case('quality_trend')
                            Quality Trend Report
                            @break
                        @case('evaluation_volume')
                            Evaluation Volume Report
                            @break
                    @endswitch
                </h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {{ count($reportData) }} records
                    </span>
                </div>
            </div>
            
            @if(count($reportData) > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                @switch($reportType)
                                    @case('agent_performance')
                                        <th scope="col" class="px-4 py-3">Agent</th>
                                        <th scope="col" class="px-4 py-3">Calls</th>
                                        <th scope="col" class="px-4 py-3">Evaluations</th>
                                        <th scope="col" class="px-4 py-3">Average Score</th>
                                        <th scope="col" class="px-4 py-3">Performance Distribution</th>
                                        @break
                                    @case('campaign_performance')
                                        <th scope="col" class="px-4 py-3">Campaign</th>
                                        <th scope="col" class="px-4 py-3">Calls</th>
                                        <th scope="col" class="px-4 py-3">Evaluations</th>
                                        <th scope="col" class="px-4 py-3">Average Score</th>
                                        <th scope="col" class="px-4 py-3">Evaluation Rate</th>
                                        @break
                                    @case('evaluation_criteria')
                                        <th scope="col" class="px-4 py-3">Criteria</th>
                                        <th scope="col" class="px-4 py-3">Category</th>
                                        <th scope="col" class="px-4 py-3">Scores</th>
                                        <th scope="col" class="px-4 py-3">Average Score</th>
                                        @break
                                    @case('quality_trend')
                                        <th scope="col" class="px-4 py-3">Date</th>
                                        <th scope="col" class="px-4 py-3">Evaluations</th>
                                        <th scope="col" class="px-4 py-3">Average Score</th>
                                        @break
                                    @case('evaluation_volume')
                                        <th scope="col" class="px-4 py-3">Date</th>
                                        <th scope="col" class="px-4 py-3">Calls</th>
                                        <th scope="col" class="px-4 py-3">Evaluations</th>
                                        <th scope="col" class="px-4 py-3">Evaluation Rate</th>
                                        @break
                                @endswitch
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($reportData as $row)
                                <tr class="border-b dark:border-gray-700">
                                    @switch($reportType)
                                        @case('agent_performance')
                                            <td class="px-4 py-3 font-medium text-gray-900 dark:text-white">{{ $row['name'] }}</td>
                                            <td class="px-4 py-3">{{ $row['calls_count'] }}</td>
                                            <td class="px-4 py-3">{{ $row['evaluations_count'] }}</td>
                                            <td class="px-4 py-3">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $row['average_score'] >= 4 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : ($row['average_score'] >= 3 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300') }}">
                                                    {{ $row['average_score'] }}
                                                </span>
                                            </td>
                                            <td class="px-4 py-3">
                                                <div class="text-xs">
                                                    <div>Exceptional: {{ $row['score_distribution']['exceptional'] }}%</div>
                                                    <div>Exceeds: {{ $row['score_distribution']['exceeds'] }}%</div>
                                                    <div>Meets: {{ $row['score_distribution']['meets'] }}%</div>
                                                </div>
                                            </td>
                                            @break
                                        @case('campaign_performance')
                                            <td class="px-4 py-3 font-medium text-gray-900 dark:text-white">{{ $row['name'] }}</td>
                                            <td class="px-4 py-3">{{ $row['calls_count'] }}</td>
                                            <td class="px-4 py-3">{{ $row['evaluations_count'] }}</td>
                                            <td class="px-4 py-3">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $row['average_score'] >= 4 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : ($row['average_score'] >= 3 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300') }}">
                                                    {{ $row['average_score'] }}
                                                </span>
                                            </td>
                                            <td class="px-4 py-3">{{ $row['evaluation_rate'] }}%</td>
                                            @break
                                        @case('evaluation_criteria')
                                            <td class="px-4 py-3 font-medium text-gray-900 dark:text-white">{{ $row['name'] }}</td>
                                            <td class="px-4 py-3">
                                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                                    {{ ucfirst($row['category']) }}
                                                </span>
                                            </td>
                                            <td class="px-4 py-3">{{ $row['scores_count'] }}</td>
                                            <td class="px-4 py-3">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $row['average_score'] >= 4 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : ($row['average_score'] >= 3 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300') }}">
                                                    {{ $row['average_score'] }}
                                                </span>
                                            </td>
                                            @break
                                        @case('quality_trend')
                                            <td class="px-4 py-3 font-medium text-gray-900 dark:text-white">{{ $row['date'] }}</td>
                                            <td class="px-4 py-3">{{ $row['evaluations_count'] }}</td>
                                            <td class="px-4 py-3">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $row['average_score'] >= 4 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : ($row['average_score'] >= 3 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300') }}">
                                                    {{ $row['average_score'] }}
                                                </span>
                                            </td>
                                            @break
                                        @case('evaluation_volume')
                                            <td class="px-4 py-3 font-medium text-gray-900 dark:text-white">{{ $row['date'] }}</td>
                                            <td class="px-4 py-3">{{ $row['calls_count'] }}</td>
                                            <td class="px-4 py-3">{{ $row['evaluations_count'] }}</td>
                                            <td class="px-4 py-3">{{ $row['evaluation_rate'] }}%</td>
                                            @break
                                    @endswitch
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="flex flex-col items-center justify-center py-12">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <p class="mt-2 text-gray-500 dark:text-gray-400">No data available for the selected criteria</p>
                </div>
            @endif
        </div>
    </x-content-body>
</x-content>
