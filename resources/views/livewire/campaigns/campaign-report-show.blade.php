<div>
    <x-content-header title="{{ __('Report Details') }}">
        <x-page-button type="back" label="{{ __('Back to Reports') }}" action="backToReports" />
        <x-page-button type="primary" label="{{ __('Export PDF') }}" action="exportReport('pdf')" />
        <x-page-button type="secondary" label="{{ __('Export DOCX') }}" action="exportReport('docx')" />
    </x-content-header>

    <x-content-body>
        <div wire:loading class="flex justify-center my-8">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>

        <div wire:loading.remove>
            @if($report)
                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $report->title ?? "Report #{$report->id}" }}</h2>
                            <div class="flex space-x-2">
                                <span class="px-3 py-1 text-sm font-medium rounded-full
                                    {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                      ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                      ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                      ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                    {{ ucfirst($report->status) }}
                                </span>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('Report Information') }}</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Created By') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ $report->creator->first_name }} {{ $report->creator->last_name }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Date') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ $report->date->format('M d, Y') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Campaign') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ $report->campaign->name }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Type') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ ucfirst($report->type ?? 'Standard') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Category') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ ucfirst($report->category ?? 'General') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Priority') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ ucfirst($report->priority ?? 'Normal') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('Approval Information') }}</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Status') }}</p>
                                            <p class="text-base font-medium
                                                {{ $report->approval_status === 'approved' ? 'text-green-600 dark:text-green-400' :
                                                  ($report->approval_status === 'rejected' ? 'text-red-600 dark:text-red-400' :
                                                  'text-yellow-600 dark:text-yellow-400') }}">
                                                {{ ucfirst($report->approval_status) }}
                                            </p>
                                        </div>
                                        @if($report->approver)
                                            <div>
                                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Approved/Rejected By') }}</p>
                                                <p class="text-base text-gray-900 dark:text-white">{{ $report->approver->first_name }} {{ $report->approver->last_name }}</p>
                                            </div>
                                        @endif
                                        @if($report->approved_at)
                                            <div>
                                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Approved At') }}</p>
                                                <p class="text-base text-gray-900 dark:text-white">{{ $report->approved_at->format('M d, Y H:i') }}</p>
                                            </div>
                                        @endif
                                        @if($report->rejected_at)
                                            <div>
                                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Rejected At') }}</p>
                                                <p class="text-base text-gray-900 dark:text-white">{{ $report->rejected_at->format('M d, Y H:i') }}</p>
                                            </div>
                                        @endif
                                    </div>

                                    @if(auth()->user()->role_id <= 3 && $report->approval_status === 'pending')
                                        <div class="mt-4 flex space-x-2">
                                            <button wire:click="approveReport" class="px-4 py-2 text-sm font-medium rounded-lg bg-green-600 text-white hover:bg-green-700">
                                                <i class="fas fa-check mr-1"></i> {{ __('Approve') }}
                                            </button>
                                            <button wire:click="rejectReport" class="px-4 py-2 text-sm font-medium rounded-lg bg-red-600 text-white hover:bg-red-700">
                                                <i class="fas fa-times mr-1"></i> {{ __('Reject') }}
                                            </button>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('Content') }}</h3>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <div class="prose max-w-none text-gray-700 dark:text-gray-300">{!! $report->content !!}</div>
                            </div>
                        </div>

                        @if($report->performance_score || $report->quality_score || $report->compliance_score)
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('Performance Metrics') }}</h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    @if($report->performance_score)
                                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Performance') }}</p>
                                            <p class="text-2xl font-bold {{ $report->performance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->performance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                {{ $report->performance_score }}
                                            </p>
                                        </div>
                                    @endif
                                    @if($report->quality_score)
                                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Quality') }}</p>
                                            <p class="text-2xl font-bold {{ $report->quality_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->quality_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                {{ $report->quality_score }}
                                            </p>
                                        </div>
                                    @endif
                                    @if($report->compliance_score)
                                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ __('Compliance') }}</p>
                                            <p class="text-2xl font-bold {{ $report->compliance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->compliance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                {{ $report->compliance_score }}
                                            </p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        @if(!empty($report->tags))
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('Tags') }}</h3>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($report->tags as $tag)
                                        <span class="px-3 py-1 text-sm font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                            {{ $tag }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        @if($report->media->count() > 0)
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('Attachments') }}</h3>
                                <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                    <ul class="space-y-2">
                                        @foreach($report->media as $media)
                                            <li class="flex items-center">
                                                <i class="far fa-file mr-2 text-gray-500"></i>
                                                <a href="#" class="text-blue-600 hover:underline dark:text-blue-400">{{ $media->file_name }}</a>
                                                <span class="ml-2 text-xs text-gray-500">({{ $media->human_readable_size }})</span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
                    <p class="text-gray-700 dark:text-gray-300">{{ __('Report not found') }}</p>
                </div>
            @endif
        </div>
    </x-content-body>
</div>
