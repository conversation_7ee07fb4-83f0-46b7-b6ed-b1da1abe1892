<x-content>
    <x-card key="campaign-index-header" class="p-4">
        <div class="w-fCampaigns;1">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <div class="lg:pr-3">
                        <label for="campaigns-search" class="sr-only">Search</label>
                        <div class="relative mt-1 lg:w-64 xl:w-96">
                            <input
                                type="text"
                                wire:model.live.debounce.300ms="search"
                                id="campaigns-search"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Search for campaign">
                        </div>
                    </div>
                    <div class="flex pl-0 mt-3 space-x-1 sm:pl-2 sm:mt-0">
                        <button
                            wire:click="deleteSelected"
                            wire:confirm="Are you sure you want to delete the selected campaigns?"
                            class="inline-flex justify-center p-1 text-gray-500 rounded {{$selectedCampaigns ? 'cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white' : 'opacity-50 cursor-not-allowed'}}"
                            {{$selectedCampaigns ? '' : 'disabled'}}
                        >
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                        </button>
                    </div>
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <button type="button" wire:click="$dispatch('to-campaign-create')" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" wire:navigate>
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                        Add campaign
                    </button>
                    <a href="#" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path></svg>
                        Export
                    </a>
                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        @foreach ([5, 10, 25, 50, 100] as $value)
                            <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
    </x-card>
    <x-card key="campaign-index-table" class="overflow-x-auto mt-4">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="p-4 w-4">
                        <input
                            type="checkbox"
                            wire:model.live="selectAll"
                            wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            aria-label="Select all campaigns"
                        >
                    </th>
                    <th wire:click="sortBy('name')" class="cursor-pointer px-6 py-3">
                        Name {{ $sortField === 'name' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('mamager_id')" class="cursor-pointer px-6 py-3">
                        Manager {{ $sortField === 'mamager_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('customer_id')" class="cursor-pointer px-6 py-3">
                        Customer {{ $sortField === 'customer_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('status')" class="cursor-pointer px-6 py-3">
                        Status {{ $sortField === 'status' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                </tr>
            </thead>
            <tbody>
                @forelse ($campaigns as $campaign)
                    <tr wire:key="campaign-{{ $campaign->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="p-4 w-4">
                            <input
                                type="checkbox"
                                wire:change="toggleCampaignSelection({{ $campaign->id }})"
                                wire:key="checkbox-{{ $campaign->id }}-{{ in_array($campaign->id, $selectedCampaigns) ? 'checked' : 'unchecked' }}"
                                value="{{ $campaign->id }}"
                                {{ in_array($campaign->id, $selectedCampaigns) ? 'checked' : '' }}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                aria-label="Select campaign {{ $campaign->name }}"
                            >
                        </td>
                        <th scope="row" class="flex items-center px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white cursor-pointer" wire:click="$dispatch('to-campaign-show', { campaign: {{ $campaign->id }} })">
                            {{-- <img class="w-10 h-10 rounded-full" src="{{ $campaign->profilePicture()?->file_path }}" alt="{{ $campaign->name }}"> --}}
                            <div class="ps-3">
                                <div class="text-base font-semibold">{{ $campaign->name }}</div>
                                <div class="font-normal text-gray-500">{{ $campaign->email }}</div>
                            </div>
                        </th>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-campaign-show', { campaign: {{ $campaign->id }} })">
                            {{ $campaign->manager?->getFullNameAttribute() ?? 'N/A' }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-campaign-show', { campaign: {{ $campaign->id }} })">
                            {{ $campaign->customer->name ?? 'N/A' }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-campaign-show', { campaign: {{ $campaign->id }} })">
                            <div class="flex items-center">
                                <div class="h-2.5 w-2.5 rounded-full {{ in_array($campaign->status, ['active', 'actif']) ? 'bg-green-500' : 'bg-red-500' }} me-2"></div>
                                {{ ucfirst($campaign->status) }}
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No campaigns found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        <div class="p-4">
            {{ $campaigns->links() }}
        </div>
    </x-card>
</x-content>

