<div x-data="{ activeTab: 'overview' }">
    <!-- Customer Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                {{ $selectedCustomer->name }}
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full {{ $selectedCustomer->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                    {{ ucfirst($selectedCustomer->status) }}
                </span>
            </h2>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                {{ $selectedCustomer->industry ?? 'No industry specified' }} |
                {{ $selectedCustomer->customer_type ?? 'No type specified' }} |
                {{ $selectedCustomer->size ?? 'No size specified' }}
            </p>
        </div>

        <div class="flex space-x-2">
            <button wire:click="edit({{ $selectedCustomer->id }})" class="px-3 py-1.5 text-sm font-medium text-white bg-yellow-600 rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                <i class="fas fa-edit mr-1"></i> Edit
            </button>
            <button
                wire:click="delete({{ $selectedCustomer->id }})"
                class="px-3 py-1.5 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
                <i class="fas fa-trash mr-1"></i> Delete
            </button>
        </div>
    </div>

    <!-- Tabs -->
    <div class="border-b border-gray-200 dark:border-gray-700 mb-4">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
            <li class="mr-2">
                <button
                    type="button"
                    @click="activeTab = 'overview'"
                    :class="activeTab === 'overview' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                    class="inline-block p-4 border-b-2 rounded-t-lg"
                >
                    Overview
                </button>
            </li>
            <li class="mr-2">
                <button
                    type="button"
                    @click="activeTab = 'contacts'"
                    :class="activeTab === 'contacts' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                    class="inline-block p-4 border-b-2 rounded-t-lg"
                >
                    Contacts
                </button>
            </li>
            <li class="mr-2">
                <button
                    type="button"
                    @click="activeTab = 'campaigns'"
                    :class="activeTab === 'campaigns' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                    class="inline-block p-4 border-b-2 rounded-t-lg"
                >
                    Campaigns
                </button>
            </li>
            <li class="mr-2">
                <button
                    type="button"
                    @click="activeTab = 'documents'"
                    :class="activeTab === 'documents' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                    class="inline-block p-4 border-b-2 rounded-t-lg"
                >
                    Documents
                </button>
            </li>
            <li>
                <button
                    type="button"
                    @click="activeTab = 'activities'"
                    :class="activeTab === 'activities' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                    class="inline-block p-4 border-b-2 rounded-t-lg"
                >
                    Activities
                </button>
            </li>
        </ul>
    </div>

    <!-- Overview Tab -->
    <div x-show="activeTab === 'overview'" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Information</h3>

                <div class="space-y-3">
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->name }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Industry</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->industry ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Type</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->customer_type ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Size</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->size ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Segment</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->segment ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Tier</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->customer_tier ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</div>
                        <div class="col-span-2 text-sm">
                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $selectedCustomer->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                {{ ucfirst($selectedCustomer->status) }}
                            </span>
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Manager</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            {{ $selectedCustomer->accountManager->name ?? 'N/A' }}
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Satisfaction</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            @if($selectedCustomer->customer_satisfaction)
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star {{ $i <= $selectedCustomer->customer_satisfaction ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600' }} mr-1"></i>
                                    @endfor
                                    <span class="ml-1">{{ $selectedCustomer->customer_satisfaction }}/5</span>
                                </div>
                            @else
                                N/A
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Contact Information</h3>

                <div class="space-y-3">
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->email ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->phone ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Website</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            @if($selectedCustomer->website)
                                <a href="{{ $selectedCustomer->website }}" target="_blank" class="text-blue-600 dark:text-blue-500 hover:underline">
                                    {{ $selectedCustomer->website }}
                                </a>
                            @else
                                N/A
                            @endif
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            @if($selectedCustomer->address)
                                {{ $selectedCustomer->address }}<br>
                                {{ $selectedCustomer->city ?? '' }} {{ $selectedCustomer->postal_code ?? '' }}<br>
                                {{ $selectedCustomer->country ?? '' }}
                            @else
                                N/A
                            @endif
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Primary Contact</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            @if($selectedCustomer->contact_person)
                                {{ $selectedCustomer->contact_person }}<br>
                                <span class="text-gray-500 dark:text-gray-400">{{ $selectedCustomer->contact_position ?? '' }}</span><br>
                                {{ $selectedCustomer->contact_phone ?? '' }}
                            @else
                                N/A
                            @endif
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Language</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->language_preference ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Timezone</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->timezone ?? 'N/A' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Business Details -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Business Details</h3>

                <div class="space-y-3">
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Annual Revenue</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            {{ $selectedCustomer->annual_revenue ? number_format($selectedCustomer->annual_revenue, 2) : 'N/A' }}
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Employees</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            {{ $selectedCustomer->employee_count ? number_format($selectedCustomer->employee_count) : 'N/A' }}
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Tax ID</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->tax_id ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Registration #</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $selectedCustomer->registration_number ?? 'N/A' }}</div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Established</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            {{ $selectedCustomer->established_date ? $selectedCustomer->established_date->format('M d, Y') : 'N/A' }}
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Relationship Since</div>
                        <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                            {{ $selectedCustomer->relationship_since ? $selectedCustomer->relationship_since->format('M d, Y') : 'N/A' }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tags & Notes -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tags & Notes</h3>

                <!-- Tags -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Tags</h4>
                    <div class="flex flex-wrap gap-2">
                        @if($selectedCustomer->tags && count($selectedCustomer->tags) > 0)
                            @foreach($selectedCustomer->tags as $tag)
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ $tag }}
                                </span>
                            @endforeach
                        @else
                            <span class="text-sm text-gray-500 dark:text-gray-400">No tags</span>
                        @endif
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Notes</h4>
                    <div class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p class="text-sm text-gray-900 dark:text-white whitespace-pre-line">
                            {{ $selectedCustomer->notes ?? 'No notes available.' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contacts Tab -->
    <div x-show="activeTab === 'contacts'" class="space-y-4">
        <livewire:campaigns.customer-contacts :customer-id="$selectedCustomer->id" :key="'contacts-'.$selectedCustomer->id" />
    </div>

    <!-- Campaigns Tab -->
    <div x-show="activeTab === 'campaigns'" class="space-y-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Campaigns</h3>
                <a href="{{ route('campaigns.create', ['customer_id' => $selectedCustomer->id]) }}" class="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i class="fas fa-plus mr-1"></i> New Campaign
                </a>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">Start Date</th>
                            <th scope="col" class="px-6 py-3">End Date</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($selectedCustomer->campaigns as $campaign)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    {{ $campaign->name }}
                                </th>
                                <td class="px-6 py-4">
                                    {{ $campaign->start_date ? $campaign->start_date->format('M d, Y') : 'N/A' }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ $campaign->end_date ? $campaign->end_date->format('M d, Y') : 'N/A' }}
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $campaign->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                        {{ ucfirst($campaign->status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <a href="{{ route('campaigns.show', $campaign->id) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td colspan="5" class="px-6 py-4 text-center">
                                    No campaigns found for this customer.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Documents Tab -->
    <div x-show="activeTab === 'documents'" class="space-y-4">
        <livewire:campaigns.customer-documents :customer-id="$selectedCustomer->id" :key="'documents-'.$selectedCustomer->id" />
    </div>

    <!-- Activities Tab -->
    <div x-show="activeTab === 'activities'" class="space-y-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Activities</h3>

            <p class="text-sm text-gray-500 dark:text-gray-400">
                Activity tracking will be implemented in a future update.
            </p>
        </div>
    </div>
</div>
