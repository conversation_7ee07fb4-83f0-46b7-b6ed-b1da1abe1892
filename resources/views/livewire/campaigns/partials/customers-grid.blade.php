<div>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        @forelse($customers as $customer)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <div class="p-5">
                    <div class="flex justify-between items-start">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                            {{ $customer->name }}
                        </h3>
                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $customer->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                            {{ ucfirst($customer->status) }}
                        </span>
                    </div>

                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <p class="flex items-center mb-1">
                            <i class="fas fa-building mr-2 w-5 text-center"></i>
                            <span>{{ $customer->industry ?? 'N/A' }}</span>
                        </p>
                        <p class="flex items-center mb-1">
                            <i class="fas fa-user mr-2 w-5 text-center"></i>
                            <span>{{ $customer->contact_person ?? 'N/A' }}</span>
                        </p>
                        <p class="flex items-center mb-1">
                            <i class="fas fa-envelope mr-2 w-5 text-center"></i>
                            <span class="truncate">{{ $customer->email ?? 'N/A' }}</span>
                        </p>
                        <p class="flex items-center mb-1">
                            <i class="fas fa-phone mr-2 w-5 text-center"></i>
                            <span>{{ $customer->phone ?? 'N/A' }}</span>
                        </p>
                        <p class="flex items-center mb-1">
                            <i class="fas fa-tag mr-2 w-5 text-center"></i>
                            <span>{{ $customer->customer_type ?? 'N/A' }}</span>
                        </p>
                    </div>

                    <!-- Customer Metrics -->
                    <div class="mt-4 grid grid-cols-2 gap-2 text-xs">
                        <div class="bg-gray-100 dark:bg-gray-700 p-2 rounded">
                            <p class="text-gray-500 dark:text-gray-400">Campaigns</p>
                            <p class="font-semibold text-gray-900 dark:text-white">{{ $customer->campaigns_count ?? $customer->campaigns->count() }}</p>
                        </div>
                        <div class="bg-gray-100 dark:bg-gray-700 p-2 rounded">
                            <p class="text-gray-500 dark:text-gray-400">Contacts</p>
                            <p class="font-semibold text-gray-900 dark:text-white">{{ $customer->contacts_count ?? 0 }}</p>
                        </div>
                    </div>

                    <!-- Customer Tags -->
                    @if($customer->tags && count($customer->tags) > 0)
                        <div class="mt-4 flex flex-wrap gap-1">
                            @foreach($customer->tags as $tag)
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ $tag }}
                                </span>
                            @endforeach
                        </div>
                    @endif
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 flex justify-between">
                    <button wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})" class="text-sm font-medium text-blue-600 dark:text-blue-500 hover:underline">
                        <i class="fas fa-eye mr-1"></i> View
                    </button>
                    <button wire:click="$dispatch('to-customer-edit', {'customer': {{ $customer->id }}})" class="text-sm font-medium text-yellow-600 dark:text-yellow-500 hover:underline">
                        <i class="fas fa-edit mr-1"></i> Edit
                    </button>
                    <button
                        wire:click="delete({{ $customer->id }})"
                        class="text-sm font-medium text-red-600 dark:text-red-500 hover:underline"
                    >
                        <i class="fas fa-trash mr-1"></i> Delete
                    </button>
                </div>
            </div>
        @empty
            <div class="col-span-full bg-white dark:bg-gray-800 rounded-lg shadow p-6 text-center">
                <p class="text-gray-500 dark:text-gray-400">No customers found.</p>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $customers->links() }}
    </div>
</div>
