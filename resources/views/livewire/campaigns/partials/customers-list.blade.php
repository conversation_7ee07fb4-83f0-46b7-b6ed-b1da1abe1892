<x-card key="customer-index-table" class="overflow-x-auto mt-4">
    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="p-4 w-4">
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            wire:model.live="selectAll"
                            wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            aria-label="Select all customers"
                        >
                        <label for="checkbox-all-search" class="sr-only">checkbox</label>
                    </div>
                </th>
                <th wire:click="sortBy('name')" class="cursor-pointer px-6 py-3">
                    Name {{ $sortField === 'name' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                </th>
                <th wire:click="sortBy('industry')" class="cursor-pointer px-6 py-3">
                    Industry {{ $sortField === 'industry' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                </th>
                <th wire:click="sortBy('contact_person')" class="cursor-pointer px-6 py-3">
                    Contact {{ $sortField === 'contact_person' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                </th>
                <th wire:click="sortBy('email')" class="cursor-pointer px-6 py-3">
                    Email {{ $sortField === 'email' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                </th>
                <th wire:click="sortBy('status')" class="cursor-pointer px-6 py-3">
                    Status {{ $sortField === 'status' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                </th>
                <th class="px-6 py-3">
                    Campaigns
                </th>
                <th class="px-6 py-3">
                    <span class="sr-only">Actions</span>
                </th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            @forelse($customers as $customer)
                <tr wire:key="customer-{{ $customer->id }}" class="bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-600">
                    <td class="w-4 p-4">
                        <div class="flex items-center">
                            <input
                                type="checkbox"
                                wire:change="toggleCustomerSelection({{ $customer->id }})"
                                wire:key="checkbox-{{ $customer->id }}-{{ in_array((string)$customer->id, $selectedCustomers) ? 'checked' : 'unchecked' }}"
                                value="{{ $customer->id }}"
                                {{ in_array((string)$customer->id, $selectedCustomers) ? 'checked' : '' }}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                aria-label="Select customer {{ $customer->name }}"
                            >
                            <label for="checkbox-table-search-{{ $customer->id }}" class="sr-only">checkbox</label>
                        </div>
                    </td>
                    <th scope="row" class="flex items-center px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white cursor-pointer" wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})">
                        <div class="ps-3">
                            <div class="text-base font-semibold">{{ $customer->name }}</div>
                            <div class="font-normal text-gray-500">{{ $customer->customer_type ?? 'N/A' }}</div>
                        </div>
                    </th>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})">
                        {{ $customer->industry ?? 'N/A' }}
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})">
                        {{ $customer->contact_person ?? 'N/A' }}
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})">
                        {{ $customer->email ?? 'N/A' }}
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})">
                        <div class="flex items-center">
                            <div class="h-2.5 w-2.5 rounded-full {{ $customer->status === 'active' ? 'bg-green-500' : 'bg-red-500' }} me-2"></div>
                            {{ ucfirst($customer->status) }}
                        </div>
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})">
                        {{ $customer->campaigns_count ?? $customer->campaigns->count() }}
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex justify-end space-x-2">
                            <button wire:click.stop="$dispatch('to-customer-edit', {'customer': {{ $customer->id }}})" class="font-medium text-yellow-600 dark:text-yellow-500 hover:underline">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button
                                wire:click.stop="delete({{ $customer->id }})"
                                class="font-medium text-red-600 dark:text-red-500 hover:underline"
                            >
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No customers found.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
    <div class="p-4">
        {{ $customers->links() }}
    </div>
</x-card>
