<div>
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <!-- Total Customers -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3">
                    <i class="fas fa-users text-blue-600 dark:text-blue-300"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Customers</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $customerStats['total'] }}</p>
                </div>
            </div>
        </div>

        <!-- Active Customers -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-full p-3">
                    <i class="fas fa-user-check text-green-600 dark:text-green-300"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Customers</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $customerStats['active'] }}</p>
                </div>
            </div>
        </div>

        <!-- Inactive Customers -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-full p-3">
                    <i class="fas fa-user-times text-red-600 dark:text-red-300"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Inactive Customers</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $customerStats['inactive'] }}</p>
                </div>
            </div>
        </div>

        <!-- With Campaigns -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-full p-3">
                    <i class="fas fa-bullhorn text-purple-600 dark:text-purple-300"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">With Campaigns</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $customerStats['withCampaigns'] }}</p>
                </div>
            </div>
        </div>

        <!-- Without Campaigns -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-yellow-100 dark:bg-yellow-900 rounded-full p-3">
                    <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-300"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">No Campaigns</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ $customerStats['withoutCampaigns'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Customers by Industry -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customers by Industry</h3>
            <div id="industry-chart" class="h-80"></div>
        </div>

        <!-- Customers by Type -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customers by Type</h3>
            <div id="type-chart" class="h-80"></div>
        </div>
    </div>

    <!-- More Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Customers by Segment -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customers by Segment</h3>
            <div id="segment-chart" class="h-80"></div>
        </div>

        <!-- Active vs Inactive -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Active vs Inactive Customers</h3>
            <div id="status-chart" class="h-80"></div>
        </div>
    </div>

    <!-- Recent Customers -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-6">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Customers</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">Name</th>
                        <th scope="col" class="px-6 py-3">Industry</th>
                        <th scope="col" class="px-6 py-3">Contact Person</th>
                        <th scope="col" class="px-6 py-3">Status</th>
                        <th scope="col" class="px-6 py-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($customers->take(5) as $customer)
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                {{ $customer->name }}
                            </th>
                            <td class="px-6 py-4">
                                {{ $customer->industry ?? 'N/A' }}
                            </td>
                            <td class="px-6 py-4">
                                {{ $customer->contact_person ?? 'N/A' }}
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full {{ $customer->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                    {{ ucfirst($customer->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <button wire:click="$dispatch('to-customer-show', {'customer': {{ $customer->id }}})" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function initCharts() {
            // Industry Chart
            const industryData = @json($customerStats['byIndustry']);
            if (industryData.length > 0) {
                const industryLabels = industryData.map(item => item.industry);
                const industrySeries = industryData.map(item => item.count);

                new ApexCharts(document.querySelector("#industry-chart"), {
                    series: industrySeries,
                    labels: industryLabels,
                    chart: {
                        type: 'donut',
                        height: 320,
                        fontFamily: 'Inter, sans-serif',
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '65%'
                            }
                        }
                    },
                    colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#F8B4B4', '#D1FAE5'],
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Inter, sans-serif',
                        labels: {
                            colors: document.documentElement.classList.contains('dark') ? '#fff' : '#111827'
                        }
                    },
                    tooltip: {
                        fillSeriesColor: false,
                        theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                    }
                }).render();
            }

            // Type Chart
            const typeData = @json($customerStats['byType']);
            if (typeData.length > 0) {
                const typeLabels = typeData.map(item => item.customer_type);
                const typeSeries = typeData.map(item => item.count);

                new ApexCharts(document.querySelector("#type-chart"), {
                    series: typeSeries,
                    labels: typeLabels,
                    chart: {
                        type: 'donut',
                        height: 320,
                        fontFamily: 'Inter, sans-serif',
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '65%'
                            }
                        }
                    },
                    colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#F8B4B4', '#D1FAE5'],
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Inter, sans-serif',
                        labels: {
                            colors: document.documentElement.classList.contains('dark') ? '#fff' : '#111827'
                        }
                    },
                    tooltip: {
                        fillSeriesColor: false,
                        theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                    }
                }).render();
            }

            // Segment Chart
            const segmentData = @json($customerStats['bySegment']);
            if (segmentData.length > 0) {
                const segmentLabels = segmentData.map(item => item.segment);
                const segmentSeries = segmentData.map(item => item.count);

                new ApexCharts(document.querySelector("#segment-chart"), {
                    series: segmentSeries,
                    labels: segmentLabels,
                    chart: {
                        type: 'donut',
                        height: 320,
                        fontFamily: 'Inter, sans-serif',
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '65%'
                            }
                        }
                    },
                    colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#F8B4B4', '#D1FAE5'],
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Inter, sans-serif',
                        labels: {
                            colors: document.documentElement.classList.contains('dark') ? '#fff' : '#111827'
                        }
                    },
                    tooltip: {
                        fillSeriesColor: false,
                        theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                    }
                }).render();
            }

            // Status Chart
            new ApexCharts(document.querySelector("#status-chart"), {
                series: [{{ $customerStats['active'] }}, {{ $customerStats['inactive'] }}],
                labels: ['Active', 'Inactive'],
                chart: {
                    type: 'donut',
                    height: 320,
                    fontFamily: 'Inter, sans-serif',
                },
                plotOptions: {
                    pie: {
                        donut: {
                            size: '65%'
                        }
                    }
                },
                colors: ['#16A34A', '#DC2626'],
                legend: {
                    position: 'bottom',
                    fontFamily: 'Inter, sans-serif',
                    labels: {
                        colors: document.documentElement.classList.contains('dark') ? '#fff' : '#111827'
                    }
                },
                tooltip: {
                    fillSeriesColor: false,
                    theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                }
            }).render();
        }

        // Initialize charts when the dashboard is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('industry-chart')) {
                initCharts();
            }
        });
    </script>
</div>
