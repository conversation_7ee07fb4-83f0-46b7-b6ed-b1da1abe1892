<x-content>
    <form action="#">
        <x-content-header title="Remove agent">
            <x-page-button type="save" label="Yes I'm sure" action="submit"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-campaign-agent')"/>
        </x-content-header>
        <x-content-body>
            <div class="p-4 text-center bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <svg class="w-16 h-16 mx-auto text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <h3 class="mt-5 mb-4 text-lg text-gray-500 dark:text-gray-400">Are you sure you want to remove {{ $agent->getFullNameAttribute() }} from this campaign?</h3>

                <div class="flex items-center justify-center mt-4 mb-6">
                    <input
                        id="recycle"
                        type="checkbox"
                        wire:model="recycle"
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                    >
                    <label for="recycle" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">
                        Send directly to training for recycling
                    </label>
                </div>

                <p class="text-sm text-gray-500 dark:text-gray-400">
                    @if($recycle)
                        The agent will be immediately sent to training with progress reset.
                    @else
                        The agent will be removed from the campaign and marked as available for training.
                    @endif
                </p>
            </div>
        </x-content-body>
    </form>
</x-content>
