<x-content>
    <div>
        <x-content-header title="Campaign Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-campaign-edit', { campaign: {{ $campaign->id }} })"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-campaign-delete', { campaign: {{ $campaign->id }} })"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-campaign-index')"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Right Content -->
                <div class="col-span-full xl:col-auto">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <div>
                                <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">Emission</h3>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flow-root">
                            <h3 class="text-xl font-semibold dark:text-white">Historique</h3>
                            <ol class="relative mt-4 border-l border-gray-200 dark:border-gray-700">
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Date de creation</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $campaign->created_at }}</h3>
                                </li>
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Status</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Active</h3>
                                    {{-- <p class="mb-4 text-base font-normal text-gray-500 dark:text-gray-400">Campagne d'emission d'appel.</p> --}}
                                </li>
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Dernier agent affecter</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $campaign->users->last()?->getFullNameAttribute() ?? 'test' }}</h3>
                                    <p class="text-base font-normal text-gray-500 dark:text-gray-400">Affecter le {{ $campaign->users->last()?->created_at ?? $campaign->created_at }} </p>
                                </li>
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Dernier rdv</time>
                                    <audio controls class="w-full my-2">
                                        <source src="" type="">
                                        Votre navigateur ne supporte pas l'audio.
                                    </audio>
                                    <p class="text-base font-normal text-gray-500 dark:text-gray-400">Enrgistrer le {{ $campaign->appointments->last()?->created_at ?? $campaign->created_at }}</p>
                                    <p class="text-base font-normal text-gray-500 dark:text-gray-400">par {{ $campaign->appointments->last()?->user?->getFullNameAttribute() ?? 'Agent par default' }}.</p>
                                </li>
                                 <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Date d'envoie du dernier rapport</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $campaign->reports->last()?->created_at ?? $campaign->created_at }}</h3>
                                    <p class="text-base font-normal text-gray-500 dark:text-gray-400">Enrgistrer le {{ $campaign->reports->last()?->created_at ?? $campaign->created_at }}</p>
                                    <p class="text-base font-normal text-gray-500 dark:text-gray-400">par {{ $campaign->reports->last()?->creator?->getFullNameAttribute() ?? 'Manager par default' }}.</p>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
                <!-- Left Content -->
                <div class="col-span-2">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">General information</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Name</label>
                                <input readonly type="text" value="{{ $campaign->name }}" name="name" id="name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Jonathan">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="customer" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Customer</label>
                                <input readonly type="customer" value="{{ $campaign->customer?->name }}" name="customer" id="role" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<EMAIL>">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="manager" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Manager</label>
                                <input readonly type="manager" value="{{ $campaign->manager?->getFullNameAttribute() }}" name="manager" id="role" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<EMAIL>">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="start-date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Date</label>
                                <input readonly type="text" value="{{ $campaign->start_date }}" name="start-date" id="start-date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="end-date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End Date</label>
                                <input readonly type="text" value="{{ $campaign->end_date }}" name="end-date" id="end-date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="agents" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nombre d'agents</label>
                                <input readonly type="text" value="{{ '20' }}" name="agents" id="agents" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="doublons" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nombre de doublons</label>
                                <input readonly type="text" value="{{ '5' }}" name="doublons" id="doublons" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="rdvs" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Nombre de rdvs</label>
                                <input readonly type="text" value="{{ '45' }}" name="rdvs" id="rdvs" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                            </div>
                            <!-- Add platform information to the campaign details -->
<div class="mb-4">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Platform</h3>
    <p class="text-sm text-gray-500 dark:text-gray-400">
        {{ $campaign->platform ? $campaign->platform->name : 'No platform assigned' }}
    </p>
</div>
                        </div>
                    </div>
                </div>

                <!-- Campaign Requirements Section -->
                <div class="col-span-2">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Campaign Requirements</h3>
                        <p class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                            Skills and certifications required for agents to work on this campaign.
                        </p>

                        <livewire:campaigns.campaign-requirements :campaign="$campaign" />
                    </div>
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>

