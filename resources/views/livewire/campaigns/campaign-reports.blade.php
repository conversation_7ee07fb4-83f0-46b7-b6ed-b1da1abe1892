<div>
    <x-content>

    <x-slot name="pageheader">
        <x-page-header title="{{ __('Campaign Reports') }}" description="{{ __('Manage campaign reports') }}" icon="<path d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'></path>">
            <x-page-button type="primary" icon="fas fa-plus" label="{{ __('Create Report') }}" action="createReport" />
        </x-page-header>
    </x-slot>

    <x-slot name="pagesidebar">
        <x-page-sidebar>
            <x-slot name="title">
                {{ __('Report Filters') }}
            </x-slot>
            <x-slot name="content">
                <div class="space-y-4">
                    <!-- Campaign Filter -->
                    <div>
                        <label for="campaign-filter" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Campaign') }}</label>
                        <div class="relative">
                            <input type="text" id="campaign-filter" wire:model.debounce.300ms="campaignSearch" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="{{ __('Search campaign...') }}">

                            @if($filteredCampaigns->count() > 0 && $campaignSearch)
                                <div class="absolute z-10 w-full mt-1 bg-white rounded-lg shadow-lg dark:bg-gray-700 max-h-60 overflow-y-auto">
                                    @foreach($filteredCampaigns as $campaign)
                                        <div wire:click="selectCampaign({{ $campaign->id }})" class="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-gray-600 {{ $selectedCampaignId === $campaign->id ? 'bg-blue-100 dark:bg-blue-900' : '' }}">
                                            {{ $campaign->name }}
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Date Range Filter -->
                    <div>
                        <label for="date-range" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Date Range') }}</label>
                        <select id="date-range" wire:model.live="dateRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="today">{{ __('Today') }}</option>
                            <option value="yesterday">{{ __('Yesterday') }}</option>
                            <option value="this_week">{{ __('This Week') }}</option>
                            <option value="last_week">{{ __('Last Week') }}</option>
                            <option value="this_month">{{ __('This Month') }}</option>
                            <option value="last_month">{{ __('Last Month') }}</option>
                            <option value="this_quarter">{{ __('This Quarter') }}</option>
                            <option value="last_quarter">{{ __('Last Quarter') }}</option>
                            <option value="this_year">{{ __('This Year') }}</option>
                            <option value="last_year">{{ __('Last Year') }}</option>
                            <option value="custom">{{ __('Custom Range') }}</option>
                        </select>

                        @if($dateRange === 'custom')
                            <div class="grid grid-cols-2 gap-2 mt-2">
                                <div>
                                    <label for="start-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">{{ __('Start Date') }}</label>
                                    <input type="date" id="start-date" wire:model.live="startDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="end-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">{{ __('End Date') }}</label>
                                    <input type="date" id="end-date" wire:model.live="endDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Report Type Filter -->
                    <div>
                        <label for="report-type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Report Type') }}</label>
                        <select id="report-type" wire:model.live="reportType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Types') }}</option>
                            <option value="daily">{{ __('Daily') }}</option>
                            <option value="weekly">{{ __('Weekly') }}</option>
                            <option value="monthly">{{ __('Monthly') }}</option>
                            <option value="quarterly">{{ __('Quarterly') }}</option>
                            <option value="annual">{{ __('Annual') }}</option>
                            <option value="ad_hoc">{{ __('Ad Hoc') }}</option>
                        </select>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label for="report-category" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Category') }}</label>
                        <select id="report-category" wire:model.live="reportCategory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Categories') }}</option>
                            <option value="performance">{{ __('Performance') }}</option>
                            <option value="quality">{{ __('Quality') }}</option>
                            <option value="compliance">{{ __('Compliance') }}</option>
                            <option value="training">{{ __('Training') }}</option>
                            <option value="operations">{{ __('Operations') }}</option>
                            <option value="financial">{{ __('Financial') }}</option>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="report-status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Status') }}</label>
                        <select id="report-status" wire:model.live="reportStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Statuses') }}</option>
                            <option value="draft">{{ __('Draft') }}</option>
                            <option value="submitted">{{ __('Submitted') }}</option>
                            <option value="in_review">{{ __('In Review') }}</option>
                            <option value="approved">{{ __('Approved') }}</option>
                            <option value="rejected">{{ __('Rejected') }}</option>
                        </select>
                    </div>

                    <!-- Priority Filter -->
                    <div>
                        <label for="report-priority" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Priority') }}</label>
                        <select id="report-priority" wire:model.live="reportPriority" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Priorities') }}</option>
                            <option value="low">{{ __('Low') }}</option>
                            <option value="normal">{{ __('Normal') }}</option>
                            <option value="high">{{ __('High') }}</option>
                            <option value="urgent">{{ __('Urgent') }}</option>
                        </select>
                    </div>

                    <div class="pt-4">
                        <button wire:click="resetFilters" class="w-full px-3 py-2 text-sm font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                            <i class="fas fa-redo-alt mr-1"></i> {{ __('Reset Filters') }}
                        </button>
                    </div>
                </div>
            </x-slot>
        </x-page-sidebar>
    </x-slot>

    <!-- Filter Controls Card -->
    <x-card key="campaign-reports-header" class="p-4 mb-4">
        <div class="w-full">
            <!-- Search and Actions Row -->
            <div class="flex flex-col md:flex-row justify-between gap-4 mb-4">
                <div class="w-full md:w-1/3">
                    <label for="search" class="sr-only">{{ __('Search') }}</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <i class="fas fa-search text-gray-500 dark:text-gray-400"></i>
                        </div>
                        <input type="text" id="search" wire:model.debounce.300ms="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="{{ __('Search reports...') }}">
                    </div>
                </div>

                <div class="flex flex-wrap items-center gap-2">
                    <!-- Active Filters -->
                    @if($selectedCampaignId || $reportType !== 'all' || $reportCategory !== 'all' || $reportStatus !== 'all' || $reportPriority !== 'all' || !empty($selectedTags) || $dateRange !== 'this_month')
                        <div class="flex flex-wrap items-center gap-2 mr-2">
                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Active Filters:') }}</span>

                            @if($selectedCampaignId)
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ __('Campaign') }}: {{ $campaignSearch }}
                                    <button wire:click="$set('selectedCampaignId', null)" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endif

                            @if($dateRange !== 'this_month')
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ __('Date') }}: {{ ucwords(str_replace('_', ' ', $dateRange)) }}
                                    <button wire:click="$set('dateRange', 'this_month')" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endif

                            @if($reportType !== 'all')
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ __('Type') }}: {{ ucfirst($reportType) }}
                                    <button wire:click="$set('reportType', 'all')" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endif

                            @if($reportCategory !== 'all')
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ __('Category') }}: {{ ucfirst($reportCategory) }}
                                    <button wire:click="$set('reportCategory', 'all')" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endif

                            @if($reportStatus !== 'all')
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ __('Status') }}: {{ ucfirst($reportStatus) }}
                                    <button wire:click="$set('reportStatus', 'all')" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endif

                            @if($reportPriority !== 'all')
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ __('Priority') }}: {{ ucfirst($reportPriority) }}
                                    <button wire:click="$set('reportPriority', 'all')" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endif

                            @foreach($selectedTags as $tag)
                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    {{ $tag }}
                                    <button wire:click="toggleTag('{{ $tag }}')" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </span>
                            @endforeach

                            <button wire:click="resetFilters" class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                                <i class="fas fa-times-circle mr-1"></i>{{ __('Clear All') }}
                            </button>
                        </div>
                    @endif

                    <div>
                        <label for="per-page" class="sr-only">{{ __('Per Page') }}</label>
                        <select id="per-page" wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="10">10 {{ __('per page') }}</option>
                            <option value="25">25 {{ __('per page') }}</option>
                            <option value="50">50 {{ __('per page') }}</option>
                            <option value="100">100 {{ __('per page') }}</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Batch Actions -->
            <div class="flex items-center gap-2 mt-2"
                x-data="{
                    showBatchActions: @entangle('selectedReports').length > 0,
                    showConfirmModal: false,
                    confirmAction: '',
                    confirmTitle: '',
                    confirmMessage: '',
                    confirmButtonText: '',
                    confirmButtonClass: '',

                    confirm(action, title, message, buttonText, buttonClass) {
                        this.confirmAction = action;
                        this.confirmTitle = title;
                        this.confirmMessage = message;
                        this.confirmButtonText = buttonText;
                        this.confirmButtonClass = buttonClass;
                        this.showConfirmModal = true;
                    },

                    executeConfirmedAction() {
                        if (this.confirmAction === 'approve') {
                            $wire.batchApprove();
                        } else if (this.confirmAction === 'reject') {
                            $wire.batchReject();
                        } else if (this.confirmAction === 'delete') {
                            $wire.batchDelete();
                        }
                        this.showConfirmModal = false;
                    }
                }"
                x-show="showBatchActions"
                x-cloak
            >
                <button @click="confirm('approve', '{{ __('Approve Reports') }}', '{{ __('Are you sure you want to approve the selected reports?') }}', '{{ __('Approve') }}', 'bg-green-600 hover:bg-green-700')" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-green-600 text-white hover:bg-green-700">
                    <i class="fas fa-check mr-1"></i> {{ __('Approve Selected') }}
                </button>
                <button @click="confirm('reject', '{{ __('Reject Reports') }}', '{{ __('Are you sure you want to reject the selected reports?') }}', '{{ __('Reject') }}', 'bg-red-600 hover:bg-red-700')" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-red-600 text-white hover:bg-red-700">
                    <i class="fas fa-times mr-1"></i> {{ __('Reject Selected') }}
                </button>
                <button @click="confirm('delete', '{{ __('Delete Reports') }}', '{{ __('Are you sure you want to delete the selected reports? This action cannot be undone.') }}', '{{ __('Delete') }}', 'bg-red-600 hover:bg-red-700')" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-red-600 text-white hover:bg-red-700">
                    <i class="fas fa-trash-alt mr-1"></i> {{ __('Delete Selected') }}
                </button>
                <button wire:click="exportBatch('pdf')" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                    <i class="fas fa-file-export mr-1"></i> {{ __('Export Selected') }}
                </button>
                <button wire:click="clearSelection" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-gray-600 text-white hover:bg-gray-700">
                    <i class="fas fa-times-circle mr-1"></i> {{ __('Clear Selection') }}
                </button>
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ count($selectedReports) }} {{ __('selected') }}</span>

                <!-- Confirmation Modal -->
                <div x-show="showConfirmModal" class="fixed inset-0 z-50 flex items-center justify-center" x-cloak>
                    <div class="fixed inset-0 bg-black opacity-50"></div>
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white" x-text="confirmTitle"></h3>
                            <button @click="showConfirmModal = false" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="mb-6">
                            <p class="text-gray-700 dark:text-gray-300" x-text="confirmMessage"></p>
                        </div>
                        <div class="flex justify-end space-x-2">
                            <button @click="showConfirmModal = false" class="px-4 py-2 text-sm font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                                {{ __('Cancel') }}
                            </button>
                            <button @click="executeConfirmedAction()" class="px-4 py-2 text-sm font-medium rounded-lg text-white" :class="confirmButtonClass" x-text="confirmButtonText"></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-card>

    <!-- Notifications -->
    <div x-data="{
            show: false,
            message: '',
            type: 'success',
            showNotification(message, type = 'success') {
                this.show = true;
                this.message = message;
                this.type = type;
                setTimeout(() => { this.show = false }, 3000);
            }
        }"
        x-init="$wire.on('notify', ({type, message}) => showNotification(message, type))"
        x-show="show"
        x-transition
        x-cloak
        class="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg"
        :class="{
            'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': type === 'success',
            'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': type === 'error',
            'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300': type === 'info',
            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300': type === 'warning'
        }"
    >
        <div class="flex items-center">
            <div class="mr-2" :class="{
                'text-green-500 dark:text-green-400': type === 'success',
                'text-red-500 dark:text-red-400': type === 'error',
                'text-blue-500 dark:text-blue-400': type === 'info',
                'text-yellow-500 dark:text-yellow-400': type === 'warning'
            }">
                <template x-if="type === 'success'">
                    <i class="fas fa-check-circle text-xl"></i>
                </template>
                <template x-if="type === 'error'">
                    <i class="fas fa-exclamation-circle text-xl"></i>
                </template>
                <template x-if="type === 'info'">
                    <i class="fas fa-info-circle text-xl"></i>
                </template>
                <template x-if="type === 'warning'">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </template>
            </div>
            <div x-text="message"></div>
            <button @click="show = false" class="ml-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>


    @if($viewMode === 'dashboard')
        <!-- Dashboard View -->
        <div class="mb-6">
            <!-- Metrics Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <!-- Total Reports Card -->
                <x-card key="total-reports" class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Total Reports') }}</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['total_reports'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900">
                            <i class="fas fa-file-alt w-6 h-6 text-blue-600 dark:text-blue-300"></i>
                        </div>
                    </div>
                </x-card>

                <!-- Performance Score Card -->
                <x-card key="performance-score" class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Avg Performance') }}</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_performance_score'] ?? 0 }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $metrics['avg_performance_score'] >= 90 ? __('Excellent') :
                                   ($metrics['avg_performance_score'] >= 80 ? __('Good') :
                                   ($metrics['avg_performance_score'] >= 70 ? __('Satisfactory') :
                                   ($metrics['avg_performance_score'] >= 60 ? __('Needs Improvement') : __('Unsatisfactory')))) }}
                            </p>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full
                            {{ $metrics['avg_performance_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                               ($metrics['avg_performance_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                               ($metrics['avg_performance_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                               ($metrics['avg_performance_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                            <i class="fas fa-chart-line w-6 h-6
                                {{ $metrics['avg_performance_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                   ($metrics['avg_performance_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                   ($metrics['avg_performance_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                   ($metrics['avg_performance_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"></i>
                        </div>
                    </div>
                </x-card>

                <!-- Quality Score Card -->
                <x-card key="quality-score" class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Avg Quality') }}</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_quality_score'] ?? 0 }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $metrics['avg_quality_score'] >= 90 ? __('Excellent') :
                                   ($metrics['avg_quality_score'] >= 80 ? __('Good') :
                                   ($metrics['avg_quality_score'] >= 70 ? __('Satisfactory') :
                                   ($metrics['avg_quality_score'] >= 60 ? __('Needs Improvement') : __('Unsatisfactory')))) }}
                            </p>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full
                            {{ $metrics['avg_quality_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                               ($metrics['avg_quality_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                               ($metrics['avg_quality_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                               ($metrics['avg_quality_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                            <i class="fas fa-check-circle w-6 h-6
                                {{ $metrics['avg_quality_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                   ($metrics['avg_quality_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                   ($metrics['avg_quality_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                   ($metrics['avg_quality_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"></i>
                        </div>
                    </div>
                </x-card>

                <!-- Compliance Score Card -->
                <x-card key="compliance-score" class="p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Avg Compliance') }}</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_compliance_score'] ?? 0 }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $metrics['avg_compliance_score'] >= 90 ? __('Excellent') :
                                   ($metrics['avg_compliance_score'] >= 80 ? __('Good') :
                                   ($metrics['avg_compliance_score'] >= 70 ? __('Satisfactory') :
                                   ($metrics['avg_compliance_score'] >= 60 ? __('Needs Improvement') : __('Unsatisfactory')))) }}
                            </p>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full
                            {{ $metrics['avg_compliance_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                               ($metrics['avg_compliance_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                               ($metrics['avg_compliance_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                               ($metrics['avg_compliance_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                            <i class="fas fa-shield-alt w-6 h-6
                                {{ $metrics['avg_compliance_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                   ($metrics['avg_compliance_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                   ($metrics['avg_compliance_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                   ($metrics['avg_compliance_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"></i>
                        </div>
                    </div>
                </x-card>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                <!-- Reports Over Time Chart -->
                <x-card key="reports-chart" class="p-4">
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Reports Over Time') }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Trend of report submissions over time') }}</p>
                    </div>
                    <div id="reports-chart" class="h-80"></div>
                </x-card>

                <!-- Scores Chart -->
                <x-card key="scores-chart" class="p-4">
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Performance Metrics') }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('Average scores across key metrics') }}</p>
                    </div>
                    <div id="scores-chart" class="h-80"></div>
                </x-card>
            </div>

            <!-- Breakdown Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <!-- Status Breakdown -->
                <x-card key="status-breakdown" class="p-4">
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Status Breakdown') }}</h3>
                    </div>
                    <div class="space-y-3">
                        @foreach($metrics['status_breakdown'] ?? [] as $status => $count)
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                    <div class="h-2.5 rounded-full
                                        {{ $status === 'approved' ? 'bg-green-600' :
                                           ($status === 'rejected' ? 'bg-red-600' :
                                           ($status === 'submitted' ? 'bg-blue-600' :
                                           ($status === 'draft' ? 'bg-gray-600' : 'bg-yellow-600'))) }}"
                                        style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($status) }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                            </div>
                        @endforeach
                    </div>
                </x-card>

                <!-- Category Breakdown -->
                <x-card key="category-breakdown" class="p-4">
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Category Breakdown') }}</h3>
                    </div>
                    <div class="space-y-3">
                        @foreach($metrics['category_breakdown'] ?? [] as $category => $count)
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                    <div class="h-2.5 rounded-full
                                        {{ $category === 'performance' ? 'bg-blue-600' :
                                           ($category === 'quality' ? 'bg-green-600' :
                                           ($category === 'compliance' ? 'bg-purple-600' :
                                           ($category === 'training' ? 'bg-yellow-600' :
                                           ($category === 'operations' ? 'bg-indigo-600' : 'bg-gray-600')))) }}"
                                        style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($category) }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                            </div>
                        @endforeach
                    </div>
                </x-card>

                <!-- Priority Breakdown -->
                <x-card key="priority-breakdown" class="p-4">
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('Priority Breakdown') }}</h3>
                    </div>
                    <div class="space-y-3">
                        @foreach($metrics['priority_breakdown'] ?? [] as $priority => $count)
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                    <div class="h-2.5 rounded-full
                                        {{ $priority === 'low' ? 'bg-blue-600' :
                                           ($priority === 'normal' ? 'bg-green-600' :
                                           ($priority === 'high' ? 'bg-yellow-600' : 'bg-red-600')) }}"
                                        style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($priority) }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                            </div>
                        @endforeach
                    </div>
                </x-card>
            </div>
        </div>
    @endif

    @if($viewMode === 'list')
        <!-- List View -->
        <x-card key="campaign-reports-table" class="overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="p-4">
                                <div class="flex items-center">
                                    <input id="checkbox-all" type="checkbox"
                                        wire:model.live="selectAll"
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                    <label for="checkbox-all" class="sr-only">checkbox</label>
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3">
                                {{ __('Title') }}
                            </th>
                            <th scope="col" class="px-6 py-3">
                                {{ __('Date') }}
                            </th>
                            <th scope="col" class="px-6 py-3">
                                {{ __('Status') }}
                            </th>
                            <th scope="col" class="px-6 py-3">
                                {{ __('Priority') }}
                            </th>
                            <th scope="col" class="px-6 py-3">
                                {{ __('Type') }}
                            </th>
                            <th scope="col" class="px-6 py-3">
                                {{ __('Category') }}
                            </th>
                            <th scope="col" class="px-6 py-3">
                                {{ __('Creator') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($reports as $report)
                            <tr wire:key="report-{{ $report->id }}" wire:click="viewReport({{ $report->id }})" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer">
                                <td class="w-4 p-4" onclick="event.stopPropagation();">
                                    <div class="flex items-center">
                                        <input id="checkbox-{{ $report->id }}" wire:model.live="selectedReports" value="{{ $report->id }}" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="checkbox-{{ $report->id }}" class="sr-only">checkbox</label>
                                    </div>
                                </td>
                                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    {{ $report->title ?? "Report #$report->id" }}
                                </th>
                                <td class="px-6 py-4">
                                    {{ $report->date->format('M d, Y') }}
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                          ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                          ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                          ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                        {{ ucfirst($report->status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {{ $report->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                          ($report->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                          ($report->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                        {{ ucfirst($report->priority ?? 'Normal') }}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    {{ ucfirst($report->type ?? 'Standard') }}
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                        {{ ucfirst($report->category ?? 'General') }}
                                    </span>
                                </td>
                                <td class="px-6 py-4">
                                    {{ $report->creator->first_name }} {{ $report->creator->last_name }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center">
                                    {{ __('No reports found') }}
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </x-card>

        <div class="mt-4">
            {{ $reports->links() }}
        </div>
    @endif

    @if($viewMode === 'grid')
        <!-- Grid View -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @forelse($reports as $report)
                <x-card key="report-{{ $report->id }}" wire:click="viewReport({{ $report->id }})" class="overflow-hidden cursor-pointer hover:shadow-md transition-shadow">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <div class="flex items-center gap-2">
                            <div onclick="event.stopPropagation();">
                                <input id="grid-checkbox-{{ $report->id }}" wire:model.live="selectedReports" value="{{ $report->id }}" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                {{ $report->title ?? "Report #$report->id" }}
                            </h3>
                        </div>
                        <div class="flex space-x-2">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {{ $report->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                  ($report->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                  ($report->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                {{ ucfirst($report->priority ?? 'Normal') }}
                            </span>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex justify-between mb-3">
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <i class="far fa-calendar-alt mr-1"></i> {{ $report->date->format('M d, Y') }}
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <i class="far fa-file-alt mr-1"></i> {{ ucfirst($report->type ?? 'Standard') }}
                            </div>
                        </div>
                        <div class="mb-3">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                  ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                  ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                  ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                  'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                {{ ucfirst($report->status) }}
                            </span>
                            <span class="px-2 py-1 text-xs font-medium rounded-full ml-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                {{ ucfirst($report->category ?? 'General') }}
                            </span>
                        </div>
                        <p class="text-gray-700 dark:text-gray-300 mb-4 line-clamp-3">
                            {{ str_limit(strip_tags($report->content), 150) }}
                        </p>

                        @if($report->performance_score || $report->quality_score || $report->compliance_score)
                            <div class="grid grid-cols-3 gap-2 mb-4">
                                @if($report->performance_score)
                                    <div class="text-center">
                                        <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Performance</div>
                                        <div class="text-lg font-bold {{ $report->performance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->performance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                            {{ $report->performance_score }}
                                        </div>
                                    </div>
                                @endif
                                @if($report->quality_score)
                                    <div class="text-center">
                                        <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Quality</div>
                                        <div class="text-lg font-bold {{ $report->quality_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->quality_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                            {{ $report->quality_score }}
                                        </div>
                                    </div>
                                @endif
                                @if($report->compliance_score)
                                    <div class="text-center">
                                        <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance</div>
                                        <div class="text-lg font-bold {{ $report->compliance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->compliance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                            {{ $report->compliance_score }}
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endif

                        @if(!empty($report->tags))
                            <div class="flex flex-wrap gap-1 mb-4">
                                @foreach($report->tags as $tag)
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        {{ $tag }}
                                    </span>
                                @endforeach
                            </div>
                        @endif
                    </div>
                    <div class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <i class="far fa-user mr-1"></i> {{ $report->creator->first_name }} {{ $report->creator->last_name }}
                        </div>
                        <div class="flex justify-center items-center space-x-2">
                            <button wire:click="viewReport({{ $report->id }})" class="px-3 py-1 text-xs font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                                <i class="fas fa-eye mr-1"></i> {{ __('View') }}
                            </button>
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open" class="px-3 py-1 text-xs font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div x-show="open" @click.away="open = false" class="absolute right-0 bottom-8 z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                    <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                                        <li>
                                            <a href="#" wire:click.prevent="editReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <i class="fas fa-edit mr-2"></i> {{ __('Edit') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <i class="far fa-file-pdf mr-2 text-red-500"></i> {{ __('Export PDF') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'docx')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <i class="far fa-file-word mr-2 text-blue-500"></i> {{ __('Export DOCX') }}
                                            </a>
                                        </li>
                                        @if(auth()->user()->role_id <= 3 && $report->approval_status === 'pending')
                                            <li>
                                                <a href="#" wire:click.prevent="approveReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                    <i class="fas fa-check mr-2 text-green-500"></i> {{ __('Approve') }}
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#" wire:click.prevent="rejectReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                    <i class="fas fa-times mr-2 text-red-500"></i> {{ __('Reject') }}
                                                </a>
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </x-card>
            @empty
                <div class="col-span-3">
                    <x-card class="p-6 text-center">
                        <p class="text-gray-700 dark:text-gray-300">{{ __('No reports found') }}</p>
                    </x-card>
                </div>
            @endforelse
        </div>

        <div class="mt-4">
            {{ $reports->links() }}
        </div>
    @endif

    @if($viewMode === 'chat')
            <!-- Chat View -->
            <div
                class="flex flex-col min-h-full lg:max-h-[60rem] 2xl:max-h-fit"
            >
                @if($reports->count() > 0)
                <div
                    id="reports-container"
                    class="relative overflow-y-auto flex flex-col p-2 {{ auth()->user()->role_id !== 1 ? 'max-h-[30rem]' : 'max-h-[20rem]' }}"
                    x-data
                    x-init="$nextTick(() => { $el.scrollTop = $el.scrollHeight })"
                >
                    @foreach ($reports as $report)
                        <!-- Report content -->
                        <article id="report-{{ $report->id }}" class="mb-5">
                            <!-- Content header -->
                            <header
                                id="report-header-{{ $report->id }}"
                                class="flex items-center justify-between my-4"
                                x-data="{ open: false }"
                            >
                                <div class="flex items-center">
                                    <p class="inline-flex items-center mr-3 text-sm font-semibold text-gray-900 dark:text-white">
                                        <img class="w-6 h-6 mr-2 rounded-full" src="https://avatar.iran.liara.run/public/{{$report->creator->id}}"alt="Jese avatar">
                                        {{ $report->creator->getFullNameAttribute() }}
                                    </p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        <time pubdate datetime="2022-02-08" title="February 8th, 2022">
                                            {{ $report->created_at->format('d/m/Y H:i A') }}
                                        </time>
                                    </p>
                                </div>
                                <!-- Content action Button (Manager Only) -->
                                @if(auth()->user()->role_id === 1)
                                    @if(!$editingReportId)
                                    <button
                                        class="inline-flex items-center p-2 text-sm font-medium text-center text-gray-500 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-gray-300 dark:focus:ring-gray-600"
                                        type="button"
                                        x-on:click="open = !open"
                                    >
                                        <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        </svg>
                                        <span class="sr-only">Comment settings</span>
                                    </button>
                                    @endif
                                    <!-- Dropdown menu -->
                                    <div
                                        x-cloak
                                        x-show="open"
                                        x-on:click.away="open = false"
                                        class="absolute right-0 z-10 mr-1 bg-white divide-y divide-gray-100 rounded shadow w-36 dark:bg-gray-700 dark:divide-gray-600"
                                    >
                                        <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownMenuIconHorizontalButton">
                                            <li class="relative w-full">
                                                <button
                                                    x-on:click="open = false"
                                                    type="button"
                                                    class="block text-sm w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                                                    wire:click ="editReport({{ $report->id }})"
                                                >
                                                Edit
                                                </button>
                                            </li>
                                            <li>
                                                <button
                                                    type="button"
                                                    class="block text-sm w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                                                    wire:click="deleteReport({{ $report->id }})"
                                                >
                                                Remove
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                @endif
                            </header>
                            <!-- Report Content -->
                            @if($editingReportId === $report->id)
                                <!-- Edit Report Form -->
                                <div
                                    id="report-edit-{{ $report->id }}"
                                    class="pl-8 mb-5"
                                >
                                    <form wire:submit.prevent="updateReport({{ $report->id }})">
                                        <label for="edit-report-{{ $report->id }}" class="sr-only">Your message</label>
                                        <div class="flex items-center mb-5">
                                            <textarea
                                                id="edit-report-{{ $report->id }}"
                                                wire:model.defer="updatedReport"
                                                rows="4"
                                                class="block mr-4 p-2.5 w-full text-sm text-gray-900 bg-white rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Reply in thread..."
                                            >
                                            </textarea>
                                            @error('updatedReport') <span class="text-red-600">{{ $message }}</span> @enderror
                                            <button
                                                type="submit"
                                                class="inline-flex justify-center p-2 rounded-lg cursor-pointer text-primary-600 hover:bg-primary-100 dark:text-primary-500 dark:hover:bg-gray-600"
                                            >
                                                <svg aria-hidden="true" class="w-6 h-6 rotate-90" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path></svg>
                                                <span class="sr-only">Send</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            @else
                                <p
                                    id="report-content-{{ $report->id }}"
                                    class="mb-2 text-gray-900 dark:text-white"
                                >
                                    {{ $report->content }}
                                </p>
                            @endif

                            <!-- Export Button (Director Only) -->
                            @if(in_array(auth()->user()->role_id, [1, 2]) && !$editingReportId)
                                <div
                                    x-cloakid="export-button-{{ $report->id }}"
                                    class="items-center w-fit hover:cursor-pointer"
                                >
                                    <!-- Item -->
                                    <div class="flex items-center p-2 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-100">
                                        <div class="flex items-center ml-auto">
                                            <button
                                                type="button"
                                                class="rounded"
                                                wire:click="exportReport({{ $report->id }}, 'pdf')"
                                            >
                                                <svg class="w-3 h-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                                    <path clip-rule="evenodd" fill-rule="evenodd" d="M12 2.25a.75.75 0 01.75.75v11.69l3.22-3.22a.75.75 0 111.06 1.06l-4.5 4.5a.75.75 0 01-1.06 0l-4.5-4.5a.75.75 0 111.06-1.06l3.22 3.22V3a.75.75 0 01.75-.75zm-9 13.5a.75.75 0 01.75.75v2.25a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5V16.5a.75.75 0 011.5 0v2.25a3 3 0 01-3 3H5.25a3 3 0 01-3-3V16.5a.75.75 0 01.75-.75z"></path>
                                                </svg>
                                                <span class="sr-only">Download</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </article>
                        <!-- Report response -->
                        <article id="response-{{ $report->id }}" class="pl-12 mb-5">
                            @if($report->response)
                                <!-- Response header -->
                                <header
                                    id="response-header-{{ $report->id }}"
                                    class="flex items-center justify-between mb-4"
                                    x-data="{ open: false }"
                                >
                                    <div class="flex items-center">
                                        <p class="inline-flex items-center mr-3 text-sm font-semibold text-gray-900 dark:text-white"><img class="w-6 h-6 mr-2 rounded-full" src="https://avatar.iran.liara.run/public/{{$director->id}}"alt="Jese avatar">
                                            {{ $director->getFullNameAttribute() }}
                                        </p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            <time pubdate datetime="2022-02-08" title="February 8th, 2022">
                                                {{ $report->created_at->format('d/m/Y H:i A') }}
                                            </time>
                                        </p>
                                    </div>
                                    <!-- Response action (Director Only) -->
                                    @if(auth()->user()->role_id === 2)
                                    @if(!$editingResponseId)
                                    <button
                                        class="inline-flex items-center p-2 text-sm font-medium text-center text-gray-500 bg-white rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-gray-300 dark:focus:ring-gray-600"
                                        type="button"
                                        x-on:click="open = !open"
                                    >
                                        <svg class="w-5 h-5" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                        </svg>
                                        <span class="sr-only">Comment settings</span>
                                    </button>
                                    @endif
                                    <!-- Dropdown menu -->
                                    <div
                                        x-cloak
                                        x-show="open"
                                        x-on:click.away="open = false"
                                        class="absolute right-0 z-10 mr-1 bg-white divide-y divide-gray-100 rounded shadow w-36 dark:bg-gray-700 dark:divide-gray-600"
                                    >
                                        <ul class="py-1 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownMenuIconHorizontalButton">
                                            @if(!$editingResponseId)
                                            <li class="relative w-full">
                                                <button
                                                    x-on:click="open = false"
                                                    type="button"
                                                    class="block text-sm w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                                                    wire:click ="editResponse({{ $report->id }})"
                                                >
                                                Edit
                                                </button>
                                            </li>
                                            @endif
                                            <li>
                                                <button
                                                    type="button"
                                                    class="block text-sm w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                                                    wire:click="deleteResponse({{ $report->id }})"
                                                >
                                                Remove
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                    @endif
                                </header>
                            @endif
                            <!-- Response content -->
                            @if($editingResponseId === $report->id)
                                <!-- Edit Form Response content -->
                                <div
                                    id="response-edit-{{ $report->id }}"
                                    class="pl-8 mb-5"
                                >
                                    <form wire:submit.prevent="updateResponse({{ $report->id }})">
                                        <label for="update-response-{{ $report->id }}" class="sr-only">Your message</label>
                                        <div class="flex items-center mb-5">
                                            <textarea
                                                id="update-response-{{ $report->id }}"
                                                wire:model.defer="updatedResponse"
                                                rows="4"
                                                class="block mr-4 p-2.5 w-full text-sm text-gray-900 bg-white rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Reply in thread..."
                                            >
                                            </textarea>
                                            @error('updatedResponse') <span class="text-red-600">{{ $message }}</span> @enderror
                                            <button
                                                type="submit"
                                                class="inline-flex justify-center p-2 rounded-lg cursor-pointer text-primary-600 hover:bg-primary-100 dark:text-primary-500 dark:hover:bg-gray-600"
                                            >
                                                <svg aria-hidden="true" class="w-6 h-6 rotate-90" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path></svg>
                                                <span class="sr-only">Send</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            @elseif($report->response)
                                <div
                                    id="response-content-{{ $report->id }}"
                                    class="mb-4 pl-4 text-gray-900 dark:text-white border-l-4 border-primary-500 dark:border-primary-400"
                                >
                                    <p class="text-gray-600 dark:text-gray-300">{{ $report->response }}</p>
                                </div>
                            @else
                                <!-- Response Form (Director Only) -->
                                @if(auth()->user()->role_id === 2)
                                    <div
                                        x-cloak
                                        id="response-form-{{ $report->id }}"
                                        class="pl-8 mb-5"
                                    >
                                        <form
                                            wire:submit.prevent="submitResponse({{ $report->id }})"
                                        >
                                            <label for="response-{{ $report->id }}" class="sr-only">Your message</label>
                                            <div class="flex items-center mb-5">
                                                <textarea
                                                    id="response-{{ $report->id }}"
                                                    wire:model.defer="newResponse"
                                                    rows="4"
                                                    class="block mr-4 p-2.5 w-full text-sm text-gray-900 bg-white rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Reply in thread..."
                                                >
                                                </textarea>
                                                @error('newResponse') <span class="text-red-600">{{ $message }}</span> @enderror
                                                <button
                                                    type="submit"
                                                    class="inline-flex justify-center p-2 rounded-lg cursor-pointer text-primary-600 hover:bg-primary-100 dark:text-primary-500 dark:hover:bg-gray-600"
                                                >
                                                    <svg aria-hidden="true" class="w-6 h-6 rotate-90" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path></svg>
                                                    <span class="sr-only">Send</span>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                @endif
                            @endif
                        </article>
                    @endforeach
                </div>
                @else
                <p class="text-md text-gray-900 dark:text-gray-100">
                    Pas de Rapports.
                </p>
                @endif
                <!-- New Report Form (Manager Only) -->
                @if(auth()->user()->role_id === 3)
                <form wire:submit.prevent="submitReport">
                    <div class="w-full border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600 mt-4">
                        <div class="px-4 py-2 bg-white rounded-t-lg dark:bg-gray-800">
                            <label for="newReport" class="sr-only">Write your message</label>
                            <textarea
                                id="newReport"
                                wire:model.defer="newReport"
                                rows="4"
                                class="w-full px-0 text-sm text-gray-900 bg-white border-0 dark:bg-gray-800 focus:ring-0 dark:text-white dark:placeholder-gray-400"
                                placeholder="Write your report"
                                required>
                            </textarea>
                            @error('newReport') <span class="text-red-600">{{ $message }}</span> @enderror
                        </div>
                        {{-- <div class="px-4 py-2">
                            <input
                                type="file"
                                wire:model="file"
                                class="block"
                            />
                            @error('file') <span class="text-red-600">{{ $message }}</span> @enderror
                        </div> --}}
                        <div class="flex items-center justify-between px-3 py-2 border-t dark:border-gray-600">
                            <button
                                type="submit"
                                class="inline-flex justify-center p-2 rounded-lg cursor-pointer text-primary-600 hover:bg-primary-100 dark:text-primary-500 dark:hover:bg-gray-600"
                            >
                                <svg aria-hidden="true" class="w-6 h-6 rotate-90" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"></path></svg>
                                <span class="sr-only">Send</span>
                            </button>
                        </div>
                    </div>
                </form>
                @endif
            </div>
        </div>
    </x-content-body>

    <!-- Chart Scripts -->
    @push('scripts')
    <script>
        document.addEventListener('livewire:initialized', function () {
            initCharts();

            // Listen for chart updates
            Livewire.on('chartsUpdated', function() {
                initCharts();
            });
        });

        function initCharts() {
            if (document.getElementById('reports-chart')) {
                initReportsChart();
            }

            if (document.getElementById('scores-chart')) {
                initScoresChart();
            }
        }

        function initReportsChart() {
            const chartData = @json($chartData ?? ['labels' => [], 'series' => []]);

            const options = {
                chart: {
                    type: 'area',
                    height: 320,
                    fontFamily: 'Inter, sans-serif',
                    toolbar: {
                        show: false
                    }
                },
                series: chartData.series,
                xaxis: {
                    categories: chartData.labels,
                    labels: {
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                            fontSize: '12px',
                            fontWeight: 500
                        }
                    },
                    axisBorder: {
                        show: false
                    },
                    axisTicks: {
                        show: false
                    }
                },
                yaxis: {
                    labels: {
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                            fontSize: '12px',
                            fontWeight: 500
                        },
                        formatter: function (value) {
                            return Math.round(value);
                        }
                    }
                },
                grid: {
                    show: true,
                    borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                    strokeDashArray: 4
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                fill: {
                    type: 'gradient',
                    gradient: {
                        shade: 'dark',
                        type: 'vertical',
                        shadeIntensity: 0.3,
                        opacityFrom: 0.4,
                        opacityTo: 0.1,
                        stops: [0, 100]
                    }
                },
                colors: ['#3b82f6'],
                tooltip: {
                    theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
                    y: {
                        formatter: function (value) {
                            return value + ' reports';
                        }
                    }
                }
            };

            const reportsChart = new ApexCharts(document.getElementById('reports-chart'), options);
            reportsChart.render();
        }

        function initScoresChart() {
            const scoreData = @json($scoreChartData ?? ['labels' => [], 'series' => []]);

            const options = {
                chart: {
                    type: 'radar',
                    height: 320,
                    fontFamily: 'Inter, sans-serif',
                    toolbar: {
                        show: false
                    }
                },
                series: [{
                    name: 'Score',
                    data: scoreData.series
                }],
                labels: scoreData.labels,
                plotOptions: {
                    radar: {
                        size: 140,
                        polygons: {
                            strokeColors: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                            fill: {
                                colors: ['#f8fafc', '#f1f5f9']
                            }
                        }
                    }
                },
                colors: ['#3b82f6'],
                markers: {
                    size: 4,
                    colors: ['#3b82f6'],
                    strokeWidth: 2
                },
                tooltip: {
                    theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
                    y: {
                        formatter: function (value) {
                            return value;
                        }
                    }
                },
                yaxis: {
                    show: false,
                    min: 0,
                    max: 100
                },
                xaxis: {
                    categories: scoreData.labels,
                    labels: {
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                            fontSize: '12px',
                            fontWeight: 500
                        }
                    }
                }
            };

            const scoresChart = new ApexCharts(document.getElementById('scores-chart'), options);
            scoresChart.render();
        }
    </script>
    @endpush


</x-content>
