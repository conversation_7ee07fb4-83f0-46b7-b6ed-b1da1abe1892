<x-content>
    <x-content-header title="Customer Details: {{ $customer->name }}">
        <x-page-button type="edit" label="Edit" action="$dispatch('to-customer-edit', {customer: {{ $customer->id }}})"/>
        <x-page-button type="delete" label="Delete" action="delete"/>
        <x-page-button type="back" label="Back" action="$dispatch('to-customer-index')"/>
    </x-content-header>

    @if (session()->has('message'))
        <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            <span class="font-medium">Success!</span> {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
            <span class="font-medium">Error!</span> {{ session('error') }}
        </div>
    @endif

    <x-content-body>
        <div x-data="{ activeTab: 'overview' }">
            <!-- Tabs -->
            <div class="border-b border-gray-200 dark:border-gray-700 mb-4">
                <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
                    <li class="mr-2">
                        <button
                            type="button"
                            @click="activeTab = 'overview'"
                            :class="activeTab === 'overview' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                            class="inline-block p-4 border-b-2 rounded-t-lg"
                        >
                            Overview
                        </button>
                    </li>
                    <li class="mr-2">
                        <button
                            type="button"
                            @click="activeTab = 'contacts'"
                            :class="activeTab === 'contacts' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                            class="inline-block p-4 border-b-2 rounded-t-lg"
                        >
                            Contacts
                        </button>
                    </li>
                    <li class="mr-2">
                        <button
                            type="button"
                            @click="activeTab = 'campaigns'"
                            :class="activeTab === 'campaigns' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                            class="inline-block p-4 border-b-2 rounded-t-lg"
                        >
                            Campaigns
                        </button>
                    </li>
                    <li class="mr-2">
                        <button
                            type="button"
                            @click="activeTab = 'documents'"
                            :class="activeTab === 'documents' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                            class="inline-block p-4 border-b-2 rounded-t-lg"
                        >
                            Documents
                        </button>
                    </li>
                    <li>
                        <button
                            type="button"
                            @click="activeTab = 'activities'"
                            :class="activeTab === 'activities' ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500' : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'"
                            class="inline-block p-4 border-b-2 rounded-t-lg"
                        >
                            Activities
                        </button>
                    </li>
                </ul>
            </div>

            <!-- Overview Tab -->
            <div x-show="activeTab === 'overview'" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Information</h3>

                        <div class="space-y-3">
                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->name }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Industry</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->industry ?? 'N/A' }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Type</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->customer_type ?? 'N/A' }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Size</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->size ?? 'N/A' }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Segment</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->segment ?? 'N/A' }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Tier</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->customer_tier ?? 'N/A' }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</div>
                                <div class="col-span-2 text-sm">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $customer->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                        {{ ucfirst($customer->status) }}
                                    </span>
                                </div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Manager</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                                    {{ $customer->accountManager->name ?? 'N/A' }}
                                </div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Satisfaction</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                                    @if($customer->customer_satisfaction)
                                        <div class="flex items-center">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star {{ $i <= $customer->customer_satisfaction ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600' }} mr-1"></i>
                                            @endfor
                                            <span class="ml-1">{{ $customer->customer_satisfaction }}/5</span>
                                        </div>
                                    @else
                                        N/A
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Contact Information</h3>

                        <div class="space-y-3">
                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->email ?? 'N/A' }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">{{ $customer->phone ?? 'N/A' }}</div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Website</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                                    @if($customer->website)
                                        <a href="{{ $customer->website }}" target="_blank" class="text-blue-600 dark:text-blue-500 hover:underline">
                                            {{ $customer->website }}
                                        </a>
                                    @else
                                        N/A
                                    @endif
                                </div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                                    @if($customer->address)
                                        {{ $customer->address }}<br>
                                        {{ $customer->city ?? '' }} {{ $customer->postal_code ?? '' }}<br>
                                        {{ $customer->country ?? '' }}
                                    @else
                                        N/A
                                    @endif
                                </div>
                            </div>

                            <div class="grid grid-cols-3 gap-4">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Primary Contact</div>
                                <div class="col-span-2 text-sm text-gray-900 dark:text-white">
                                    @if($customer->contact_person)
                                        {{ $customer->contact_person }}<br>
                                        <span class="text-gray-500 dark:text-gray-400">{{ $customer->contact_position ?? '' }}</span><br>
                                        {{ $customer->contact_phone ?? '' }}
                                    @else
                                        N/A
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contacts Tab -->
            <div x-show="activeTab === 'contacts'" class="space-y-4">
                <livewire:campaigns.campaign-customer-contacts :customer-id="$customer->id" :key="'contacts-'.$customer->id" />
            </div>

            <!-- Campaigns Tab -->
            <div x-show="activeTab === 'campaigns'" class="space-y-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Campaigns</h3>
                        <a href="{{ route('campaigns.create', ['customer_id' => $customer->id]) }}" class="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-1"></i> New Campaign
                        </a>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3">Name</th>
                                    <th scope="col" class="px-6 py-3">Start Date</th>
                                    <th scope="col" class="px-6 py-3">End Date</th>
                                    <th scope="col" class="px-6 py-3">Status</th>
                                    <th scope="col" class="px-6 py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($customer->campaigns as $campaign)
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            {{ $campaign->name }}
                                        </th>
                                        <td class="px-6 py-4">
                                            {{ $campaign->start_date ? $campaign->start_date->format('M d, Y') : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $campaign->end_date ? $campaign->end_date->format('M d, Y') : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $campaign->status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                                {{ ucfirst($campaign->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <a href="{{ route('campaigns.show', $campaign->id) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td colspan="5" class="px-6 py-4 text-center">
                                            No campaigns found for this customer.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Documents Tab -->
            <div x-show="activeTab === 'documents'" class="space-y-4">
                <livewire:campaigns.campaign-customer-documents :customer-id="$customer->id" :key="'documents-'.$customer->id" />
            </div>

            <!-- Activities Tab -->
            <div x-show="activeTab === 'activities'" class="space-y-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Activities</h3>

                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Activity tracking will be implemented in a future update.
                    </p>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
