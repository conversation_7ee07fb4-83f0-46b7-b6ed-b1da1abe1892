<div>
    <!-- Skill Requirements Section -->
    <div class="mb-6">
        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Current Skill Requirements</h4>
        
        @if($requiredSkills->count() > 0)
            <div class="overflow-x-auto relative">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="py-3 px-6">Skill</th>
                            <th scope="col" class="py-3 px-6">Min. Proficiency</th>
                            <th scope="col" class="py-3 px-6">Mandatory</th>
                            <th scope="col" class="py-3 px-6">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($requiredSkills as $skill)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td class="py-4 px-6">{{ $skill->name }}</td>
                                <td class="py-4 px-6">{{ $skill->pivot->minimum_proficiency_level }}/5</td>
                                <td class="py-4 px-6">
                                    @if($skill->pivot->is_mandatory)
                                        <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                            Yes
                                        </span>
                                    @else
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                                            No
                                        </span>
                                    @endif
                                </td>
                                <td class="py-4 px-6 flex space-x-2">
                                    <button wire:click="removeSkillRequirement({{ $skill->id }})" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-600">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">No skill requirements defined yet.</p>
        @endif
        
        <!-- Add Skill Requirement Form -->
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h5 class="text-md font-medium text-gray-900 dark:text-white mb-3">Add Skill Requirement</h5>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="selectedSkill" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Skill</label>
                    <select wire:model="selectedSkill" id="selectedSkill" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select a skill</option>
                        @foreach($availableSkills as $skill)
                            <option value="{{ $skill->id }}">{{ $skill->name }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label for="minimumProficiencyLevel" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Min. Proficiency (1-5)</label>
                    <select wire:model="minimumProficiencyLevel" id="minimumProficiencyLevel" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        @for($i = 1; $i <= 5; $i++)
                            <option value="{{ $i }}">{{ $i }}</option>
                        @endfor
                    </select>
                </div>
                
                <div>
                    <label for="isSkillMandatory" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mandatory</label>
                    <select wire:model="isSkillMandatory" id="isSkillMandatory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </div>
            </div>
            
            <button wire:click="addSkillRequirement" class="mt-4 text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Add Skill Requirement
            </button>
        </div>
    </div>
    
    <!-- Certification Requirements Section -->
    <div class="mt-8">
        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Current Certification Requirements</h4>
        
        @if($requiredCertifications->count() > 0)
            <div class="overflow-x-auto relative">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="py-3 px-6">Certification</th>
                            <th scope="col" class="py-3 px-6">Mandatory</th>
                            <th scope="col" class="py-3 px-6">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($requiredCertifications as $certification)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td class="py-4 px-6">{{ $certification->name }}</td>
                                <td class="py-4 px-6">
                                    @if($certification->pivot->is_mandatory)
                                        <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                            Yes
                                        </span>
                                    @else
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                                            No
                                        </span>
                                    @endif
                                </td>
                                <td class="py-4 px-6 flex space-x-2">
                                    <button wire:click="removeCertificationRequirement({{ $certification->id }})" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-600">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">No certification requirements defined yet.</p>
        @endif
        
        <!-- Add Certification Requirement Form -->
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h5 class="text-md font-medium text-gray-900 dark:text-white mb-3">Add Certification Requirement</h5>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="selectedCertification" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Certification</label>
                    <select wire:model="selectedCertification" id="selectedCertification" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select a certification</option>
                        @foreach($availableCertifications as $certification)
                            <option value="{{ $certification->id }}">{{ $certification->name }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label for="isCertificationMandatory" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Mandatory</label>
                    <select wire:model="isCertificationMandatory" id="isCertificationMandatory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="1">Yes</option>
                        <option value="0">No</option>
                    </select>
                </div>
            </div>
            
            <button wire:click="addCertificationRequirement" class="mt-4 text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Add Certification Requirement
            </button>
        </div>
    </div>
</div>
