<div>
    <x-content>
        <x-card key="customer-index-header" class="p-4">
            <div class="w-full">
                <!-- Main filters and actions -->
                <div class="flex flex-col space-y-4">
                    <!-- Search and primary filters -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                        <!-- Search input -->
                        <div>
                            <label for="customers-search" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Search</label>
                            <input
                                type="text"
                                wire:model.live.debounce.300ms="search"
                                id="customers-search"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Search for customer">
                        </div>

                        <!-- Status filter -->
                        <div>
                            <label for="status-filter" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                            <select
                                id="status-filter"
                                wire:model.live="filterStatus"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                                <option value="">All Statuses</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>

                        <!-- Industry filter -->
                        <div>
                            <label for="industry-filter" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Industry</label>
                            <select
                                id="industry-filter"
                                wire:model.live="filterIndustry"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                                <option value="">All Industries</option>
                                @foreach($industries as $industry)
                                    <option value="{{ $industry }}">{{ $industry }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Type filter -->
                        <div>
                            <label for="type-filter" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Type</label>
                            <select
                                id="type-filter"
                                wire:model.live="filterType"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                                <option value="">All Types</option>
                                @foreach($customerTypes as $type)
                                    <option value="{{ $type }}">{{ $type }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- Actions row -->
                    <div class="flex flex-wrap items-center justify-between gap-3 pt-2">
                        <!-- Left side - Delete button -->
                        <div>
                            <button
                                wire:click="deleteSelected"
                                wire:confirm="Are you sure you want to delete the selected customers?"
                                class="inline-flex items-center justify-center p-2 text-gray-500 rounded {{count($selectedCustomers) ? 'cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white' : 'opacity-50 cursor-not-allowed'}}"
                                {{count($selectedCustomers) ? '' : 'disabled'}}
                            >
                                <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                                Delete Selected
                            </button>
                        </div>

                        <!-- Right side - Action buttons -->
                        <div class="flex flex-wrap items-center gap-2">
                            <button wire:click="$dispatch('to-customer-create')" class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                                Add Customer
                            </button>
                            <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                @foreach ([10, 25, 50, 100] as $value)
                                    <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <!-- View Mode Buttons -->
                    <div class="flex flex-wrap justify-end gap-2 pt-2">
                        <button wire:click="setViewMode('list')" class="px-3 py-2 text-xs font-medium {{ $viewMode === 'list' ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white' }} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-list mr-1"></i> List
                        </button>
                        <button wire:click="setViewMode('grid')" class="px-3 py-2 text-xs font-medium {{ $viewMode === 'grid' ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white' }} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-th-large mr-1"></i> Grid
                        </button>
                        <button wire:click="setViewMode('dashboard')" class="px-3 py-2 text-xs font-medium {{ $viewMode === 'dashboard' ? 'text-white bg-blue-600 hover:bg-blue-700' : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white' }} rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <i class="fas fa-chart-pie mr-1"></i> Dashboard
                        </button>
                    </div>
                </div>
            </div>
        </x-card>

        @if (session()->has('message'))
            <div class="p-4 mt-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="p-4 mt-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <span class="font-medium">Error!</span> {{ session('error') }}
            </div>
        @endif

        <!-- Content based on view mode -->
        @if($viewMode === 'list')
            @include('livewire.campaigns.partials.customers-list')
        @elseif($viewMode === 'grid')
            @include('livewire.campaigns.partials.customers-grid')
        @elseif($viewMode === 'dashboard')
            @include('livewire.campaigns.partials.customers-dashboard')
        @endif

        <!-- Toast Messages -->
        <div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

        <script>
            document.addEventListener('livewire:initialized', function () {
                Livewire.on('show-message', event => {
                    const toast = document.createElement('div');
                    toast.className = 'flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800';
                    toast.innerHTML = `
                        <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="ml-3 text-sm font-normal">${event.message}</div>
                        <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" onclick="this.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    document.getElementById('toast-container').appendChild(toast);
                    setTimeout(() => {
                        toast.remove();
                    }, 5000);
                });

                Livewire.on('show-error', event => {
                    const toast = document.createElement('div');
                    toast.className = 'flex items-center w-full max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow dark:text-gray-400 dark:bg-gray-800';
                    toast.innerHTML = `
                        <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="ml-3 text-sm font-normal">${event.message}</div>
                        <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 dark:text-gray-500 dark:hover:text-white dark:bg-gray-800 dark:hover:bg-gray-700" onclick="this.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    document.getElementById('toast-container').appendChild(toast);
                    setTimeout(() => {
                        toast.remove();
                    }, 5000);
                });

                Livewire.on('dashboardUpdated', () => {
                    if (typeof initCharts === 'function') {
                        setTimeout(() => {
                            initCharts();
                        }, 100);
                    }
                });
            });
        </script>
    </x-content>
</div>
