<x-content>
    <form wire:submit="submit" class="space-y-8" x-data="{
        performance: @entangle('form.performance'),
        productivity: @entangle('form.productivity'),
        rating: @entangle('form.rating'),
        init() {
            this.$wire.on('performanceUpdated', () => {
                this.performance = this.$wire.form.performance;
            });
            this.$wire.on('productivityUpdated', () => {
                this.productivity = this.$wire.form.productivity;
            });
            this.$wire.on('ratingUpdated', () => {
                this.rating = this.$wire.form.rating;
            });
        }
    }">
        <x-content-header title="Agent Observation">
            <x-page-button type="save" label="Save" />
            <x-page-button type="back" label="Back" :href="route('campaigns.agents')" wire:navigate />
        </x-content-header>

        @if (session()->has('message'))
        <div class="p-4 mt-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            {{ session('message') }}
        </div>
        @endif
        <x-content-body>
            <x-card key="campaign-observation" class="flex flex-col gap-4 p-6 w-full">
                <div>
                    <label for="date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Date</label>
                    <input type="date" wire:model="form.date" name="date" id="date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    @error('form.date') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>
                <div>
                    <label for="subject" class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-300">Subject</label>
                    <input type="text" wire:model="form.subject" id="subject" class="block p-3 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 dark:shadow-sm-light" placeholder="Brief subject of the observation">
                    @error('form.subject') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>
                <div class="sm:col-span-2">
                    <label for="observation" class="block mb-2 text-sm font-medium text-gray-900 dark:text-gray-400">Your observation</label>
                    <textarea wire:model="form.observation" id="observation" rows="6" class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg shadow-sm border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Detailed observation notes..."></textarea>
                    @error('form.observation') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="performance" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Performance (<span x-text="performance"></span>%)</label>
                        <div class="flex items-center space-x-4">
                            <input id="performance" wire:model.live="form.performance" wire:change="updatePerformance" type="range" min="0" max="100" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                            <div class="flex space-x-1">
                                <button type="button" wire:click="$set('form.performance', 25)" class="px-2 py-1 text-xs font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-300">25%</button>
                                <button type="button" wire:click="$set('form.performance', 50)" class="px-2 py-1 text-xs font-medium text-center text-white bg-orange-500 rounded-lg hover:bg-orange-600 focus:ring-2 focus:ring-orange-300">50%</button>
                                <button type="button" wire:click="$set('form.performance', 75)" class="px-2 py-1 text-xs font-medium text-center text-white bg-yellow-500 rounded-lg hover:bg-yellow-600 focus:ring-2 focus:ring-yellow-300">75%</button>
                                <button type="button" wire:click="$set('form.performance', 100)" class="px-2 py-1 text-xs font-medium text-center text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-300">100%</button>
                            </div>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $form['performance'] <= 60 ? 'bg-red-600' :
                                          ($form['performance'] <= 80 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ $form['performance'] }}%">
                            </div>
                        </div>
                        @error('form.performance') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <label for="productivity" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Productivity (<span x-text="productivity"></span>%)</label>
                        <div class="flex items-center space-x-4">
                            <input id="productivity" wire:model.live="form.productivity" wire:change="updateProductivity" type="range" min="0" max="100" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                            <div class="flex space-x-1">
                                <button type="button" wire:click="$set('form.productivity', 25)" class="px-2 py-1 text-xs font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-300">25%</button>
                                <button type="button" wire:click="$set('form.productivity', 50)" class="px-2 py-1 text-xs font-medium text-center text-white bg-orange-500 rounded-lg hover:bg-orange-600 focus:ring-2 focus:ring-orange-300">50%</button>
                                <button type="button" wire:click="$set('form.productivity', 75)" class="px-2 py-1 text-xs font-medium text-center text-white bg-yellow-500 rounded-lg hover:bg-yellow-600 focus:ring-2 focus:ring-yellow-300">75%</button>
                                <button type="button" wire:click="$set('form.productivity', 100)" class="px-2 py-1 text-xs font-medium text-center text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-300">100%</button>
                            </div>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $form['productivity'] <= 60 ? 'bg-red-600' :
                                          ($form['productivity'] <= 80 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ $form['productivity'] }}%">
                            </div>
                        </div>
                        @error('form.productivity') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>

                <div>
                    <label for="rating" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Rating (<span x-text="rating"></span>/3)</label>
                    <div class="flex items-center space-x-4">
                        <input id="rating" wire:model.live="form.rating" wire:change="updateRating" type="range" min="0" max="3" step="0.1" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                        <div class="flex space-x-1">
                            <button type="button" wire:click="$set('form.rating', 0)" class="px-2 py-1 text-xs font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-300">0</button>
                            <button type="button" wire:click="$set('form.rating', 1)" class="px-2 py-1 text-xs font-medium text-center text-white bg-orange-500 rounded-lg hover:bg-orange-600 focus:ring-2 focus:ring-orange-300">1</button>
                            <button type="button" wire:click="$set('form.rating', 2)" class="px-2 py-1 text-xs font-medium text-center text-white bg-yellow-500 rounded-lg hover:bg-yellow-600 focus:ring-2 focus:ring-yellow-300">2</button>
                            <button type="button" wire:click="$set('form.rating', 3)" class="px-2 py-1 text-xs font-medium text-center text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-300">3</button>
                        </div>
                    </div>
                    <div class="mt-2 w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div class="{{ $form['rating'] <= 1 ? 'bg-red-600' :
                                      ($form['rating'] <= 2 ? 'bg-yellow-500' :
                                      'bg-green-600') }} h-2.5 rounded-full"
                             style="width: {{ ($form['rating'] / 3) * 100 }}%">
                        </div>
                    </div>
                    @error('form.rating') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>
            </x-card>
        </x-content-body>
    </form>
</x-content>
