<div class="fixed inset-0 z-50 overflow-y-auto" x-show="open" x-cloak>
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true" x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
            <div class="absolute inset-0 bg-gray-500 opacity-75 dark:bg-gray-900 dark:opacity-80"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block w-full max-w-5xl overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl dark:bg-gray-800 sm:my-8 sm:align-middle" x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <!-- Modal header -->
            <div class="flex items-start justify-between p-4 border-b rounded-t dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                    {{ $report->title ?? "Report #$report->id" }}
                </h3>
                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-700 dark:hover:text-white" @click="open = false">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                </button>
            </div>

            <!-- Modal body -->
            <div class="p-6 space-y-6 max-h-[calc(100vh-200px)] overflow-y-auto">
                <!-- Report metadata -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Report Details</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">ID:</span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">{{ $report->id }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Date:</span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">{{ $report->date->format('M d, Y') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Type:</span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">{{ ucfirst($report->type ?? 'Standard') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Category:</span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">{{ ucfirst($report->category ?? 'General') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Status:</span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                      ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                      ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                      ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                    {{ ucfirst($report->status) }}
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Priority:</span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $report->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                      ($report->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                      ($report->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                    {{ ucfirst($report->priority ?? 'Normal') }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Campaign & Author</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Campaign:</span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">{{ $report->campaign->name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Created By:</span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">{{ $report->creator->first_name }} {{ $report->creator->last_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">Created At:</span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">{{ $report->created_at->format('M d, Y H:i') }}</span>
                            </div>
                            @if($report->approval_status)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">Approval Status:</span>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {{ $report->approval_status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                          ($report->approval_status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                        {{ ucfirst($report->approval_status) }}
                                    </span>
                                </div>
                            @endif
                            @if($report->approver)
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">Approved By:</span>
                                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ $report->approver->first_name }} {{ $report->approver->last_name }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">Approved At:</span>
                                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ $report->approved_at->format('M d, Y H:i') }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                @if($report->performance_score !== null || $report->quality_score !== null || $report->compliance_score !== null)
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-4">Performance Metrics</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Performance Score -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Performance</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $report->performance_score ?? 0 }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="h-2.5 rounded-full bg-blue-600" style="width: {{ $report->performance_score ?? 0 }}%"></div>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {{ $report->performance_score >= 90 ? 'Excellent' :
                                      ($report->performance_score >= 80 ? 'Good' :
                                      ($report->performance_score >= 70 ? 'Satisfactory' :
                                      ($report->performance_score >= 60 ? 'Needs Improvement' : 'Unsatisfactory'))) }}
                                </p>
                            </div>

                            <!-- Quality Score -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Quality</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $report->quality_score ?? 0 }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="h-2.5 rounded-full bg-green-600" style="width: {{ $report->quality_score ?? 0 }}%"></div>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {{ $report->quality_score >= 90 ? 'Excellent' :
                                      ($report->quality_score >= 80 ? 'Good' :
                                      ($report->quality_score >= 70 ? 'Satisfactory' :
                                      ($report->quality_score >= 60 ? 'Needs Improvement' : 'Unsatisfactory'))) }}
                                </p>
                            </div>

                            <!-- Compliance Score -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Compliance</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $report->compliance_score ?? 0 }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="h-2.5 rounded-full bg-purple-600" style="width: {{ $report->compliance_score ?? 0 }}%"></div>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {{ $report->compliance_score >= 90 ? 'Excellent' :
                                      ($report->compliance_score >= 80 ? 'Good' :
                                      ($report->compliance_score >= 70 ? 'Satisfactory' :
                                      ($report->compliance_score >= 60 ? 'Needs Improvement' : 'Unsatisfactory'))) }}
                                </p>
                            </div>
                        </div>

                        <!-- Overall Score -->
                        @if($report->overall_score)
                            <div class="mt-4">
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">Overall Score</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $report->overall_score }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="h-2.5 rounded-full
                                        {{ $report->overall_score >= 90 ? 'bg-green-600' :
                                          ($report->overall_score >= 80 ? 'bg-blue-600' :
                                          ($report->overall_score >= 70 ? 'bg-yellow-600' :
                                          ($report->overall_score >= 60 ? 'bg-orange-600' : 'bg-red-600'))) }}"
                                        style="width: {{ $report->overall_score }}%"></div>
                                </div>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    {{ $report->overall_score >= 90 ? 'Excellent' :
                                      ($report->overall_score >= 80 ? 'Good' :
                                      ($report->overall_score >= 70 ? 'Satisfactory' :
                                      ($report->overall_score >= 60 ? 'Needs Improvement' : 'Unsatisfactory'))) }}
                                </p>
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Tags -->
                @if(!empty($report->tags))
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Tags</h4>
                        <div class="flex flex-wrap gap-2">
                            @foreach($report->tags as $tag)
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                    {{ $tag }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Report Content -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Report Content</h4>
                    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="prose dark:prose-invert max-w-none">
                            {!! nl2br(e($report->content)) !!}
                        </div>
                    </div>
                </div>

                <!-- Response -->
                @if($report->response)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Response</h4>
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="prose dark:prose-invert max-w-none">
                                {!! nl2br(e($report->response)) !!}
                            </div>
                            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                Responded at: {{ $report->responded_at ? $report->responded_at->format('M d, Y H:i') : 'N/A' }}
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Approval Notes -->
                @if($report->approval_notes)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Approval Notes</h4>
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <div class="prose dark:prose-invert max-w-none">
                                {!! nl2br(e($report->approval_notes)) !!}
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Attachments -->
                @if($report->media && $report->media->count() > 0)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Attachments</h4>
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($report->media as $media)
                                    <li class="py-2 flex items-center justify-between">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a3 3 0 006 0V7a1 1 0 112 0v4a5 5 0 01-10 0V7a5 5 0 0110 0v1h2V7a7 7 0 00-14 0v4a7 7 0 0014 0V7a5 5 0 00-10 0v4a3 3 0 006 0V7a1 1 0 00-2 0v4a1 1 0 01-2 0V7a3 3 0 00-3-3z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="text-sm text-gray-700 dark:text-gray-300">{{ $media->file_name ?? basename($media->file_path) }}</span>
                                        </div>
                                        <a href="{{ Storage::url($media->file_path) }}" target="_blank" class="text-sm font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                            Download
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif

                <!-- Comments -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Comments</h4>

                    @if($report->comments && $report->comments->count() > 0)
                        <div class="space-y-4">
                            @foreach($report->comments->where('parent_id', null) as $comment)
                                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {{ substr($comment->user->first_name, 0, 1) }}{{ substr($comment->user->last_name, 0, 1) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $comment->user->first_name }} {{ $comment->user->last_name }}</span>
                                                    <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">{{ $comment->created_at->format('M d, Y H:i') }}</span>
                                                </div>

                                                @if(!$comment->is_resolved)
                                                    <button wire:click="resolveComment({{ $comment->id }})" class="text-xs font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                                        Mark as Resolved
                                                    </button>
                                                @else
                                                    <span class="text-xs font-medium text-green-600 dark:text-green-500">
                                                        Resolved by {{ $comment->resolver->first_name }} {{ $comment->resolver->last_name }}
                                                    </span>
                                                @endif
                                            </div>
                                            <div class="mt-1 text-sm text-gray-700 dark:text-gray-300">
                                                {!! nl2br(e($comment->content)) !!}
                                            </div>

                                            <!-- Reply button -->
                                            <div class="mt-2">
                                                <button wire:click="replyToComment({{ $comment->id }})" class="text-xs font-medium text-gray-600 dark:text-gray-400 hover:underline">
                                                    Reply
                                                </button>
                                            </div>

                                            <!-- Replies -->
                                            @if($comment->replies && $comment->replies->count() > 0)
                                                <div class="mt-3 space-y-3 pl-6 border-l-2 border-gray-200 dark:border-gray-700">
                                                    @foreach($comment->replies as $reply)
                                                        <div class="flex items-start">
                                                            <div class="flex-shrink-0">
                                                                <div class="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                                                                        {{ substr($reply->user->first_name, 0, 1) }}{{ substr($reply->user->last_name, 0, 1) }}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div class="ml-2 flex-1">
                                                                <div>
                                                                    <span class="text-xs font-medium text-gray-900 dark:text-white">{{ $reply->user->first_name }} {{ $reply->user->last_name }}</span>
                                                                    <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">{{ $reply->created_at->format('M d, Y H:i') }}</span>
                                                                </div>
                                                                <div class="mt-1 text-xs text-gray-700 dark:text-gray-300">
                                                                    {!! nl2br(e($reply->content)) !!}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endif

                                            <!-- Reply form -->
                                            @if($replyingToCommentId === $comment->id)
                                                <div class="mt-3 pl-6">
                                                    <div class="mb-2">
                                                        <label for="reply-{{ $comment->id }}" class="sr-only">Your reply</label>
                                                        <textarea id="reply-{{ $comment->id }}" rows="2" wire:model.defer="newComment" class="block w-full px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Write your reply..."></textarea>
                                                    </div>
                                                    <div class="flex justify-end space-x-2">
                                                        <button wire:click="cancelReply" class="px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                                                            Cancel
                                                        </button>
                                                        <button wire:click="addComment({{ $report->id }})" class="px-3 py-1.5 text-xs font-medium text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                                            Reply
                                                        </button>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 text-center">
                            <p class="text-sm text-gray-500 dark:text-gray-400">No comments yet</p>
                        </div>
                    @endif

                    <!-- Add comment form -->
                    <div class="mt-4">
                        <div class="mb-2">
                            <label for="new-comment" class="sr-only">Add a comment</label>
                            <textarea id="new-comment" rows="3" wire:model.defer="newComment" class="block w-full px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Add a comment..."></textarea>
                        </div>
                        <div class="flex justify-end">
                            <button wire:click="addComment({{ $report->id }})" class="px-4 py-2 text-sm font-medium text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                Add Comment
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal footer -->
            <div class="flex items-center justify-between p-4 border-t border-gray-200 rounded-b dark:border-gray-700">
                <div>
                    @if(auth()->user()->role_id <= 3 && $report->approval_status === 'pending')
                        <button wire:click="approveReport({{ $report->id }})" class="px-4 py-2 mr-2 text-sm font-medium text-white bg-green-700 rounded-lg hover:bg-green-800 focus:ring-4 focus:ring-green-300 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800">
                            Approve
                        </button>
                        <button wire:click="rejectReport({{ $report->id }})" class="px-4 py-2 text-sm font-medium text-white bg-red-700 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800">
                            Reject
                        </button>
                    @endif
                </div>

                <div class="flex space-x-2">
                    <button wire:click="exportReport({{ $report->id }}, 'pdf')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                        <i class="far fa-file-pdf mr-1 text-red-500"></i> Export PDF
                    </button>
                    <button wire:click="exportReport({{ $report->id }}, 'docx')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                        <i class="far fa-file-word mr-1 text-blue-500"></i> Export DOCX
                    </button>
                    <button @click="open = false" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>