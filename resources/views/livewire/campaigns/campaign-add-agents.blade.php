<x-content>
    <x-content-header title="Add Agents to Campaign">
        <x-page-button type="save" label="Add Selected to Campaign" action="addToCampaign"/>
        <x-page-button type="cancel" label="Cancel" action="cancel"/>
    </x-content-header>

    <x-content-body>
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="mb-4">
                <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4">
                    <div class="w-full md:w-1/2">
                        <form class="flex items-center">
                            <label for="simple-search" class="sr-only">Search</label>
                            <div class="relative w-full">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <input wire:model.live.debounce.300ms="search" type="text" id="simple-search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search" required="">
                            </div>
                        </form>
                    </div>
                    <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
                        <div class="flex items-center space-x-3 w-full md:w-auto">
                            <div class="w-full md:w-40">
                                <select wire:model.live="perPage" id="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <option value="8">8 per page</option>
                                    <option value="16">16 per page</option>
                                    <option value="32">32 per page</option>
                                    <option value="64">64 per page</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <input
                            id="checkbox-all"
                            type="checkbox"
                            wire:click="$toggle('selectAll')"
                            wire:change="toggleSelectAll"
                            {{ $selectAll ? 'checked' : '' }}
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                        >
                        <label for="checkbox-all" class="sr-only">Select all</label>
                        <span class="ml-2 text-sm font-medium text-gray-900 dark:text-white">
                            {{ count($selectedAgents) }} agent(s) selected
                        </span>
                    </div>

                    <div class="w-full md:w-1/3">
                        <select wire:model.live="campaign_id" id="campaign_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="">Select Campaign</option>
                            @foreach($campaigns as $campaign)
                                <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                            @endforeach
                        </select>
                        @error('campaign_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            @if (session()->has('message'))
                <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                    {{ session('message') }}
                </div>
            @endif

            @if (session()->has('error'))
                <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                    {{ session('error') }}
                </div>
            @endif

            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mt-4">
                @forelse ($agents as $agent)
                    <x-card key="agent-{{ $agent->id }}" class="max-w-sm">
                        <div class="flex justify-between px-4 pt-4">
                            <div class="flex items-center">
                                <input
                                    id="checkbox-{{ $agent->id }}"
                                    type="checkbox"
                                    wire:click="toggleAgentSelection({{ $agent->id }})"
                                    {{ in_array((string)$agent->id, $selectedAgents) ? 'checked' : '' }}
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                                >
                                <label for="checkbox-{{ $agent->id }}" class="sr-only">Select agent</label>
                            </div>
                        </div>
                        <div class="flex flex-col items-center pb-10">
                            <img class="w-24 h-24 mb-3 rounded-full shadow-lg" src="https://avatar.iran.liara.run/public/{{$agent->id}}" alt="Agent image"/>
                            <h5 class="mb-1 text-xl font-medium text-gray-900 dark:text-white">{{ $agent->getFullNameAttribute() }}</h5>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $agent->email }}</span>

                            <div class="w-full px-4 mt-3">
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-blue-700 dark:text-white">Training Progress</span>
                                    <span class="text-sm font-medium text-blue-700 dark:text-white">
                                        {{ $agent->training ? $agent->training->progress : 0 }}%
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    <div class="bg-blue-600 h-2.5 rounded-full"
                                         style="width: {{ $agent->training ? $agent->training->progress : 0 }}%">
                                    </div>
                                </div>

                                <div class="flex justify-between mt-3 mb-1">
                                    <span class="text-sm font-medium text-blue-700 dark:text-white">Rating</span>
                                    @php
                                        $rating = $agent->training && isset($agent->training->rating) ? $agent->training->rating : 0;
                                    @endphp
                                    <span class="text-sm font-medium
                                        {{ $rating <= 1 ? 'text-red-600 dark:text-red-400' :
                                           ($rating <= 2 ? 'text-yellow-600 dark:text-yellow-400' :
                                           'text-green-600 dark:text-green-400') }}">
                                        {{ number_format($rating, 1) }}/3
                                    </span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    <div class="{{ $rating <= 1 ? 'bg-red-600' :
                                                  ($rating <= 2 ? 'bg-yellow-500' :
                                                  'bg-green-600') }} h-2.5 rounded-full"
                                         style="width: {{ ($rating / 3) * 100 }}%">
                                    </div>
                                </div>

                                <div class="flex justify-between mt-3 mb-1">
                                    <span class="text-sm font-medium text-blue-700 dark:text-white">Validated</span>
                                    <span class="text-sm font-medium text-green-600 dark:text-green-400">
                                        {{ $agent->training && $agent->training->validated_at ? $agent->training->validated_at->format('M d, Y') : 'N/A' }}
                                    </span>
                                </div>
                            </div>

                            <div class="flex mt-4 space-x-2">
                                <button
                                    type="button"
                                    wire:click="toggleAgentSelection({{ $agent->id }})"
                                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                                >
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        @if(in_array((string)$agent->id, $selectedAgents))
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        @else
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                                        @endif
                                    </svg>
                                    {{ in_array((string)$agent->id, $selectedAgents) ? 'Deselect' : 'Select' }}
                                </button>
                            </div>
                        </div>
                    </x-card>
                @empty
                    <div class="col-span-4 p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                        <span class="font-medium">No validated agents in training found!</span> All validated agents have already been assigned to campaigns.
                    </div>
                @endforelse
            </div>

            <div class="mt-4">
                {{ $agents->links() }}
            </div>
        </div>
    </x-content-body>
</x-content>
