<div>
    <x-content-header title="{{ __('Create New Report') }}">
        <x-page-button type="back" label="{{ __('Back to Reports') }}" action="cancel" />
    </x-content-header>

    <x-content-body>
        <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
            <div class="p-6">
                <form wire:submit.prevent="submitReport">
                    <div class="space-y-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Title') }} <span class="text-red-500">*</span></label>
                            <input type="text" id="title" wire:model="title" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            @error('title') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="campaign_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Campaign') }} <span class="text-red-500">*</span></label>
                                <select id="campaign_id" wire:model="campaign_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <option value="">{{ __('Select Campaign') }}</option>
                                    @foreach($campaigns as $campaign)
                                        <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                                    @endforeach
                                </select>
                                @error('campaign_id') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Type') }} <span class="text-red-500">*</span></label>
                                <select id="type" wire:model="type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <option value="daily">{{ __('Daily') }}</option>
                                    <option value="weekly">{{ __('Weekly') }}</option>
                                    <option value="monthly">{{ __('Monthly') }}</option>
                                    <option value="quarterly">{{ __('Quarterly') }}</option>
                                    <option value="annual">{{ __('Annual') }}</option>
                                    <option value="ad_hoc">{{ __('Ad Hoc') }}</option>
                                </select>
                                @error('type') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Priority') }} <span class="text-red-500">*</span></label>
                                <select id="priority" wire:model="priority" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <option value="low">{{ __('Low') }}</option>
                                    <option value="normal">{{ __('Normal') }}</option>
                                    <option value="high">{{ __('High') }}</option>
                                    <option value="urgent">{{ __('Urgent') }}</option>
                                </select>
                                @error('priority') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                            </div>
                        </div>

                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Category') }} <span class="text-red-500">*</span></label>
                            <select id="category" wire:model="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="performance">{{ __('Performance') }}</option>
                                <option value="quality">{{ __('Quality') }}</option>
                                <option value="compliance">{{ __('Compliance') }}</option>
                                <option value="training">{{ __('Training') }}</option>
                                <option value="operations">{{ __('Operations') }}</option>
                                <option value="financial">{{ __('Financial') }}</option>
                            </select>
                            @error('category') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div>
                            <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Content') }} <span class="text-red-500">*</span></label>
                            <div class="mt-1">
                                <textarea id="content" wire:model="content" rows="8" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                            </div>
                            @error('content') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="performance_score" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Performance Score') }}</label>
                                <input type="number" id="performance_score" wire:model="performance_score" min="0" max="100" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                @error('performance_score') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="quality_score" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Quality Score') }}</label>
                                <input type="number" id="quality_score" wire:model="quality_score" min="0" max="100" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                @error('quality_score') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="compliance_score" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Compliance Score') }}</label>
                                <input type="number" id="compliance_score" wire:model="compliance_score" min="0" max="100" step="0.01" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                @error('compliance_score') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                            </div>
                        </div>

                        <div>
                            <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Tags') }}</label>
                            <div class="mt-1">
                                <div class="flex flex-wrap gap-2 mb-2">
                                    @foreach($tags as $index => $tag)
                                        <div class="flex items-center bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-1 rounded-full text-sm">
                                            {{ $tag }}
                                            <button type="button" wire:click="removeTag({{ $index }})" class="ml-1 text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-200">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="flex">
                                    <input type="text" id="newTag" wire:model="newTag" wire:keydown.enter.prevent="addTag" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="{{ __('Add a tag and press Enter') }}">
                                    <button type="button" wire:click="addTag" class="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="attachments" class="block text-sm font-medium text-gray-700 dark:text-gray-300">{{ __('Attachments') }}</label>
                            <div class="mt-1">
                                <input type="file" id="attachments" wire:model="attachments" multiple class="block w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 cursor-pointer dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400">
                            </div>
                            <div wire:loading wire:target="attachments" class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ __('Uploading...') }}</div>
                            @error('attachments.*') <span class="text-red-600 text-sm">{{ $message }}</span> @enderror
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" wire:click="cancel" class="px-4 py-2 text-sm font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                                {{ __('Cancel') }}
                            </button>
                            <button type="submit" wire:loading.attr="disabled" class="px-4 py-2 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                                <span wire:loading.remove wire:target="submitReport">{{ __('Submit') }}</span>
                                <span wire:loading wire:target="submitReport">{{ __('Submitting...') }}</span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </x-content-body>

    @push('scripts')
    <script>
        document.addEventListener('livewire:initialized', function () {
            // Initialize any JavaScript components here if needed
        });
    </script>
    @endpush
</div>
