<div>
<x-content>
    <!-- Dashboard Metrics -->
    <div class="mb-6">
        <div class="flex flex-col md:flex-row justify-between items-center mb-4">
            <h2 class="text-xl font-semibold dark:text-white">Reports Dashboard</h2>

            <!-- View Mode Switcher -->
            <div class="flex items-center space-x-2 mt-2 md:mt-0">
                <button wire:click="setViewMode('dashboard')" class="px-3 py-1.5 text-sm font-medium rounded-lg {{ $viewMode === 'dashboard' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }}">
                    <i class="fas fa-chart-pie mr-1"></i> Dashboard
                </button>
                <button wire:click="setViewMode('list')" class="px-3 py-1.5 text-sm font-medium rounded-lg {{ $viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }}">
                    <i class="fas fa-list mr-1"></i> List
                </button>
                <button wire:click="setViewMode('grid')" class="px-3 py-1.5 text-sm font-medium rounded-lg {{ $viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }}">
                    <i class="fas fa-th-large mr-1"></i> Grid
                </button>
            </div>
        </div>

        @if($viewMode === 'dashboard')
            <!-- Metrics Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <!-- Total Reports Card -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">Total Reports</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['total_reports'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900">
                            <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Performance Score Card -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">Avg Performance</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_performance_score'] ?? 0 }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $metrics['avg_performance_score'] >= 90 ? 'Excellent' :
                                   ($metrics['avg_performance_score'] >= 80 ? 'Good' :
                                   ($metrics['avg_performance_score'] >= 70 ? 'Satisfactory' :
                                   ($metrics['avg_performance_score'] >= 60 ? 'Needs Improvement' : 'Unsatisfactory'))) }}
                            </p>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full
                            {{ $metrics['avg_performance_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                               ($metrics['avg_performance_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                               ($metrics['avg_performance_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                               ($metrics['avg_performance_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                            <svg class="w-6 h-6
                                {{ $metrics['avg_performance_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                   ($metrics['avg_performance_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                   ($metrics['avg_performance_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                   ($metrics['avg_performance_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"
                                fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Quality Score Card -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">Avg Quality</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_quality_score'] ?? 0 }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $metrics['avg_quality_score'] >= 90 ? 'Excellent' :
                                   ($metrics['avg_quality_score'] >= 80 ? 'Good' :
                                   ($metrics['avg_quality_score'] >= 70 ? 'Satisfactory' :
                                   ($metrics['avg_quality_score'] >= 60 ? 'Needs Improvement' : 'Unsatisfactory'))) }}
                            </p>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full
                            {{ $metrics['avg_quality_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                               ($metrics['avg_quality_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                               ($metrics['avg_quality_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                               ($metrics['avg_quality_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                            <svg class="w-6 h-6
                                {{ $metrics['avg_quality_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                   ($metrics['avg_quality_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                   ($metrics['avg_quality_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                   ($metrics['avg_quality_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"
                                fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Compliance Score Card -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-base font-normal text-gray-500 dark:text-gray-400">Avg Compliance</span>
                            <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_compliance_score'] ?? 0 }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $metrics['avg_compliance_score'] >= 90 ? 'Excellent' :
                                   ($metrics['avg_compliance_score'] >= 80 ? 'Good' :
                                   ($metrics['avg_compliance_score'] >= 70 ? 'Satisfactory' :
                                   ($metrics['avg_compliance_score'] >= 60 ? 'Needs Improvement' : 'Unsatisfactory'))) }}
                            </p>
                        </div>
                        <div class="flex items-center justify-center w-12 h-12 rounded-full
                            {{ $metrics['avg_compliance_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                               ($metrics['avg_compliance_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                               ($metrics['avg_compliance_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                               ($metrics['avg_compliance_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                            <svg class="w-6 h-6
                                {{ $metrics['avg_compliance_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                   ($metrics['avg_compliance_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                   ($metrics['avg_compliance_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                   ($metrics['avg_compliance_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"
                                fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                <!-- Reports Over Time Chart -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Reports Over Time</h3>
                    <div id="reports-chart" class="h-80"></div>
                </div>

                <!-- Scores Chart -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Performance Metrics</h3>
                    <div id="scores-chart" class="h-80"></div>
                </div>
            </div>

            <!-- Breakdown Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Status Breakdown -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Status Breakdown</h3>
                    <div class="space-y-3">
                        @foreach($metrics['status_breakdown'] ?? [] as $status => $count)
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                    <div class="h-2.5 rounded-full
                                        {{ $status === 'approved' ? 'bg-green-600' :
                                           ($status === 'rejected' ? 'bg-red-600' :
                                           ($status === 'submitted' ? 'bg-blue-600' :
                                           ($status === 'draft' ? 'bg-gray-600' : 'bg-yellow-600'))) }}"
                                        style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($status) }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Category Breakdown -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Category Breakdown</h3>
                    <div class="space-y-3">
                        @foreach($metrics['category_breakdown'] ?? [] as $category => $count)
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                    <div class="h-2.5 rounded-full
                                        {{ $category === 'performance' ? 'bg-blue-600' :
                                           ($category === 'quality' ? 'bg-green-600' :
                                           ($category === 'compliance' ? 'bg-purple-600' :
                                           ($category === 'training' ? 'bg-yellow-600' :
                                           ($category === 'operations' ? 'bg-indigo-600' : 'bg-gray-600')))) }}"
                                        style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($category) }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Priority Breakdown -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                    <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Priority Breakdown</h3>
                    <div class="space-y-3">
                        @foreach($metrics['priority_breakdown'] ?? [] as $priority => $count)
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                    <div class="h-2.5 rounded-full
                                        {{ $priority === 'low' ? 'bg-blue-600' :
                                           ($priority === 'normal' ? 'bg-green-600' :
                                           ($priority === 'high' ? 'bg-yellow-600' : 'bg-red-600')) }}"
                                        style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($priority) }}</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Filters Section -->
    <div class="mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <h2 class="text-xl font-semibold dark:text-white">Report Filters</h2>

            <div class="flex items-center space-x-2 mt-2 md:mt-0">
                <button wire:click="resetFilters" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                    <i class="fas fa-redo-alt mr-1"></i> Reset Filters
                </button>

                <div class="relative">
                    <button id="exportDropdownButton" data-dropdown-toggle="exportDropdown" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                        <i class="fas fa-file-export mr-1"></i> Export
                    </button>
                    <div id="exportDropdown" class="hidden z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                        <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                            <li>
                                <a href="#" wire:click.prevent="exportBatch('pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                    <i class="far fa-file-pdf mr-2 text-red-500"></i> Export as PDF
                                </a>
                            </li>
                            <li>
                                <a href="#" wire:click.prevent="exportBatch('csv')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                    <i class="far fa-file-csv mr-2 text-green-500"></i> Export as CSV
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <!-- Campaign Filter -->
            <div>
                <label for="campaign-filter" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign</label>
                <div class="relative">
                    <input type="text" id="campaign-filter" wire:model.debounce.300ms="campaignSearch" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Search campaign...">

                    @if($filteredCampaigns->count() > 0 && $campaignSearch)
                        <div class="absolute z-10 w-full mt-1 bg-white rounded-lg shadow-lg dark:bg-gray-700 max-h-60 overflow-y-auto">
                            @foreach($filteredCampaigns as $campaign)
                                <div wire:click="selectCampaign({{ $campaign->id }})" class="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-gray-600 {{ $selectedCampaignId === $campaign->id ? 'bg-blue-100 dark:bg-blue-900' : '' }}">
                                    {{ $campaign->name }}
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>

            <!-- Date Range Filter -->
            <div>
                <label for="date-range" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Date Range</label>
                <select id="date-range" wire:model="dateRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="today">Today</option>
                    <option value="yesterday">Yesterday</option>
                    <option value="this_week">This Week</option>
                    <option value="last_week">Last Week</option>
                    <option value="this_month">This Month</option>
                    <option value="last_month">Last Month</option>
                    <option value="this_quarter">This Quarter</option>
                    <option value="last_quarter">Last Quarter</option>
                    <option value="this_year">This Year</option>
                    <option value="last_year">Last Year</option>
                    <option value="custom">Custom Range</option>
                </select>

                @if($dateRange === 'custom')
                    <div class="grid grid-cols-2 gap-2 mt-2">
                        <div>
                            <label for="start-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Start Date</label>
                            <input type="date" id="start-date" wire:model="startDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        </div>
                        <div>
                            <label for="end-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">End Date</label>
                            <input type="date" id="end-date" wire:model="endDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        </div>
                    </div>
                @endif
            </div>

            <!-- Report Type Filter -->
            <div>
                <label for="report-type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Report Type</label>
                <select id="report-type" wire:model="reportType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="all">All Types</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="annual">Annual</option>
                    <option value="ad_hoc">Ad Hoc</option>
                </select>
            </div>

            <!-- Category Filter -->
            <div>
                <label for="report-category" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Category</label>
                <select id="report-category" wire:model="reportCategory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="all">All Categories</option>
                    <option value="performance">Performance</option>
                    <option value="quality">Quality</option>
                    <option value="compliance">Compliance</option>
                    <option value="training">Training</option>
                    <option value="operations">Operations</option>
                    <option value="financial">Financial</option>
                </select>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Status Filter -->
            <div>
                <label for="report-status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                <select id="report-status" wire:model="reportStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="all">All Statuses</option>
                    <option value="draft">Draft</option>
                    <option value="submitted">Submitted</option>
                    <option value="in_review">In Review</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                </select>
            </div>

            <!-- Priority Filter -->
            <div>
                <label for="report-priority" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Priority</label>
                <select id="report-priority" wire:model="reportPriority" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="all">All Priorities</option>
                    <option value="low">Low</option>
                    <option value="normal">Normal</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                </select>
            </div>

            <!-- Tags Filter -->
            <div class="lg:col-span-2">
                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Tags</label>
                <div class="flex flex-wrap gap-2">
                    @if(isset($availableTags) && count($availableTags) > 0)
                        @foreach($availableTags as $tag)
                            <button wire:click="toggleTag('{{ $tag }}')" class="px-2 py-1 text-xs font-medium rounded-full {{ in_array($tag, $selectedTags) ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }}">
                                {{ $tag }}
                            </button>
                        @endforeach
                    @else
                        <span class="text-sm text-gray-500 dark:text-gray-400">No tags available</span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- List View -->
    @if($viewMode === 'list')
        <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
            <div class="flex items-center justify-between pb-4">
                <div>
                    <label for="per-page" class="sr-only">Per Page</label>
                    <select id="per-page" wire:model="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="10">10 per page</option>
                        <option value="25">25 per page</option>
                        <option value="50">50 per page</option>
                        <option value="100">100 per page</option>
                    </select>
                </div>
            </div>

            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('id')">
                            ID
                            @if($sortField === 'id')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('title')">
                            Title
                            @if($sortField === 'title')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('date')">
                            Date
                            @if($sortField === 'date')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('type')">
                            Type
                            @if($sortField === 'type')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('category')">
                            Category
                            @if($sortField === 'category')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('status')">
                            Status
                            @if($sortField === 'status')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('priority')">
                            Priority
                            @if($sortField === 'priority')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('overall_score')">
                            Score
                            @if($sortField === 'overall_score')
                                <span class="ml-1">
                                    @if($sortDirection === 'asc')
                                        <i class="fas fa-arrow-up"></i>
                                    @else
                                        <i class="fas fa-arrow-down"></i>
                                    @endif
                                </span>
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($reports as $report)
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                            <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                {{ $report->id }}
                            </td>
                            <td class="px-6 py-4">
                                {{ $report->title ?? "Report #$report->id" }}
                            </td>
                            <td class="px-6 py-4">
                                {{ $report->date->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4">
                                {{ ucfirst($report->type ?? 'Standard') }}
                            </td>
                            <td class="px-6 py-4">
                                {{ ucfirst($report->category ?? 'General') }}
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                      ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                      ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                      ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                    {{ ucfirst($report->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $report->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                      ($report->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                      ($report->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                    {{ ucfirst($report->priority ?? 'Normal') }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                @if($report->overall_score)
                                    <div class="flex items-center">
                                        <div class="h-2.5 w-2.5 rounded-full
                                            {{ $report->overall_score >= 90 ? 'bg-green-500' :
                                              ($report->overall_score >= 80 ? 'bg-blue-500' :
                                              ($report->overall_score >= 70 ? 'bg-yellow-500' :
                                              ($report->overall_score >= 60 ? 'bg-orange-500' : 'bg-red-500'))) }} mr-2"></div>
                                        {{ $report->overall_score }}
                                    </div>
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <button wire:click="$dispatch('openReportModal', { id: {{ $report->id }} })" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                        <i class="fas fa-eye"></i>
                                    </button>

                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open" class="font-medium text-gray-600 dark:text-gray-400 hover:underline">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <div x-show="open" @click.away="open = false" class="absolute right-0 z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                            <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                                                <li>
                                                    <a href="#" wire:click.prevent="editReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        <i class="fas fa-edit mr-2"></i> Edit
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        <i class="far fa-file-pdf mr-2 text-red-500"></i> Export PDF
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'docx')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                        <i class="far fa-file-word mr-2 text-blue-500"></i> Export DOCX
                                                    </a>
                                                </li>
                                                @if(auth()->user()->role_id <= 3 && $report->approval_status === 'pending')
                                                    <li>
                                                        <a href="#" wire:click.prevent="approveReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <i class="fas fa-check mr-2 text-green-500"></i> Approve
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" wire:click.prevent="rejectReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <i class="fas fa-times mr-2 text-red-500"></i> Reject
                                                        </a>
                                                    </li>
                                                @endif
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td colspan="9" class="px-6 py-4 text-center">No reports found</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>

            <div class="p-4">
                {{ $reports->links() }}
            </div>
        </div>
    @endif

    <!-- Grid View -->
    @if($viewMode === 'grid')
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            @forelse($reports as $report)
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700 overflow-hidden">
                    <div class="p-4">
                        <div class="flex justify-between items-start mb-3">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                {{ $report->title ?? "Report #$report->id" }}
                            </h3>
                            <div class="flex items-center space-x-1">
                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                    {{ $report->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                      ($report->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                      ($report->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                    {{ ucfirst($report->priority ?? 'Normal') }}
                                </span>
                            </div>
                        </div>

                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $report->date->format('M d, Y') }}
                            <span class="mx-2">•</span>
                            {{ ucfirst($report->type ?? 'Standard') }}
                            <span class="mx-2">•</span>
                            {{ ucfirst($report->category ?? 'General') }}
                        </div>

                        <div class="flex items-center mb-3">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                  ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                  ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                  ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                  'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                {{ ucfirst($report->status) }}
                            </span>

                            @if($report->approval_status && $report->approval_status !== 'pending')
                                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full
                                    {{ $report->approval_status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                    {{ ucfirst($report->approval_status) }}
                                </span>
                            @endif
                        </div>

                        @if(!empty($report->tags))
                            <div class="flex flex-wrap gap-1 mb-3">
                                @foreach($report->tags as $tag)
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        {{ $tag }}
                                    </span>
                                @endforeach
                            </div>
                        @endif

                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                            {{ Str::limit(strip_tags($report->content), 150) }}
                        </p>

                        @if($report->performance_score !== null || $report->quality_score !== null || $report->compliance_score !== null)
                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Performance</span>
                                    <span class="text-xs font-medium text-gray-900 dark:text-white">{{ $report->performance_score ?? 0 }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                                    <div class="h-1.5 rounded-full bg-blue-600" style="width: {{ $report->performance_score ?? 0 }}%"></div>
                                </div>

                                <div class="flex justify-between items-center mb-1 mt-2">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Quality</span>
                                    <span class="text-xs font-medium text-gray-900 dark:text-white">{{ $report->quality_score ?? 0 }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                                    <div class="h-1.5 rounded-full bg-green-600" style="width: {{ $report->quality_score ?? 0 }}%"></div>
                                </div>

                                <div class="flex justify-between items-center mb-1 mt-2">
                                    <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Compliance</span>
                                    <span class="text-xs font-medium text-gray-900 dark:text-white">{{ $report->compliance_score ?? 0 }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                                    <div class="h-1.5 rounded-full bg-purple-600" style="width: {{ $report->compliance_score ?? 0 }}%"></div>
                                </div>

                                @if($report->overall_score)
                                    <div class="flex justify-between items-center mb-1 mt-2">
                                        <span class="text-xs font-medium text-gray-700 dark:text-gray-300">Overall</span>
                                        <span class="text-xs font-medium text-gray-900 dark:text-white">{{ $report->overall_score }}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-700">
                                        <div class="h-1.5 rounded-full
                                            {{ $report->overall_score >= 90 ? 'bg-green-600' :
                                              ($report->overall_score >= 80 ? 'bg-blue-600' :
                                              ($report->overall_score >= 70 ? 'bg-yellow-600' :
                                              ($report->overall_score >= 60 ? 'bg-orange-600' : 'bg-red-600'))) }}"
                                            style="width: {{ $report->overall_score }}%"></div>
                                    </div>
                                @endif
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                {{ $report->creator->first_name }} {{ $report->creator->last_name }}
                            </div>

                            <div class="flex items-center space-x-2">
                                @if($report->comments_count || $report->comments->count())
                                    <span class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                                        </svg>
                                        {{ $report->comments_count ?? $report->comments->count() }}
                                    </span>
                                @endif

                                @if($report->media_count || $report->media->count())
                                    <span class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a3 3 0 006 0V7a1 1 0 112 0v4a5 5 0 01-10 0V7a5 5 0 0110 0v1h2V7a7 7 0 00-14 0v4a7 7 0 0014 0V7a5 5 0 00-10 0v4a3 3 0 006 0V7a1 1 0 00-2 0v4a1 1 0 01-2 0V7a3 3 0 00-3-3z" clip-rule="evenodd"></path>
                                        </svg>
                                        {{ $report->media_count ?? $report->media->count() }}
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 flex justify-between">
                        <button wire:click="$dispatch('openReportModal', { id: {{ $report->id }} })" class="text-sm font-medium text-blue-600 dark:text-blue-500 hover:underline">
                            View Details
                        </button>

                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="text-sm font-medium text-gray-600 dark:text-gray-400 hover:underline">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div x-show="open" @click.away="open = false" class="absolute right-0 bottom-6 z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                                    <li>
                                        <a href="#" wire:click.prevent="editReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                            <i class="fas fa-edit mr-2"></i> Edit
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                            <i class="far fa-file-pdf mr-2 text-red-500"></i> Export PDF
                                        </a>
                                    </li>
                                    <li>
                                        <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'docx')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                            <i class="far fa-file-word mr-2 text-blue-500"></i> Export DOCX
                                        </a>
                                    </li>
                                    @if(auth()->user()->role_id <= 3 && $report->approval_status === 'pending')
                                        <li>
                                            <a href="#" wire:click.prevent="approveReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <i class="fas fa-check mr-2 text-green-500"></i> Approve
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" wire:click.prevent="rejectReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                <i class="fas fa-times mr-2 text-red-500"></i> Reject
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="col-span-full p-4 text-center text-gray-500 dark:text-gray-400">
                    No reports found
                </div>
            @endforelse

            <div class="col-span-full p-4">
                {{ $reports->links() }}
            </div>
        </div>
    @endif

    <!-- JavaScript for Charts -->
    @push('scripts')
    <script>
        document.addEventListener('livewire:initialized', function() {
            Livewire.on('chartsUpdated', function() {
                initCharts();
            });

            initCharts();

            function initCharts() {
                if (document.getElementById('reports-chart')) {
                    initReportsChart();
                }

                if (document.getElementById('scores-chart')) {
                    initScoresChart();
                }
            }

            function initReportsChart() {
                const chartData = @this.chartData;

                if (!chartData || !chartData.labels || !chartData.series) {
                    return;
                }

                const options = {
                    chart: {
                        type: 'area',
                        height: 320,
                        fontFamily: 'Inter, sans-serif',
                        toolbar: {
                            show: false
                        },
                        animations: {
                            enabled: true,
                            easing: 'easeinout',
                            speed: 800,
                            animateGradually: {
                                enabled: true,
                                delay: 150
                            },
                            dynamicAnimation: {
                                enabled: true,
                                speed: 350
                            }
                        }
                    },
                    series: chartData.series,
                    xaxis: {
                        categories: chartData.labels,
                        labels: {
                            style: {
                                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                                fontSize: '12px',
                                fontWeight: 400
                            }
                        },
                        axisBorder: {
                            show: false
                        },
                        axisTicks: {
                            show: false
                        }
                    },
                    yaxis: {
                        labels: {
                            style: {
                                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                                fontSize: '12px',
                                fontWeight: 400
                            }
                        }
                    },
                    colors: ['#3b82f6'],
                    stroke: {
                        curve: 'smooth',
                        width: 3
                    },
                    dataLabels: {
                        enabled: false
                    },
                    tooltip: {
                        style: {
                            fontSize: '12px',
                            fontFamily: 'Inter, sans-serif'
                        },
                        y: {
                            formatter: function(value) {
                                return value + ' reports';
                            }
                        }
                    },
                    grid: {
                        show: true,
                        borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                        strokeDashArray: 4,
                        padding: {
                            left: 0,
                            right: 0
                        }
                    },
                    markers: {
                        size: 5,
                        strokeColors: '#3b82f6',
                        strokeWidth: 2,
                        strokeOpacity: 0.9,
                        fillOpacity: 1,
                        hover: {
                            size: 7
                        }
                    },
                    fill: {
                        type: 'gradient',
                        gradient: {
                            shadeIntensity: 1,
                            opacityFrom: 0.4,
                            opacityTo: 0.1,
                            stops: [0, 90, 100]
                        }
                    }
                };

                const chart = new ApexCharts(document.getElementById('reports-chart'), options);
                chart.render();

                // Clean up previous chart instance if it exists
                return function cleanup() {
                    chart.destroy();
                };
            }

            function initScoresChart() {
                const chartData = @this.scoreChartData;

                if (!chartData || !chartData.labels || !chartData.series) {
                    return;
                }

                const options = {
                    chart: {
                        type: 'radar',
                        height: 320,
                        fontFamily: 'Inter, sans-serif',
                        toolbar: {
                            show: false
                        },
                        dropShadow: {
                            enabled: true,
                            blur: 1,
                            left: 1,
                            top: 1
                        }
                    },
                    series: [{
                        name: 'Score',
                        data: chartData.series
                    }],
                    labels: chartData.labels,
                    colors: ['#3b82f6'],
                    stroke: {
                        width: 2
                    },
                    fill: {
                        opacity: 0.1
                    },
                    markers: {
                        size: 5,
                        hover: {
                            size: 7
                        }
                    },
                    tooltip: {
                        style: {
                            fontSize: '12px',
                            fontFamily: 'Inter, sans-serif'
                        },
                        y: {
                            formatter: function(value) {
                                return value.toFixed(2) + ' / 100';
                            }
                        }
                    },
                    xaxis: {
                        labels: {
                            style: {
                                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                                fontSize: '12px',
                                fontWeight: 500
                            }
                        }
                    },
                    yaxis: {
                        tickAmount: 5,
                        labels: {
                            style: {
                                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                                fontSize: '12px',
                                fontWeight: 400
                            },
                            formatter: function(value) {
                                return value.toFixed(0);
                            }
                        }
                    },
                    grid: {
                        borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb'
                    }
                };

                const chart = new ApexCharts(document.getElementById('scores-chart'), options);
                chart.render();

                // Clean up previous chart instance if it exists
                return function cleanup() {
                    chart.destroy();
                };
            }

            // Handle dark mode toggle
            const darkModeToggle = document.querySelector('[data-toggle-dark="true"]');
            if (darkModeToggle) {
                darkModeToggle.addEventListener('click', function() {
                    setTimeout(function() {
                        initCharts();
                    }, 100);
                });
            }
        });
    </script>
    @endpush

    <!-- Report Detail Modal -->
    <div x-data="{ open: false }" @open-modal="open = true" @close-modal="open = false">
        @if($selectedReport)
            <livewire:campaigns.report-detail-modal :report-id="$selectedReport->id" :key="$selectedReport->id" />
        @endif
    </div>

    <!-- Toast Messages -->
    <div id="toast-container" class="fixed bottom-4 right-4 z-50"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toast message system
            document.addEventListener('show-message', event => {
                showToast(event.detail.message, 'success');
            });

            document.addEventListener('show-error', event => {
                showToast(event.detail.message, 'error');
            });

            function showToast(message, type = 'success') {
                const container = document.getElementById('toast-container');
                const toast = document.createElement('div');

                toast.className = `flex items-center p-4 mb-4 w-full max-w-xs text-gray-500 rounded-lg shadow ${type === 'success' ? 'bg-green-100' : 'bg-red-100'} dark:${type === 'success' ? 'bg-green-800' : 'bg-red-800'} dark:text-gray-200`;

                const icon = `<div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${type === 'success' ? 'text-green-500 bg-green-100 dark:bg-green-900 dark:text-green-300' : 'text-red-500 bg-red-100 dark:bg-red-900 dark:text-red-300'} rounded-lg">
                    ${type === 'success'
                        ? '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>'
                        : '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>'
                    }
                </div>`;

                toast.innerHTML = `
                    ${icon}
                    <div class="ml-3 text-sm font-normal">${message}</div>
                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 ${type === 'success' ? 'bg-green-100 text-green-500 dark:bg-green-800 dark:text-green-300' : 'bg-red-100 text-red-500 dark:bg-red-800 dark:text-red-300'} hover:${type === 'success' ? 'text-green-900' : 'text-red-900'} rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 dark:hover:bg-gray-700" onclick="this.parentElement.remove()">
                        <span class="sr-only">Close</span>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                    </button>
                `;

                container.appendChild(toast);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 5000);
            }
        });
    </script>
</x-content>
</div>