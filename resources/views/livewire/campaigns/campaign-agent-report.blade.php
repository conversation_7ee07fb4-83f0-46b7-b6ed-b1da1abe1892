<x-content>
    <div class="space-y-8">
        <x-content-header title="Agent Performance Report">
            <x-page-button type="back" label="Back" :href="route('campaigns.agents')" wire:navigate/>
        </x-content-header>
        <x-content-body>
            @if($campaign)
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <x-card key="performance-card" class="p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Metrics</h3>

                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Performance</h4>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $campaign->performance <= 60 ? 'bg-red-600' :
                                          ($campaign->performance <= 80 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ $campaign->performance }}%">
                            </div>
                        </div>
                        <p class="mt-1 text-sm {{ $campaign->performance <= 60 ? 'text-red-600 dark:text-red-400' :
                                                  ($campaign->performance <= 80 ? 'text-yellow-600 dark:text-yellow-400' :
                                                  'text-green-600 dark:text-green-400') }}">
                            {{ $campaign->performance }}% Performance
                        </p>
                    </div>

                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Productivity</h4>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $campaign->productivity <= 60 ? 'bg-red-600' :
                                          ($campaign->productivity <= 80 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ $campaign->productivity }}%">
                            </div>
                        </div>
                        <p class="mt-1 text-sm {{ $campaign->productivity <= 60 ? 'text-red-600 dark:text-red-400' :
                                                  ($campaign->productivity <= 80 ? 'text-yellow-600 dark:text-yellow-400' :
                                                  'text-green-600 dark:text-green-400') }}">
                            {{ $campaign->productivity }}% Productivity
                        </p>
                    </div>

                    @if(isset($campaign->rating))
                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Rating</h4>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $campaign->rating <= 1 ? 'bg-red-600' :
                                          ($campaign->rating <= 2 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ ($campaign->rating / 3) * 100 }}%">
                            </div>
                        </div>
                        <p class="mt-1 text-sm
                            {{ $campaign->rating <= 1 ? 'text-red-600 dark:text-red-400' :
                               ($campaign->rating <= 2 ? 'text-yellow-600 dark:text-yellow-400' :
                               'text-green-600 dark:text-green-400') }}">
                            {{ number_format($campaign->rating, 1) }}/3 Rating
                        </p>
                    </div>
                    @endif
                </x-card>

                <x-card key="agent-info" class="p-4">
                    <div class="flex flex-col items-center">
                        <img class="w-24 h-24 mb-3 rounded-full shadow-lg" src="https://avatar.iran.liara.run/public/{{$agent->id}}" alt="Agent image"/>
                        <h5 class="mb-1 text-xl font-medium text-gray-900 dark:text-white">{{ $agent->getFullNameAttribute() }}</h5>
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ $agent->email }}</span>

                        <div class="mt-4 flex flex-col w-full">
                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                                <span class="text-gray-500 dark:text-gray-400">Status</span>
                                <span class="font-medium {{ $agent->status === 'active' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                    {{ ucfirst($agent->status) }}
                                </span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                                <span class="text-gray-500 dark:text-gray-400">Start Date</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $campaign->start_date ? $campaign->start_date->format('M d, Y') : 'N/A' }}</span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                                <span class="text-gray-500 dark:text-gray-400">Campaign</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $campaign->name ?? 'N/A' }}</span>
                            </div>
                            <div class="flex justify-between py-2">
                                <span class="text-gray-500 dark:text-gray-400">Last Observation</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $campaign->updated_at ? $campaign->updated_at->format('M d, Y') : 'N/A' }}</span>
                            </div>
                        </div>
                    </div>
                </x-card>

                <x-card key="action-buttons" class="p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
                    <div class="flex flex-col gap-3">
                        <a
                            href="{{ route('campaigns.agent.observation', $agent->id)}}"
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
                            New Observation
                        </a>
                        <button
                            onclick="window.print()"
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700"
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"></path></svg>
                            Print Report
                        </button>
                        <a
                            href="{{ route('campaigns.agents')}}"
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            Back to Agents
                        </a>
                    </div>
                </x-card>
            </div>
            @endif

            <div class="flex flex-col gap-4">
                <x-card key="agent-observation" class="flex flex-col gap-4 p-6 w-full">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Observation History</h3>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">Last 30 days</span>
                            <button id="dropdownDefaultButton" data-dropdown-toggle="dropdown" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 font-medium rounded-lg text-sm px-3 py-1.5 text-center inline-flex items-center dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-700" type="button">
                                <svg class="w-4 h-4 ml-2" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                            </button>
                            <!-- Dropdown menu -->
                            <div id="dropdown" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Last 7 days</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Last 30 days</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Last 90 days</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">All time</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    @if($campaign && !empty($campaign->notes))
                    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3">
                                        Date
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        Subject
                                    </th>
                                    <th scope="col" class="px-6 py-3">
                                        Observation
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {{ $campaign->updated_at ? $campaign->updated_at->format('M d, Y') : 'N/A' }}
                                    </th>
                                    <td class="px-6 py-4">
                                        {{ Str::before($campaign->notes, ':') }}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ Str::after($campaign->notes, ': ') }}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="flex flex-col items-center justify-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500 dark:text-gray-400 mb-2">No observations found</p>
                        <a
                            href="{{ route('campaigns.agent.observation', $agent->id)}}"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
                            Create First Observation
                        </a>
                    </div>
                    @endif
                </x-card>

                <x-card key="performance-trends" class="flex flex-col gap-4 p-6 w-full">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Trends</h3>
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="w-full md:w-1/2">
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Performance History</h4>
                            <div class="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                                <span class="text-gray-500 dark:text-gray-400">Performance chart will be displayed here</span>
                            </div>
                        </div>
                        <div class="w-full md:w-1/2">
                            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Productivity History</h4>
                            <div class="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                                <span class="text-gray-500 dark:text-gray-400">Productivity chart will be displayed here</span>
                            </div>
                        </div>
                    </div>
                </x-card>

                <x-card key="daily-summary" class="flex flex-col gap-4 p-6 w-full">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Daily Summary</h3>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">Last 7 days</span>
                            <button id="summaryDropdown" data-dropdown-toggle="summaryDropdownMenu" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 font-medium rounded-lg text-sm px-3 py-1.5 text-center inline-flex items-center dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-700" type="button">
                                <svg class="w-4 h-4 ml-2" aria-hidden="true" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                            </button>
                            <div id="summaryDropdownMenu" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="summaryDropdown">
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Last 7 days</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Last 30 days</a>
                                    </li>
                                    <li>
                                        <a href="#" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">All time</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @foreach (array(1, 2, 3) as $number)
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-4 dark:bg-gray-800 dark:border-gray-700">
                            <div class="flex justify-between items-center mb-2">
                                <h5 class="text-lg font-semibold text-gray-900 dark:text-white">{{ now()->subDays($number)->format('M d, Y') }}</h5>
                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Day {{ $number }}</span>
                            </div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">Production Time:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">6 hours</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">Calls Made:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">245</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">Appointments:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">14</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">Conversion Rate:</span>
                                    <span class="text-sm font-medium text-green-600 dark:text-green-400">5.7%</span>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </x-card>
            </div>
        </x-content-body>
    </div>
</x-content>
