<div>
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Documents</h3>
            <button wire:click="create" class="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-plus mr-1"></i> Upload Document
            </button>
        </div>
        
        <!-- Filters -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <label for="search" class="sr-only">Search</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input wire:model.live.debounce.300ms="search" type="text" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Search documents...">
                    </div>
                </div>
                
                <div class="w-full md:w-48">
                    <label for="filterType" class="sr-only">Document Type</label>
                    <select wire:model.live="filterType" id="filterType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="">All Types</option>
                        @foreach($documentTypes as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div class="w-full md:w-48">
                    <label for="filterConfidential" class="sr-only">Confidential</label>
                    <select wire:model.live="filterConfidential" id="filterConfidential" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="">All Documents</option>
                        <option value="yes">Confidential Only</option>
                        <option value="no">Non-Confidential Only</option>
                    </select>
                </div>
                
                <div class="w-full md:w-32">
                    <label for="perPage" class="sr-only">Per Page</label>
                    <select wire:model.live="perPage" id="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- Documents Table -->
        <div class="overflow-x-auto">
            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">Title</th>
                        <th scope="col" class="px-6 py-3">Type</th>
                        <th scope="col" class="px-6 py-3">Date</th>
                        <th scope="col" class="px-6 py-3">Size</th>
                        <th scope="col" class="px-6 py-3">Confidential</th>
                        <th scope="col" class="px-6 py-3">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($documents as $document)
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                {{ $document->title }}
                                @if($document->is_expired)
                                    <span class="ml-1 px-2 py-0.5 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                        Expired
                                    </span>
                                @endif
                            </th>
                            <td class="px-6 py-4">
                                {{ $documentTypes[$document->document_type] ?? $document->document_type }}
                            </td>
                            <td class="px-6 py-4">
                                {{ $document->document_date ? $document->document_date->format('M d, Y') : 'N/A' }}
                                @if($document->expiry_date)
                                    <span class="block text-xs text-gray-500 dark:text-gray-400">
                                        Expires: {{ $document->expiry_date->format('M d, Y') }}
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                {{ $document->formatted_file_size }}
                            </td>
                            <td class="px-6 py-4">
                                @if($document->is_confidential)
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                                        Yes
                                    </span>
                                @else
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        No
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex space-x-2">
                                    <button wire:click="download({{ $document->id }})" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button wire:click="edit({{ $document->id }})" class="font-medium text-yellow-600 dark:text-yellow-500 hover:underline">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button 
                                        x-data
                                        @click="if (confirm('Are you sure you want to delete this document?')) { $wire.delete({{ $document->id }}) }"
                                        class="font-medium text-red-600 dark:text-red-500 hover:underline"
                                    >
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td colspan="6" class="px-6 py-4 text-center">
                                No documents found.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="px-6 py-3">
            {{ $documents->links() }}
        </div>
    </div>
    
    <!-- Document Form Modal -->
    <div x-data="{ open: @entangle('showForm') }" x-show="open" x-transition.opacity class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="open" x-transition.opacity class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"></div>
            
            <div x-show="open" x-transition class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 rounded-lg shadow-xl">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                        {{ $isEditing ? 'Edit Document' : 'Upload New Document' }}
                    </h3>
                    <button @click="open = false" wire:click="cancelForm" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form wire:submit.prevent="save" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Title -->
                        <div>
                            <label for="title" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Title <span class="text-red-500">*</span></label>
                            <input wire:model="title" type="text" id="title" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter document title" required>
                            @error('title') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>
                        
                        <!-- Document Type -->
                        <div>
                            <label for="documentType" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Document Type <span class="text-red-500">*</span></label>
                            <select wire:model="documentType" id="documentType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                                <option value="">Select Document Type</option>
                                @foreach($documentTypes as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>
                            @error('documentType') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>
                        
                        <!-- Document Date -->
                        <div>
                            <label for="documentDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Document Date</label>
                            <input wire:model="documentDate" type="date" id="documentDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        </div>
                        
                        <!-- Expiry Date -->
                        <div>
                            <label for="expiryDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Expiry Date</label>
                            <input wire:model="expiryDate" type="date" id="expiryDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        </div>
                        
                        <!-- Confidential -->
                        <div class="flex items-center">
                            <input wire:model="isConfidential" id="isConfidential" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="isConfidential" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Confidential Document</label>
                        </div>
                    </div>
                    
                    <!-- Description -->
                    <div>
                        <label for="description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                        <textarea wire:model="description" id="description" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="Enter document description"></textarea>
                    </div>
                    
                    <!-- File Upload -->
                    @if(!$isEditing || !$existingFilePath)
                        <div>
                            <label for="file" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">File <span class="text-red-500">*</span></label>
                            <input wire:model="file" type="file" id="file" class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400" required>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Max file size: 10MB</p>
                            @error('file') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            
                            <div wire:loading wire:target="file" class="mt-2">
                                <div class="flex items-center">
                                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                    <span class="text-sm text-gray-500 dark:text-gray-400">Uploading...</span>
                                </div>
                            </div>
                        </div>
                    @else
                        <div>
                            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Current File</label>
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-file-alt text-gray-500 dark:text-gray-400 mr-2"></i>
                                    <span class="text-sm text-gray-900 dark:text-white">{{ basename($existingFilePath) }}</span>
                                </div>
                                <div class="flex items-center">
                                    <button type="button" wire:click="$set('existingFilePath', null)" class="text-sm text-red-600 dark:text-red-500 hover:underline">
                                        Replace
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endif
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" wire:click="cancelForm" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            {{ $isEditing ? 'Update Document' : 'Upload Document' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
