<x-content>
    <x-slot name="page-header">
        <x-page-header>
            <x-slot name="title">
                {{ __('Campaign Reports') }}
            </x-slot>
            <x-slot name="description">
                {{ __('Manage and view campaign reports') }}
            </x-slot>
            <x-slot name="icon">
                <path d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </x-slot>
            <x-slot name="actions">
                <div class="flex items-center gap-2">
                    <div class="relative">
                        <button id="exportDropdownButton" data-dropdown-toggle="exportDropdown" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                            <i class="fas fa-file-export mr-1"></i> {{ __('Export') }}
                        </button>
                        <div id="exportDropdown" class="hidden z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                            <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                                <li>
                                    <a href="#" wire:click.prevent="exportBatch('pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                        <i class="far fa-file-pdf mr-2 text-red-500"></i> {{ __('Export as PDF') }}
                                    </a>
                                </li>
                                <li>
                                    <a href="#" wire:click.prevent="exportBatch('csv')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                        <i class="far fa-file-csv mr-2 text-green-500"></i> {{ __('Export as CSV') }}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </x-slot>
        </x-page-header>
    </x-slot>

    <x-slot name="pagesidebar">
        <x-page-sidebar>
            <x-slot name="title">
                {{ __('Report Filters') }}
            </x-slot>
            <x-slot name="content">
                <div class="space-y-4">
                    <!-- Campaign Filter -->
                    <div>
                        <label for="campaign-filter" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Campaign') }}</label>
                        <div class="relative">
                            <input type="text" id="campaign-filter" wire:model.debounce.300ms="campaignSearch" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="{{ __('Search campaign...') }}">

                            @if($filteredCampaigns->count() > 0 && $campaignSearch)
                                <div class="absolute z-10 w-full mt-1 bg-white rounded-lg shadow-lg dark:bg-gray-700 max-h-60 overflow-y-auto">
                                    @foreach($filteredCampaigns as $campaign)
                                        <div wire:click="selectCampaign({{ $campaign->id }})" class="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-gray-600 {{ $selectedCampaignId === $campaign->id ? 'bg-blue-100 dark:bg-blue-900' : '' }}">
                                            {{ $campaign->name }}
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Date Range Filter -->
                    <div>
                        <label for="date-range" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Date Range') }}</label>
                        <select id="date-range" wire:model.live="dateRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="today">{{ __('Today') }}</option>
                            <option value="yesterday">{{ __('Yesterday') }}</option>
                            <option value="this_week">{{ __('This Week') }}</option>
                            <option value="last_week">{{ __('Last Week') }}</option>
                            <option value="this_month">{{ __('This Month') }}</option>
                            <option value="last_month">{{ __('Last Month') }}</option>
                            <option value="this_quarter">{{ __('This Quarter') }}</option>
                            <option value="last_quarter">{{ __('Last Quarter') }}</option>
                            <option value="this_year">{{ __('This Year') }}</option>
                            <option value="last_year">{{ __('Last Year') }}</option>
                            <option value="custom">{{ __('Custom Range') }}</option>
                        </select>

                        @if($dateRange === 'custom')
                            <div class="grid grid-cols-2 gap-2 mt-2">
                                <div>
                                    <label for="start-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">{{ __('Start Date') }}</label>
                                    <input type="date" id="start-date" wire:model.live="startDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                </div>
                                <div>
                                    <label for="end-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">{{ __('End Date') }}</label>
                                    <input type="date" id="end-date" wire:model.live="endDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Report Type Filter -->
                    <div>
                        <label for="report-type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Report Type') }}</label>
                        <select id="report-type" wire:model.live="reportType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Types') }}</option>
                            <option value="daily">{{ __('Daily') }}</option>
                            <option value="weekly">{{ __('Weekly') }}</option>
                            <option value="monthly">{{ __('Monthly') }}</option>
                            <option value="quarterly">{{ __('Quarterly') }}</option>
                            <option value="annual">{{ __('Annual') }}</option>
                            <option value="ad_hoc">{{ __('Ad Hoc') }}</option>
                        </select>
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label for="report-category" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Category') }}</label>
                        <select id="report-category" wire:model.live="reportCategory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Categories') }}</option>
                            <option value="performance">{{ __('Performance') }}</option>
                            <option value="quality">{{ __('Quality') }}</option>
                            <option value="compliance">{{ __('Compliance') }}</option>
                            <option value="training">{{ __('Training') }}</option>
                            <option value="operations">{{ __('Operations') }}</option>
                            <option value="financial">{{ __('Financial') }}</option>
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="report-status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Status') }}</label>
                        <select id="report-status" wire:model.live="reportStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Statuses') }}</option>
                            <option value="draft">{{ __('Draft') }}</option>
                            <option value="submitted">{{ __('Submitted') }}</option>
                            <option value="in_review">{{ __('In Review') }}</option>
                            <option value="approved">{{ __('Approved') }}</option>
                            <option value="rejected">{{ __('Rejected') }}</option>
                        </select>
                    </div>

                    <!-- Priority Filter -->
                    <div>
                        <label for="report-priority" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{ __('Priority') }}</label>
                        <select id="report-priority" wire:model.live="reportPriority" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                            <option value="all">{{ __('All Priorities') }}</option>
                            <option value="low">{{ __('Low') }}</option>
                            <option value="normal">{{ __('Normal') }}</option>
                            <option value="high">{{ __('High') }}</option>
                            <option value="urgent">{{ __('Urgent') }}</option>
                        </select>
                    </div>

                    <div class="pt-4">
                        <button wire:click="resetFilters" class="w-full px-3 py-2 text-sm font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                            <i class="fas fa-redo-alt mr-1"></i> {{ __('Reset Filters') }}
                        </button>
                    </div>
                </div>
            </x-slot>
        </x-page-sidebar>
    </x-slot>

    <!-- Filter Controls Card -->
    <x-card key="campaign-reports-header" class="p-4 mb-4">
        <div class="w-full">
            <!-- Search and Actions Row -->
            <div class="flex flex-col md:flex-row justify-between gap-4 mb-4">
                <!-- Campaign Search Box -->
                <div class="w-full md:w-1/3">
                    <label for="campaign-filter" class="sr-only">{{ __('Search Campaign') }}</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                            </svg>
                        </div>
                        <input
                            type="text"
                            id="campaign-filter"
                            wire:model.debounce.300ms="campaignSearch"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            placeholder="{{ __('Search campaign...') }}">

                        @if($filteredCampaigns->count() > 0 && $campaignSearch)
                            <div class="absolute z-10 w-full mt-1 bg-white rounded-lg shadow-lg dark:bg-gray-700 max-h-60 overflow-y-auto">
                                @foreach($filteredCampaigns as $campaign)
                                    <div wire:click="selectCampaign({{ $campaign->id }})" class="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-gray-600 {{ $selectedCampaignId === $campaign->id ? 'bg-blue-100 dark:bg-blue-900' : '' }}">
                                        {{ $campaign->name }}
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-2">
                    <div class="relative">
                        <button id="exportDropdownButton" data-dropdown-toggle="exportDropdown" class="px-3 py-1.5 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                            <i class="fas fa-file-export mr-1"></i> {{ __('Export') }}
                        </button>
                        <div id="exportDropdown" class="hidden z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                            <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                                <li>
                                    <a href="#" wire:click.prevent="exportBatch('pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                        <i class="far fa-file-pdf mr-2 text-red-500"></i> {{ __('Export as PDF') }}
                                    </a>
                                </li>
                                <li>
                                    <a href="#" wire:click.prevent="exportBatch('csv')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                        <i class="far fa-file-csv mr-2 text-green-500"></i> {{ __('Export as CSV') }}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div>
                        <button wire:click="setViewMode('dashboard')" class="px-3 py-1.5 text-sm font-medium rounded-lg {{ $viewMode === 'dashboard' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }}">
                            <i class="fas fa-chart-pie mr-1"></i> {{ __('Dashboard') }}
                        </button>
                        <button wire:click="setViewMode('list')" class="px-3 py-1.5 text-sm font-medium rounded-lg {{ $viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }}">
                            <i class="fas fa-list mr-1"></i> {{ __('List') }}
                        </button>
                        <button wire:click="setViewMode('grid')" class="px-3 py-1.5 text-sm font-medium rounded-lg {{ $viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }}">
                            <i class="fas fa-th-large mr-1"></i> {{ __('Grid') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filters Row -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mt-4">
                <!-- Date Range Filter -->
                <div class="flex flex-wrap gap-2 items-center">
                    <select id="date-range" wire:model.live="dateRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="today">{{ __('Today') }}</option>
                        <option value="yesterday">{{ __('Yesterday') }}</option>
                        <option value="this_week">{{ __('This Week') }}</option>
                        <option value="last_week">{{ __('Last Week') }}</option>
                        <option value="this_month">{{ __('This Month') }}</option>
                        <option value="last_month">{{ __('Last Month') }}</option>
                        <option value="this_quarter">{{ __('This Quarter') }}</option>
                        <option value="last_quarter">{{ __('Last Quarter') }}</option>
                        <option value="this_year">{{ __('This Year') }}</option>
                        <option value="last_year">{{ __('Last Year') }}</option>
                        <option value="custom">{{ __('Custom Range') }}</option>
                    </select>

                    <select id="report-type" wire:model.live="reportType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="all">{{ __('All Types') }}</option>
                        <option value="daily">{{ __('Daily') }}</option>
                        <option value="weekly">{{ __('Weekly') }}</option>
                        <option value="monthly">{{ __('Monthly') }}</option>
                        <option value="quarterly">{{ __('Quarterly') }}</option>
                        <option value="annual">{{ __('Annual') }}</option>
                        <option value="ad_hoc">{{ __('Ad Hoc') }}</option>
                    </select>

                    <select id="report-category" wire:model.live="reportCategory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="all">{{ __('All Categories') }}</option>
                        <option value="performance">{{ __('Performance') }}</option>
                        <option value="quality">{{ __('Quality') }}</option>
                        <option value="compliance">{{ __('Compliance') }}</option>
                        <option value="training">{{ __('Training') }}</option>
                        <option value="operations">{{ __('Operations') }}</option>
                        <option value="financial">{{ __('Financial') }}</option>
                    </select>

                    <select id="report-status" wire:model.live="reportStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="all">{{ __('All Statuses') }}</option>
                        <option value="draft">{{ __('Draft') }}</option>
                        <option value="submitted">{{ __('Submitted') }}</option>
                        <option value="in_review">{{ __('In Review') }}</option>
                        <option value="approved">{{ __('Approved') }}</option>
                        <option value="rejected">{{ __('Rejected') }}</option>
                    </select>

                    <select id="report-priority" wire:model.live="reportPriority" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="all">{{ __('All Priorities') }}</option>
                        <option value="low">{{ __('Low') }}</option>
                        <option value="normal">{{ __('Normal') }}</option>
                        <option value="high">{{ __('High') }}</option>
                        <option value="urgent">{{ __('Urgent') }}</option>
                    </select>

                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        @foreach ([10, 25, 50, 100] as $value)
                            <option value="{{ $value }}">{{ $value }} {{ __('per page') }}</option>
                        @endforeach
                    </select>

                    @if($reportType !== 'all' || $reportCategory !== 'all' || $reportStatus !== 'all' || $reportPriority !== 'all' || $dateRange !== 'this_month')
                    <button
                        wire:click="resetFilters"
                        type="button"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900"
                    >
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        {{ __('Clear Filters') }}
                    </button>
                    @endif
                </div>
            </div>

            @if($dateRange === 'custom')
                <div class="grid grid-cols-2 gap-2 mt-4 max-w-md">
                    <div>
                        <label for="start-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">{{ __('Start Date') }}</label>
                        <input type="date" id="start-date" wire:model.live="startDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                    <div>
                        <label for="end-date" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">{{ __('End Date') }}</label>
                        <input type="date" id="end-date" wire:model.live="endDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    </div>
                </div>
            @endif
        </div>
    </x-card>

    @if (session()->has('message'))
        <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            {{ session('message') }}
        </div>
    @endif

    @if($reportType !== 'all' || $reportCategory !== 'all' || $reportStatus !== 'all' || $reportPriority !== 'all' || count($selectedTags) > 0)
        <div class="p-3 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
            <div class="flex flex-wrap items-center gap-2">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="font-medium">{{ __('Active filters:') }}</span>
                </div>

                @if($reportType !== 'all')
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                        {{ __('Type:') }} {{ ucfirst($reportType) }}
                        <button type="button" wire:click="$set('reportType', 'all')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">{{ __('Remove filter') }}</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endif

                @if($reportCategory !== 'all')
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                        {{ __('Category:') }} {{ ucfirst($reportCategory) }}
                        <button type="button" wire:click="$set('reportCategory', 'all')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">{{ __('Remove filter') }}</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endif

                @if($reportStatus !== 'all')
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                        {{ __('Status:') }} {{ ucfirst($reportStatus) }}
                        <button type="button" wire:click="$set('reportStatus', 'all')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">{{ __('Remove filter') }}</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endif

                @if($reportPriority !== 'all')
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                        {{ __('Priority:') }} {{ ucfirst($reportPriority) }}
                        <button type="button" wire:click="$set('reportPriority', 'all')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">{{ __('Remove filter') }}</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endif

                @foreach($selectedTags as $tag)
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                        {{ $tag }}
                        <button type="button" wire:click="toggleTag('{{ $tag }}')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">{{ __('Remove tag') }}</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endforeach

                <button
                    wire:click="resetFilters"
                    type="button"
                    class="ml-auto text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                >
                    {{ __('Clear all filters') }}
                </button>
            </div>
        </div>
    @endif

    <x-slot name="pagecontent">
        <x-page-content>
            <x-slot name="title">
                {{ __('Campaign Reports') }}
            </x-slot>
            <x-slot name="actions">
                <!-- Empty actions slot as we've moved the buttons to the filter card -->
            </x-slot>
            <x-slot name="content">
                @if($viewMode === 'dashboard')
                    <!-- Dashboard View -->
                    <div class="mb-6">
                        <!-- Metrics Cards -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <!-- Total Reports Card -->
                            <x-card key="total-reports" class="p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Total Reports') }}</span>
                                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['total_reports'] ?? 0 }}</h3>
                                    </div>
                                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900">
                                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                            </x-card>

                            <!-- Performance Score Card -->
                            <x-card key="performance-score" class="p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Avg Performance') }}</span>
                                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_performance_score'] ?? 0 }}</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $metrics['avg_performance_score'] >= 90 ? __('Excellent') :
                                               ($metrics['avg_performance_score'] >= 80 ? __('Good') :
                                               ($metrics['avg_performance_score'] >= 70 ? __('Satisfactory') :
                                               ($metrics['avg_performance_score'] >= 60 ? __('Needs Improvement') : __('Unsatisfactory')))) }}
                                        </p>
                                    </div>
                                    <div class="flex items-center justify-center w-12 h-12 rounded-full
                                        {{ $metrics['avg_performance_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                                           ($metrics['avg_performance_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                                           ($metrics['avg_performance_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                                           ($metrics['avg_performance_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                                        <svg class="w-6 h-6
                                            {{ $metrics['avg_performance_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                               ($metrics['avg_performance_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                               ($metrics['avg_performance_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                               ($metrics['avg_performance_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"
                                            fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    </div>
                                </div>
                            </x-card>

                            <!-- Quality Score Card -->
                            <x-card key="quality-score" class="p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Avg Quality') }}</span>
                                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_quality_score'] ?? 0 }}</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $metrics['avg_quality_score'] >= 90 ? __('Excellent') :
                                               ($metrics['avg_quality_score'] >= 80 ? __('Good') :
                                               ($metrics['avg_quality_score'] >= 70 ? __('Satisfactory') :
                                               ($metrics['avg_quality_score'] >= 60 ? __('Needs Improvement') : __('Unsatisfactory')))) }}
                                        </p>
                                    </div>
                                    <div class="flex items-center justify-center w-12 h-12 rounded-full
                                        {{ $metrics['avg_quality_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                                           ($metrics['avg_quality_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                                           ($metrics['avg_quality_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                                           ($metrics['avg_quality_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                                        <svg class="w-6 h-6
                                            {{ $metrics['avg_quality_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                               ($metrics['avg_quality_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                               ($metrics['avg_quality_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                               ($metrics['avg_quality_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"
                                            fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                            </x-card>

                            <!-- Compliance Score Card -->
                            <x-card key="compliance-score" class="p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ __('Avg Compliance') }}</span>
                                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $metrics['avg_compliance_score'] ?? 0 }}</h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $metrics['avg_compliance_score'] >= 90 ? __('Excellent') :
                                               ($metrics['avg_compliance_score'] >= 80 ? __('Good') :
                                               ($metrics['avg_compliance_score'] >= 70 ? __('Satisfactory') :
                                               ($metrics['avg_compliance_score'] >= 60 ? __('Needs Improvement') : __('Unsatisfactory')))) }}
                                        </p>
                                    </div>
                                    <div class="flex items-center justify-center w-12 h-12 rounded-full
                                        {{ $metrics['avg_compliance_score'] >= 90 ? 'bg-green-100 dark:bg-green-900' :
                                           ($metrics['avg_compliance_score'] >= 80 ? 'bg-blue-100 dark:bg-blue-900' :
                                           ($metrics['avg_compliance_score'] >= 70 ? 'bg-yellow-100 dark:bg-yellow-900' :
                                           ($metrics['avg_compliance_score'] >= 60 ? 'bg-orange-100 dark:bg-orange-900' : 'bg-red-100 dark:bg-red-900'))) }}">
                                        <svg class="w-6 h-6
                                            {{ $metrics['avg_compliance_score'] >= 90 ? 'text-green-600 dark:text-green-300' :
                                               ($metrics['avg_compliance_score'] >= 80 ? 'text-blue-600 dark:text-blue-300' :
                                               ($metrics['avg_compliance_score'] >= 70 ? 'text-yellow-600 dark:text-yellow-300' :
                                               ($metrics['avg_compliance_score'] >= 60 ? 'text-orange-600 dark:text-orange-300' : 'text-red-600 dark:text-red-300'))) }}"
                                            fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </div>
                            </x-card>
                        </div>

                        <!-- Charts Row -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                            <!-- Reports Over Time Chart -->
                            <x-card key="reports-chart" class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Reports Over Time') }}</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ __('Trend of report submissions over the selected time period') }}</p>
                                <div id="reports-chart" class="h-80"></div>
                            </x-card>

                            <!-- Scores Chart -->
                            <x-card key="scores-chart" class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Performance Metrics') }}</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ __('Comparison of key performance indicators') }}</p>
                                <div id="scores-chart" class="h-80"></div>
                            </x-card>
                        </div>

                        <!-- Breakdown Row -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- Status Breakdown -->
                            <x-card key="status-breakdown" class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Status Breakdown') }}</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ __('Distribution of reports by status') }}</p>
                                <div class="space-y-3">
                                    @foreach($metrics['status_breakdown'] ?? [] as $status => $count)
                                        <div class="flex items-center">
                                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                                <div class="h-2.5 rounded-full
                                                    {{ $status === 'approved' ? 'bg-green-600' :
                                                       ($status === 'rejected' ? 'bg-red-600' :
                                                       ($status === 'submitted' ? 'bg-blue-600' :
                                                       ($status === 'draft' ? 'bg-gray-600' : 'bg-yellow-600'))) }}"
                                                    style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($status) }}</span>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </x-card>

                            <!-- Category Breakdown -->
                            <x-card key="category-breakdown" class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Category Breakdown') }}</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ __('Distribution of reports by category') }}</p>
                                <div class="space-y-3">
                                    @foreach($metrics['category_breakdown'] ?? [] as $category => $count)
                                        <div class="flex items-center">
                                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                                <div class="h-2.5 rounded-full
                                                    {{ $category === 'performance' ? 'bg-blue-600' :
                                                       ($category === 'quality' ? 'bg-green-600' :
                                                       ($category === 'compliance' ? 'bg-purple-600' :
                                                       ($category === 'training' ? 'bg-yellow-600' :
                                                       ($category === 'operations' ? 'bg-indigo-600' : 'bg-gray-600')))) }}"
                                                    style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($category) }}</span>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </x-card>

                            <!-- Priority Breakdown -->
                            <x-card key="priority-breakdown" class="p-4">
                                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{{ __('Priority Breakdown') }}</h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">{{ __('Distribution of reports by priority') }}</p>
                                <div class="space-y-3">
                                    @foreach($metrics['priority_breakdown'] ?? [] as $priority => $count)
                                        <div class="flex items-center">
                                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                                <div class="h-2.5 rounded-full
                                                    {{ $priority === 'low' ? 'bg-blue-600' :
                                                       ($priority === 'normal' ? 'bg-green-600' :
                                                       ($priority === 'high' ? 'bg-yellow-600' : 'bg-red-600')) }}"
                                                    style="width: {{ ($count / max(1, $metrics['total_reports'])) * 100 }}%"></div>
                                            </div>
                                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300 w-24">{{ ucfirst($priority) }}</span>
                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </x-card>
                        </div>
                    </div>
                @endif

                @if($viewMode === 'list')
                    <!-- List View -->
                    <x-card key="reports-list" class="p-4">
                        <div class="relative overflow-x-auto">
                            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('id')">
                                            {{ __('ID') }}
                                            @if($sortField === 'id')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('title')">
                                            {{ __('Title') }}
                                            @if($sortField === 'title')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('date')">
                                            {{ __('Date') }}
                                            @if($sortField === 'date')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('type')">
                                            {{ __('Type') }}
                                            @if($sortField === 'type')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('category')">
                                            {{ __('Category') }}
                                            @if($sortField === 'category')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('status')">
                                            {{ __('Status') }}
                                            @if($sortField === 'status')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('priority')">
                                            {{ __('Priority') }}
                                            @if($sortField === 'priority')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('overall_score')">
                                            {{ __('Score') }}
                                            @if($sortField === 'overall_score')
                                                <span class="ml-1">
                                                    @if($sortDirection === 'asc')
                                                        <i class="fas fa-arrow-up"></i>
                                                    @else
                                                        <i class="fas fa-arrow-down"></i>
                                                    @endif
                                                </span>
                                            @endif
                                        </th>
                                        <th scope="col" class="px-6 py-3">
                                            {{ __('Actions') }}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($reports as $report)
                                        <tr wire:click="$dispatch('openReportModal', { id: {{ $report->id }} })" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer">
                                            <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                {{ $report->id }}
                                            </td>
                                            <td class="px-6 py-4">
                                                {{ $report->title ?? "Report #$report->id" }}
                                            </td>
                                            <td class="px-6 py-4">
                                                {{ $report->date->format('M d, Y') }}
                                            </td>
                                            <td class="px-6 py-4">
                                                {{ ucfirst($report->type ?? 'Standard') }}
                                            </td>
                                            <td class="px-6 py-4">
                                                {{ ucfirst($report->category ?? 'General') }}
                                            </td>
                                            <td class="px-6 py-4">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                                    {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                                      ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                                      ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                                      ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                                    {{ ucfirst($report->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                                    {{ $report->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                                      ($report->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                                      ($report->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                                    {{ ucfirst($report->priority ?? 'Normal') }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4">
                                                @if($report->overall_score)
                                                    <div class="flex items-center">
                                                        <div class="h-2.5 w-2.5 rounded-full
                                                            {{ $report->overall_score >= 90 ? 'bg-green-500' :
                                                              ($report->overall_score >= 80 ? 'bg-blue-500' :
                                                              ($report->overall_score >= 70 ? 'bg-yellow-500' :
                                                              ($report->overall_score >= 60 ? 'bg-orange-500' : 'bg-red-500'))) }} mr-2"></div>
                                                        {{ $report->overall_score }}
                                                    </div>
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td class="px-6 py-4" onclick="event.stopPropagation()">
                                                <div class="flex space-x-2">
                                                    <div class="relative" x-data="{ open: false }">
                                                        <button @click="open = !open" class="font-medium text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <div x-show="open" @click.away="open = false" class="absolute right-0 z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                                            <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                                                                <li>
                                                                    <a href="#" wire:click.prevent="editReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                        <i class="fas fa-edit mr-2"></i> {{ __('Edit') }}
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                        <i class="far fa-file-pdf mr-2 text-red-500"></i> {{ __('Export PDF') }}
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'docx')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                        <i class="far fa-file-word mr-2 text-blue-500"></i> {{ __('Export DOCX') }}
                                                                    </a>
                                                                </li>
                                                                @if(auth()->user()->role_id <= 3 && $report->approval_status === 'pending')
                                                                    <li>
                                                                        <a href="#" wire:click.prevent="approveReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                            <i class="fas fa-check mr-2 text-green-500"></i> {{ __('Approve') }}
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <a href="#" wire:click.prevent="rejectReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                            <i class="fas fa-times mr-2 text-red-500"></i> {{ __('Reject') }}
                                                                        </a>
                                                                    </li>
                                                                @endif
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                            <td colspan="9" class="px-6 py-4 text-center">{{ __('No reports found') }}</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>

                            <div class="p-4">
                                {{ $reports->links() }}
                            </div>
                        </div>
                    </x-card>
                @endif

                @if($viewMode === 'grid')
                    <!-- Grid View -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @forelse($reports as $report)
                            <x-card key="report-{{ $report->id }}" class="overflow-hidden">
                                <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate">
                                        {{ $report->title ?? "Report #$report->id" }}
                                    </h3>
                                    <div class="flex space-x-2">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            {{ $report->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                              ($report->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                              ($report->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                              'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                            {{ ucfirst($report->priority ?? 'Normal') }}
                                        </span>
                                    </div>
                                </div>
                                <div class="p-4">
                                    <div class="flex justify-between mb-3">
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            <i class="far fa-calendar-alt mr-1"></i> {{ $report->date->format('M d, Y') }}
                                        </div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">
                                            <i class="far fa-file-alt mr-1"></i> {{ ucfirst($report->type ?? 'Standard') }}
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            {{ $report->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                              ($report->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                              ($report->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                              ($report->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                              'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                            {{ ucfirst($report->status) }}
                                        </span>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full ml-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                            {{ ucfirst($report->category ?? 'General') }}
                                        </span>
                                    </div>
                                    <p class="text-gray-700 dark:text-gray-300 mb-4 line-clamp-3">
                                        {{ Str::limit(strip_tags($report->content), 150) }}
                                    </p>

                                    @if($report->performance_score || $report->quality_score || $report->compliance_score)
                                        <div class="grid grid-cols-3 gap-2 mb-4">
                                            @if($report->performance_score)
                                                <div class="text-center">
                                                    <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Performance</div>
                                                    <div class="text-lg font-bold {{ $report->performance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->performance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                        {{ $report->performance_score }}
                                                    </div>
                                                </div>
                                            @endif
                                            @if($report->quality_score)
                                                <div class="text-center">
                                                    <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Quality</div>
                                                    <div class="text-lg font-bold {{ $report->quality_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->quality_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                        {{ $report->quality_score }}
                                                    </div>
                                                </div>
                                            @endif
                                            @if($report->compliance_score)
                                                <div class="text-center">
                                                    <div class="text-sm font-medium text-gray-600 dark:text-gray-400">Compliance</div>
                                                    <div class="text-lg font-bold {{ $report->compliance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($report->compliance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                        {{ $report->compliance_score }}
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    @endif

                                    @if(!empty($report->tags))
                                        <div class="flex flex-wrap gap-1 mb-4">
                                            @foreach($report->tags as $tag)
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                    {{ $tag }}
                                                </span>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                                <div class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 flex justify-between items-center">
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        <i class="far fa-user mr-1"></i> {{ $report->creator->first_name }} {{ $report->creator->last_name }}
                                    </div>
                                    <div class="flex justify-center items-center space-x-2">
                                        <button wire:click="$dispatch('openReportModal', { id: {{ $report->id }} })" class="px-3 py-1 text-xs font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                                            <i class="fas fa-eye mr-1"></i> {{ __('View') }}
                                        </button>
                                        <div class="relative" x-data="{ open: false }">
                                            <button @click="open = !open" class="px-3 py-1 text-xs font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <div x-show="open" @click.away="open = false" class="absolute right-0 bottom-8 z-10 w-44 bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700 dark:divide-gray-600">
                                                <ul class="py-1 text-sm text-gray-700 dark:text-gray-200">
                                                    <li>
                                                        <a href="#" wire:click.prevent="editReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <i class="fas fa-edit mr-2"></i> {{ __('Edit') }}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'pdf')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <i class="far fa-file-pdf mr-2 text-red-500"></i> {{ __('Export PDF') }}
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" wire:click.prevent="exportReport({{ $report->id }}, 'docx')" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                            <i class="far fa-file-word mr-2 text-blue-500"></i> {{ __('Export DOCX') }}
                                                        </a>
                                                    </li>
                                                    @if(auth()->user()->role_id <= 3 && $report->approval_status === 'pending')
                                                        <li>
                                                            <a href="#" wire:click.prevent="approveReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                <i class="fas fa-check mr-2 text-green-500"></i> {{ __('Approve') }}
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="#" wire:click.prevent="rejectReport({{ $report->id }})" class="block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                <i class="fas fa-times mr-2 text-red-500"></i> {{ __('Reject') }}
                                                            </a>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </x-card>
                        @empty
                            <div class="col-span-3">
                                <x-card class="p-6 text-center">
                                    <p class="text-gray-700 dark:text-gray-300">{{ __('No reports found') }}</p>
                                </x-card>
                            </div>
                        @endforelse
                    </div>

                    <div class="mt-4">
                        {{ $reports->links() }}
                    </div>
                @endif

                <!-- Report Modal -->
                <div id="reportModal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
                    <div class="relative w-full max-w-4xl max-h-full">
                        <!-- Modal content -->
                        <div class="relative bg-white rounded-lg shadow dark:bg-gray-800">
                            <!-- Modal header -->
                            <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-700">
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                                    {{ $selectedReport ? ($selectedReport->title ?? "Report #{$selectedReport->id}") : 'Report Details' }}
                                </h3>
                                <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-700 dark:hover:text-white" data-modal-hide="reportModal">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                                    <span class="sr-only">Close modal</span>
                                </button>
                            </div>

                            @if($selectedReport)
                                <!-- Modal body -->
                                <div class="p-6 space-y-6">
                                    <div class="grid grid-cols-2 gap-4 mb-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Campaign') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ $selectedReport->campaign->name }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Date') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ $selectedReport->date->format('F d, Y') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Type') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ ucfirst($selectedReport->type ?? 'Standard') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Category') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">{{ ucfirst($selectedReport->category ?? 'General') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Status') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                                    {{ $selectedReport->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                                      ($selectedReport->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                                      ($selectedReport->status === 'in_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                                      ($selectedReport->status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' :
                                                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'))) }}">
                                                    {{ ucfirst($selectedReport->status) }}
                                                </span>
                                            </p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Priority') }}</p>
                                            <p class="text-base text-gray-900 dark:text-white">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                                    {{ $selectedReport->priority === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                                      ($selectedReport->priority === 'normal' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                                      ($selectedReport->priority === 'high' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                                                      'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300')) }}">
                                                    {{ ucfirst($selectedReport->priority ?? 'Normal') }}
                                                </span>
                                            </p>
                                        </div>
                                    </div>

                                    @if($selectedReport->performance_score || $selectedReport->quality_score || $selectedReport->compliance_score)
                                        <div class="mb-6">
                                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('Performance Metrics') }}</h4>
                                            <div class="grid grid-cols-3 gap-4">
                                                @if($selectedReport->performance_score)
                                                    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600 text-center">
                                                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Performance') }}</div>
                                                        <div class="text-2xl font-bold {{ $selectedReport->performance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($selectedReport->performance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                            {{ $selectedReport->performance_score }}
                                                        </div>
                                                    </div>
                                                @endif
                                                @if($selectedReport->quality_score)
                                                    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600 text-center">
                                                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Quality') }}</div>
                                                        <div class="text-2xl font-bold {{ $selectedReport->quality_score >= 80 ? 'text-green-600 dark:text-green-400' : ($selectedReport->quality_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                            {{ $selectedReport->quality_score }}
                                                        </div>
                                                    </div>
                                                @endif
                                                @if($selectedReport->compliance_score)
                                                    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600 text-center">
                                                        <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ __('Compliance') }}</div>
                                                        <div class="text-2xl font-bold {{ $selectedReport->compliance_score >= 80 ? 'text-green-600 dark:text-green-400' : ($selectedReport->compliance_score >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400') }}">
                                                            {{ $selectedReport->compliance_score }}
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endif

                                    <div class="mb-6">
                                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('Report Content') }}</h4>
                                        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600">
                                            <div class="prose max-w-none dark:prose-invert">
                                                {!! $selectedReport->content !!}
                                            </div>
                                        </div>
                                    </div>

                                    @if($selectedReport->response)
                                        <div class="mb-6">
                                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('Response') }}</h4>
                                            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600">
                                                <div class="prose max-w-none dark:prose-invert">
                                                    {!! $selectedReport->response !!}
                                                </div>
                                            </div>
                                        </div>
                                    @endif

                                    @if(!empty($selectedReport->tags))
                                        <div class="mb-6">
                                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('Tags') }}</h4>
                                            <div class="flex flex-wrap gap-2">
                                                @foreach($selectedReport->tags as $tag)
                                                    <span class="px-3 py-1 text-sm font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                        {{ $tag }}
                                                    </span>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif

                                    @if($selectedReport->comments && $selectedReport->comments->count() > 0)
                                        <div class="mb-6">
                                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('Comments') }}</h4>
                                            <div class="space-y-4">
                                                @foreach($selectedReport->comments->where('parent_id', null) as $comment)
                                                    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600">
                                                        <div class="flex justify-between items-center mb-2">
                                                            <div class="flex items-center">
                                                                <div class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 mr-2">
                                                                    {{ substr($comment->user->first_name, 0, 1) }}{{ substr($comment->user->last_name, 0, 1) }}
                                                                </div>
                                                                <div>
                                                                    <p class="text-sm font-semibold text-gray-900 dark:text-white">
                                                                        {{ $comment->user->first_name }} {{ $comment->user->last_name }}
                                                                    </p>
                                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                                        {{ $comment->created_at->format('M d, Y H:i') }}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                            @if($comment->is_resolved)
                                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                                    {{ __('Resolved') }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                        <div class="text-sm text-gray-700 dark:text-gray-300 mb-2">
                                                            {{ $comment->content }}
                                                        </div>

                                                        @if($comment->replies && $comment->replies->count() > 0)
                                                            <div class="ml-8 mt-3 space-y-3">
                                                                @foreach($comment->replies as $reply)
                                                                    <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-800 dark:border-gray-700">
                                                                        <div class="flex justify-between items-center mb-2">
                                                                            <div class="flex items-center">
                                                                                <div class="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 mr-2">
                                                                                    {{ substr($reply->user->first_name, 0, 1) }}{{ substr($reply->user->last_name, 0, 1) }}
                                                                                </div>
                                                                                <div>
                                                                                    <p class="text-xs font-semibold text-gray-900 dark:text-white">
                                                                                        {{ $reply->user->first_name }} {{ $reply->user->last_name }}
                                                                                    </p>
                                                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                                                        {{ $reply->created_at->format('M d, Y H:i') }}
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="text-sm text-gray-700 dark:text-gray-300">
                                                                            {{ $reply->content }}
                                                                        </div>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                <!-- Modal footer -->
                                <div class="flex items-center justify-between p-6 space-x-2 border-t border-gray-200 rounded-b dark:border-gray-700">
                                    <div>
                                        @if(auth()->user()->role_id <= 3 && $selectedReport->approval_status === 'pending')
                                            <button wire:click="approveReport({{ $selectedReport->id }})" class="px-5 py-2.5 text-sm font-medium rounded-lg bg-green-600 text-white hover:bg-green-700">
                                                <i class="fas fa-check mr-1"></i> {{ __('Approve') }}
                                            </button>
                                            <button wire:click="rejectReport({{ $selectedReport->id }})" class="px-5 py-2.5 text-sm font-medium rounded-lg bg-red-600 text-white hover:bg-red-700">
                                                <i class="fas fa-times mr-1"></i> {{ __('Reject') }}
                                            </button>
                                        @endif
                                    </div>
                                    <div>
                                        <button wire:click="exportReport({{ $selectedReport->id }}, 'pdf')" class="px-5 py-2.5 text-sm font-medium rounded-lg bg-blue-600 text-white hover:bg-blue-700">
                                            <i class="far fa-file-pdf mr-1"></i> {{ __('Export PDF') }}
                                        </button>
                                        <button type="button" class="px-5 py-2.5 text-sm font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600" data-modal-hide="reportModal">
                                            {{ __('Close') }}
                                        </button>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </x-slot>
        </x-page-content>
    </x-slot>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();

            // Listen for Livewire events
            window.addEventListener('chartsUpdated', function() {
                setTimeout(function() {
                    initializeCharts();
                }, 300);
            });

            // Initialize modal functionality
            window.addEventListener('openReportModal', event => {
                const reportId = event.detail.id;
                @this.openReportModal(reportId);

                // Use Flowbite's modal API to show the modal
                const $targetEl = document.getElementById('reportModal');
                const modal = new Modal($targetEl);
                modal.show();
            });
        });

        function initializeCharts() {
            // Reports Over Time Chart
            if (document.getElementById('reports-chart')) {
                const reportsChartData = @this.chartData;

                if (reportsChartData && reportsChartData.labels && reportsChartData.series) {
                    const reportsChartOptions = {
                        chart: {
                            type: 'area',
                            height: 350,
                            toolbar: {
                                show: false
                            },
                            zoom: {
                                enabled: false
                            }
                        },
                        dataLabels: {
                            enabled: false
                        },
                        stroke: {
                            curve: 'smooth',
                            width: 2
                        },
                        series: reportsChartData.series,
                        xaxis: {
                            categories: reportsChartData.labels,
                            labels: {
                                style: {
                                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                                }
                            }
                        },
                        yaxis: {
                            labels: {
                                style: {
                                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                                }
                            }
                        },
                        tooltip: {
                            x: {
                                format: 'dd MMM yyyy'
                            }
                        },
                        fill: {
                            type: 'gradient',
                            gradient: {
                                shadeIntensity: 1,
                                opacityFrom: 0.7,
                                opacityTo: 0.3,
                                stops: [0, 90, 100]
                            }
                        },
                        colors: ['#3b82f6'],
                        grid: {
                            borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                        },
                        theme: {
                            mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                        }
                    };

                    const reportsChart = new ApexCharts(document.getElementById('reports-chart'), reportsChartOptions);
                    reportsChart.render();
                }
            }

            // Scores Chart
            if (document.getElementById('scores-chart')) {
                const scoresChartData = @this.scoreChartData;

                if (scoresChartData && scoresChartData.labels && scoresChartData.series) {
                    const scoresChartOptions = {
                        chart: {
                            type: 'radar',
                            height: 350,
                            toolbar: {
                                show: false
                            }
                        },
                        series: [{
                            name: 'Score',
                            data: scoresChartData.series
                        }],
                        labels: scoresChartData.labels,
                        plotOptions: {
                            radar: {
                                size: 140,
                                polygons: {
                                    strokeColors: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                                    fill: {
                                        colors: ['#f8fafc', '#f1f5f9']
                                    }
                                }
                            }
                        },
                        colors: ['#3b82f6'],
                        markers: {
                            size: 4,
                            colors: ['#3b82f6'],
                            strokeWidth: 2,
                        },
                        tooltip: {
                            y: {
                                formatter: function(val) {
                                    return val;
                                }
                            }
                        },
                        yaxis: {
                            tickAmount: 5,
                            min: 0,
                            max: 100,
                            labels: {
                                style: {
                                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                                }
                            }
                        },
                        grid: {
                            borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                        },
                        theme: {
                            mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                        }
                    };

                    const scoresChart = new ApexCharts(document.getElementById('scores-chart'), scoresChartOptions);
                    scoresChart.render();
                }
            }
        }
    </script>
    @endpush
</x-app-layout>