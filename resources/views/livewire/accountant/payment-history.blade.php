<x-content>
    <x-content-header title="Payment History">
        <div class="flex space-x-2">
            <button type="button" wire:click="exportPaymentHistory" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Export History
            </button>
        </div>
    </x-content-header>

    <x-content-body>
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        <!-- Filters -->
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-4">
                <div class="w-full md:w-1/2">
                    <form class="flex items-center">
                        <label for="simple-search" class="sr-only">Search</label>
                        <div class="relative w-full">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input wire:model.live.debounce.300ms="search" type="text" id="simple-search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search agent name or period" required="">
                        </div>
                    </form>
                </div>
                <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
                    <div class="flex items-center space-x-3 w-full md:w-auto">
                        <select wire:model.live="dateRange" id="dateRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="all">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="quarter">This Quarter</option>
                            <option value="year">This Year</option>
                            <option value="custom">Custom Range</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-4">
                @if($dateRange === 'custom')
                    <div class="w-full md:w-1/4">
                        <label for="startDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Date</label>
                        <input wire:model.live="startDate" type="date" id="startDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    </div>
                    <div class="w-full md:w-1/4">
                        <label for="endDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End Date</label>
                        <input wire:model.live="endDate" type="date" id="endDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    </div>
                @endif
                <div class="w-full md:w-1/4">
                    <label for="selectedAgent" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Agent</label>
                    <select wire:model.live="selectedAgent" id="selectedAgent" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Agents</option>
                        @foreach($agents as $agent)
                            <option value="{{ $agent->id }}">{{ $agent->first_name }} {{ $agent->last_name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="w-full md:w-1/4">
                    <label for="selectedStatus" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                    <select wire:model.live="selectedStatus" id="selectedStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Statuses</option>
                        <option value="paid">Paid</option>
                        <option value="on_hold">On Hold</option>
                    </select>
                </div>
                <div class="w-full md:w-1/4">
                    <label for="perPage" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Per Page</label>
                    <select wire:model.live="perPage" id="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Payment Summary</h3>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Payments</h4>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $totalPayments }}</p>
                </div>

                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Amount</h4>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalAmount, 2) }}</p>
                </div>

                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Paid Amount</h4>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ number_format($paidAmount, 2) }}</p>
                </div>

                <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Amount</h4>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ number_format($pendingAmount, 2) }}</p>
                </div>
            </div>
        </div>

        <!-- Payment History Table -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Agent</th>
                            <th scope="col" class="px-4 py-3">Period</th>
                            <th scope="col" class="px-4 py-3">Hours Worked</th>
                            <th scope="col" class="px-4 py-3">Amount</th>
                            <th scope="col" class="px-4 py-3">Status</th>
                            <th scope="col" class="px-4 py-3">Created At</th>
                            <th scope="col" class="px-4 py-3">Paid At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($payments as $payment)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <th scope="row" class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    {{ $payment->user->first_name }} {{ $payment->user->last_name }}
                                </th>
                                <td class="px-4 py-3">{{ $payment->period }}</td>
                                <td class="px-4 py-3">{{ $payment->hours_worked }}</td>
                                <td class="px-4 py-3">{{ number_format($payment->amount, 2) }}</td>
                                <td class="px-4 py-3">
                                    @if($payment->status === 'paid')
                                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Paid</span>
                                    @else
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">On Hold</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">{{ $payment->created_at->format('M d, Y') }}</td>
                                <td class="px-4 py-3">{{ $payment->paid_at ? $payment->paid_at->format('M d, Y') : 'N/A' }}</td>
                            </tr>
                        @empty
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td colspan="7" class="px-4 py-3 text-center">No payment history found.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $payments->links() }}
            </div>
        </div>
    </x-content-body>
</x-content>
