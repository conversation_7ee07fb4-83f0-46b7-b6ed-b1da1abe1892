<x-content>
    <x-content-header title="Salary Calculation">
        <div class="flex space-x-2">
            <button type="button" wire:click="openPaymentModal" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                Create Payment
            </button>
        </div>
    </x-content-header>

    <x-content-body>
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <span class="font-medium">Error!</span> {{ session('error') }}
            </div>
        @endif

        <!-- Calculation Form -->
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Calculation Parameters</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="selectedAgent" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Agent</label>
                    <select wire:model.live="selectedAgent" id="selectedAgent" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">Select Agent</option>
                        @foreach($agents as $agent)
                            <option value="{{ $agent->id }}">{{ $agent->first_name }} {{ $agent->last_name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="selectedMonth" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Month</label>
                    <select wire:model.live="selectedMonth" id="selectedMonth" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        @foreach($months as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label for="selectedYear" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Year</label>
                    <select wire:model.live="selectedYear" id="selectedYear" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        @foreach($years as $year)
                            <option value="{{ $year }}">{{ $year }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="flex justify-end">
                <button type="button" wire:click="calculateSalary" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                    Calculate
                </button>
            </div>
        </div>

        <!-- Calculation Results -->
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Calculation Results</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Period</h4>
                    <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $paymentPeriod }}</p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Hours Worked</h4>
                    <p class="text-base font-semibold text-gray-900 dark:text-white">{{ number_format($hoursWorked, 2) }}</p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Hourly Rate</h4>
                    <p class="text-base font-semibold text-gray-900 dark:text-white">{{ number_format($hourlyRate, 2) }}</p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Base Salary</h4>
                    <p class="text-base font-semibold text-gray-900 dark:text-white">{{ number_format($baseSalary, 2) }}</p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Bonuses</h4>
                    <p class="text-base font-semibold text-green-600 dark:text-green-400">{{ number_format($bonusAmount, 2) }}</p>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Deductions</h4>
                    <p class="text-base font-semibold text-red-600 dark:text-red-400">{{ number_format($deductionsAmount, 2) }}</p>
                </div>
            </div>

            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                <div class="flex justify-between items-center">
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">Total Amount</h4>
                    <p class="text-xl font-bold text-gray-900 dark:text-white">{{ number_format($totalAmount, 2) }}</p>
                </div>
            </div>
        </div>

        <!-- Payment Modal -->
        <div x-data="{ show: @entangle('showPaymentModal') }" x-show="show" x-cloak class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full dark:bg-gray-800">
                    <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4" id="modal-title">Create Payment</h3>

                        <form wire:submit.prevent="createPayment">
                            <div class="mb-4">
                                <label for="paymentPeriod" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Period</label>
                                <input type="text" wire:model="paymentPeriod" id="paymentPeriod" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" readonly>
                            </div>

                            <div class="mb-4">
                                <label for="totalAmount" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Amount</label>
                                <input type="text" wire:model="totalAmount" id="totalAmount" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" readonly>
                            </div>

                            <div class="mb-4">
                                <label for="paymentStatus" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                                <select wire:model="paymentStatus" id="paymentStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                                    <option value="on_hold">On Hold</option>
                                    <option value="paid">Paid</option>
                                </select>
                            </div>

                            <div class="mb-4">
                                <label for="paymentNotes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                                <textarea wire:model="paymentNotes" id="paymentNotes" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button wire:click="createPayment" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Create Payment
                        </button>
                        <button wire:click="closePaymentModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
