<x-content>
    <x-content-header title="Financial Reports">
        <button id="print-report" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
            <svg class="w-4 h-4 mr-2 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"></path>
            </svg>
            Print Report
        </button>
    </x-content-header>

    <x-content-body>
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 mb-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="reportType" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Report Type</label>
                    <select wire:model.live="reportType" id="reportType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        @foreach($reportTypes as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="selectedYear" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Year</label>
                    <select wire:model.live="selectedYear" id="selectedYear" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        @foreach($years as $year)
                            <option value="{{ $year }}">{{ $year }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="selectedMonth" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Month</label>
                    <select wire:model.live="selectedMonth" id="selectedMonth" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                        @foreach($months as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="flex items-end">
                    <button wire:click="generateReport" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                        Generate Report
                    </button>
                </div>
            </div>
        </div>

        <div id="report-content">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 mb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">
                        {{ $reportTypes[$reportType] }} - {{ $selectedYear }} {{ $selectedMonth !== 'all' ? '(' . $months[$selectedMonth] . ')' : '' }}
                    </h3>
                    <div class="text-2xl font-bold text-gray-900 dark:text-white">
                        Total: {{ number_format($totalAmount, 2) }}
                    </div>
                </div>
                <div id="report-chart" class="h-80"></div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Report Details</h3>
                </div>
                <div class="overflow-x-auto">
                    @if($reportType === 'income')
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-4 py-3">Invoice #</th>
                                    <th scope="col" class="px-4 py-3">Client</th>
                                    <th scope="col" class="px-4 py-3">Date</th>
                                    <th scope="col" class="px-4 py-3">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($reportData as $invoice)
                                    <tr class="border-b dark:border-gray-700">
                                        <th scope="row" class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            {{ $invoice->invoice_number }}
                                        </th>
                                        <td class="px-4 py-3">{{ $invoice->client_name }}</td>
                                        <td class="px-4 py-3">{{ $invoice->created_at->format('M d, Y') }}</td>
                                        <td class="px-4 py-3">{{ number_format($invoice->total_amount, 2) }}</td>
                                    </tr>
                                @empty
                                    <tr class="border-b dark:border-gray-700">
                                        <td colspan="4" class="px-4 py-3 text-center">No data available for the selected period.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    @elseif($reportType === 'tax')
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-4 py-3">Invoice #</th>
                                    <th scope="col" class="px-4 py-3">Client</th>
                                    <th scope="col" class="px-4 py-3">Date</th>
                                    <th scope="col" class="px-4 py-3">Tax Rate</th>
                                    <th scope="col" class="px-4 py-3">Tax Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($reportData as $invoice)
                                    <tr class="border-b dark:border-gray-700">
                                        <th scope="row" class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            {{ $invoice->invoice_number }}
                                        </th>
                                        <td class="px-4 py-3">{{ $invoice->client_name }}</td>
                                        <td class="px-4 py-3">{{ $invoice->created_at->format('M d, Y') }}</td>
                                        <td class="px-4 py-3">{{ $invoice->tax_rate }}%</td>
                                        <td class="px-4 py-3">{{ number_format($invoice->tax_amount, 2) }}</td>
                                    </tr>
                                @empty
                                    <tr class="border-b dark:border-gray-700">
                                        <td colspan="5" class="px-4 py-3 text-center">No data available for the selected period.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    @elseif($reportType === 'agent')
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-4 py-3">Agent</th>
                                    <th scope="col" class="px-4 py-3">Date</th>
                                    <th scope="col" class="px-4 py-3">Amount</th>
                                    <th scope="col" class="px-4 py-3">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($reportData as $payment)
                                    <tr class="border-b dark:border-gray-700">
                                        <th scope="row" class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                            {{ $payment->user ? $payment->user->first_name . ' ' . $payment->user->last_name : 'Unknown' }}
                                        </th>
                                        <td class="px-4 py-3">{{ $payment->created_at->format('M d, Y') }}</td>
                                        <td class="px-4 py-3">{{ number_format($payment->amount, 2) }}</td>
                                        <td class="px-4 py-3">{{ $payment->status }}</td>
                                    </tr>
                                @empty
                                    <tr class="border-b dark:border-gray-700">
                                        <td colspan="4" class="px-4 py-3 text-center">No data available for the selected period.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    @else
                        <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                            No data available for the selected report type.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>

@push('scripts')
<script>
    // Function to initialize financial report chart
    function initializeFinancialReportChart() {
        console.log('Initializing financial report chart');

        const chartData = @json($chartData);

        let chartType = 'bar';
        if (@json($reportType) === 'agent') {
            chartType = 'bar';
        } else if (@json($reportType) === 'profit') {
            chartType = 'bar';
        } else {
            chartType = 'line';
        }

        const chartOptions = {
            series: [{
                name: @json($reportTypes[$reportType]),
                data: chartData.data || []
            }],
            chart: {
                type: chartType,
                height: 320,
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: @json($reportType) === 'agent' ? true : false,
                    columnWidth: '55%',
                    borderRadius: 5
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 2
            },
            xaxis: {
                categories: chartData.labels || [],
                labels: {
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                    },
                    formatter: function(val) {
                        return val.toFixed(2);
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val.toFixed(2);
                    }
                }
            },
            theme: {
                mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
            },
            colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
        };

        // Clear any existing chart
        const reportChartElement = document.querySelector("#report-chart");
        if (reportChartElement) {
            reportChartElement.innerHTML = '';

            // Destroy existing chart instance if it exists
            if (window.chartInstances && window.chartInstances['report-chart']) {
                try {
                    window.chartInstances['report-chart'].destroy();
                } catch (e) {
                    console.warn('Failed to destroy existing chart:', e);
                }
            }

            // Create and render the chart
            const chart = new ApexCharts(reportChartElement, chartOptions);
            chart.render();

            // Store the chart instance
            window.chartInstances = window.chartInstances || {};
            window.chartInstances['report-chart'] = chart;
        }
    }

    // Initialize chart on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeFinancialReportChart();

        // Update chart when Livewire updates the component
        Livewire.on('reportGenerated', initializeFinancialReportChart);

        // Print report
        document.getElementById('print-report').addEventListener('click', function() {
            const printContents = document.getElementById('report-content').innerHTML;
            const originalContents = document.body.innerHTML;

            document.body.innerHTML = `
                <div style="padding: 20px;">
                    <h1 style="text-align: center; margin-bottom: 20px;">${@json($reportTypes[$reportType])} - ${@json($selectedYear)} ${@json($selectedMonth) !== 'all' ? '(' + @json($months[$selectedMonth]) + ')' : ''}</h1>
                    ${printContents}
                </div>
            `;

            window.print();
            document.body.innerHTML = originalContents;
            location.reload();
        });
    });

    // Initialize chart on Livewire navigation
    document.addEventListener('livewire:navigated', function() {
        console.log('Livewire navigation detected for financial reports');
        setTimeout(initializeFinancialReportChart, 300);
    });
</script>
@endpush
