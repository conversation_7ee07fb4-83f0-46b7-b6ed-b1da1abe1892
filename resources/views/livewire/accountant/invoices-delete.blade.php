<x-content>
    <x-content-header title="Delete Invoice">
        <x-page-button type="back" label="Back" action="$dispatch('to-accountant-invoices-show', { invoice: {{ $invoice->id }} })" />
    </x-content-header>

    <x-content-body>
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="flex flex-col items-center">
                <svg class="w-16 h-16 text-red-600 dark:text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                
                <h3 class="mb-5 text-lg font-semibold text-gray-900 dark:text-white">Confirm Invoice Deletion</h3>
                
                <p class="text-gray-500 dark:text-gray-400 mb-6 text-center">
                    Are you sure you want to delete invoice <span class="font-semibold">{{ $invoice->invoice_number }}</span> for <span class="font-semibold">{{ $invoice->client_name }}</span>?<br>
                    This action cannot be undone and will permanently delete the invoice and all related data.
                </p>
                
                <div class="flex items-center space-x-4">
                    <button wire:click="cancelDelete" type="button" class="py-2.5 px-5 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                        Cancel
                    </button>
                    <button wire:click="deleteInvoice" type="button" class="py-2.5 px-5 text-sm font-medium text-white focus:outline-none bg-red-700 rounded-lg border border-red-700 hover:bg-red-800 focus:z-10 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900">
                        Yes, delete invoice
                    </button>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
