<x-content>
    <x-content-header title="Invoice #{{ $invoice->invoice_number }}">
        <div class="flex space-x-2">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-accountant-invoices-edit', { invoice: {{ $invoice->id }} })" />
            <x-page-button type="delete" label="Delete" action="$dispatch('to-accountant-invoices-delete', { invoice: {{ $invoice->id }} })" />
            <x-page-button type="back" label="Back" action="$dispatch('to-accountant-invoices')" />
        </div>
    </x-content-header>

    <x-content-body>
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
            <!-- Left Content -->
            <div class="col-span-2">
                <!-- Invoice Details -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold dark:text-white">Invoice Details</h3>
                        <div class="flex space-x-2">
                            @if($invoice->status === 'draft')
                                <button wire:click="markAsSent" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                                    Mark as Sent
                                </button>
                            @endif
                            
                            @if($invoice->status === 'sent' || $invoice->status === 'overdue')
                                <button wire:click="markAsPaid" class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 focus:outline-none dark:focus:ring-green-800">
                                    Mark as Paid
                                </button>
                            @endif
                            
                            @if($invoice->status === 'sent' && $invoice->is_overdue)
                                <button wire:click="markAsOverdue" class="text-white bg-yellow-700 hover:bg-yellow-800 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-yellow-600 dark:hover:bg-yellow-700 focus:outline-none dark:focus:ring-yellow-800">
                                    Mark as Overdue
                                </button>
                            @endif
                            
                            @if($invoice->status !== 'cancelled')
                                <button wire:click="markAsCancelled" class="text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 focus:outline-none dark:focus:ring-red-800">
                                    Cancel Invoice
                                </button>
                            @endif
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Invoice Number</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->invoice_number }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h4>
                            <p class="text-base font-semibold">
                                @if($invoice->status === 'draft')
                                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300">Draft</span>
                                @elseif($invoice->status === 'sent')
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">Sent</span>
                                @elseif($invoice->status === 'paid')
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Paid</span>
                                @elseif($invoice->status === 'overdue')
                                    <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Overdue</span>
                                @elseif($invoice->status === 'cancelled')
                                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300">Cancelled</span>
                                @endif
                            </p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Issue Date</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->issue_date->format('M d, Y') }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Due Date</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->due_date->format('M d, Y') }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created By</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->creator->name ?? 'Unknown' }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created At</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->created_at->format('M d, Y H:i') }}</p>
                        </div>
                    </div>
                    
                    @if($invoice->notes)
                        <div class="mt-4">
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Notes</h4>
                            <p class="text-base text-gray-900 dark:text-white">{{ $invoice->notes }}</p>
                        </div>
                    @endif
                </div>
                
                <!-- Client Information -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <h3 class="text-xl font-semibold dark:text-white mb-4">Client Information</h3>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Client Name</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->client_name }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Client Email</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->client_email ?? 'N/A' }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Client Address</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->client_address ?? 'N/A' }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Client Phone</h4>
                            <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $invoice->client_phone ?? 'N/A' }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- Invoice Items -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <h3 class="text-xl font-semibold dark:text-white mb-4">Invoice Items</h3>
                    
                    <div class="relative overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-4 py-3">Description</th>
                                    <th scope="col" class="px-4 py-3">Quantity</th>
                                    <th scope="col" class="px-4 py-3">Unit Price</th>
                                    <th scope="col" class="px-4 py-3">Tax Rate</th>
                                    <th scope="col" class="px-4 py-3">Tax Amount</th>
                                    <th scope="col" class="px-4 py-3">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($invoice->items as $item)
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td class="px-4 py-3">{{ $item->description }}</td>
                                        <td class="px-4 py-3">{{ $item->quantity }}</td>
                                        <td class="px-4 py-3">{{ number_format($item->unit_price, 2) }}</td>
                                        <td class="px-4 py-3">{{ $item->tax_rate }}%</td>
                                        <td class="px-4 py-3">{{ number_format($item->tax_amount, 2) }}</td>
                                        <td class="px-4 py-3">{{ number_format($item->total_amount, 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr class="font-semibold text-gray-900 dark:text-white">
                                    <td colspan="5" class="px-4 py-3 text-right">Subtotal:</td>
                                    <td class="px-4 py-3">{{ number_format($invoice->subtotal, 2) }}</td>
                                </tr>
                                <tr class="font-semibold text-gray-900 dark:text-white">
                                    <td colspan="5" class="px-4 py-3 text-right">Tax:</td>
                                    <td class="px-4 py-3">{{ number_format($invoice->tax_amount, 2) }}</td>
                                </tr>
                                <tr class="font-semibold text-gray-900 dark:text-white">
                                    <td colspan="5" class="px-4 py-3 text-right">Discount:</td>
                                    <td class="px-4 py-3">{{ number_format($invoice->discount_amount, 2) }}</td>
                                </tr>
                                <tr class="font-semibold text-gray-900 dark:text-white">
                                    <td colspan="5" class="px-4 py-3 text-right">Total:</td>
                                    <td class="px-4 py-3">{{ number_format($invoice->total_amount, 2) }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Right Content -->
            <div class="col-span-1">
                <!-- Payment Summary -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold dark:text-white">Payment Summary</h3>
                        @if(!$invoice->is_paid && $invoice->status !== 'cancelled')
                            <button wire:click="openPaymentModal" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                                Record Payment
                            </button>
                        @endif
                    </div>
                    
                    <div class="grid grid-cols-1 gap-4 mb-4">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Amount</h4>
                            <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ number_format($invoice->total_amount, 2) }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Amount Paid</h4>
                            <p class="text-lg font-semibold text-green-600 dark:text-green-400">{{ number_format($invoice->total_paid, 2) }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Balance Due</h4>
                            <p class="text-lg font-semibold {{ $invoice->remaining_balance > 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-900 dark:text-white' }}">
                                {{ number_format($invoice->remaining_balance, 2) }}
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Payment History -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                    <h3 class="text-xl font-semibold dark:text-white mb-4">Payment History</h3>
                    
                    @if($invoice->payments->count() > 0)
                        <div class="relative overflow-x-auto">
                            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                    <tr>
                                        <th scope="col" class="px-4 py-3">Date</th>
                                        <th scope="col" class="px-4 py-3">Amount</th>
                                        <th scope="col" class="px-4 py-3">Method</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoice->payments as $payment)
                                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                            <td class="px-4 py-3">{{ $payment->payment_date->format('M d, Y') }}</td>
                                            <td class="px-4 py-3">{{ number_format($payment->amount, 2) }}</td>
                                            <td class="px-4 py-3">{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <p class="text-gray-500 dark:text-gray-400">No payments recorded yet.</p>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Payment Modal -->
        <div x-data="{ show: @entangle('showPaymentModal') }" x-show="show" x-cloak class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                
                <div x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full dark:bg-gray-800">
                    <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4" id="modal-title">Record Payment</h3>
                        
                        <form wire:submit.prevent="recordPayment">
                            <div class="mb-4">
                                <label for="paymentAmount" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Payment Amount</label>
                                <input type="number" wire:model="paymentAmount" id="paymentAmount" min="0.01" step="0.01" max="{{ $invoice->remaining_balance }}" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                                @error('paymentAmount') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div class="mb-4">
                                <label for="paymentDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Payment Date</label>
                                <input type="date" wire:model="paymentDate" id="paymentDate" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                                @error('paymentDate') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div class="mb-4">
                                <label for="paymentMethod" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Payment Method</label>
                                <select wire:model="paymentMethod" id="paymentMethod" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                                    @foreach($paymentMethods as $value => $label)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                                @error('paymentMethod') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div class="mb-4">
                                <label for="transactionReference" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Transaction Reference</label>
                                <input type="text" wire:model="transactionReference" id="transactionReference" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('transactionReference') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                            
                            <div class="mb-4">
                                <label for="paymentNotes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                                <textarea wire:model="paymentNotes" id="paymentNotes" rows="3" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"></textarea>
                                @error('paymentNotes') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                            </div>
                        </form>
                    </div>
                    <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button wire:click="recordPayment" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Record Payment
                        </button>
                        <button wire:click="closePaymentModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-600 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
