<x-content>
    <form wire:submit="$dispatch('create-invoice')">
        <x-content-header title="Create Invoice">
            <x-page-button type="save" label="Save" action="submit"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-accountant-invoices')"/>
        </x-content-header>

        <x-content-body>
            @if (session()->has('message'))
                <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                    <span class="font-medium">Success!</span> {{ session('message') }}
                </div>
            @endif

            <!-- Client Information -->
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Client Information</h3>

                <!-- Customer Mode Selection -->
                <div class="mb-4 flex space-x-4">
                    <button type="button"
                        wire:click="toggleCustomerMode('existing')"
                        class="px-4 py-2 text-sm font-medium rounded-lg {{ !$isNewCustomer ? 'bg-primary-700 text-white' : 'bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-300' }}">
                        Select Existing Customer
                    </button>
                    <button type="button"
                        wire:click="toggleCustomerMode('new')"
                        class="px-4 py-2 text-sm font-medium rounded-lg {{ $isNewCustomer ? 'bg-primary-700 text-white' : 'bg-gray-200 text-gray-900 dark:bg-gray-700 dark:text-gray-300' }}">
                        Enter New Customer
                    </button>
                </div>

                @if(!$isNewCustomer)
                    <!-- Customer Selection -->
                    <div class="mb-4">
                        <label for="customer_select" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Select Customer</label>
                        <select id="customer_select" wire:model="selectedCustomerId" wire:change="selectCustomer($event.target.value)" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select a customer</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Display selected customer information (read-only) -->
                    @if($selectedCustomerId)
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Name</label>
                                <div class="p-2.5 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                                    {{ $form->client_name }}
                                </div>
                            </div>

                            <div class="col-span-6 sm:col-span-3">
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Email</label>
                                <div class="p-2.5 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                                    {{ $form->client_email }}
                                </div>
                            </div>

                            <div class="col-span-6 sm:col-span-3">
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Address</label>
                                <div class="p-2.5 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                                    {{ $form->client_address }}
                                </div>
                            </div>

                            <div class="col-span-6 sm:col-span-3">
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Phone</label>
                                <div class="p-2.5 bg-gray-100 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                                    {{ $form->client_phone }}
                                </div>
                            </div>
                        </div>
                    @endif
                @else
                    <!-- New Customer Form -->
                    <div class="grid grid-cols-6 gap-6">
                        <div class="col-span-6 sm:col-span-3">
                            <label for="client_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Name</label>
                            <input type="text" wire:model="form.client_name" id="client_name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                            @error('form.client_name') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <label for="client_email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Email</label>
                            <input type="email" wire:model="form.client_email" id="client_email" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            @error('form.client_email') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <label for="client_address" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Address</label>
                            <input type="text" wire:model="form.client_address" id="client_address" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            @error('form.client_address') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>

                        <div class="col-span-6 sm:col-span-3">
                            <label for="client_phone" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Client Phone</label>
                            <input type="text" wire:model="form.client_phone" id="client_phone" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            @error('form.client_phone') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                        </div>
                    </div>
                @endif
            </div>

            <!-- Invoice Details -->
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Invoice Details</h3>

                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="issue_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Issue Date</label>
                        <input type="date" wire:model="form.issue_date" id="issue_date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                        @error('form.issue_date') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="due_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Due Date</label>
                        <input type="date" wire:model="form.due_date" id="due_date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                        @error('form.due_date') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                        <select wire:model="form.status" id="status" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                            <option value="draft">Draft</option>
                            <option value="sent">Sent</option>
                            <option value="paid">Paid</option>
                            <option value="overdue">Overdue</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                        @error('form.status') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="tax_rate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Tax Rate (%)</label>
                        <input type="number" wire:model="form.tax_rate" id="tax_rate" min="0" max="100" step="0.01" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        @error('form.tax_rate') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>

                    <div class="col-span-6">
                        <label for="notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                        <textarea wire:model="form.notes" id="notes" rows="4" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"></textarea>
                        @error('form.notes') <p class="mt-2 text-sm text-red-600 dark:text-red-500">{{ $message }}</p> @enderror
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold dark:text-white">Invoice Items</h3>
                    <button type="button" wire:click="addItem" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
                        Add Item
                    </button>
                </div>

                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-4 py-3 w-1/3">Description</th>
                                <th scope="col" class="px-4 py-3">Quantity</th>
                                <th scope="col" class="px-4 py-3">Unit Price</th>
                                <th scope="col" class="px-4 py-3">Discount</th>
                                <th scope="col" class="px-4 py-3">Tax Rate (%)</th>
                                <th scope="col" class="px-4 py-3">Tax Amount</th>
                                <th scope="col" class="px-4 py-3">Total</th>
                                <th scope="col" class="px-4 py-3">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($form->items as $index => $item)
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td class="px-4 py-3">
                                        <input type="text" wire:model.live="form.items.{{ $index }}.description" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Item description" required>
                                    </td>
                                    <td class="px-4 py-3">
                                        <input type="number" wire:model.live="form.items.{{ $index }}.quantity" min="1" step="0.01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                                    </td>
                                    <td class="px-4 py-3">
                                        <input type="number" wire:model.live="form.items.{{ $index }}.unit_price" min="0" step="0.01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" required>
                                    </td>
                                    <td class="px-4 py-3">
                                        <input type="number" wire:model.live="form.items.{{ $index }}.discount_amount" min="0" step="0.01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    </td>
                                    <td class="px-4 py-3">
                                        <input type="number" wire:model.live="form.items.{{ $index }}.tax_rate" min="0" max="100" step="0.01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    </td>
                                    <td class="px-4 py-3">
                                        {{ number_format($item['tax_amount'], 2) }}
                                    </td>
                                    <td class="px-4 py-3">
                                        {{ number_format($item['total_amount'], 2) }}
                                    </td>
                                    <td class="px-4 py-3">
                                        <button type="button" wire:click="removeItem({{ $index }})" class="font-medium text-red-600 dark:text-red-500 hover:underline">Remove</button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr class="font-semibold text-gray-900 dark:text-white">
                                <td colspan="5" class="px-4 py-3 text-right">Subtotal:</td>
                                <td class="px-4 py-3">{{ number_format($form->subtotal, 2) }}</td>
                                <td></td>
                            </tr>
                            <tr class="font-semibold text-gray-900 dark:text-white">
                                <td colspan="5" class="px-4 py-3 text-right">Tax:</td>
                                <td class="px-4 py-3">{{ number_format($form->tax_amount, 2) }}</td>
                                <td></td>
                            </tr>
                            <tr class="font-semibold text-gray-900 dark:text-white">
                                <td colspan="5" class="px-4 py-3 text-right">Discount:</td>
                                <td class="px-4 py-3">
                                    <input type="number" wire:model.live="form.discount_amount" min="0" step="0.01" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                </td>
                                <td></td>
                            </tr>
                            <tr class="font-semibold text-gray-900 dark:text-white">
                                <td colspan="5" class="px-4 py-3 text-right">Total:</td>
                                <td class="px-4 py-3">{{ number_format($form->total_amount, 2) }}</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
