<x-content>
    <x-content-header title="Accountant Dashboard">
        <x-page-button type="add" label="Create Invoice" action="$dispatch('to-accountant-invoices-create')" />
    </x-content-header>

    <x-content-body>
        <!-- Dashboard Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ number_format($totalPaid, 2) }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Total Payments</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ $totalAgents }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Active Agents</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 dark:bg-green-900">
                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ $totalInvoices }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Total Invoices</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900">
                        <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ $pendingInvoices }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Pending Invoices</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-yellow-100 dark:bg-yellow-900">
                        <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Monthly Payments</h3>
                </div>
                <div id="monthly-payments-chart" class="h-80"></div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Invoice Status</h3>
                </div>
                <div id="invoice-status-chart" class="h-80"></div>
            </div>
        </div>

        <!-- Recent Payments and Invoices -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Recent Payments</h5>
                    <a href="{{ route('accountant.payment.history') }}" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">
                        View all
                    </a>
                </div>
                <div class="flow-root">
                    <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($recentPayments as $payment)
                            <li class="py-3 sm:py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        @if($payment->user && $payment->user->profile_photo_path)
                                            <img class="w-8 h-8 rounded-full" src="{{ Storage::url($payment->user->profile_photo_path) }}" alt="{{ $payment->user->name }}">
                                        @else
                                            <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                                <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                            {{ $payment->user ? $payment->user->first_name . ' ' . $payment->user->last_name : 'Unknown User' }}
                                        </p>
                                        <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                            {{ $payment->created_at->format('M d, Y') }}
                                        </p>
                                    </div>
                                    <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                        {{ number_format($payment->amount, 2) }}
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="py-3 sm:py-4">
                                <p class="text-sm text-gray-500 dark:text-gray-400">No recent payments found.</p>
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Recent Invoices</h5>
                    <a href="{{ route('accountant.invoices') }}" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">
                        View all
                    </a>
                </div>
                <div class="flow-root">
                    <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($recentInvoices as $invoice)
                            <li class="py-3 sm:py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                            <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                            {{ $invoice->invoice_number }}
                                        </p>
                                        <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                            {{ $invoice->client_name }}
                                        </p>
                                    </div>
                                    <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                        {{ number_format($invoice->total_amount, 2) }}
                                    </div>
                                </div>
                            </li>
                        @empty
                            <li class="py-3 sm:py-4">
                                <p class="text-sm text-gray-500 dark:text-gray-400">No recent invoices found.</p>
                            </li>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>

@push('scripts')
<script>
    // Function to initialize accountant dashboard charts
    function initializeAccountantDashboardCharts() {
        console.log('Initializing accountant dashboard charts');

        // Monthly Payments Chart
        const monthlyPaymentsData = @json($monthlyPayments);
        const months = monthlyPaymentsData.map(item => item.month);
        const amounts = monthlyPaymentsData.map(item => item.amount);

        const monthlyPaymentsOptions = {
            series: [{
                name: 'Monthly Payments',
                data: amounts
            }],
            chart: {
                type: 'bar',
                height: 320,
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    borderRadius: 5
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                show: true,
                width: 2,
                colors: ['transparent']
            },
            xaxis: {
                categories: months,
                labels: {
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                    }
                }
            },
            yaxis: {
                labels: {
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                    },
                    formatter: function(val) {
                        return val.toFixed(2);
                    }
                }
            },
            fill: {
                opacity: 1
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return val.toFixed(2);
                    }
                }
            },
            theme: {
                mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
            }
        };

        // Clear any existing chart
        const monthlyPaymentsElement = document.querySelector("#monthly-payments-chart");
        if (monthlyPaymentsElement) {
            monthlyPaymentsElement.innerHTML = '';
            const monthlyPaymentsChart = new ApexCharts(monthlyPaymentsElement, monthlyPaymentsOptions);
            monthlyPaymentsChart.render();

            // Store the chart instance
            window.chartInstances = window.chartInstances || {};
            window.chartInstances['monthly-payments-chart'] = monthlyPaymentsChart;
        }

        // Invoice Status Chart
        const invoiceStatusOptions = {
            series: [{{ $totalInvoices - $pendingInvoices }}, {{ $pendingInvoices }}],
            chart: {
                type: 'donut',
                height: 320
            },
            labels: ['Completed', 'Pending'],
            colors: ['#10b981', '#f59e0b'],
            legend: {
                position: 'bottom',
                labels: {
                    colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                }
            },
            dataLabels: {
                enabled: true,
                formatter: function(val) {
                    return Math.round(val) + '%';
                }
            },
            plotOptions: {
                pie: {
                    donut: {
                        size: '50%'
                    }
                }
            },
            theme: {
                mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
            }
        };

        // Clear any existing chart
        const invoiceStatusElement = document.querySelector("#invoice-status-chart");
        if (invoiceStatusElement) {
            invoiceStatusElement.innerHTML = '';
            const invoiceStatusChart = new ApexCharts(invoiceStatusElement, invoiceStatusOptions);
            invoiceStatusChart.render();

            // Store the chart instance
            window.chartInstances = window.chartInstances || {};
            window.chartInstances['invoice-status-chart'] = invoiceStatusChart;
        }

        // Update charts when theme changes
        document.addEventListener('dark-mode', function() {
            if (window.chartInstances && window.chartInstances['monthly-payments-chart']) {
                window.chartInstances['monthly-payments-chart'].updateOptions({
                    theme: {
                        mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                    },
                    xaxis: {
                        labels: {
                            style: {
                                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                            }
                        }
                    },
                    yaxis: {
                        labels: {
                            style: {
                                colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                            }
                        }
                    }
                });
            }

            if (window.chartInstances && window.chartInstances['invoice-status-chart']) {
                window.chartInstances['invoice-status-chart'].updateOptions({
                    theme: {
                        mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
                    },
                    legend: {
                        labels: {
                            colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                        }
                    }
                });
            }
        });
    }

    // Initialize charts on DOMContentLoaded
    document.addEventListener('DOMContentLoaded', initializeAccountantDashboardCharts);

    // Initialize charts on Livewire navigation
    document.addEventListener('livewire:navigated', function() {
        console.log('Livewire navigation detected for accountant dashboard');
        setTimeout(initializeAccountantDashboardCharts, 300);
    });

    // Listen for the custom event from our chart handler
    document.addEventListener('initialize-accountant-charts', initializeAccountantDashboardCharts);
</script>
@endpush
