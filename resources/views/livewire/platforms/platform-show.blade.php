<x-content>
    <x-content-header title="Platform Details">
        <x-page-button type="edit" label="Edit" action="$dispatch('to-platform-edit', { platform: {{ $platform->id }} })"/>
        <x-page-button type="delete" label="Delete" action="$dispatch('to-platform-delete', { platform: {{ $platform->id }} })"/>
        <x-page-button type="back" label="Back" action="$dispatch('to-platforms')"/>
    </x-content-header>
    <x-content-body>
        {{-- Platform Information --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Platform Information</h3>
            <div class="grid grid-cols-6 gap-6">
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Platform Name</label>
                    <p class="text-gray-900 dark:text-white">{{ $platform->name }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Platform Type</label>
                    <p class="text-gray-900 dark:text-white capitalize">{{ $platform->type }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Platform Manager</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($platform->manager)
                            {{ $platform->manager->first_name }} {{ $platform->manager->last_name }}
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IT Support</label>
                    <p class="text-gray-900 dark:text-white">
                        @if($platform->itSupport)
                            {{ $platform->itSupport->first_name }} {{ $platform->itSupport->last_name }}
                        @else
                            Not assigned
                        @endif
                    </p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Capacity</label>
                    <p class="text-gray-900 dark:text-white">{{ $platform->capacity ?? 'Not specified' }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                    <span class="bg-{{ $platform->status === 'active' ? 'green' : 'red' }}-100 text-{{ $platform->status === 'active' ? 'green' : 'red' }}-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-{{ $platform->status === 'active' ? 'green' : 'red' }}-900 dark:text-{{ $platform->status === 'active' ? 'green' : 'red' }}-300">
                        {{ ucfirst($platform->status) }}
                    </span>
                </div>
                <div class="col-span-6">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                    <p class="text-gray-900 dark:text-white">{{ $platform->description ?? 'No description provided' }}</p>
                </div>
            </div>
        </div>

        {{-- Technical Information --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Technical Information</h3>
            <div class="grid grid-cols-6 gap-6">
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">IP Address</label>
                    <p class="text-gray-900 dark:text-white">{{ $platform->ip_address ?? 'Not specified' }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Server Location</label>
                    <p class="text-gray-900 dark:text-white">{{ $platform->server_location ?? 'Not specified' }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Software Version</label>
                    <p class="text-gray-900 dark:text-white">{{ $platform->software_version ?? 'Not specified' }}</p>
                </div>
                <div class="col-span-6 sm:col-span-3">
                    <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Last Maintenance</label>
                    <p class="text-gray-900 dark:text-white">{{ $platform->last_maintenance ? date('F j, Y', strtotime($platform->last_maintenance)) : 'Not specified' }}</p>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
