<x-content>
    <x-content-header>
        <x-slot name="title">Certifications</x-slot>
        <x-slot name="description">Manage certifications and their requirements</x-slot>
        <x-slot name="actions">
            <a href="{{ route('certifications.create') }}" wire:navigate class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-primary-700 focus:bg-primary-700 active:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Certification
            </a>
        </x-slot>
    </x-content-header>

    <x-content-body>
        <!-- Filters -->
        <div class="mb-6 p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="search" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Search</label>
                    <input type="text" wire:model.debounce.300ms="search" id="search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search certifications...">
                </div>
                <div>
                    <label for="issuer" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Issuer</label>
                    <select wire:model="issuer" id="issuer" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Issuers</option>
                        @foreach($issuers as $issuerOption)
                            <option value="{{ $issuerOption }}">{{ $issuerOption }}</option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="perPage" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Per Page</label>
                    <select wire:model="perPage" id="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Certifications Table -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-semibold dark:text-white">Certifications</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Showing {{ $certifications->firstItem() ?? 0 }} - {{ $certifications->lastItem() ?? 0 }} of {{ $certifications->total() }}
                    </span>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3 cursor-pointer" wire:click="sortBy('name')">
                                Name
                                @if($sortField === 'name')
                                    <span class="ml-1">{{ $sortDirection === 'asc' ? '↑' : '↓' }}</span>
                                @endif
                            </th>
                            <th scope="col" class="px-4 py-3">Issuer</th>
                            <th scope="col" class="px-4 py-3">Description</th>
                            <th scope="col" class="px-4 py-3 cursor-pointer" wire:click="sortBy('validity_period')">
                                Validity Period
                                @if($sortField === 'validity_period')
                                    <span class="ml-1">{{ $sortDirection === 'asc' ? '↑' : '↓' }}</span>
                                @endif
                            </th>
                            <th scope="col" class="px-4 py-3">Status</th>
                            <th scope="col" class="px-4 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($certifications as $certification)
                            <tr class="border-b dark:border-gray-700">
                                <td class="px-4 py-3">
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">{{ $certification->name }}</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">ID: {{ $certification->id }}</p>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    @if($certification->issuer)
                                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                            {{ $certification->issuer }}
                                        </span>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">-</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    @if($certification->description)
                                        <p class="text-sm">{{ \Illuminate\Support\Str::limit($certification->description, 50) }}</p>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">No description</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    @if($certification->validity_period)
                                        <span class="text-sm font-medium">{{ $certification->validity_period }} months</span>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">No expiry</span>
                                    @endif
                                </td>
                                <td class="px-4 py-3">
                                    <button wire:click="toggleCertificationStatus({{ $certification->id }})" class="px-2 py-1 text-xs font-medium rounded-full {{ $certification->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' }}">
                                        {{ $certification->is_active ? 'Active' : 'Inactive' }}
                                    </button>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('certifications.show', $certification) }}" wire:navigate class="text-blue-600 hover:underline dark:text-blue-500">
                                            View
                                        </a>
                                        <a href="{{ route('certifications.edit', $certification) }}" wire:navigate class="text-green-600 hover:underline dark:text-green-500">
                                            Edit
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-4 py-6 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <p class="mt-2 text-gray-500 dark:text-gray-400">No certifications found</p>
                                        <a href="{{ route('certifications.create') }}" wire:navigate class="mt-2 text-primary-600 hover:text-primary-700 dark:text-primary-400">
                                            Create your first certification
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4">
                {{ $certifications->links() }}
            </div>
        </div>
    </x-content-body>

    @if (session()->has('message'))
        <div class="fixed top-4 right-4 z-50">
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded" role="alert">
                <span class="block sm:inline">{{ session('message') }}</span>
            </div>
        </div>
    @endif
</x-content>
