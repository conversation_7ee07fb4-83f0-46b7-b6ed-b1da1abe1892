<div class="flex flex-col gap-6">
    <x-auth-header
        :title="__('Log In')"
        :description="__('Enter your credentials to login')"
    />
    <form class="space-y-4 md:space-y-6" wire:submit.prevent="login">
        <x-input
            id="email"
            name="email"
            type="email"
            model="email"
            :placeholder="__('<EMAIL>')"
            :label="__('Your email')"
        />
        <x-password-input
            name="password"
            model="password"
            :placeholder="__('••••••••')"
            :label="__('Password')"
            :showPassword="$showPassword"
        />
        <div class="flex items-center justify-between">
            <div class="flex items-start">
                <div class="flex items-center h-5">
                <input id="remember" aria-describedby="remember" type="checkbox" wire:model="remember" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800">
                </div>
                <div class="ml-3 text-sm">
                <label for="remember" class="text-gray-500 dark:text-gray-300">Remember me</label>
                </div>
            </div>
            <a href="{{ route('password.request') }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">Forgot password?</a>
        </div>
        <x-button
            type="submit"
            title="Sign in"
        />
    </form>
    <x-auth-footer
        message="Don’t have an account yet?"
        label="Sign up"
        route="register"
    />
</div>
