<div class="flex flex-col gap-6">
    <x-auth-header
        :title="__('Reset Password')"
        :description="__('Fill this to reset your password!')"
    />
    <form class="mt-4 space-y-4 lg:mt-5 md:space-y-5" wire:submit="resetPassword">
        <x-input
            id="email"
            name="email"
            type="email"
            model="email"
            :placeholder="__('<EMAIL>')"
            :label="__('Your email')"
        />
        <x-input
            id="password"
            name="password"
            type="password"
            model="password"
            :placeholder="__('••••••••')"
            :label="__('Password')"
        />
        <x-input
            id="confirm-password"
            name="confirm-password"
            type="password"
            model="password_confirmation"
            :placeholder="__('••••••••')"
            :label="__('Password')"
        />
        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="newsletter" aria-describedby="newsletter" type="checkbox" class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800" required="">
            </div>
            <div class="ml-3 text-sm">
                <label for="newsletter" class="font-light text-gray-500 dark:text-gray-300">I accept the <a class="font-medium text-primary-600 hover:underline dark:text-primary-500" href="#">Terms and Conditions</a></label>
            </div>
        </div>
        <x-button
            type="submit"
            title="Sign in"
        />
    </form>
</div>
