<div class="mt-4 flex flex-col gap-6">
    <p class="text-center text-gray-700 dark:text-gray-300">
        {{ __('Please verify your email address by clicking on the link we just emailed to you.') }}
    </p>

    @if (session('status') == 'verification-link-sent')
        <p class="text-center font-medium text-green-600 dark:text-green-400">
            {{ __('A new verification link has been sent to the email address you provided during registration.') }}
        </p>
    @endif

    <div class="flex flex-col items-center justify-between space-y-3">
        <button wire:click="sendVerification" type="button" class="w-full text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800">
            {{ __('Resend verification email') }}
        </button>

        <a wire:click="logout" class="text-sm cursor-pointer text-primary-600 hover:underline dark:text-primary-500">
            {{ __('Log out') }}
        </a>
    </div>
</div>
