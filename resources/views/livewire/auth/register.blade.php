<div class="flex flex-col gap-6">
    <x-auth-header
        :title="__('Create an account')"
        :description="__('Enter your details below to create your account')"
    />
    <form class="space-y-4 md:space-y-6" wire:submit="register">
        <x-input
            id="name"
            name="name"
            type="text"
            model="name"
            :placeholder="__('<PERSON> Doe')"
            :label="__('Your name')"
        />
        <x-input
            id="email"
            name="email"
            type="email"
            model="email"
            :placeholder="__('<EMAIL>')"
            :label="__('Your email')"
        />
        <x-password-input
            name="password"
            model="password"
            :placeholder="__('••••••••')"
            :label="__('Password')"
            :showPassword="$showPassword"
        />
        <x-password-input
            name="password_confirmation"
            model="password_confirmation"
            :placeholder="__('••••••••')"
            :label="__('Confirm Password')"
            :showPassword="$showPasswordConfirmation"
        />
        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input
                    id="terms"
                    aria-describedby="terms"
                    type="checkbox"
                    wire:model="terms"
                    class="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-primary-600 dark:ring-offset-gray-800"
                >
            </div>
            <div class="ml-3 text-sm">
                <label
                    for="terms"
                    class="font-light text-gray-500 dark:text-gray-300"
                >
                    I accept the <a class="font-medium text-primary-600 hover:underline dark:text-primary-500" href="#">Terms and Conditions</a>
                </label>
                @error('terms')
                    <div class="text-red-600 text-xs mt-1">{{ $message }}</div>
                @enderror
            </div>
        </div>
        <x-button
            type="submit"
            title="Create an account"
        />
    </form>
    <x-auth-footer
        message="Already have an account? "
        label="Login here"
        route="login"
    />
</div>
