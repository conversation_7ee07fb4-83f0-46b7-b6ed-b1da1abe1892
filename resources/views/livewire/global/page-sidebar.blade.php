<div>
    <aside
        class="mr-4 w-full pb-4 md:w-[180px]"
        aria-label="Sidenav"
        id="page-navigation"
    >
        <div class="overflow-y-auto h-full">
            <ul class="space-y-2 pb-4">
                @if(is_array($pages))
                    @foreach ($pages as $key=>$page)
                        @if(isset($page['display']) && $page['display'])
                            @if(isset($page['authorized_permissions']))
                                @can($page['authorized_permissions'])
                                <x-page-sidebar-item
                                    :title="$page['title'] ?? ''"
                                    :route="$page['route'] ?? ''"
                                    :routes="$page['section_routes'] ?? []"
                                    :display="$page['display'] ?? false"
                                    :currentRoute="$currentRoute"
                                />
                                @endcan
                            @elseif(isset($page['authorized_roles']) && is_array($page['authorized_roles']) &&
                                  \App\Helpers\RoleHelper::userHasAnyRoleId(auth()->user(), $page['authorized_roles']))
                                <x-page-sidebar-item
                                    :title="$page['title'] ?? ''"
                                    :route="$page['route'] ?? ''"
                                    :routes="$page['section_routes'] ?? []"
                                    :display="$page['display'] ?? false"
                                    :currentRoute="$currentRoute"
                                />
                            @endif
                        @endif
                    @endforeach
                @else
                    <li class="p-4 text-red-500">Error: Pages data is not an array</li>
                @endif
            </ul>
        </div>
    </aside>
</div>
