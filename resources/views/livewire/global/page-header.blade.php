<div class="relative mb-6 w-full">
    <div>
        <div>
            <nav class="sm:hidden" aria-label="Back">
              <a href="#" class="flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400">
                <svg class="-ml-1 mr-1 h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                </svg>
                Back
              </a>
            </nav>
            <nav class="hidden mb-4 sm:flex" aria-label="Breadcrumb">
                <ol role="list" class="flex items-center space-x-4">
                  <li>
                    <div class="flex">
                        <a
                            href="{{ isset($currentModule['routes']) && !empty($currentModule['routes']) ? $this->generatePageUrl($currentModule['routes'][0]) : '#' }}"
                            class="text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400"
                            wire:navigate
                        >
                        {{ $currentModule['title'] ?? 'Module' }}
                        </a>
                    </div>
                  </li>
                  <li>
                    <div class="flex items-center">
                        <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>
                        <a
                            href="{{ $this->generatePageUrl($currentPage['route'] ?? '') }}"
                            aria-current="page"
                            class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400"
                            wire:navigate
                            >
                            {{ $currentPage['title'] ?? 'Page' }}
                        </a>
                    </div>
                  </li>
                  @if($currentPageSection)
                  <li>
                    <div class="flex items-center">
                        <svg class="h-5 w-5 flex-shrink-0 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>
                        <a
                            class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400"
                            wire:navigate
                        >
                        {{ $currentPageSection['title'] ?? 'Section' }}
                        </a>
                    </div>
                  </li>
                  @endif
                </ol>
            </nav>
        </div>
    </div>
    <div class="w-full min-w-ful mx-aut">
        <div class="relative overflow-hidden bg-white shadow-md dark:bg-gray-800 sm:rounded-lg">
            <div class="flex-row items-center justify-between p-4 space-y-3 sm:flex sm:space-y-0 sm:space-x-4">
                <div>
                    <h5 class="mr-3 font-semibold dark:text-white">{{ $currentPage['title'] ?? 'Page' }}</h5>
                    <p class="text-gray-500 dark:text-gray-400">{{ $currentPage['description'] ?? '' }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
