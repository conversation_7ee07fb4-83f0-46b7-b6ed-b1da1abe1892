<div>
    <div x-data="{
        expanded: {{ $isExpandedByDefault ? 'true' : 'false' }},
        contentType: '{{ $contentType }}',
        entityData: @js($entityData)
    }" class="relative">
        <!-- Collapse/Expand <PERSON> (visible only on smaller screens) -->
        <button
            @click="expanded = !expanded"
            class="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-full bg-gray-100 dark:bg-gray-700 xl:hidden z-10"
            aria-label="Toggle resume visibility"
        >
            <svg x-show="expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
            </svg>
            <svg x-show="!expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </button>

        <!-- Resume Content -->
        <div x-show="expanded" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0">
            <!-- Entity View Mode -->
            <template x-if="contentType === 'entity'">
                <x-card key="resume-entity" class="p-4">
                    <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                        <div>
                            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData.title"></span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <!-- Entity Stats -->
                        <div x-show="entityData.stats" class="grid grid-cols-2 gap-4">
                            <template x-for="(value, key) in entityData.stats" :key="key">
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <p class="text-sm text-gray-500 dark:text-gray-400 capitalize" x-text="key.replace('_', ' ')"></p>
                                    <p class="text-lg font-semibold text-gray-900 dark:text-white" x-text="value"></p>
                                </div>
                            </template>
                        </div>

                        <!-- Entity Description -->
                        <div x-show="entityData.description" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Overview</h4>
                            <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData.description"></p>
                        </div>

                        <!-- Entity Metadata -->
                        <div x-show="entityData.metadata" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Details</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 space-y-2">
                                <template x-for="(value, key) in entityData.metadata" :key="key">
                                    <div class="flex justify-between border-b border-gray-200 dark:border-gray-600 pb-2 last:border-0 last:pb-0">
                                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize" x-text="key.replace(/_/g, ' ')"></span>
                                        <span class="text-sm text-gray-900 dark:text-white" x-text="value"></span>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Entity Related Items -->
                        <div x-show="entityData.related_items" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Related Items</h4>
                            <div class="space-y-2">
                                <template x-for="(items, category) in entityData.related_items" :key="category">
                                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white capitalize mb-1" x-text="category.replace(/_/g, ' ')"></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400" x-text="items"></p>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </x-card>
            </template>

            <!-- Form Instructions Mode -->
            <template x-if="contentType === 'form'">
                <x-card key="resume-form" class="p-4">
                    <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                        <div>
                            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData.title"></span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <!-- Form Description -->
                        <div x-show="entityData.description" class="mt-2">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Purpose</h4>
                            <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData.description"></p>
                        </div>

                        <!-- Form Steps/Instructions -->
                        <div x-show="entityData.steps && entityData.steps.length" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Instructions</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                <ol class="list-decimal list-inside space-y-2 text-sm text-gray-500 dark:text-gray-400">
                                    <template x-for="(step, index) in entityData.steps" :key="index">
                                        <li x-text="step"></li>
                                    </template>
                                </ol>
                            </div>
                        </div>

                        <!-- Form Field Information -->
                        <div x-show="entityData.fields" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Key Fields</h4>
                            <div class="space-y-2">
                                <template x-for="(field, index) in entityData.fields" :key="index">
                                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <p class="text-sm font-medium text-gray-900 dark:text-white" x-text="field.name"></p>
                                            <span x-show="field.required" class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full dark:bg-red-900/30 dark:text-red-300">Required</span>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1" x-text="field.description"></p>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Form Notes -->
                        <div x-show="entityData.notes" class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-100 dark:border-yellow-900/30 rounded-lg">
                            <div class="flex">
                                <svg class="w-5 h-5 text-yellow-700 dark:text-yellow-300 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Important Note</p>
                                    <p class="text-sm text-yellow-700 dark:text-yellow-300" x-text="entityData.notes"></p>
                                </div>
                            </div>
                        </div>

                        <!-- Form Validation Rules -->
                        <div x-show="entityData.validation_rules" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Validation Rules</h4>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                <ul class="list-disc list-inside space-y-1 text-sm text-gray-500 dark:text-gray-400">
                                    <template x-for="(rule, index) in entityData.validation_rules" :key="index">
                                        <li x-text="rule"></li>
                                    </template>
                                </ul>
                            </div>
                        </div>
                    </div>
                </x-card>
            </template>

            <!-- Dashboard/Stats Mode -->
            <template x-if="contentType === 'dashboard'">
                <x-card key="resume-dashboard" class="p-4">
                    <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                        <div>
                            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData.title"></span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <!-- Key Metrics -->
                        <div x-show="entityData.metrics && entityData.metrics.length" class="grid grid-cols-2 gap-4">
                            <template x-for="(metric, index) in entityData.metrics" :key="index">
                                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                    <p class="text-sm text-gray-500 dark:text-gray-400" x-text="metric.label"></p>
                                    <div class="flex items-center">
                                        <p class="text-lg font-semibold text-gray-900 dark:text-white" x-text="metric.value"></p>
                                        <template x-if="metric.change !== null && metric.change !== undefined">
                                            <span :class="metric.change > 0 ? 'text-green-500' : 'text-red-500'" class="ml-2 text-xs flex items-center">
                                                <template x-if="metric.change > 0">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </template>
                                                <template x-if="metric.change < 0">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </template>
                                                <span x-text="Math.abs(metric.change) + '%'"></span>
                                            </span>
                                        </template>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- Summary -->
                        <div x-show="entityData.summary" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Summary</h4>
                            <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData.summary"></p>
                        </div>

                        <!-- Actions Section -->
                        <div x-show="entityData.actions" class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Available Actions</h4>
                            <div class="space-y-2">
                                <template x-for="(description, action) in entityData.actions" :key="action">
                                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white" x-text="action"></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400" x-text="description"></p>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Description (if no summary) -->
                        <div x-show="!entityData.summary && entityData.description" class="mt-4">
                            <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData.description"></p>
                        </div>

                        <!-- Fallback for backward compatibility -->
                        <div x-show="!entityData.metrics && !entityData.summary && !entityData.description" class="mt-4">
                            <p class="text-base text-gray-500 dark:text-gray-400">No additional information available.</p>
                        </div>
                    </div>
                </x-card>
            </template>

            <!-- List Mode (for recent items) -->
            <template x-if="contentType === 'list'">
                <x-card key="resume-list" class="p-4">
                    <div class="flow-root">
                        <h3 class="text-xl font-semibold dark:text-white" x-text="entityData.title"></h3>
                        <ul class="mb-6 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Backward compatibility for existing list items -->
                            @if(isset($currentPageResume['type']) && $currentPageResume['type'] == 'recent')
                                @include('livewire.global.partials.recent-items')
                            @endif
                        </ul>
                    </div>
                </x-card>
            </template>

            <!-- Default/Generic Mode -->
            <template x-if="contentType === 'default'">
                <x-card key="resume-default" class="p-4">
                    <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                        <div>
                            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData.title"></span>
                        </div>
                    </div>
                    <div class="mt-2">
                        <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData.description || 'No additional information available.'"></p>
                    </div>
                </x-card>
            </template>

            <!-- Backward compatibility for existing templates -->
            @if(isset($currentPageResume['type']))
                @if($currentPageResume['type'] == 'infos' && $contentType != 'entity')
                    @include('livewire.global.partials.info-content')
                @endif

                @if($currentPageResume['type'] == 'chart' && $contentType != 'dashboard')
                    @include('livewire.global.partials.chart-content')
                @endif
            @endif
        </div>

        <!-- Collapsed View (only shown when collapsed) -->
        <div x-show="!expanded" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="xl:hidden">
            <x-card key="resume-collapsed" class="p-2">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900 dark:text-white truncate" x-text="entityData.title"></span>
                </div>
            </x-card>
        </div>
    </div>
</div>
