<div class="antialiased bg-gray-50 dark:bg-gray-900">
    {{-- Navbar --}}
    <livewire:global.header/>
    {{-- Sidebar --}}
    <livewire:global.sidebar/>
    <main class="p-4 md:ml-64 h-auto pt-20 min-h-screen flex flex-col justify-between">
        {{-- Page --}}
        <div>
            {{-- Page header --}}
            <livewire:global.page-header :pages="$pages" :currentModule="$currentModule" :currentRoute="$currentRoute"/>
            <div class="flex items-start max-md:flex-col w-full">
                {{-- Page sidebar --}}
                <livewire:global.page-sidebar :pages="$pages" :currentRoute="$currentRoute"/>
                <hr class="md:hidden w-full mb-4 border-t border-gray-200 dark:border-gray-700" />
                <div class="flex-1 self-stretch">
                    <div class="w-full md:px-0">
                        <div class="grid grid-cols-1 xl:grid-cols-4 xl:gap-4 dark:bg-gray-900">
                            <div class="col-span-full flex flex-col gap-4 xl:col-auto">
                                {{-- Dynamic Page Resume --}}
                                <livewire:global.page-resume-dynamic
                                    :contentType="$resumeContentType ?? 'default'"
                                    :entityData="$resumeData ?? null"
                                    :title="$resumeTitle ?? 'Information'"
                                    :description="$resumeDescription ?? 'No additional information available.'"
                                />
                            </div>
                            <div class="col-span-3">
                                {{-- Page content --}}
                                <livewire:dynamic-component :is="$component" :key="$component"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{-- Footer --}}
        <livewire:global.footer/>
    </main>
</div>
