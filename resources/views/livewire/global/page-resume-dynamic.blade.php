<div>
    <div x-data="{ 
        expanded: window.innerWidth >= 1280, 
        contentType: '{{ $contentType ?? 'default' }}',
        entityData: @js($entityData ?? null)
    }" class="relative">
        <!-- Collapse/Expand <PERSON><PERSON> (visible only on smaller screens) -->
        <button 
            @click="expanded = !expanded" 
            class="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-full bg-gray-100 dark:bg-gray-700 xl:hidden z-10"
            aria-label="Toggle resume visibility"
        >
            <svg x-show="expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
            </svg>
            <svg x-show="!expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </button>

        <!-- Resume Content -->
        <div x-show="expanded" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0">
            <x-card key="resume-dynamic" class="p-4">
                <!-- Entity View Mode -->
                <template x-if="contentType === 'entity'">
                    <div>
                        <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData?.title || 'Entity Details'"></span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <!-- Entity Stats -->
                            <div x-show="entityData?.stats" class="grid grid-cols-2 gap-4">
                                <template x-for="(value, key) in entityData?.stats" :key="key">
                                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                        <p class="text-sm text-gray-500 dark:text-gray-400 capitalize" x-text="key.replace('_', ' ')"></p>
                                        <p class="text-lg font-semibold text-gray-900 dark:text-white" x-text="value"></p>
                                    </div>
                                </template>
                            </div>
                            
                            <!-- Entity Description -->
                            <div x-show="entityData?.description" class="mt-4">
                                <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData?.description"></p>
                            </div>
                            
                            <!-- Entity Metadata -->
                            <div x-show="entityData?.metadata" class="mt-4 space-y-2">
                                <template x-for="(value, key) in entityData?.metadata" :key="key">
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize" x-text="key.replace('_', ' ')"></span>
                                        <span class="text-sm text-gray-900 dark:text-white" x-text="value"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- Form Instructions Mode -->
                <template x-if="contentType === 'form'">
                    <div>
                        <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData?.title || 'Form Instructions'"></span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <!-- Form Description -->
                            <div x-show="entityData?.description" class="mt-2">
                                <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData?.description"></p>
                            </div>
                            
                            <!-- Form Steps/Instructions -->
                            <div x-show="entityData?.steps?.length" class="mt-4">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Steps:</h4>
                                <ol class="list-decimal list-inside space-y-1 text-sm text-gray-500 dark:text-gray-400">
                                    <template x-for="(step, index) in entityData?.steps" :key="index">
                                        <li x-text="step"></li>
                                    </template>
                                </ol>
                            </div>
                            
                            <!-- Form Notes -->
                            <div x-show="entityData?.notes" class="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-100 dark:border-yellow-900/30 rounded-lg">
                                <p class="text-sm text-yellow-800 dark:text-yellow-200">
                                    <span class="font-medium">Note: </span>
                                    <span x-text="entityData?.notes"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- Dashboard/Stats Mode -->
                <template x-if="contentType === 'dashboard'">
                    <div>
                        <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData?.title || 'Dashboard'"></span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <!-- Key Metrics -->
                            <div x-show="entityData?.metrics" class="grid grid-cols-2 gap-4">
                                <template x-for="(metric, index) in entityData?.metrics" :key="index">
                                    <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                        <p class="text-sm text-gray-500 dark:text-gray-400" x-text="metric.label"></p>
                                        <div class="flex items-center">
                                            <p class="text-lg font-semibold text-gray-900 dark:text-white" x-text="metric.value"></p>
                                            <template x-if="metric.change">
                                                <span :class="metric.change > 0 ? 'text-green-500' : 'text-red-500'" class="ml-2 text-xs flex items-center">
                                                    <template x-if="metric.change > 0">
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </template>
                                                    <template x-if="metric.change < 0">
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </template>
                                                    <span x-text="Math.abs(metric.change) + '%'"></span>
                                                </span>
                                            </template>
                                        </div>
                                    </div>
                                </template>
                            </div>
                            
                            <!-- Summary -->
                            <div x-show="entityData?.summary" class="mt-4">
                                <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData?.summary"></p>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- Default/Generic Mode -->
                <template x-if="contentType === 'default' || !contentType">
                    <div>
                        <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                            <div>
                                <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white" x-text="entityData?.title || '{{ $title ?? 'Information' }}'"></span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-base text-gray-500 dark:text-gray-400" x-text="entityData?.description || '{{ $description ?? 'No additional information available.' }}'"></p>
                        </div>
                    </div>
                </template>
            </x-card>
        </div>

        <!-- Collapsed View (only shown when collapsed) -->
        <div x-show="!expanded" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="xl:hidden">
            <x-card key="resume-collapsed" class="p-2">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900 dark:text-white truncate" x-text="entityData?.title || '{{ $title ?? 'Information' }}'"></span>
                </div>
            </x-card>
        </div>
    </div>
</div>
