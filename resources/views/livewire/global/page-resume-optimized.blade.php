<div>
    <div x-data="{ expanded: window.innerWidth >= 1280 }" class="relative">
        <!-- Collapse/Expand <PERSON> (visible only on smaller screens) -->
        <button 
            @click="expanded = !expanded" 
            class="absolute top-2 right-2 p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 rounded-full bg-gray-100 dark:bg-gray-700 xl:hidden z-10"
            aria-label="Toggle resume visibility"
        >
            <svg x-show="expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
            </svg>
            <svg x-show="!expanded" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </button>

        <!-- Resume Content -->
        <div x-show="expanded" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform -translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0">
            @if($currentPageResume['type'] == "recent")
                <x-card key="resume-1" class="p-4">
                    <div class="flow-root">
                        <h3 class="text-xl font-semibold dark:text-white">{{ $currentPageResume['title'] }}</h3>
                        <ul class="mb-6 divide-y divide-gray-200 dark:divide-gray-700">
                            <!-- Recent items content -->
                        </ul>
                    </div>
                </x-card>
            @endif

            @if($currentPageResume['type'] == "infos")
                <x-card key="resume-2" class="p-4">
                    <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                        <div>
                            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $currentPageResume['title'] }}</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                            {{ $currentPageResume['description'] ?? 'No description available.' }}
                        </p>
                    </div>
                </x-card>
            @endif

            @if($currentPageResume['type'] == "chart")
                <x-card key="resume-3" class="p-4">
                    <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                        <div>
                            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $currentPageResume['title'] }}</span>
                        </div>
                    </div>
                    <div class="p-4">
                        @if(isset($currentPageResume['value']) && isset($currentPageResume['description']))
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-xl font-bold dark:text-white">{{ $currentPageResume['value'] }}</h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ $currentPageResume['description'] }}</p>
                                </div>
                            </div>
                        @else
                            <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                                {{ $currentPageResume['description'] ?? 'No description available.' }}
                            </p>
                        @endif
                    </div>
                </x-card>
            @endif
        </div>

        <!-- Collapsed View (only shown when collapsed) -->
        <div x-show="!expanded" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" class="xl:hidden">
            <x-card key="resume-collapsed" class="p-2">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ $currentPageResume['title'] }}</span>
                </div>
            </x-card>
        </div>
    </div>
</div>
