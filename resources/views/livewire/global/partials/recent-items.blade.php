<li class="py-4">
    <div class="flex justify-between xl:block 2xl:flex align-center 2xl:space-x-4">
        <div class="flex space-x-4 xl:mb-4 2xl:mb-0">
            <div>
                <img class="w-6 h-6 rounded-full" src="/images/users/bonnie-green.png" alt="Bonnie image">
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-base font-semibold text-gray-900 leading-none truncate mb-0.5 dark:text-white">
                    <PERSON>
                </p>
                <p class="mb-1 text-sm font-normal truncate text-primary-700 dark:text-primary-500">
                    New York, USA
                </p>
                <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Last seen: 1 min ago
                </p>
            </div>
        </div>
        <div class="inline-flex items-center w-auto xl:w-full 2xl:w-auto">
            <a href="#" class="w-full px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">View</a>
        </div>
    </div>
</li>
<li class="py-4">
    <div class="flex justify-between xl:block 2xl:flex align-center 2xl:space-x-4">
        <div class="flex space-x-4 xl:mb-4 2xl:mb-0">
            <div>
                <img class="w-6 h-6 rounded-full" src="/images/users/jese-leos.png" alt="Jese image">
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-base font-semibold text-gray-900 leading-none truncate mb-0.5 dark:text-white">
                    Jese Leos
                </p>
                <p class="mb-1 text-sm font-normal truncate text-primary-700 dark:text-primary-500">
                    California, USA
                </p>
                <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Last seen: 2 min ago
                </p>
            </div>
        </div>
        <div class="inline-flex items-center w-auto xl:w-full 2xl:w-auto">
            <a href="#" class="w-full px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">View</a>
        </div>
    </div>
</li>
<li class="py-4">
    <div class="flex justify-between xl:block 2xl:flex align-center 2xl:space-x-4">
        <div class="flex space-x-4 xl:mb-4 2xl:mb-0">
            <div>
                <img class="w-6 h-6 rounded-full" src="/images/users/thomas-lean.png" alt="Thomas image">
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-base font-semibold text-gray-900 leading-none truncate mb-0.5 dark:text-white">
                    Thomas Lean
                </p>
                <p class="mb-1 text-sm font-normal truncate text-primary-700 dark:text-primary-500">
                    Texas, USA
                </p>
                <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Last seen: 1 hour ago
                </p>
            </div>
        </div>
        <div class="inline-flex items-center w-auto xl:w-full 2xl:w-auto">
            <a href="#" class="w-full px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">View</a>
        </div>
    </div>
</li>
<li class="pt-4">
    <div class="flex justify-between xl:block 2xl:flex align-center 2xl:space-x-4">
        <div class="flex space-x-4 xl:mb-4 2xl:mb-0">
            <div>
                <img class="w-6 h-6 rounded-full" src="/images/users/lana-byrd.png" alt="Lana image">
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-base font-semibold text-gray-900 leading-none truncate mb-0.5 dark:text-white">
                    Lana Byrd
                </p>
                <p class="mb-1 text-sm font-normal truncate text-primary-700 dark:text-primary-500">
                    Texas, USA
                </p>
                <p class="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Last seen: 1 hour ago
                </p>
            </div>
        </div>
        <div class="inline-flex items-center w-auto xl:w-full 2xl:w-auto">
            <a href="#" class="w-full px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">View</a>
        </div>
    </div>
</li>
