<div>
    <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
        <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{{ $title ?? 'Chart' }}</h3>
            <span class="text-base font-normal text-gray-500 dark:text-gray-400">{{ $subtitle ?? '' }}</span>
        </div>
        <div class="flex items-center justify-end">
            <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                {{ $period ?? '' }}
            </div>
        </div>
    </div>

    <div id="{{ $chartId ?? 'chart' }}" class="w-full h-72" wire:ignore
        data-chart="{{ json_encode($data ?? []) }}"
        @if(isset($data['labels']))
            @foreach($data['labels'] as $index => $label)
                data-label="{{ $label }}"
            @endforeach
        @endif
        @if(isset($data['datasets'][0]['data']))
            @foreach($data['datasets'][0]['data'] as $index => $value)
                data-value="{{ $value }}"
            @endforeach
        @endif
    ></div>

    @push('scripts')
    <script>
        // Only run this script if we're not using the global dashboard charts system
        if (typeof window.usingGlobalDashboardCharts === 'undefined' || window.usingGlobalDashboardCharts !== true) {
            console.log('Individual chart initialization for {{ $chartId }}');

            document.addEventListener('DOMContentLoaded', function() {
                // Double-check that global system isn't active
                if (window.usingGlobalDashboardCharts === true) {
                    console.log('Global dashboard charts system detected for {{ $chartId }}, skipping individual initialization');
                    return;
                }

                // Initialize this chart individually
                console.log('Initializing individual chart: {{ $chartId }}');
                setTimeout(() => {
                    if (document.getElementById('{{ $chartId }}')) {
                        initializeChart{{ $chartId }}();
                    }
                }, 300);
            });

            // Also initialize when Livewire updates the DOM
            document.addEventListener('livewire:load', function() {
                // Double-check that global system isn't active
                if (window.usingGlobalDashboardCharts === true) {
                    console.log('Global dashboard charts system detected for Livewire updates, skipping individual hook');
                    return;
                }

                console.log('Setting up individual Livewire hook for {{ $chartId }}');
                Livewire.hook('message.processed', (message, component) => {
                    // Only reinitialize if this component was updated and global system isn't active
                    if (window.usingGlobalDashboardCharts !== true &&
                        component.fingerprint &&
                        (component.fingerprint.name === 'global.chart' ||
                         component.fingerprint.name === 'global.dashboard')) {
                        console.log('Reinitializing individual chart after Livewire update: {{ $chartId }}');
                        setTimeout(() => {
                            // Check if chart still exists in DOM before reinitializing
                            if (document.getElementById('{{ $chartId }}')) {
                                initializeChart{{ $chartId }}();
                            }
                        }, 300);
                    }
                });
            });
        } else {
            console.log('Global dashboard charts system active, skipping individual chart script for {{ $chartId }}');
        }

        function initializeChart{{ $chartId }}() {
            // Check if global dashboard charts system is active
            if (window.usingGlobalDashboardCharts === true) {
                console.log(`Global dashboard charts system is active, skipping individual initialization for ${chartId}`);
                return;
            }

            const chartId = '{{ $chartId ?? 'chart' }}';
            const chartType = '{{ $type ?? 'line' }}';
            const chartElement = document.getElementById(chartId);

            if (!chartElement) {
                console.error(`Chart container with ID "${chartId}" not found`);
                return;
            }

            // Check if this chart already has a canvas (already initialized)
            const existingCanvas = chartElement.querySelector('.apexcharts-canvas');
            if (existingCanvas && chartElement.classList.contains('chart-initialized')) {
                console.log(`Chart ${chartId} already initialized, skipping`);
                return;
            }

            // Get chart data
            let chartData;
            try {
                chartData = JSON.parse(chartElement.getAttribute('data-chart') || '{}');
            } catch (error) {
                console.error(`Error parsing chart data for ${chartId}:`, error);
                chartData = {};
            }

            console.log(`Chart ${chartId} (${chartType}) initialization with data:`, chartData);

            // Check if we have valid data
            if (!chartData ||
                Object.keys(chartData).length === 0 ||
                (chartData.datasets && chartData.datasets.length === 0) ||
                (chartData.series && chartData.series.length === 0)) {
                console.log(`No data for chart ${chartId}, showing loading state`);
                chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500">No data available</p></div>';
                return;
            }

            // Perform thorough cleanup of any existing charts
            try {
                // 1. Check global registry
                if (window.dashboardCharts && window.dashboardCharts[chartId]) {
                    console.log(`Destroying existing chart from global registry: ${chartId}`);
                    window.dashboardCharts[chartId].destroy();
                    delete window.dashboardCharts[chartId];
                }

                // 2. Check local registry
                if (window.chartInstances && window.chartInstances[chartId]) {
                    console.log(`Destroying existing chart from local registry: ${chartId}`);
                    window.chartInstances[chartId].destroy();
                    delete window.chartInstances[chartId];
                }

                // 3. Try using ApexCharts.exec
                if (window.ApexCharts && typeof window.ApexCharts.exec === 'function') {
                    try {
                        window.ApexCharts.exec(chartId, 'destroy');
                    } catch (e) {
                        // Ignore errors for non-existent charts
                    }
                }

                // 4. Remove any existing chart elements
                if (chartElement) {
                    const existingCanvases = chartElement.querySelectorAll('.apexcharts-canvas');
                    existingCanvases.forEach(canvas => {
                        console.log(`Removing existing chart canvas for: ${chartId}`);
                        canvas.remove();
                    });
                }
            } catch (error) {
                console.error(`Error cleaning up existing chart ${chartId}:`, error);
            }

            // Initialize chart instances registry if it doesn't exist
            window.chartInstances = window.chartInstances || {};

            // Set up chart colors
            const isDarkMode = document.documentElement.classList.contains('dark');
            const chartColors = {
                textColor: isDarkMode ? '#ffffff' : '#1f2937',
                borderColor: isDarkMode ? '#374151' : '#e5e7eb',
                backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                gridColor: isDarkMode ? '#374151' : '#e5e7eb',
                labelColor: isDarkMode ? '#9ca3af' : '#6b7280',
                legendColor: isDarkMode ? '#ffffff' : '#1f2937',
                primary: '#3b82f6',
                secondary: '#10b981',
                tertiary: '#6366f1',
                quaternary: '#f59e0b',
                quinary: '#ef4444'
            };

            // Prepare data for ApexCharts
            let series = [];
            let labels = [];
            let colors = ['#3b82f6', '#10b981', '#6366f1', '#f59e0b', '#ef4444', '#8b5cf6'];

            // Convert data format if needed
            if (chartData.datasets && chartData.labels) {
                // Chart.js format
                labels = chartData.labels;

                if (chartType === 'pie' || chartType === 'donut') {
                    // For pie/donut charts, we need a single series with all data points
                    series = chartData.datasets[0].data;
                } else {
                    // For other charts, we need multiple series
                    series = chartData.datasets.map((dataset, index) => {
                        return {
                            name: dataset.label || `Series ${index + 1}`,
                            data: dataset.data
                        };
                    });
                }
            } else if (chartData.series && chartData.categories) {
                // ApexCharts format
                series = chartData.series;
                labels = chartData.categories;
            }

            // Map chart types
            let apexChartType = chartType;
            if (chartType === 'doughnut') apexChartType = 'donut';

            // Basic chart options
            const options = {
                chart: {
                    type: apexChartType,
                    height: 350,
                    toolbar: {
                        show: false
                    },
                    background: 'transparent'
                },
                tooltip: {
                    theme: isDarkMode ? 'dark' : 'light'
                },
                colors: colors,
                legend: {
                    labels: {
                        colors: chartColors.legendColor
                    }
                },
                stroke: {
                    width: apexChartType === 'donut' || apexChartType === 'pie' ? 0 : 2
                }
            };

            // Add type-specific options
            if (chartType === 'pie' || chartType === 'donut') {
                options.labels = labels;
                options.legend = {
                    position: 'bottom',
                    horizontalAlign: 'center',
                    fontSize: '14px',
                    labels: {
                        colors: chartColors.legendColor
                    },
                    markers: {
                        width: 12,
                        height: 12,
                        strokeWidth: 0,
                        radius: 12
                    },
                    itemMargin: {
                        horizontal: 8,
                        vertical: 8
                    }
                };
                options.plotOptions = {
                    pie: {
                        donut: {
                            size: chartType === 'donut' ? '50%' : '0%',
                            labels: {
                                show: true,
                                name: {
                                    show: true,
                                    fontSize: '14px',
                                    color: chartColors.textColor
                                },
                                value: {
                                    show: true,
                                    fontSize: '16px',
                                    color: chartColors.textColor
                                },
                                total: {
                                    show: true,
                                    label: 'Total',
                                    color: chartColors.textColor
                                }
                            }
                        }
                    }
                };
            } else {
                options.xaxis = {
                    categories: labels,
                    labels: {
                        style: {
                            colors: chartColors.labelColor
                        }
                    },
                    axisBorder: {
                        color: chartColors.borderColor
                    },
                    axisTicks: {
                        color: chartColors.borderColor
                    }
                };
                options.yaxis = {
                    labels: {
                        style: {
                            colors: chartColors.labelColor
                        }
                    }
                };
                options.grid = {
                    borderColor: chartColors.gridColor,
                    strokeDashArray: 4,
                    yaxis: {
                        lines: {
                            show: true
                        }
                    }
                };
            }

            // Create and render the chart
            try {
                console.log(`Creating ${apexChartType} chart with ID: ${chartId}`);

                // Check if the chart element still exists in the DOM
                if (!document.getElementById(chartId)) {
                    console.error(`Chart element with ID "${chartId}" no longer exists in the DOM`);
                    return;
                }

                // Create chart instance
                const chartInstance = new ApexCharts(chartElement, {
                    ...options,
                    series: series
                });

                // Store the chart instance in the appropriate registry
                if (window.usingGlobalDashboardCharts === true) {
                    window.dashboardCharts[chartId] = chartInstance;
                } else {
                    window.chartInstances[chartId] = chartInstance;
                }

                // Render chart
                chartInstance.render();
                console.log(`Chart ${chartId} rendered successfully`);

                // Add a class to mark this chart as initialized
                chartElement.classList.add('chart-initialized');
            } catch (error) {
                console.error(`Error rendering chart ${chartId}:`, error);
                if (document.getElementById(chartId)) {
                    chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500">Error loading chart</p></div>';
                }
            }
        }
    </script>
    @endpush
</div>
