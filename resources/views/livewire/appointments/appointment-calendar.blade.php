<x-content>
    <x-card key="appointment-calendar-header" class="p-4">
        <div class="flex flex-col md:flex-row justify-between items-center mb-4">
            <!-- Calendar Navigation -->
            <div class="flex items-center mb-4 md:mb-0">
                <button wire:click="changeMonth('prev')" type="button" class="p-2 text-gray-500 rounded-lg hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                </button>
                <h2 class="mx-4 text-xl font-semibold text-gray-900 dark:text-white">
                    {{ date('F Y', strtotime($currentYear . '-' . $currentMonth . '-01')) }}
                </h2>
                <button wire:click="changeMonth('next')" type="button" class="p-2 text-gray-500 rounded-lg hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
                </button>
                <button wire:click="changeViewMode('month')" type="button" class="ml-4 px-3 py-1.5 text-sm font-medium {{ $viewMode === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }} rounded-lg">
                    Month
                </button>
                <button wire:click="changeViewMode('week')" type="button" class="px-3 py-1.5 text-sm font-medium {{ $viewMode === 'week' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }} rounded-lg">
                    Week
                </button>
                <button wire:click="changeViewMode('day')" type="button" class="px-3 py-1.5 text-sm font-medium {{ $viewMode === 'day' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300' }} rounded-lg">
                    Day
                </button>
            </div>
            
            <!-- Filters -->
            <div class="flex flex-col sm:flex-row gap-2">
                <select wire:model.live="selectedCampaignId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="">All Campaigns</option>
                    @foreach($campaigns as $campaign)
                        <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                    @endforeach
                </select>
                <select wire:model.live="selectedAgentId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="">All Agents</option>
                    @foreach($agents as $agent)
                        <option value="{{ $agent->id }}">{{ $agent->getFullNameAttribute() }}</option>
                    @endforeach
                </select>
                <button wire:click="$dispatch('to-appointment-create')" type="button" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                    <svg class="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                    New
                </button>
            </div>
        </div>
    </x-card>
    
    <!-- Month View -->
    @if($viewMode === 'month')
    <x-card key="appointment-calendar-month" class="mt-4 p-4">
        <div class="grid grid-cols-7 gap-2 mb-2">
            <div class="text-center font-medium text-gray-500 dark:text-gray-400">Mon</div>
            <div class="text-center font-medium text-gray-500 dark:text-gray-400">Tue</div>
            <div class="text-center font-medium text-gray-500 dark:text-gray-400">Wed</div>
            <div class="text-center font-medium text-gray-500 dark:text-gray-400">Thu</div>
            <div class="text-center font-medium text-gray-500 dark:text-gray-400">Fri</div>
            <div class="text-center font-medium text-gray-500 dark:text-gray-400">Sat</div>
            <div class="text-center font-medium text-gray-500 dark:text-gray-400">Sun</div>
        </div>
        
        <div class="grid grid-cols-7 gap-2">
            @foreach($calendarDays as $day)
                <div 
                    wire:key="day-{{ $day['date'] ?? 'empty-' . $loop->index }}"
                    class="min-h-[100px] p-2 border rounded-lg {{ $day['isCurrentMonth'] ? 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700' : 'bg-gray-100 dark:bg-gray-900 border-gray-200 dark:border-gray-800' }} {{ $day['isToday'] ?? false ? 'ring-2 ring-blue-500' : '' }} {{ $day['isSelected'] ?? false ? 'bg-blue-50 dark:bg-blue-900' : '' }}"
                >
                    @if($day['day'])
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium {{ $day['isToday'] ?? false ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white' }}">
                                {{ $day['day'] }}
                            </span>
                            <button wire:click="selectDate('{{ $day['date'] }}')" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
                            </button>
                        </div>
                        
                        <div class="space-y-1 max-h-[80px] overflow-y-auto">
                            @foreach($day['appointments'] as $appointment)
                                <a 
                                    href="{{ route('appointments.show', $appointment) }}"
                                    class="block text-xs p-1 rounded truncate {{ $appointment->status_badge_class }}"
                                >
                                    {{ $appointment->scheduled_at->format('H:i') }} - {{ $appointment->customer->name ?? 'N/A' }}
                                </a>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </x-card>
    @endif
    
    <!-- Week View -->
    @if($viewMode === 'week')
    <x-card key="appointment-calendar-week" class="mt-4 p-4">
        <div class="grid grid-cols-7 gap-2">
            @php
                $date = new DateTime($selectedDate);
                $date->modify('this week monday');
                $weekStart = clone $date;
            @endphp
            
            @for($i = 0; $i < 7; $i++)
                <div class="text-center">
                    <div class="font-medium text-gray-500 dark:text-gray-400">
                        {{ $date->format('D') }}
                    </div>
                    <div class="font-bold text-lg {{ $date->format('Y-m-d') === date('Y-m-d') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white' }}">
                        {{ $date->format('j') }}
                    </div>
                </div>
                @php $date->modify('+1 day'); @endphp
            @endfor
        </div>
        
        <div class="mt-4 grid grid-cols-1 gap-4">
            @for($hour = 8; $hour < 20; $hour++)
                <div class="flex">
                    <div class="w-20 text-right pr-4 text-gray-500 dark:text-gray-400">
                        {{ sprintf('%02d:00', $hour) }}
                    </div>
                    <div class="flex-1 grid grid-cols-7 gap-2">
                        @php $date = clone $weekStart; @endphp
                        @for($i = 0; $i < 7; $i++)
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg min-h-[60px] p-1">
                                @php
                                    $currentDateTime = $date->format('Y-m-d') . ' ' . sprintf('%02d:00:00', $hour);
                                    $nextHour = $date->format('Y-m-d') . ' ' . sprintf('%02d:00:00', $hour + 1);
                                    $hourAppointments = $appointments->filter(function($appointment) use ($currentDateTime, $nextHour) {
                                        return $appointment->scheduled_at >= $currentDateTime && $appointment->scheduled_at < $nextHour;
                                    });
                                @endphp
                                
                                @foreach($hourAppointments as $appointment)
                                    <a 
                                        href="{{ route('appointments.show', $appointment) }}"
                                        class="block text-xs p-1 mb-1 rounded truncate {{ $appointment->status_badge_class }}"
                                    >
                                        {{ $appointment->scheduled_at->format('H:i') }} - {{ $appointment->customer->name ?? 'N/A' }}
                                    </a>
                                @endforeach
                            </div>
                            @php $date->modify('+1 day'); @endphp
                        @endfor
                    </div>
                </div>
            @endfor
        </div>
    </x-card>
    @endif
    
    <!-- Day View -->
    @if($viewMode === 'day')
    <x-card key="appointment-calendar-day" class="mt-4 p-4">
        <div class="text-center mb-4">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                {{ date('l, F j, Y', strtotime($selectedDate)) }}
            </h3>
        </div>
        
        <div class="grid grid-cols-1 gap-4">
            @for($hour = 8; $hour < 20; $hour++)
                <div class="flex">
                    <div class="w-20 text-right pr-4 text-gray-500 dark:text-gray-400">
                        {{ sprintf('%02d:00', $hour) }}
                    </div>
                    <div class="flex-1 border-l border-gray-200 dark:border-gray-700 pl-4">
                        @php
                            $currentDateTime = $selectedDate . ' ' . sprintf('%02d:00:00', $hour);
                            $nextHour = $selectedDate . ' ' . sprintf('%02d:00:00', $hour + 1);
                            $hourAppointments = $appointments->filter(function($appointment) use ($currentDateTime, $nextHour) {
                                return $appointment->scheduled_at >= $currentDateTime && $appointment->scheduled_at < $nextHour;
                            });
                        @endphp
                        
                        @if($hourAppointments->count() > 0)
                            @foreach($hourAppointments as $appointment)
                                <div class="mb-2 p-3 rounded-lg {{ $appointment->status_badge_class }}">
                                    <div class="flex justify-between">
                                        <span class="font-medium">{{ $appointment->scheduled_at->format('H:i') }} - {{ $appointment->customer->name }}</span>
                                        <span class="text-sm">{{ $appointment->campaign->name }}</span>
                                    </div>
                                    <div class="mt-1 text-sm">
                                        <span>Agent: {{ $appointment->user->getFullNameAttribute() }}</span>
                                        <span class="ml-4">Type: {{ ucfirst($appointment->type ?? 'Call') }}</span>
                                    </div>
                                    @if($appointment->agent_notes)
                                        <div class="mt-2 text-sm">
                                            <p class="truncate">{{ $appointment->agent_notes }}</p>
                                        </div>
                                    @endif
                                    <div class="mt-2">
                                        <a href="{{ route('appointments.show', $appointment) }}" class="text-blue-600 hover:underline dark:text-blue-400">View Details</a>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="h-12 flex items-center text-gray-400 dark:text-gray-500">
                                No appointments scheduled
                            </div>
                        @endif
                    </div>
                </div>
            @endfor
        </div>
    </x-card>
    @endif
</x-content>
