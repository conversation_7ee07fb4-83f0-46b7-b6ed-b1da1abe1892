<x-content>
    <x-content-header title="Review Appointment">
        <div class="flex space-x-2">
            <x-page-button type="cancel" label="Cancel" action="cancel" />
            <x-page-button type="delete" label="Reject" action="rejectAppointment" />
            <x-page-button type="save" label="Validate" action="validateAppointment" />
        </div>
    </x-content-header>

    <x-content-body>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Appointment Details -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Appointment Details</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Customer</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->name ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Campaign</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->campaign->name ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Agent</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->user->getFullNameAttribute() ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Scheduled Date</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->scheduled_at ? $appointment->scheduled_at->format('M d, Y H:i') : 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Created At</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->created_at ? $appointment->created_at->format('M d, Y H:i') : 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->duration_minutes ?? 'N/A' }} minutes</p>
                    </div>
                </div>

                @if($appointment->agent_notes)
                <div class="mt-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Agent Notes</p>
                    <div class="mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                        <p class="text-sm text-gray-900 dark:text-white whitespace-pre-line">{{ $appointment->agent_notes }}</p>
                    </div>
                </div>
                @endif
            </div>

            <!-- Customer Information -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Customer Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->name ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->email ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->phone ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->address ?? 'N/A' }}</p>
                    </div>
                </div>

                @if($appointment->customer && $appointment->customer->notes)
                <div class="mt-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Customer Notes</p>
                    <div class="mt-2 p-3 bg-gray-50 rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                        <p class="text-sm text-gray-900 dark:text-white whitespace-pre-line">{{ $appointment->customer->notes }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Audio Recording - Prominent Section -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 mb-4">
            <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-500 dark:text-blue-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 14">
                    <path d="M5.5 0a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5ZM10 5a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7A.5.5 0 0 1 10 5ZM14.5 8a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5Z"/>
                </svg>
                Audio Recording
            </h3>

            @if($appointment->audio_path)
                <!-- Audio Player Section -->
                <div class="bg-blue-50 dark:bg-gray-700 p-6 rounded-lg border border-blue-200 dark:border-gray-600 mb-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                                </svg>
                                Audio Available
                            </span>
                        </div>
                        <a href="{{ $appointment->audio_url }}" download="{{ basename($appointment->audio_path) }}" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                            Download Recording
                        </a>
                    </div>

                    <!-- Enhanced Audio Player -->
                    <div class="bg-white dark:bg-gray-600 p-5 rounded-lg border border-gray-200 dark:border-gray-500 shadow-md">
                        <div class="text-sm text-gray-500 dark:text-gray-300 mb-3">
                            File: {{ basename($appointment->audio_path) }}
                        </div>
                        <audio controls class="w-full">
                            <source src="{{ $appointment->audio_url }}" type="{{ $appointment->audio_mime_type ?? 'audio/mpeg' }}">
                            <source src="{{ $appointment->audio_url }}" type="audio/mpeg">
                            <source src="{{ $appointment->audio_url }}" type="audio/wav">
                            <source src="{{ $appointment->audio_url }}" type="audio/ogg">
                            Your browser does not support audio playback.
                        </audio>
                    </div>

                    <!-- Important Notice -->
                    <div class="mt-4 p-4 bg-yellow-50 text-yellow-800 rounded-lg border border-yellow-200 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-700">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <p class="font-bold">Important:</p>
                                <p>Please listen to the entire recording before validating this appointment. This is required for quality control purposes.</p>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <!-- No Audio Available -->
                <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-center p-6">
                        <div class="text-center">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Audio Recording</h4>
                            <p class="text-gray-500 dark:text-gray-400">No audio recording is available for this appointment. You may proceed with the validation based on other information.</p>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Quality Control Notes -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Quality Control Notes</h3>

            <div>
                <label for="cqNotes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                <textarea wire:model="cqNotes" id="cqNotes" rows="4" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter quality control notes..."></textarea>
                @error('cqNotes') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>
        </div>
    </x-content-body>
</x-content>
