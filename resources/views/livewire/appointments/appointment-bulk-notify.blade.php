<x-content>
    <x-content-header title="Send Bulk Notifications">
        <div class="flex space-x-2">
            <x-page-button type="secondary" label="Cancel" action="cancel" />
            <x-page-button type="primary" label="Send Notifications" action="sendNotifications" />
        </div>
    </x-content-header>

    <x-content-body>
        <!-- Selected Appointments -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 mb-4">
            <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Selected Appointments ({{ count($appointments) }})</h3>

            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">Customer</th>
                            <th scope="col" class="px-6 py-3">Campaign</th>
                            <th scope="col" class="px-6 py-3">Agent</th>
                            <th scope="col" class="px-6 py-3">Scheduled Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($appointments as $appointment)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                <td class="px-6 py-4">
                                    {{ $appointment['customer']['name'] ?? 'N/A' }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ $appointment['campaign']['name'] ?? 'N/A' }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ $appointment['user']['first_name'] ?? '' }} {{ $appointment['user']['last_name'] ?? '' }}
                                </td>
                                <td class="px-6 py-4">
                                    {{ \Carbon\Carbon::parse($appointment['scheduled_at'])->format('M d, Y H:i') }}
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center">No appointments selected.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Notification Form -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Notification Details</h3>

            <div class="mb-4">
                <label for="notificationMethod" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notification Method</label>
                <select wire:model="notificationMethod" id="notificationMethod" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    <option value="email">Email</option>
                    <option value="sms">SMS</option>
                    <option value="both">Both Email & SMS</option>
                </select>
                @error('notificationMethod') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div class="mb-4">
                <label for="notificationMessage" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Message</label>
                <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">This message will be sent to all selected customers. You can use generic placeholders as this is a bulk notification.</p>
                <textarea wire:model="notificationMessage" id="notificationMessage" rows="8" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter notification message..."></textarea>
                @error('notificationMessage') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>


        </div>
    </x-content-body>
</x-content>
