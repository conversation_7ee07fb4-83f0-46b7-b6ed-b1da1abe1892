<x-content>
    <x-content-header title="Appointment Statistics">
        <div class="flex items-center space-x-2">
            <select wire:model.live="period" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
            </select>
            
            <select wire:model.live="campaignId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option value="">All Campaigns</option>
                @foreach($campaigns as $campaign)
                    <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                @endforeach
            </select>
            
            <select wire:model.live="agentId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option value="">All Agents</option>
                @foreach($agents as $agent)
                    <option value="{{ $agent->id }}">{{ $agent->getFullNameAttribute() }}</option>
                @endforeach
            </select>
            
            <select wire:model.live="statusFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="validated">Validated</option>
                <option value="rejected">Rejected</option>
                <option value="canceled">Canceled</option>
                <option value="completed">Completed</option>
            </select>
        </div>
    </x-content-header>
    
    <x-content-body>
        <!-- Key Metrics -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center">
                    <div class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-blue-500 bg-blue-100 rounded-lg dark:text-blue-300 dark:bg-blue-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 text-left">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Appointments</p>
                        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $totalAppointments }}</p>
                    </div>
                </div>
            </div>
            
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center">
                    <div class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:text-green-300 dark:bg-green-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 text-left">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Validation Rate</p>
                        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $validationRate }}%</p>
                    </div>
                </div>
            </div>
            
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center">
                    <div class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-yellow-500 bg-yellow-100 rounded-lg dark:text-yellow-300 dark:bg-yellow-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12zm-1-5a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 text-left">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Validation</p>
                        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $pendingAppointments }}</p>
                    </div>
                </div>
            </div>
            
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center">
                    <div class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 text-purple-500 bg-purple-100 rounded-lg dark:text-purple-300 dark:bg-purple-900">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3 text-left">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Appointments Per Day</p>
                        <p class="text-xl font-bold text-gray-900 dark:text-white">{{ $appointmentsPerDay }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts Row 1 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Appointments by Status Chart -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Appointments by Status</h3>
                </div>
                <div id="appointmentsByStatusChart" class="h-80"></div>
            </div>
            
            <!-- Appointments by Day Chart -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Appointments by Day</h3>
                </div>
                <div id="appointmentsByDayChart" class="h-80"></div>
            </div>
        </div>
        
        <!-- Charts Row 2 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Appointments by Campaign Chart -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Appointments by Campaign</h3>
                </div>
                <div id="appointmentsByCampaignChart" class="h-80"></div>
            </div>
            
            <!-- Validation Rate Chart -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Validation Rate</h3>
                </div>
                <div id="validationRateChart" class="h-80"></div>
            </div>
        </div>
        
        <!-- Top Agents Chart -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Top Agents by Appointments</h3>
            </div>
            <div id="appointmentsByAgentChart" class="h-80"></div>
        </div>
    </x-content-body>
</x-content>

@push('scripts')
<script>
    document.addEventListener('livewire:initialized', function () {
        initCharts();
        
        Livewire.on('statisticsUpdated', function() {
            initCharts();
        });
    });
    
    function initCharts() {
        // Appointments by Status Chart
        if (@json($appointmentsByStatusChartData ?? null)) {
            const statusChartData = @json($appointmentsByStatusChartData);
            const statusChartOptions = {
                series: statusChartData.series,
                chart: {
                    type: 'donut',
                    height: 320
                },
                labels: statusChartData.labels,
                colors: statusChartData.colors,
                legend: {
                    position: 'bottom'
                },
                plotOptions: {
                    pie: {
                        donut: {
                            size: '70%'
                        }
                    }
                }
            };
            
            if (document.getElementById('appointmentsByStatusChart')) {
                const statusChart = new ApexCharts(document.getElementById('appointmentsByStatusChart'), statusChartOptions);
                statusChart.render();
            }
        }
        
        // Appointments by Day Chart
        if (@json($appointmentsByDayChartData ?? null)) {
            const dayChartData = @json($appointmentsByDayChartData);
            const dayChartOptions = {
                series: dayChartData.series,
                chart: {
                    type: 'bar',
                    height: 320,
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: false,
                        columnWidth: '70%'
                    }
                },
                dataLabels: {
                    enabled: false
                },
                xaxis: {
                    categories: dayChartData.categories
                },
                colors: ['#3b82f6']
            };
            
            if (document.getElementById('appointmentsByDayChart')) {
                const dayChart = new ApexCharts(document.getElementById('appointmentsByDayChart'), dayChartOptions);
                dayChart.render();
            }
        }
        
        // Appointments by Campaign Chart
        if (@json($appointmentsByCampaignChartData ?? null)) {
            const campaignChartData = @json($appointmentsByCampaignChartData);
            const campaignChartOptions = {
                series: campaignChartData.series,
                chart: {
                    type: 'donut',
                    height: 320
                },
                labels: campaignChartData.labels,
                legend: {
                    position: 'bottom'
                },
                plotOptions: {
                    pie: {
                        donut: {
                            size: '70%'
                        }
                    }
                }
            };
            
            if (document.getElementById('appointmentsByCampaignChart')) {
                const campaignChart = new ApexCharts(document.getElementById('appointmentsByCampaignChart'), campaignChartOptions);
                campaignChart.render();
            }
        }
        
        // Validation Rate Chart
        if (@json($validationRateChartData ?? null)) {
            const validationChartData = @json($validationRateChartData);
            const validationChartOptions = {
                series: validationChartData.series,
                chart: {
                    type: 'donut',
                    height: 320
                },
                labels: validationChartData.labels,
                colors: ['#10b981', '#ef4444', '#3b82f6'],
                legend: {
                    position: 'bottom'
                },
                plotOptions: {
                    pie: {
                        donut: {
                            size: '70%'
                        }
                    }
                }
            };
            
            if (document.getElementById('validationRateChart')) {
                const validationChart = new ApexCharts(document.getElementById('validationRateChart'), validationChartOptions);
                validationChart.render();
            }
        }
        
        // Appointments by Agent Chart
        if (@json($appointmentsByAgentChartData ?? null)) {
            const agentChartData = @json($appointmentsByAgentChartData);
            const agentChartOptions = {
                series: agentChartData.series,
                chart: {
                    type: 'bar',
                    height: 320,
                    toolbar: {
                        show: false
                    }
                },
                plotOptions: {
                    bar: {
                        horizontal: true,
                        columnWidth: '70%'
                    }
                },
                dataLabels: {
                    enabled: false
                },
                xaxis: {
                    categories: agentChartData.categories
                },
                colors: ['#8b5cf6']
            };
            
            if (document.getElementById('appointmentsByAgentChart')) {
                const agentChart = new ApexCharts(document.getElementById('appointmentsByAgentChart'), agentChartOptions);
                agentChart.render();
            }
        }
    }
</script>
@endpush
