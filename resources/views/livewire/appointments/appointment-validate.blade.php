<x-content>
    <div class="w-full">
        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                {{ session('message') }}
            </div>
        @endif

        <x-card key="appointment-index-header" class="p-2">
            <ul class="text-sm font-medium text-center text-gray-500 divide-x divide-gray-200 rounded-lg sm:flex dark:divide-gray-600 dark:text-gray-400" id="fullWidthTab" role="tablist">
                <li class="w-full">
                    <button
                        wire:click="setActiveTab('pending')"
                        id="pending-tab"
                        type="button"
                        class="inline-block w-full p-4 hover:bg-gray-100 {{ $activeTab == 'pending' ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-500 font-medium border-b-2 border-blue-600 dark:border-blue-500' : 'bg-gray-50 dark:bg-gray-800'}} focus:outline-none dark:hover:bg-gray-700"
                    >
                        Pending
                    </button>
                </li>
                <li class="w-full">
                    <button
                        wire:click="setActiveTab('validated')"
                        id="validated-tab"
                        type="button"
                        class="inline-block w-full p-4 hover:bg-gray-100 {{ $activeTab == 'validated' ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-500 font-medium border-b-2 border-blue-600 dark:border-blue-500' : 'bg-gray-50 dark:bg-gray-800'}} focus:outline-none dark:hover:bg-gray-700"
                    >
                        Validated
                    </button>
                </li>
                <li class="w-full">
                    <button
                        wire:click="setActiveTab('rejected')"
                        id="rejected-tab"
                        type="button"
                        class="inline-block w-full p-4 rounded-tr-lg hover:bg-gray-100 {{ $activeTab == 'rejected' ? 'bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-500 font-medium border-b-2 border-blue-600 dark:border-blue-500' : 'bg-gray-50 dark:bg-gray-800'}} focus:outline-none dark:hover:bg-gray-700"
                    >
                        Rejected
                    </button>
                </li>
            </ul>
        </x-card>
            <div id="fullWidthTabContent" class="">
                {{-- first --}}
                @if($activeTab === 'validated')
                <div class="pt-4" id="validated" role="tabpanel" aria-labelledby="validated-tab">
                    <x-card key="appointment-index-header" class="p-4">
                        <div class="w-full1">
                            <div class="sm:flex">
                                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                                    <div class="lg:pr-3">
                                        <label for="appointments-search" class="sr-only">Search</label>
                                        <div class="relative mt-1 lg:w-64 xl:w-96">
                                            <input type="text" wire:model.live.debounce.300ms="search" id="appointments-search" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search for appointments">
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                                    <button
                                        wire:click="notifySelectedAppointments"
                                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 {{count($selectedAppointments) > 0 ? '' : 'opacity-50 cursor-not-allowed'}}"
                                        {{count($selectedAppointments) > 0 ? '' : 'disabled'}}
                                    >
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                        </svg>
                                        Send Selected to Customers
                                    </button>
                                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        @foreach ([5, 10, 25, 50, 100] as $value)
                                            <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </x-card>
                    <x-card key="appointment-index-table" class="overflow-x-auto mt-4">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="p-4 w-4">
                                        <input
                                            type="checkbox"
                                            wire:model.live="selectAll"
                                            wire:key="select-all-validated-{{ $selectAll ? 'checked' : 'unchecked' }}"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                            aria-label="Select all appointments"
                                        >
                                    </th>
                                    <th wire:click="sortBy('user_id')" class="cursor-pointer px-6 py-3">
                                        Agent {{ $sortField === 'user_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('campaign_id')" class="cursor-pointer px-6 py-3">
                                        Campaign {{ $sortField === 'campaign_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('customer_id')" class="cursor-pointer px-6 py-3">
                                        Customer {{ $sortField === 'customer_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('scheduled_at')" class="cursor-pointer px-6 py-3">
                                        Date {{ $sortField === 'scheduled_at' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('validated_at')" class="cursor-pointer px-6 py-3">
                                        Validated At {{ $sortField === 'validated_at' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th class="px-6 py-3">Validated By</th>
                                    <th class="px-6 py-3">Has Audio</th>
                                    <th class="px-6 py-3">Customer Status</th>
                                    <th class="px-6 py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($appointments as $appointment)
                                    <tr wire:key="appointment-{{ $appointment->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <td class="p-4 w-4">
                                            <input
                                                type="checkbox"
                                                wire:change="toggleAppointmentSelection({{ $appointment->id }})"
                                                wire:key="checkbox-validated-{{ $appointment->id }}-{{ in_array($appointment->id, $selectedAppointments) ? 'checked' : 'unchecked' }}"
                                                value="{{ $appointment->id }}"
                                                {{ in_array($appointment->id, $selectedAppointments) ? 'checked' : '' }}
                                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                aria-label="Select appointment {{ $appointment->id }}"
                                            >
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->user->getFullNameAttribute() }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->campaign->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->customer->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->scheduled_at->format('M d, Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->validated_at ? $appointment->validated_at->format('M d, Y H:i') : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->validatedBy ? $appointment->validatedBy->getFullNameAttribute() : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            @if($appointment->audio_path)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Available
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                                                        <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                                                    </svg>
                                                    None
                                                </span>
                                            @endif
                                        </td>

                                        <td class="px-6 py-4">
                                            @if($appointment->sent_to_customer)
                                                @if($appointment->customer_confirmed)
                                                    <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300">
                                                        Confirmed ({{ $appointment->customer_confirmed_at ? $appointment->customer_confirmed_at->format('M d, Y') : 'N/A' }})
                                                    </span>
                                                @else
                                                    <span class="px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full dark:bg-yellow-900 dark:text-yellow-300">
                                                        Sent ({{ $appointment->sent_to_customer_at ? $appointment->sent_to_customer_at->format('M d, Y') : 'N/A' }})
                                                    </span>
                                                @endif
                                            @else
                                                <span class="px-2 py-1 text-xs font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">
                                                    Not Sent
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex space-x-2">
                                                @if(!$appointment->sent_to_customer)
                                                    <button
                                                        wire:click="notifyAppointment({{ $appointment->id }})"
                                                        class="px-3 py-1 text-xs font-medium text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                    >
                                                        Send to Customer
                                                    </button>
                                                @elseif(!$appointment->customer_confirmed)
                                                    <button
                                                        wire:click="markAsConfirmed({{ $appointment->id }})"
                                                        wire:confirm="Are you sure you want to mark this appointment as confirmed by the customer?"
                                                        class="px-3 py-1 text-xs font-medium text-white bg-green-700 rounded-lg hover:bg-green-800 focus:ring-4 focus:ring-green-300 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                                                    >
                                                        Mark as Confirmed
                                                    </button>
                                                @else
                                                    <button
                                                        wire:click="notifyAppointment({{ $appointment->id }})"
                                                        class="px-3 py-1 text-xs font-medium text-white bg-purple-700 rounded-lg hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 dark:bg-purple-600 dark:hover:bg-purple-700 dark:focus:ring-purple-800"
                                                    >
                                                        Resend Notification
                                                    </button>
                                                @endif
                                                <a
                                                    href="{{ route('appointments.show', $appointment) }}"
                                                    class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 focus:ring-4 focus:ring-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:focus:ring-gray-800"
                                                >
                                                    View Details
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No validated appointments found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        <div class="p-4">
                            {{ $appointments->links() }}
                        </div>
                    </x-card>
                </div>
                @endif
                {{-- second --}}
                @if($activeTab === 'pending')
                <div class="pt-4" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                    <x-card key="appointment-index-header" class="p-4">
                        <div class="w-full1">
                            <div class="sm:flex">
                                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                                    <div class="lg:pr-3">
                                        <label for="appointments-search" class="sr-only">Search</label>
                                        <div class="relative mt-1 lg:w-64 xl:w-96">
                                            <input type="text" wire:model.live.debounce.300ms="search" id="appointments-search" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search for appointments">
                                        </div>
                                    </div>
                                    <div class="flex pl-0 mt-3 space-x-1 sm:pl-2 sm:mt-0">
                                        <button
                                            wire:click="deleteSelected"
                                            wire:confirm="Are you sure you want to delete the selected appointments?"
                                            class="inline-flex justify-center p-1 text-gray-500 rounded {{$selectedAppointments ? 'cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white' : 'opacity-50 cursor-not-allowed'}}"
                                            {{$selectedAppointments ? '' : 'disabled'}}
                                        >
                                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        @foreach ([5, 10, 25, 50, 100] as $value)
                                            <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </x-card>

                    <!-- No modals needed as we're using pages instead -->

                    <x-card key="appointment-validate-table" class="overflow-x-auto mt-4">
                        <!-- Info message about audio recordings -->
                        <div class="p-4 text-sm text-blue-800 border-b border-gray-200 bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-gray-700" role="alert">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                <span>You can listen to audio recordings directly in this table or in the review page. Please review all recordings carefully before validating appointments.</span>
                            </div>
                        </div>
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="p-4 w-4">
                                        <input
                                            type="checkbox"
                                            wire:model.live="selectAll"
                                            wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                            aria-label="Select all appointments"
                                        >
                                    </th>
                                    <th wire:click="sortBy('user_id')" class="cursor-pointer px-6 py-3">
                                        Agent {{ $sortField === 'user_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('campaign_id')" class="cursor-pointer px-6 py-3">
                                        Campaign {{ $sortField === 'campaign_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('customer_id')" class="cursor-pointer px-6 py-3">
                                        Customer {{ $sortField === 'customer_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('scheduled_at')" class="cursor-pointer px-6 py-3">
                                        Date {{ $sortField === 'scheduled_at' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th class="px-6 py-3">Has Audio</th>
                                    <th class="px-6 py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($appointments as $appointment)
                                    <tr wire:key="appointment-{{ $appointment->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <td class="p-4 w-4">
                                            <input
                                                type="checkbox"
                                                wire:change="toggleAppointmentSelection({{ $appointment->id }})"
                                                wire:key="checkbox-{{ $appointment->id }}-{{ in_array($appointment->id, $selectedAppointments) ? 'checked' : 'unchecked' }}"
                                                value="{{ $appointment->id }}"
                                                {{ in_array($appointment->id, $selectedAppointments) ? 'checked' : '' }}
                                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                aria-label="Select appointment {{ $appointment->id }}"
                                            >
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->user->getFullNameAttribute() }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->campaign->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->customer->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->scheduled_at->format('M d, Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4">
                                            @if($appointment->audio_path)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Available
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                                                        <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                                                    </svg>
                                                    None
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex space-x-2">
                                                <button wire:click="reviewAppointment({{ $appointment->id }})" class="px-3 py-1 text-xs font-medium text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                                    Review
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No pending appointments found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        <div class="p-4">
                            {{ $appointments->links() }}
                        </div>
                    </x-card>
                </div>
                @endif
                {{-- third --}}
                @if($activeTab === 'rejected')
                <div class="pt-4" id="rejected" role="tabpanel" aria-labelledby="rejected-tab">
                    <x-card key="appointment-index-header" class="p-4">
                        <div class="w-full1">
                            <div class="sm:flex">
                                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                                    <div class="lg:pr-3">
                                        <label for="appointments-search" class="sr-only">Search</label>
                                        <div class="relative mt-1 lg:w-64 xl:w-96">
                                            <input type="text" wire:model.live.debounce.300ms="search" id="appointments-search" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search for appointments">
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        @foreach ([5, 10, 25, 50, 100] as $value)
                                            <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </x-card>

                    <x-card key="appointment-rejected-table" class="overflow-x-auto mt-4">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th wire:click="sortBy('user_id')" class="cursor-pointer px-6 py-3">
                                        Agent {{ $sortField === 'user_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('campaign_id')" class="cursor-pointer px-6 py-3">
                                        Campaign {{ $sortField === 'campaign_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('customer_id')" class="cursor-pointer px-6 py-3">
                                        Customer {{ $sortField === 'customer_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('scheduled_at')" class="cursor-pointer px-6 py-3">
                                        Date {{ $sortField === 'scheduled_at' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th wire:click="sortBy('rejected_at')" class="cursor-pointer px-6 py-3">
                                        Rejected At {{ $sortField === 'rejected_at' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                    </th>
                                    <th class="px-6 py-3">Rejected By</th>
                                    <th class="px-6 py-3">Has Audio</th>
                                    <th class="px-6 py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($appointments as $appointment)
                                    <tr wire:key="appointment-{{ $appointment->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                        <td class="px-6 py-4">
                                            {{ $appointment->user->getFullNameAttribute() }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->campaign->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->customer->name ?? 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->scheduled_at->format('M d, Y H:i') }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->rejected_at ? $appointment->rejected_at->format('M d, Y H:i') : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ $appointment->rejectedBy ? $appointment->rejectedBy->getFullNameAttribute() : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            @if($appointment->audio_path)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                                                    </svg>
                                                    Available
                                                </span>
                                            @else
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                                                        <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                                                    </svg>
                                                    None
                                                </span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('appointments.show', $appointment) }}" class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 focus:ring-4 focus:ring-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:focus:ring-gray-800">
                                                    View Details
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No rejected appointments found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                        <div class="p-4">
                            {{ $appointments->links() }}
                        </div>
                    </x-card>
                </div>
                @endif
            </div>
        </div>

    <!-- No modals needed as we're using pages instead -->
</x-content>

