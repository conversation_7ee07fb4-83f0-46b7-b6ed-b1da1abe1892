<x-content>
    <x-content-header title="Send Notification to Customer">
        <div class="flex space-x-2">
            <x-page-button type="cancel" label="Cancel" action="cancel" />
            <x-page-button type="save" label="Send Notification" action="sendNotification" />
        </div>
    </x-content-header>

    <x-content-body>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Appointment Details -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Appointment Details</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Customer</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->name ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Campaign</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->campaign->name ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Agent</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->user->getFullNameAttribute() ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Scheduled Date</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->scheduled_at ? $appointment->scheduled_at->format('M d, Y H:i') : 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Validated At</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->validated_at ? $appointment->validated_at->format('M d, Y H:i') : 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->duration_minutes ?? 'N/A' }} minutes</p>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Customer Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->name ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->email ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->phone ?? 'N/A' }}</p>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</p>
                        <p class="text-base font-semibold text-gray-900 dark:text-white">{{ $appointment->customer->address ?? 'N/A' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Form -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <h3 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Notification Details</h3>

            <div class="mb-4">
                <label for="notificationMethod" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notification Method</label>
                <select wire:model="notificationMethod" id="notificationMethod" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    <option value="email">Email</option>
                    <option value="sms">SMS</option>
                    <option value="both">Both Email & SMS</option>
                </select>
                @error('notificationMethod') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>

            <div class="mb-4">
                <label for="notificationMessage" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Message</label>
                <textarea wire:model="notificationMessage" id="notificationMessage" rows="8" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter notification message..."></textarea>
                @error('notificationMessage') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
            </div>


        </div>
    </x-content-body>
</x-content>
