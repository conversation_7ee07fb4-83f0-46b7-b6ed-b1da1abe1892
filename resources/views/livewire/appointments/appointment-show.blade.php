<x-content>
    <div>
        <x-content-header title="Appointment Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-appointment-edit', { appointment: {{ $appointment->id }} })"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-appointment-delete', { appointment: {{ $appointment->id }} })"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-appointment-index')"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Right Content -->
                <div class="col-span-full xl:col-auto">
                    <!-- Audio Recording -->
                    @if($appointment->audio_path)
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Recording</h3>
                        <div class="mb-4">
                            <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <div class="flex items-center justify-between mb-3">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">Call Recording</span>
                                    <a href="{{ $appointment->audio_url }}" download="{{ basename($appointment->audio_path) }}" class="text-sm text-blue-600 hover:underline dark:text-blue-500">
                                        Download
                                    </a>
                                </div>

                                <audio controls class="w-full">
                                    <source src="{{ $appointment->audio_url }}" type="{{ $appointment->audio_mime_type ?? 'audio/mpeg' }}">
                                    <source src="{{ $appointment->audio_url }}" type="audio/mpeg">
                                    <source src="{{ $appointment->audio_url }}" type="audio/wav">
                                    <source src="{{ $appointment->audio_url }}" type="audio/ogg">
                                    Your browser does not support audio playback.
                                </audio>

                                <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                    <p>If the audio doesn't play, try downloading it or using a different browser.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Appointment Status -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Status</h3>
                        <div class="mb-4">
                            <span class="px-3 py-2 text-sm font-medium rounded-lg {{ $appointment->status_badge_class }}">
                                {{ ucfirst($appointment->status) }}
                            </span>

                            @if($appointment->is_recurring)
                            <span class="ml-2 px-3 py-2 text-sm font-medium rounded-lg bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                Recurring
                            </span>
                            @endif

                            @if($appointment->follow_up_required)
                            <span class="ml-2 px-3 py-2 text-sm font-medium rounded-lg bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                Follow-up Required
                            </span>
                            @endif

                            @if($appointment->sent_to_customer)
                                @if($appointment->customer_confirmed)
                                <span class="ml-2 px-3 py-2 text-sm font-medium rounded-lg bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                    Customer Confirmed
                                </span>
                                @else
                                <span class="ml-2 px-3 py-2 text-sm font-medium rounded-lg bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                    Sent to Customer
                                </span>
                                @endif
                            @endif
                        </div>

                        @if($appointment->validated_at)
                        <div class="mt-4">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Validated by: {{ $appointment->validatedBy->getFullNameAttribute() ?? 'N/A' }}
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Validated at: {{ $appointment->validated_at->format('M d, Y H:i') }}
                            </p>
                        </div>
                        @endif

                        @if($appointment->rejected_at)
                        <div class="mt-4">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Rejected by: {{ $appointment->rejectedBy->getFullNameAttribute() ?? 'N/A' }}
                            </p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Rejected at: {{ $appointment->rejected_at->format('M d, Y H:i') }}
                            </p>
                        </div>
                        @endif



                        @if($appointment->status === 'validated')
                        <div class="mt-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Customer Notification:</h4>
                            @if($appointment->sent_to_customer)
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Sent to customer on {{ $appointment->sent_to_customer_at->format('M d, Y H:i') }}
                                </p>
                                @if($appointment->customer_confirmed)
                                <p class="text-sm text-green-600 dark:text-green-400">
                                    Confirmed by customer on {{ $appointment->customer_confirmed_at->format('M d, Y H:i') }}
                                </p>
                                @else
                                <p class="text-sm text-yellow-600 dark:text-yellow-400">
                                    Awaiting customer confirmation
                                </p>
                                @endif
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Not yet sent to customer
                                </p>
                            @endif
                        </div>
                        @endif
                    </div>

                    <!-- Related Appointments -->
                    @if($appointment->is_recurring && $appointment->relatedAppointments()->count() > 0)
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Related Appointments</h3>
                        <div class="mb-4">
                            <ul class="space-y-2">
                                @foreach($appointment->relatedAppointments() as $relatedAppointment)
                                <li>
                                    <a href="{{ route('appointments.show', $relatedAppointment) }}" class="text-blue-600 hover:underline dark:text-blue-500">
                                        {{ $relatedAppointment->scheduled_at->format('M d, Y H:i') }} - {{ ucfirst($relatedAppointment->status) }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    @endif
                </div>
                <!-- Left Content -->
                <div class="col-span-2">
                    <!-- General Information -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">General Information</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label for="user" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Agent</label>
                                <input readonly type="text" value="{{ $appointment->user->getFullNameAttribute() }}" name="user" id="user" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="campaign" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign</label>
                                <input readonly type="text" value="{{ $appointment->campaign->name }}" name="campaign" id="campaign" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="customer" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Customer</label>
                                <input readonly type="text" value="{{ $appointment->customer->name }}" name="customer" id="customer" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Type</label>
                                <input readonly type="text" value="{{ ucfirst($appointment->type ?? 'Call') }}" name="type" id="type" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="category" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Category</label>
                                <input readonly type="text" value="{{ $appointment->category ?? 'N/A' }}" name="category" id="category" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                        </div>
                    </div>

                    <!-- Schedule Information -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Schedule Information</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label for="scheduled_at" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Date & Time</label>
                                <input readonly type="text" value="{{ $appointment->scheduled_at->format('M d, Y H:i') }}" name="scheduled_at" id="scheduled_at" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="end_time" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End Date & Time</label>
                                <input readonly type="text" value="{{ $appointment->end_time ? $appointment->end_time->format('M d, Y H:i') : 'N/A' }}" name="end_time" id="end_time" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="duration" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Duration</label>
                                <input readonly type="text" value="{{ $appointment->formatted_duration }}" name="duration" id="duration" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="location" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Location</label>
                                <input readonly type="text" value="{{ $appointment->location ?? 'N/A' }}" name="location" id="location" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>

                            @if($appointment->meeting_link)
                            <div class="col-span-6">
                                <label for="meeting_link" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Meeting Link</label>
                                <div class="flex">
                                    <input readonly type="text" value="{{ $appointment->meeting_link }}" name="meeting_link" id="meeting_link" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg rounded-r-none focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <a href="{{ $appointment->meeting_link }}" target="_blank" class="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md dark:bg-gray-600 dark:text-gray-400 dark:border-gray-600">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path><path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path></svg>
                                    </a>
                                </div>
                            </div>
                            @endif

                            @if($appointment->follow_up_required && $appointment->follow_up_date)
                            <div class="col-span-6 sm:col-span-3">
                                <label for="follow_up_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Follow-up Date</label>
                                <input readonly type="text" value="{{ $appointment->follow_up_date->format('M d, Y H:i') }}" name="follow_up_date" id="follow_up_date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            @endif

                            @if($appointment->is_recurring && $appointment->recurrence_pattern)
                            <div class="col-span-6 sm:col-span-3">
                                <label for="recurrence_pattern" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Recurrence Pattern</label>
                                <input readonly type="text" value="{{ ucfirst($appointment->recurrence_pattern) }}" name="recurrence_pattern" id="recurrence_pattern" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>

                            <div class="col-span-6 sm:col-span-3">
                                <label for="recurrence_end_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Recurrence End Date</label>
                                <input readonly type="text" value="{{ $appointment->recurrence_end_date ? $appointment->recurrence_end_date->format('M d, Y') : 'N/A' }}" name="recurrence_end_date" id="recurrence_end_date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Notes</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6">
                                <label for="agent_notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Agent Notes</label>
                                <div class="p-4 bg-gray-50 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                    <p class="text-sm text-gray-900 dark:text-white whitespace-pre-line">{{ $appointment->agent_notes ?? 'No notes available' }}</p>
                                </div>
                            </div>

                            @if($appointment->outcome)
                            <div class="col-span-6 sm:col-span-3">
                                <label for="outcome" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Outcome</label>
                                <input readonly type="text" value="{{ ucfirst($appointment->outcome) }}" name="outcome" id="outcome" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            @endif

                            @if($appointment->outcome_notes)
                            <div class="col-span-6">
                                <label for="outcome_notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Outcome Notes</label>
                                <div class="p-4 bg-gray-50 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                    <p class="text-sm text-gray-900 dark:text-white whitespace-pre-line">{{ $appointment->outcome_notes }}</p>
                                </div>
                            </div>
                            @endif

                            @if($appointment->cq_notes)
                            <div class="col-span-6">
                                <label for="cq_notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                    @if($appointment->status === 'validated')
                                        Validation Notes
                                    @elseif($appointment->status === 'rejected')
                                        Rejection Notes
                                    @else
                                        Quality Control Notes
                                    @endif
                                </label>
                                <div class="p-4 bg-gray-50 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                    <p class="text-sm text-gray-900 dark:text-white whitespace-pre-line">{{ $appointment->cq_notes }}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>


