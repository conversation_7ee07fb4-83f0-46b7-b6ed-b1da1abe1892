<x-content>
    <x-card key="appointment-index-header" class="p-4">
        <div class="w-full">
            <!-- Search and Actions Row -->
            <div class="flex flex-col md:flex-row justify-between gap-4 mb-4">
                <!-- Search Box -->
                <div class="w-full md:w-1/3">
                    <label for="appointment-search" class="sr-only">Search</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                            </svg>
                        </div>
                        <input
                            type="text"
                            wire:model.live.debounce.300ms="search"
                            id="appointment-search"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            placeholder="Search appointments (customer, agent, campaign, notes...)">
                        @if(!empty(trim($search)))
                        <button
                            type="button"
                            wire:click="clearSearch"
                            class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-2">
                    <button
                        type="button"
                        wire:click="$dispatch('to-appointment-create')"
                        class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                        wire:navigate
                    >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                        Add Appointment
                    </button>

                    <button
                        wire:click="deleteSelected"
                        wire:confirm="Are you sure you want to delete the selected appointments?"
                        class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg {{count($selectedAppointments) ? 'bg-red-600 hover:bg-red-700 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800' : 'bg-gray-400 cursor-not-allowed'}}"
                        {{count($selectedAppointments) ? '' : 'disabled'}}
                    >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                        Delete Selected
                    </button>

                    <a href="#" class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm2 10a1 1 0 10-2 0v3a1 1 0 102 0v-3zm2-3a1 1 0 011 1v5a1 1 0 11-2 0v-5a1 1 0 011-1zm4-1a1 1 0 10-2 0v7a1 1 0 102 0V8z" clip-rule="evenodd"></path></svg>
                        Export Report
                    </a>
                </div>
            </div>

            <!-- Filters Row -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mt-4">
                <!-- Select All Checkbox -->
                <div class="flex items-center mb-3 sm:mb-0">
                    <input
                        id="select-all"
                        type="checkbox"
                        wire:model.live="selectAll"
                        wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                    >
                    <label for="select-all" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Select All</label>
                </div>

                <!-- Filtering Controls -->
                <div class="flex flex-wrap gap-2 items-center">
                    <!-- Date Range Filters -->
                    <div class="flex items-center space-x-2">
                        <input
                            type="date"
                            id="start-date"
                            wire:model.live="startDate"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="Start date"
                        >
                        <span class="text-gray-500 dark:text-gray-400">to</span>
                        <input
                            type="date"
                            id="end-date"
                            wire:model.live="endDate"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="End date"
                        >
                    </div>

                    <!-- Status Filter -->
                    <select
                        wire:model.live="statusFilter"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                    >
                        <option value="">All Statuses</option>
                        <option value="pending" class="text-yellow-700 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300">Pending</option>
                        <option value="validated" class="text-green-700 bg-green-100 dark:bg-green-900 dark:text-green-300">Validated</option>
                        <option value="rejected" class="text-red-700 bg-red-100 dark:bg-red-900 dark:text-red-300">Rejected</option>
                        <option value="completed" class="text-green-700 bg-green-100 dark:bg-green-900 dark:text-green-300">Completed</option>
                        <option value="cancelled" class="text-red-700 bg-red-100 dark:bg-red-900 dark:text-red-300">Cancelled</option>
                        <option value="active" class="text-green-700 bg-green-100 dark:bg-green-900 dark:text-green-300">Active</option>
                    </select>

                    <!-- Per Page Select -->
                    <select
                        wire:model.live="perPage"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    >
                        @foreach ([5, 10, 25, 50, 100] as $value)
                            <option value="{{ $value }}">{{ $value }} per page</option>
                        @endforeach
                    </select>

                    @if($search || $startDate || $endDate || $statusFilter)
                    <button
                        wire:click="clearAllFilters"
                        type="button"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900"
                    >
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear Filters
                    </button>
                    @endif
                </div>
            </div>
        </div>
    </x-card>
    <!-- Active Filters Summary -->
    @if(!empty(trim($search)) || $startDate || $endDate || $statusFilter)
    <div class="p-3 mt-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
        <div class="flex flex-wrap items-center gap-2">
            <div class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"></path>
                </svg>
                <span class="font-medium">Active filters:</span>
            </div>

            @if(!empty(trim($search)))
            <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                Search: "{{ $search }}"
                <button type="button" wire:click="clearSearch" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                    <span class="sr-only">Remove filter</span>
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            @endif

            @if($startDate || $endDate)
            <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                Date: {{ $startDate ? \Carbon\Carbon::parse($startDate)->format('M d, Y') : 'Any' }} to {{ $endDate ? \Carbon\Carbon::parse($endDate)->format('M d, Y') : 'Any' }}
                <button type="button" wire:click="clearDateFilter" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                    <span class="sr-only">Remove filter</span>
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            @endif

            @if($statusFilter)
            <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{
                in_array($statusFilter, ['pending']) ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                (in_array($statusFilter, ['validated', 'active', 'actif', 'completed']) ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                (in_array($statusFilter, ['rejected', 'cancelled']) ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'))
            }}">
                Status: {{ ucfirst($statusFilter) }}
                <button type="button" wire:click="$set('statusFilter', '')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                    <span class="sr-only">Remove filter</span>
                    <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
            @endif

            <button
                wire:click="clearAllFilters"
                type="button"
                class="ml-auto text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
            >
                Clear all filters
            </button>
        </div>
    </div>
    @endif

    <x-card key="appointment-index-table" class="overflow-x-auto mt-4">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="p-4 w-4">
                        <input
                            type="checkbox"
                            wire:model.live="selectAll"
                            wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            aria-label="Select all appointments"
                        >
                    </th>
                    <th wire:click="sortBy('user_id')" class="cursor-pointer px-6 py-3">
                        Author {{ $sortField === 'user_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('campaign_id')" class="cursor-pointer px-6 py-3">
                        Campaign {{ $sortField === 'campaign_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('customer_id')" class="cursor-pointer px-6 py-3">
                        Customer {{ $sortField === 'customer_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('status')" class="cursor-pointer px-6 py-3">
                        Status {{ $sortField === 'status' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                </tr>
            </thead>
            <tbody>
                @forelse ($appointments as $appointment)
                    <tr wire:key="appointment-{{ $appointment->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="p-4 w-4">
                            <input
                                type="checkbox"
                                wire:change="toggleAppointmentSelection({{ $appointment->id }})"
                                wire:key="checkbox-{{ $appointment->id }}-{{ in_array($appointment->id, $selectedAppointments) ? 'checked' : 'unchecked' }}"
                                value="{{ $appointment->id }}"
                                {{ in_array($appointment->id, $selectedAppointments) ? 'checked' : '' }}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                aria-label="Select appointment {{ $appointment->name }}"
                            >
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-appointment-show', { appointment: {{ $appointment->id }} })">
                            {{ $appointment->user->getFullNameAttribute() }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-appointment-show', { appointment: {{ $appointment->id }} })">
                            {{ $appointment->campaign->name ?? 'N/A' }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-appointment-show', { appointment: {{ $appointment->id }} })">
                            {{ $appointment->customer->name ?? 'N/A' }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-appointment-show', { appointment: {{ $appointment->id }} })">
                            <div class="flex items-center">
                                <div class="h-2.5 w-2.5 rounded-full
                                    @if(in_array($appointment->status, ['pending']))
                                        bg-yellow-500
                                    @elseif(in_array($appointment->status, ['validated', 'active', 'actif', 'completed']))
                                        bg-green-500
                                    @elseif(in_array($appointment->status, ['rejected', 'cancelled']))
                                        bg-red-500
                                    @else
                                        bg-gray-500
                                    @endif
                                    me-2">
                                </div>
                                {{ ucfirst($appointment->status) }}
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No appointments found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        <div class="p-4">
            {{ $appointments->links() }}
        </div>
    </x-card>
</x-content>

