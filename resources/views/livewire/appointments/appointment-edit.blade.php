<x-content>
    @if (session()->has('error'))
        <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
            <span class="font-medium">Error!</span> {{ session('error') }}
        </div>
    @endif

    @if (session()->has('message'))
        <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            <span class="font-medium">Success!</span> {{ session('message') }}
        </div>
    @endif

    <form wire:submit="$dispatch('update-appointment')">
        <x-content-header title="Edit appointment">
            <x-page-button type="save" label="Save" action="submit"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-appointment-show', { appointment: {{ $appointment->id }} })"/>
        </x-content-header>
        <x-content-body>
            {{-- general information --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">General information</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="campaign_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Camapgne</label>
                        <select wire:model.live="form.campaign_id" id="campaign_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select a campaign</option>
                            @foreach ($campaigns as $campaign)
                                <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                            @endforeach
                        </select>
                        @error('form.campaign_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    {{-- <div class="col-span-6 sm:col-span-3">
                        <label for="customer_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Customer</label>
                        <select wire:model.live="form.customer_id" id="customer_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select a customer</option>
                            @foreach ($customers as $customer)
                                <option value="{{ $customer->id }}">{{ $customer->name }}</option>
                            @endforeach
                        </select>
                        @error('form.customer_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div> --}}
                    <!-- Date Input -->
                    <div class="col-span-6 sm:col-span-3">
                        <label for="scheduled_at" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Date</label>
                        <input
                            wire:model="form.scheduled_at"
                            type="datetime-local"
                            id="scheduled_at"
                            class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                        />
                        @error('form.scheduled_at') <span class="text-red-600">{{ $message }}</span> @enderror
                    </div>

                    <!-- Agent Notes -->
                    <div class="col-span-6 sm:col-span-6">
                        <label for="agent_notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                        <textarea
                            wire:model="form.agent_notes"
                            id="agent_notes"
                            rows="4"
                            class="block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                            placeholder="Write your thoughts here..."
                        ></textarea>
                        @error('form.agent_notes') <span class="text-red-600">{{ $message }}</span> @enderror
                    </div>

                    <!-- Audio Upload -->
                    <div class="col-span-6">
                        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                            <h4 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Call Recording</h4>

                            <!-- Audio Upload and Preview Container -->
                            <div class="mb-4">
                                <!-- Dropzone for Audio Upload -->
                                <div class="flex items-center justify-center w-full mb-4">
                                    <label for="audio" class="flex flex-col items-center justify-center w-full h-40 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                            @if($form->audio && is_object($form->audio))
                                                <!-- Show file info when a file is selected -->
                                                <div class="flex flex-col items-center">
                                                    <svg class="w-10 h-10 mb-3 text-green-500 dark:text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 4 12l5 7m6-14 5 7-5 7"/>
                                                    </svg>
                                                    <p class="mb-2 text-sm font-semibold text-green-500 dark:text-green-400">New file selected</p>
                                                    <div class="flex items-center justify-center p-2 bg-green-50 dark:bg-gray-600 rounded-lg">
                                                        <svg class="w-5 h-5 mr-2 text-green-500 dark:text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 4h1.5L9 2.5h2L13.5 4H15a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1Z"/>
                                                        </svg>
                                                        <span class="text-sm text-gray-700 dark:text-gray-300 truncate max-w-[200px]">
                                                            {{ $form->audio->getClientOriginalName() }}
                                                        </span>
                                                        <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                                                            ({{ round($form->audio->getSize() / 1024, 1) }} KB)
                                                        </span>
                                                    </div>
                                                </div>
                                            @elseif($appointment->audio_path)
                                                <!-- Show existing file info -->
                                                <div class="flex flex-col items-center">
                                                    <svg class="w-10 h-10 mb-3 text-blue-500 dark:text-blue-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                                    </svg>
                                                    <p class="mb-2 text-sm font-semibold text-blue-500 dark:text-blue-400">Current file: {{ basename($appointment->audio_path) }}</p>
                                                    <div class="flex items-center justify-center p-2 bg-blue-50 dark:bg-gray-600 rounded-lg">
                                                        <svg class="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 14">
                                                            <path d="M5.5 0a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5ZM10 5a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7A.5.5 0 0 1 10 5ZM14.5 8a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5Z"/>
                                                        </svg>
                                                        <span class="text-sm text-blue-700 dark:text-blue-300">
                                                            Upload new file to replace
                                                        </span>
                                                    </div>
                                                </div>
                                            @else
                                                <!-- Default upload icon -->
                                                <svg class="w-10 h-10 mb-3 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                                </svg>
                                                <p class="mb-2 text-sm font-semibold text-gray-500 dark:text-gray-400">Click to upload or drag and drop</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">MP3, WAV, AAC, OGG or FLAC (MAX. 2MB)</p>
                                                <p class="mt-2 text-xs text-blue-500 dark:text-blue-400">Upload the recording of your conversation with the customer</p>
                                            @endif
                                        </div>
                                        <input
                                            wire:model.live="form.audio"
                                            id="audio"
                                            type="file"
                                            accept=".mp3,.wav,.aac,.ogg,.flac,audio/*"
                                            class="hidden"
                                        />
                                    </label>
                                </div>

                                <!-- Progress and Error Messages -->
                                <div class="mb-4">
                                    <!-- Show progress indicator during file upload -->
                                    <div wire:loading wire:target="form.audio" class="p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                                        <div class="flex items-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            <span>Processing audio file...</span>
                                        </div>
                                    </div>

                                    <!-- Error messages -->
                                    @error('form.audio')
                                        <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                                            <span class="font-medium">Error!</span> {{ $message }}
                                        </div>
                                    @enderror

                                    @if($uploadError)
                                        <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                                            <span class="font-medium">Error!</span> {{ $uploadError }}
                                        </div>
                                    @enderror

                                    @error('audio')
                                        <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                                            <span class="font-medium">Error!</span> {{ $message }}
                                        </div>
                                    @enderror

                                    @error('form')
                                        <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                                            <span class="font-medium">Error!</span> {{ $message }}
                                        </div>
                                    @enderror
                                </div>

                                <!-- Audio Preview Section -->
                                @if ($form->audio && is_object($form->audio) && method_exists($form->audio, 'temporaryUrl'))
                                    <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 mr-2 text-green-500 dark:text-green-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm3.982 13.982a1 1 0 0 1-1.414 0l-3.274-3.274A1.012 1.012 0 0 1 9 10V6a1 1 0 0 1 2 0v3.586l2.982 2.982a1 1 0 0 1 0 1.414Z"/>
                                                </svg>
                                                <h5 class="text-sm font-medium text-gray-900 dark:text-white">Audio ready for upload</h5>
                                            </div>

                                            <!-- Remove button -->
                                            <button
                                                type="button"
                                                wire:click="removeAudio"
                                                class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-600 dark:focus:ring-red-900"
                                            >
                                                <div class="flex items-center">
                                                    <svg class="w-3.5 h-3.5 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                                    </svg>
                                                    Remove
                                                </div>
                                            </button>
                                        </div>

                                        <!-- Audio file details -->
                                        <div class="flex items-center justify-between p-3 mb-3 bg-white rounded-lg border border-gray-200 dark:bg-gray-600 dark:border-gray-500">
                                            <div class="flex items-center">
                                                <svg class="w-8 h-8 mr-3 text-blue-500 dark:text-blue-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                                </svg>
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $form->audio->getClientOriginalName() }}</p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                                        Size: {{ round($form->audio->getSize() / 1024, 1) }} KB | Type: {{ strtoupper($form->audio->getClientOriginalExtension()) }}
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="inline-flex items-center">
                                                <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
                                                    Ready
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Information message -->
                                        <div class="flex p-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                                            <svg class="flex-shrink-0 inline w-4 h-4 mr-3 mt-[2px]" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                            </svg>
                                            <div>
                                                <span class="font-medium">Info:</span> The audio will be available for playback after saving the appointment.
                                            </div>
                                        </div>
                                    </div>
                                @elseif($appointment->audio_path)
                                    <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 mr-2 text-blue-500 dark:text-blue-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                                </svg>
                                                <h5 class="text-sm font-medium text-gray-900 dark:text-white">Current Audio Recording</h5>
                                            </div>

                                            <!-- Remove button -->
                                            <button
                                                type="button"
                                                wire:click="removeExistingAudio"
                                                class="text-red-700 hover:text-white border border-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-white dark:hover:bg-red-600 dark:focus:ring-red-900"
                                            >
                                                <div class="flex items-center">
                                                    <svg class="w-3.5 h-3.5 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                                                    </svg>
                                                    Remove
                                                </div>
                                            </button>
                                        </div>

                                        <!-- Audio player -->
                                        <div class="p-3 mb-3 bg-white rounded-lg border border-gray-200 dark:bg-gray-600 dark:border-gray-500">
                                            <div class="flex items-center justify-between mb-2">
                                                <div class="flex items-center">
                                                    <svg class="w-6 h-6 mr-2 text-blue-500 dark:text-blue-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 14">
                                                        <path d="M5.5 0a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5ZM10 5a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7A.5.5 0 0 1 10 5ZM14.5 8a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-1 0v-7a.5.5 0 0 1 .5-.5Z"/>
                                                    </svg>
                                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ basename($appointment->audio_path) }}</span>
                                                </div>
                                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                                    Current
                                                </span>
                                            </div>

                                            <!-- Enhanced audio player -->
                                            <div class="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                                                <audio controls class="w-full">
                                                    <source src="{{ asset('storage/' . $appointment->audio_path) }}" type="audio/mpeg">
                                                    <source src="{{ asset('storage/' . $appointment->audio_path) }}" type="audio/wav">
                                                    <source src="{{ asset('storage/' . $appointment->audio_path) }}" type="audio/ogg">
                                                    Your browser does not support audio playback.
                                                </audio>
                                            </div>
                                        </div>

                                        <!-- Information message -->
                                        <div class="flex p-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300" role="alert">
                                            <svg class="flex-shrink-0 inline w-4 h-4 mr-3 mt-[2px]" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                            </svg>
                                            <div>
                                                <span class="font-medium">Note:</span> Uploading a new file will replace this recording.
                                            </div>
                                        </div>
                                    </div>
                                @endif


                            </div>

                            <!-- Help Text -->
                            <div class="mt-4 flex p-4 text-sm text-gray-800 rounded-lg bg-gray-50 dark:bg-gray-800 dark:text-gray-300" role="alert">
                                <svg class="flex-shrink-0 inline w-4 h-4 mr-3 mt-[2px]" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                                </svg>
                                <div>
                                    <span class="font-medium">Note:</span> Upload the recording of your conversation with the customer. This will be reviewed by the quality control team. Make sure the audio is clear and all important details are audible.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
