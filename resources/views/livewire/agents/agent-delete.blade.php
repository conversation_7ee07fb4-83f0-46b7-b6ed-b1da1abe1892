<x-content>
    <form action="#">
        <x-content-header title="Delete agent">
            <x-page-button type="delete" label="Yes I'm sure" action="$dispatch('destroy-agent')"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-agent-show', { agent: {{ $agent->id }} })"/>
        </x-content-header>
        <x-content-body>
            <div class="p-4 text-center bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <svg class="w-16 h-16 mx-auto text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <h3 class="mt-5 mb-6 text-lg text-gray-500 dark:text-gray-400">Are you sure you want to delete this agent {{ $agent->name }}?</h3>
                <p class="mb-5 text-sm text-gray-500 dark:text-gray-400">This action cannot be undone. All agent data, including profile information, documents, and relationships will be permanently removed from the system.</p>

                <div class="flex items-center justify-center mb-4">
                    <input wire:model="forceDelete" wire:click="toggleForceDelete" id="force-delete-checkbox" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                    <label for="force-delete-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Force delete (removes all relationships)</label>
                </div>

                @if($forceDelete)
                    <div class="p-2 mb-4 text-xs text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        Warning: Force delete will remove all relationships associated with this agent before deletion. This action cannot be undone.
                    </div>
                @endif

                @if (session()->has('error'))
                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        <span class="font-medium">Error!</span> {{ session('error') }}
                    </div>
                @endif

                <div class="mt-6 p-4 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white">Agent Information</h4>
                    <ul class="space-y-2 text-sm text-left">
                        <li><strong>ID:</strong> {{ $agent->id }}</li>
                        <li><strong>Name:</strong> {{ $agent->first_name }} {{ $agent->last_name }}</li>
                        <li><strong>Email:</strong> {{ $agent->email }}</li>
                        <li><strong>Role:</strong> {{ $agent->role->name ?? 'None' }}</li>
                        <li><strong>Created:</strong> {{ $agent->created_at->format('M d, Y') }}</li>
                    </ul>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
