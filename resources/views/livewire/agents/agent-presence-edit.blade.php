<x-content>
    <form wire:submit.prevent="updateShift">
        <x-content-header title="Edit shift">
            <x-page-button type="save" label="Save" action="submit"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })"/>
        </x-content-header>
        <x-content-body>
            <!-- General information -->
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Agent Information</h3>
                        <div class="flex items-center mb-4">
                            <img class="w-10 h-10 rounded-full mr-4" src="https://avatar.iran.liara.run/public/{{$shift->user->id}}" alt="Agent image">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $shift->user->getFullNameAttribute() }}</p>
                                @if($shift->user->campaign)
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $shift->user->campaign->name }}</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-span-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Shift Details</h3>
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="shift-date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Record date</label>
                        <input readonly type="text" value="{{ \Carbon\Carbon::parse($shift->date)->format('M d, Y') }}" name="shift-date" id="shift-date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="login-time" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start time</label>
                        <input type="datetime-local" wire:model="form.login_time" name="login-time" id="login-time" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="00/00/00 00:00:00">
                        @error('form.login_time') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="logout-time" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">End time</label>
                        <input type="datetime-local" wire:model="form.logout_time" name="logout-time" id="logout-time" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="00/00/00 00:00:00">
                        @error('form.logout_time') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="break-duration" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Break duration (minutes)</label>
                        <input type="number" min=0 wire:model="form.break_duration" name="break-duration" id="break-duration" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="00">
                        @error('form.break_duration') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="hours-worked" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Hours worked</label>
                        <input type="number" step="0.01" min=0 wire:model="form.hours_worked" name="hours-worked" id="hours-worked" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="0.00">
                        @error('form.hours_worked') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6">
                        <div class="flex items-center">
                            <input id="is-validated" type="checkbox" wire:model="form.is_validated" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="is-validated" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Validate this shift</label>
                        </div>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Validating a shift confirms that the recorded hours are correct and approved.</p>
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
