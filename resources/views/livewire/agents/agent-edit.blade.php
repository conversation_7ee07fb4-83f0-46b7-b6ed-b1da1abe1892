<x-content>
    <form wire:submit="save">
        <x-content-header title="Edit Agent">
            <x-page-button type="save" label="Save" action="submit"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-agent-show', { agent: {{ $agent->id }} })"/>
        </x-content-header>

        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <span class="font-medium">Error!</span> {{ session('error') }}
            </div>
        @endif
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Left Column - Profile Picture and Agent Status -->
                <div class="col-span-1">
                    <!-- Profile Picture -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800"
                         wire:key="profile-picture-section-{{ $agent->id }}"
                         wire:loading.class="opacity-50"
                         wire:target="removeProfilePicture,form.profile_picture">
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <div class="relative mb-4 sm:mb-0 xl:mb-4 2xl:mb-0">
                                <label for="profilePictureInput" class="cursor-pointer block relative group">
                                    @if(isset($form['profile_picture']))
                                        <img class="object-cover rounded-lg w-28 h-28 border-2 border-gray-200 dark:border-gray-700 group-hover:opacity-75 transition-opacity"
                                            src="{{ $form['profile_picture']->temporaryUrl() }}"
                                            alt="Profile picture">
                                    @else
                                        <img class="object-cover rounded-lg w-28 h-28 border-2 border-gray-200 dark:border-gray-700 group-hover:opacity-75 transition-opacity"
                                            src="{{ $form['current_profile_picture'] }}"
                                            alt="Profile picture">
                                    @endif
                                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                        <div class="bg-primary-700 rounded-full p-2 text-white">
                                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M5.5 13a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 13H11V9.413l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13H5.5z"></path>
                                                <path d="M9 13h2v5a1 1 0 11-2 0v-5z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </label>
                                <input
                                    wire:model.live="form.profile_picture"
                                    type="file"
                                    id="profilePictureInput"
                                    class="hidden"
                                    accept="image/jpeg,image/png"
                                >
                                @if(isset($form['profile_picture']) || $form['current_profile_picture'] != '/images/users/default.png')
                                    <button type="button" wire:click="removeProfilePicture" class="absolute top-0 right-0 -mt-2 -mr-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none">
                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                @endif
                            </div>
                            <div>
                                <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">Profile picture</h3>
                                <div class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                    JPG, JPEG or PNG. Max size of 1MB
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    Click on the image to upload or change
                                </div>
                                @error('form.profile_picture') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Agent Status -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Agent Status</h3>
                        <div class="space-y-4">
                            <div>
                                <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                                <select wire:model="form.status" id="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="in_training">In Training</option>
                                    <option value="validated">Validated</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="engaged">Engaged</option>
                                </select>
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                    <span class="font-medium">Note:</span> "Validated" status is for agents who have completed training but aren't assigned to a campaign yet.
                                </p>
                                @error('form.status') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="campaign_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign Assignment</label>
                                <select wire:model="form.campaign_id" id="campaign_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Assigned</option>
                                    @foreach($campaigns as $campaign)
                                        <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                                    @endforeach
                                </select>
                                @error('form.campaign_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="registration_number" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Registration Number</label>
                                <input type="text" wire:model="form.registration_number" id="registration_number" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.registration_number') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="hire_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Hire Date</label>
                                <input type="date" wire:model="form.hire_date" id="hire_date" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.hire_date') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Training Information -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Training Information</h3>
                        <div class="space-y-4">
                            <div>
                                <label for="training_module_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Training Module</label>
                                <select wire:model="form.training_module_id" id="training_module_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Assigned</option>
                                    @foreach($trainingModules as $module)
                                        <option value="{{ $module->id }}">{{ $module->name }}</option>
                                    @endforeach
                                </select>
                                @error('form.training_module_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="training_start_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Training Start Date</label>
                                <input type="date" wire:model="form.training_start_date" id="training_start_date" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.training_start_date') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="training_completion" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Training Completion Date</label>
                                <input type="date" wire:model="form.training_completion_date" id="training_completion" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.training_completion_date') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="training_progress" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Training Progress (%)</label>
                                <input type="number" min="0" max="100" wire:model="form.training_progress" id="training_progress" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.training_progress') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="training_rating" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Training Rating (0-3)</label>
                                <input type="number" min="0" max="3" step="0.1" wire:model="form.training_rating" id="training_rating" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.training_rating') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="trainer_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Assigned Trainer</label>
                                <select wire:model="form.trainer_id" id="trainer_id" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not Assigned</option>
                                    @foreach($trainers as $trainer)
                                        <option value="{{ $trainer->id }}">{{ $trainer->first_name }} {{ $trainer->last_name }}</option>
                                    @endforeach
                                </select>
                                @error('form.trainer_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div class="flex items-center">
                                <input wire:model="form.training_validated" id="training_validated" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                <label for="training_validated" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Mark as Validated</label>
                            </div>
                        </div>
                    </div>

                    <!-- Password Update -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Update Password</h3>
                        <div class="space-y-4">
                            <div>
                                <label for="new_password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">New Password</label>
                                <input type="password" wire:model="form.new_password" id="new_password" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Leave blank to keep current password" value="">
                                @error('form.new_password') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>

                            <div>
                                <label for="new_password_confirmation" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Confirm New Password</label>
                                <input type="password" wire:model="form.new_password_confirmation" id="new_password_confirmation" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Leave blank to keep current password" value="">
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Leave blank to keep current password</p>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Personal & Performance Information -->
                <div class="col-span-2">
                    <!-- Personal Information -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Personal Information</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label for="first_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">First Name</label>
                                <input type="text" wire:model="form.first_name" id="first_name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.first_name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="last_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Last Name</label>
                                <input type="text" wire:model="form.last_name" id="last_name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.last_name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="birth_date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Birth Date</label>
                                <input type="date" wire:model="form.birth_date" id="birth_date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.birth_date') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                                <input type="email" wire:model="form.email" id="email" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.email') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="phone_number" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Phone Number</label>
                                <input type="text" wire:model="form.phone_number" id="phone_number" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.phone_number') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Address Information</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label for="country" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Country</label>
                                <input type="text" wire:model="form.country" id="country" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.country') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="city" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">City</label>
                                <input type="text" wire:model="form.city" id="city" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.city') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-span-6">
                                <label for="address" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Address</label>
                                <input type="text" wire:model="form.address" id="address" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.address') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Performance Targets -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Performance Targets</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label for="daily_appointment_target" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Daily Appointment Target</label>
                                <input type="number" min="0" wire:model="form.daily_appointment_target" id="daily_appointment_target" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.daily_appointment_target') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="weekly_hours_target" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Weekly Hours Target</label>
                                <input type="number" min="0" step="0.5" wire:model="form.weekly_hours_target" id="weekly_hours_target" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                @error('form.weekly_hours_target') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    {{-- Documents section --}}
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Employee Documents</h3>
                        <p class="mb-4 text-sm text-gray-500 dark:text-gray-400">Upload important employee documents such as resume, ID card, certificates, and other relevant documents.</p>

                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {{-- Resume upload --}}
                            <div>
                                <label for="resume" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Resume/CV</label>
                                <div class="flex items-center justify-center w-full">
                                    <label for="resume-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                            <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                            </svg>
                                            <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, DOCX (MAX. 5MB)</p>
                                        </div>
                                        <input id="resume-upload" wire:model="resume" type="file" class="hidden" accept=".pdf,.doc,.docx" />
                                    </label>
                                </div>
                                @error('resume') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                                @if($resume)
                                    <div class="mt-2 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                        <span class="text-sm truncate">{{ $resume->getClientOriginalName() }}</span>
                                        <button type="button" wire:click="$set('resume', null)" class="text-red-500 hover:text-red-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                @endif

                                @if(isset($current_documents['resume']))
                                    <div class="mt-2">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current Resume:</h4>
                                        @foreach($current_documents['resume'] as $document)
                                            <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                                <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                                <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'resume')" class="text-red-500 hover:text-red-700">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>

                            {{-- ID Card upload --}}
                            <div>
                                <label for="id_card" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">ID Card</label>
                                <div class="flex items-center justify-center w-full">
                                    <label for="id-card-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                            <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                            </svg>
                                            <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">PDF, JPG, JPEG, PNG (MAX. 2MB)</p>
                                        </div>
                                        <input id="id-card-upload" wire:model="id_card" type="file" class="hidden" accept=".pdf,.jpg,.jpeg,.png" />
                                    </label>
                                </div>
                                @error('id_card') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                                @if($id_card)
                                    <div class="mt-2 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                        <span class="text-sm truncate">{{ $id_card->getClientOriginalName() }}</span>
                                        <button type="button" wire:click="$set('id_card', null)" class="text-red-500 hover:text-red-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                @endif

                                @if(isset($current_documents['id_card']))
                                    <div class="mt-2">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current ID Card:</h4>
                                        @foreach($current_documents['id_card'] as $document)
                                            <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                                <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                                <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'id_card')" class="text-red-500 hover:text-red-700">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>

                        {{-- Certificates upload --}}
                        <div class="mt-6">
                            <label for="certificates" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Certificates</label>
                            <div class="flex items-center justify-center w-full">
                                <label for="certificates-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                        <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                        </svg>
                                        <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">PDF, JPG, JPEG, PNG (MAX. 5MB each) - Multiple files allowed</p>
                                    </div>
                                    <input id="certificates-upload" wire:model="certificates" type="file" class="hidden" accept=".pdf,.jpg,.jpeg,.png" multiple />
                                </label>
                            </div>
                            @error('certificates.*') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                            @if(!empty($certificates))
                                <div class="mt-2 space-y-2">
                                    @foreach($certificates as $index => $certificate)
                                        <div class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                            <span class="text-sm truncate">{{ $certificate->getClientOriginalName() }}</span>
                                            <button type="button" wire:click="removeCertificate({{ $index }})" class="text-red-500 hover:text-red-700">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            @if(isset($current_documents['certificate']))
                                <div class="mt-2">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current Certificates:</h4>
                                    @foreach($current_documents['certificate'] as $document)
                                        <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                            <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                            <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'certificate')" class="text-red-500 hover:text-red-700">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>

                        {{-- Other documents upload --}}
                        <div class="mt-6">
                            <label for="other_documents" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Other Documents</label>
                            <div class="flex items-center justify-center w-full">
                                <label for="other-documents-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                    <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                        <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                        </svg>
                                        <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, DOCX, JPG, JPEG, PNG, XLS, XLSX (MAX. 10MB each) - Multiple files allowed</p>
                                    </div>
                                    <input id="other-documents-upload" wire:model="other_documents" type="file" class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx" multiple />
                                </label>
                            </div>
                            @error('other_documents.*') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                            @if(!empty($other_documents))
                                <div class="mt-2 space-y-2">
                                    @foreach($other_documents as $index => $document)
                                        <div class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                            <span class="text-sm truncate">{{ $document->getClientOriginalName() }}</span>
                                            <button type="button" wire:click="removeOtherDocument({{ $index }})" class="text-red-500 hover:text-red-700">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            @if(isset($current_documents['other_document']))
                                <div class="mt-2">
                                    <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current Other Documents:</h4>
                                    @foreach($current_documents['other_document'] as $document)
                                        <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                            <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                            <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'other_document')" class="text-red-500 hover:text-red-700">
                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
