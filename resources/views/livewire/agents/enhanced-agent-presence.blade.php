<x-content>
    <!-- Dashboard Metrics -->
    <div class="grid grid-cols-1 gap-4 mb-4 sm:grid-cols-2 lg:grid-cols-6">
        <!-- Total Shifts Card -->
        <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Total Shifts</span>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $metrics['total_shifts'] }}</h3>
                </div>
                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900">
                    <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- On-Time Logins Card -->
        <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">On-Time</span>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $metrics['on_time_logins'] }}</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $metrics['total_shifts'] > 0 ? round(($metrics['on_time_logins'] / $metrics['total_shifts']) * 100) : 0 }}% of shifts
                    </p>
                </div>
                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 dark:bg-green-900">
                    <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Late Logins Card -->
        <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Late</span>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $metrics['late_logins'] }}</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $metrics['total_shifts'] > 0 ? round(($metrics['late_logins'] / $metrics['total_shifts']) * 100) : 0 }}% of shifts
                    </p>
                </div>
                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 dark:bg-red-900">
                    <svg class="w-6 h-6 text-red-600 dark:text-red-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Early Logouts Card -->
        <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Early Out</span>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $metrics['early_logouts'] }}</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $metrics['total_shifts'] > 0 ? round(($metrics['early_logouts'] / $metrics['total_shifts']) * 100) : 0 }}% of shifts
                    </p>
                </div>
                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900">
                    <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Adherence Card -->
        <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Adherence</span>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ number_format($metrics['adherence_percentage'], 1) }}%</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ $metrics['adherence_percentage'] >= 95 ? 'Excellent' :
                           ($metrics['adherence_percentage'] >= 90 ? 'Good' :
                           ($metrics['adherence_percentage'] >= 85 ? 'Average' : 'Poor')) }}
                    </p>
                </div>
                <div class="flex items-center justify-center w-10 h-10 rounded-full
                    {{ $metrics['adherence_percentage'] >= 95 ? 'bg-green-100 dark:bg-green-900' :
                       ($metrics['adherence_percentage'] >= 90 ? 'bg-blue-100 dark:bg-blue-900' :
                       ($metrics['adherence_percentage'] >= 85 ? 'bg-yellow-100 dark:bg-yellow-900' : 'bg-red-100 dark:bg-red-900')) }}">
                    <svg class="w-6 h-6
                        {{ $metrics['adherence_percentage'] >= 95 ? 'text-green-600 dark:text-green-300' :
                           ($metrics['adherence_percentage'] >= 90 ? 'text-blue-600 dark:text-blue-300' :
                           ($metrics['adherence_percentage'] >= 85 ? 'text-yellow-600 dark:text-yellow-300' : 'text-red-600 dark:text-red-300')) }}"
                        fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Average Break Time Card -->
        <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <span class="text-base font-normal text-gray-500 dark:text-gray-400">Avg Break</span>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $metrics['average_break_time'] }} min</h3>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                        {{ floor($metrics['average_break_time'] / 60) }}h {{ $metrics['average_break_time'] % 60 }}m
                    </p>
                </div>
                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900">
                    <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Content action Button (Agent Only) -->
    @if(auth()->user()->role_id === 6)
        @php
            $shift = $this->currentShift;
        @endphp
        <div class="flex flex-col gap-4 mb-6">
            {{-- 1) Not started yet --}}
            @if (!$shift)
                <x-card class="flex justify-between items-center gap-2 p-4">
                    <button
                        wire:click="startDay"
                        class="w-[300px] text-white bg-green-700 hover:bg-green-800 rounded-lg px-5 py-2.5"
                    >
                        Start Shift
                    </button>
                    <p class="font-semibold dark:text-white">Mark the start of your shift.</p>
                </x-card>
            {{-- 2) Shift in progress --}}
            @elseif (is_null($shift->logout_time))
                {{-- 2a) On break --}}
                @if($onBreak)
                    <x-card class="flex justify-between items-center gap-2 p-4">
                        <button
                            wire:click="endBreak"
                            class="w-[300px] text-white bg-yellow-700 hover:bg-yellow-800 rounded-lg px-5 py-2.5"
                        >
                            End {{ ucfirst($currentBreakType) }} Break
                        </button>
                        <p class="font-semibold dark:text-white">Mark your return from break.</p>
                    </x-card>
                {{-- 2b) Not on break --}}
                @else
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <x-card class="flex flex-col justify-between items-center gap-2 p-4">
                            <h3 class="text-lg font-semibold mb-2">Break Options</h3>
                            <div class="flex flex-wrap gap-2 justify-center">
                                <button
                                    wire:click="startBreak"
                                    class="text-white bg-blue-700 hover:bg-blue-800 rounded-lg px-4 py-2"
                                >
                                    Standard Break
                                </button>
                                <button
                                    wire:click="startLunchBreak"
                                    class="text-white bg-green-700 hover:bg-green-800 rounded-lg px-4 py-2"
                                >
                                    Lunch Break
                                </button>
                                <button
                                    wire:click="startTrainingBreak"
                                    class="text-white bg-purple-700 hover:bg-purple-800 rounded-lg px-4 py-2"
                                >
                                    Training Break
                                </button>
                                <button
                                    wire:click="startPersonalBreak"
                                    class="text-white bg-orange-700 hover:bg-orange-800 rounded-lg px-4 py-2"
                                >
                                    Personal Break
                                </button>
                            </div>
                        </x-card>

                        <x-card class="flex justify-between items-center gap-2 p-4">
                            <button
                                wire:click="endDay"
                                class="w-full text-white bg-red-700 hover:bg-red-800 rounded-lg px-5 py-2.5"
                            >
                                End Shift
                            </button>
                            <p class="font-semibold dark:text-white">Mark the end of your shift.</p>
                        </x-card>
                    </div>
                @endif
            @endif
        </div>
    @endif

    <!-- Filters and Actions -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
        <!-- Left side: Filters -->
        <div class="flex flex-wrap gap-2 items-center">
            <!-- Date Filter -->
            <select wire:model="dateFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="this_week">This Week</option>
                <option value="last_week">Last Week</option>
                <option value="this_month">This Month</option>
                <option value="last_month">Last Month</option>
            </select>

            <!-- Status Filter -->
            <select wire:model="statusFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="exception">Exception</option>
            </select>

            <!-- Shift Type Filter -->
            <select wire:model="shiftTypeFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="">All Shift Types</option>
                <option value="regular">Regular</option>
                <option value="overtime">Overtime</option>
                <option value="training">Training</option>
                <option value="meeting">Meeting</option>
            </select>

            <!-- Adherence Filter -->
            <select wire:model="adherenceFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="">All Adherence</option>
                <option value="excellent">Excellent (≥95%)</option>
                <option value="good">Good (90-94.9%)</option>
                <option value="average">Average (85-89.9%)</option>
                <option value="poor">Poor (<85%)</option>
            </select>
        </div>

        <!-- Right side: Actions -->
        <div class="flex gap-2">
            @if(count($selectedShifts) > 0)
                <button wire:click="deleteSelected" class="text-white bg-red-700 hover:bg-red-800 rounded-lg px-4 py-2 text-sm">
                    Delete Selected
                </button>
            @endif

            <!-- Per Page Selector -->
            <select wire:model="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="10">10 per page</option>
                <option value="25">25 per page</option>
                <option value="50">50 per page</option>
                <option value="100">100 per page</option>
            </select>
        </div>
    </div>

<!-- Shifts Table -->
<div class="relative overflow-x-auto shadow-md sm:rounded-lg">
    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="p-4">
                    <div class="flex items-center">
                        <input id="checkbox-all" wire:model="selectAll" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="checkbox-all" class="sr-only">checkbox</label>
                    </div>
                </th>
                <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('date')">
                    Date
                    @if($sortField === 'date')
                        <span class="ml-1">
                            @if($sortDirection === 'asc')
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            @else
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            @endif
                        </span>
                    @endif
                </th>
                <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('user_name')">
                    Agent
                    @if($sortField === 'user_name')
                        <span class="ml-1">
                            @if($sortDirection === 'asc')
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            @else
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            @endif
                        </span>
                    @endif
                </th>
                <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('login_time')">
                    Login
                    @if($sortField === 'login_time')
                        <span class="ml-1">
                            @if($sortDirection === 'asc')
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            @else
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            @endif
                        </span>
                    @endif
                </th>
                <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('logout_time')">
                    Logout
                    @if($sortField === 'logout_time')
                        <span class="ml-1">
                            @if($sortDirection === 'asc')
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            @else
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            @endif
                        </span>
                    @endif
                </th>
                <th scope="col" class="px-6 py-3">
                    Breaks
                </th>
                <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('hours_worked')">
                    Hours
                    @if($sortField === 'hours_worked')
                        <span class="ml-1">
                            @if($sortDirection === 'asc')
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            @else
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            @endif
                        </span>
                    @endif
                </th>
                <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('adherence_percentage')">
                    Adherence
                    @if($sortField === 'adherence_percentage')
                        <span class="ml-1">
                            @if($sortDirection === 'asc')
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            @else
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            @endif
                        </span>
                    @endif
                </th>
                <th scope="col" class="px-6 py-3 cursor-pointer" wire:click="sortBy('status')">
                    Status
                    @if($sortField === 'status')
                        <span class="ml-1">
                            @if($sortDirection === 'asc')
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            @else
                                <svg class="w-3 h-3 inline" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
                            @endif
                        </span>
                    @endif
                </th>
                <th scope="col" class="px-6 py-3">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody>
            @forelse($shifts as $shift)
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <td class="w-4 p-4">
                        <div class="flex items-center">
                            <input id="checkbox-{{ $shift->id }}" wire:model="selectedShifts" wire:click="toggleShiftSelection({{ $shift->id }})" value="{{ $shift->id }}" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="checkbox-{{ $shift->id }}" class="sr-only">checkbox</label>
                        </div>
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                        {{ \Carbon\Carbon::parse($shift->date)->format('M d, Y') }}
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8">
                                <img class="h-8 w-8 rounded-full" src="https://avatar.iran.liara.run/public/{{ $shift->user_id }}" alt="">
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $shift->user->getFullNameAttribute() }}</p>
                                @if($shift->user->campaign)
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $shift->user->campaign->name }}</p>
                                @endif
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                        <div>
                            {{ $shift->login_time ? \Carbon\Carbon::parse($shift->login_time)->format('H:i') : '-' }}
                            @if($shift->scheduled_start_time)
                                @php
                                    $diffMinutes = $shift->login_time->diffInMinutes($shift->scheduled_start_time, false);
                                    $onTime = abs($diffMinutes) <= 5;
                                    $early = $diffMinutes < -5;
                                    $late = $diffMinutes > 5;
                                @endphp
                                <span class="text-xs ml-1
                                    {{ $onTime ? 'text-green-500 dark:text-green-400' :
                                      ($early ? 'text-blue-500 dark:text-blue-400' :
                                      'text-red-500 dark:text-red-400') }}">
                                    @if($onTime)
                                        (On Time)
                                    @elseif($early)
                                        ({{ abs($diffMinutes) }}m early)
                                    @else
                                        ({{ $diffMinutes }}m late)
                                    @endif
                                </span>
                            @endif
                        </div>
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                        <div>
                            {{ $shift->logout_time ? \Carbon\Carbon::parse($shift->logout_time)->format('H:i') : 'In progress' }}
                            @if($shift->logout_time && $shift->scheduled_end_time)
                                @php
                                    $diffMinutes = $shift->logout_time->diffInMinutes($shift->scheduled_end_time, false);
                                    $onTime = abs($diffMinutes) <= 5;
                                    $early = $diffMinutes < -5;
                                    $late = $diffMinutes > 5;
                                @endphp
                                <span class="text-xs ml-1
                                    {{ $onTime ? 'text-green-500 dark:text-green-400' :
                                      ($early ? 'text-red-500 dark:text-red-400' :
                                      'text-blue-500 dark:text-blue-400') }}">
                                    @if($onTime)
                                        (On Time)
                                    @elseif($early)
                                        ({{ abs($diffMinutes) }}m early)
                                    @else
                                        ({{ $diffMinutes }}m late)
                                    @endif
                                </span>
                            @endif
                        </div>
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                        <div class="flex flex-col">
                            <span class="font-medium">{{ $shift->total_break_time }} min total</span>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                @if($shift->break_duration > 0)
                                    <span class="mr-2">Standard: {{ $shift->break_duration }}m</span>
                                @endif
                                @if($shift->lunch_duration > 0)
                                    <span class="mr-2">Lunch: {{ $shift->lunch_duration }}m</span>
                                @endif
                                @if($shift->training_duration > 0)
                                    <span class="mr-2">Training: {{ $shift->training_duration }}m</span>
                                @endif
                                @if($shift->personal_duration > 0)
                                    <span>Personal: {{ $shift->personal_duration }}m</span>
                                @endif
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                        @if($shift->hours_worked)
                            {{ $shift->hours_worked }}h
                        @else
                            -
                        @endif
                    </td>
                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                        @if($shift->adherence_percentage)
                            <div class="flex items-center">
                                <div class="h-2.5 w-2.5 rounded-full bg-{{ $shift->adherence_color }}-500 mr-2"></div>
                                {{ number_format($shift->adherence_percentage, 1) }}%
                            </div>
                        @else
                            -
                        @endif
                    </td>
                    <td class="px-6 py-4">
                        <span class="px-2 py-1 text-xs font-medium rounded-full
                            {{ $shift->status === 'approved' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                              ($shift->status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                              ($shift->status === 'exception' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                              'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300')) }}">
                            {{ ucfirst($shift->status) }}
                        </span>
                    </td>
                    <td class="px-6 py-4">
                        <div class="flex space-x-2">
                            <button wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">View</button>

                            @if(auth()->user()->role_id <= 3 && $shift->status === 'pending')
                                <button wire:click="approveShift({{ $shift->id }})" class="font-medium text-green-600 dark:text-green-500 hover:underline">Approve</button>
                                <button wire:click="rejectShift({{ $shift->id }})" class="font-medium text-red-600 dark:text-red-500 hover:underline">Reject</button>
                            @endif
                        </div>
                    </td>
                </tr>
            @empty
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                    <td colspan="10" class="px-6 py-4 text-center">No shifts found</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Pagination -->
<div class="mt-4">
    {{ $shifts->links() }}
</div>
</x-content>