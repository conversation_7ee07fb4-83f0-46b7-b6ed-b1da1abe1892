<x-content>
    <div>
        <x-content-header title="Shift Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-agent-presence-edit', { shift: {{ $shift->id }} })"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-agent-presence-delete', { shift: {{ $shift->id }} })"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-agent-presence')"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Right Content -->
                <div class="col-span-full xl:col-auto">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <img class="mb-4 rounded-lg w-28 h-28 sm:mb-0 xl:mb-4 2xl:mb-0" src="https://avatar.iran.liara.run/public/{{$shift->user->id}}" alt="Jese picture">
                            <div>
                                <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">{{ $shift->user->getFullNameAttribute() }}</h3>
                                <p class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">Date: {{ \Carbon\Carbon::parse($shift->date)->format('M d, Y') }}</p>
                                @if($shift->user->campaign)
                                    <p class="mb-2 text-base font-normal text-gray-500 dark:text-gray-400">Campaign: {{ $shift->user->campaign->name }}</p>
                                @endif
                                <div class="flex items-center mb-4">
                                    <div class="mr-2">Status:</div>
                                    @if($shift->is_validated)
                                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                            Validated
                                        </span>
                                    @else
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                                            Pending
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Left Content -->
                <div class="col-span-2">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flow-root">
                            <h3 class="text-xl font-semibold dark:text-white">Timeline</h3>
                            <ol class="relative mt-4 border-l border-gray-200 dark:border-gray-700">
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-blue-600 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-blue-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Record date</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($shift->created_at)->format('M d, Y H:i') }}</h3>
                                </li>
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-green-600 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-green-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Start time</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $shift->login_time ? \Carbon\Carbon::parse($shift->login_time)->format('H:i') : 'Not started' }}</h3>
                                    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">
                                        {{ $shift->login_time ? \Carbon\Carbon::parse($shift->login_time)->format('l, F j, Y') : '' }}
                                    </p>
                                </li>

                                @if($shift->on_break)
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-yellow-600 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-yellow-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Currently on break</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                                            Break in progress
                                        </span>
                                    </h3>
                                </li>
                                @endif

                                @if($shift->logout_time)
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-red-600 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-red-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">End time</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($shift->logout_time)->format('H:i') }}</h3>
                                    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">
                                        {{ \Carbon\Carbon::parse($shift->logout_time)->format('l, F j, Y') }}
                                    </p>
                                </li>
                                @else
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">End time</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                                            In progress
                                        </span>
                                    </h3>
                                </li>
                                @endif

                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-purple-600 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-purple-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Break duration</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $shift->break_duration }} minutes</h3>
                                </li>

                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-indigo-600 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-indigo-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Hours worked</time>
                                    @if($shift->hours_worked)
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $shift->hours_worked }} hours</h3>
                                    @elseif($shift->login_time && $shift->logout_time)
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                            {{ \Carbon\Carbon::parse($shift->login_time)->diffInHours(\Carbon\Carbon::parse($shift->logout_time)) }} hours
                                        </h3>
                                    @else
                                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">-</h3>
                                    @endif
                                </li>

                                <li class="ml-4">
                                    <div class="absolute w-3 h-3 bg-{{ $shift->is_validated ? 'green' : 'yellow' }}-600 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-{{ $shift->is_validated ? 'green' : 'yellow' }}-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Validation status</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        @if($shift->is_validated)
                                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                                Validated
                                            </span>
                                        @else
                                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                                                Pending validation
                                            </span>
                                        @endif
                                    </h3>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>


