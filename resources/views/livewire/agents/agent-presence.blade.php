<x-content>
    <!-- Agent View -->
    @if(auth()->user()->role_id === 6)
        <div class="flex justify-between items-start gap-4" x-data="{ startDay:true, endDay:false, startBreak:false, endBreak:false}">
            <div class="flex flex-auto flex-col gap-4 w-full text-gray-500">
                <!-- Shift Controls -->
                @php $shift = $this->currentShift; @endphp
                
                @if (!$shift)
                    <x-card key="shift-step" class="flex justify-between items-center gap-2 p-4">
                        <button wire:click="startDay" class="w-[300px] text-white bg-green-700 hover:bg-green-800 rounded-lg px-5 py-2.5">
                            Commencer la journée
                        </button>
                        <p class="font-semibold dark:text-white">Mar<PERSON> votre début de journée.</p>
                    </x-card>
                @elseif (is_null($shift->logout_time))
                    @if($onBreak)
                        <x-card key="shift-step" class="flex justify-between items-center gap-2 p-4">
                            <button wire:click="endBreak" class="w-[300px] text-white bg-yellow-700 hover:bg-yellow-800 rounded-lg px-5 py-2.5">
                                Retour de pause
                            </button>
                            <p class="font-semibold dark:text-white">Marquez votre retour de pause.</p>
                        </x-card>
                    @else
                        <div class="space-y-4">
                            <x-card key="shift-step" class="flex justify-between items-center gap-2 p-4">
                                <button wire:click="startBreak" class="w-[300px] text-white bg-blue-700 hover:bg-blue-800 rounded-lg px-5 py-2.5">
                                    Prendre une pause
                                </button>
                                <p class="font-semibold dark:text-white">Marquez votre début de pause.</p>
                            </x-card>
                            <x-card key="shift-step" class="flex justify-between items-center gap-2 p-4">
                                <button wire:click="endDay" class="w-[300px] text-white bg-red-700 hover:bg-red-800 rounded-lg px-5 py-2.5">
                                    Fin de la journée
                                </button>
                                <p class="font-semibold dark:text-white">Marquez votre fin de journée.</p>
                            </x-card>
                        </div>
                    @endif
                @else
                    <x-card key="shift-step" class="flex justify-between items-center gap-2 p-4">
                        <p class="font-semibold dark:text-white">Journée terminée.</p>
                    </x-card>
                @endif
            </div>
            
            <!-- Shift Summary -->
            <x-card key="resume" class="flex-1/3 h-full w-full p-4">
                <h3 class="text-lg font-semibold dark:text-white">Résumé</h3>
                <ul class="mt-4 flex flex-col gap-3 text-gray-500 dark:text-gray-400">
                    <li class="font-semibold dark:text-white">
                        📅 Date : {{ optional($this->currentShift)->date ?? now()->toDateString() }}
                    </li>
                    <li class="font-semibold dark:text-white">
                        ⏳ Statut :
                        @if (! $shift)
                            Non démarré
                        @elseif ($this->breakStartedAt)
                            En pause
                        @elseif (! $shift->logout_time)
                            En cours
                        @else
                            Terminé
                        @endif
                    </li>
                    @if ($shift)
                        <li class="font-semibold dark:text-white">
                            🕒 Arrivée : {{ \Carbon\Carbon::parse($shift->login_time)->format('H:i:s') }}
                        </li>
                        <li class="font-semibold dark:text-white">
                            🕒 Pauses totales : {{ $shift->break_duration }} min
                        </li>
                        @if ($shift->logout_time)
                            <li class="font-semibold dark:text-white">
                                🕓 Départ : {{ \Carbon\Carbon::parse($shift->logout_time)->format('H:i:s') }}
                            </li>
                            <li class="font-semibold dark:text-white">
                                ⏲️ Heures travaillées : {{ $shift->hours_worked }} h
                            </li>
                        @endif
                    @endif
                </ul>
            </x-card>
        </div>
    @endif

        <!-- Content action Button (Manager and Higther Only) -->
        @if(auth()->user()->role_id !== 6)
            <x-card key="shift-index-header" class="p-4">
                <div class="w-full">
                    <div class="sm:flex">
                        <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                            @if(auth()->user()->role_id !== 6)
                                <div class="lg:pr-3">
                                    <label for="shifts-search" class="sr-only">Search</label>
                                    <div x-data="{ open: false }" class="relative mt-1 lg:w-64 xl:w-96">
                                        <input
                                            id="shifts-search"
                                            type="text"
                                            x-on:focus="open = true"
                                            x-on:click.away="open = false"
                                            wire:model.live="agentSearch"
                                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                            placeholder="Search by agent"
                                        />
                                        <ul
                                            x-show="open"
                                            x-cloak
                                            class="absolute z-10 mt-1 w-full text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                        >
                                            @forelse($filteredAgents as $agent)
                                                <li
                                                    wire:click="selectAgent({{ $agent->id }})"
                                                    x-on:click="open = false"
                                                    class="px-2 py-1 hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-700"
                                                >
                                                    {{ $agent->first_name }} {{ $agent->last_name }}
                                                </li>
                                            @empty
                                                <li class="w-full p-4 text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                                                    No agent found.
                                                </li>
                                            @endforelse
                                        </ul>
                                    </div>
                                </div>
                            @endif
                             <div class="flex flex-wrap sm:flex-nowrap pl-0 mt-3 space-x-1 sm:space-x-2 sm:pl-2 sm:mt-0">
                                <select wire:model.live="dateRangeFilter" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <option value="today">Today</option>
                                    <option value="yesterday">Yesterday</option>
                                    <option value="this_week">This Week</option>
                                    <option value="last_week">Last Week</option>
                                    <option value="this_month">This Month</option>
                                    <option value="custom">Custom Range</option>
                                </select>
                                @if($dateRangeFilter === 'custom')
                                    <div class="flex flex-wrap sm:flex-nowrap space-x-2">
                                        <div class="relative w-full sm:w-auto">
                                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                                    <path d="M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V12zM6 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H6zM7.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8a.75.75 0 01-.75-.75V12zM8 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H8zM9.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V10zM10 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H10zM9.25 14a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V14zM12 9.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V10a.75.75 0 00-.75-.75H12zM11.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12a.75.75 0 01-.75-.75V12zM12 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H12zM13.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H14a.75.75 0 01-.75-.75V10zM14 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H14z"></path>
                                                    <path clip-rule="evenodd" fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"></path>
                                                </svg>
                                            </div>
                                            <input
                                                type="date"
                                                wire:model.live="startDate"
                                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 sm:w-48 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                placeholder="Start date"
                                            />
                                        </div>
                                        <div class="relative w-full sm:w-auto">
                                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                                    <path d="M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V12zM6 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H6zM7.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8a.75.75 0 01-.75-.75V12zM8 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H8zM9.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V10zM10 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H10zM9.25 14a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V14zM12 9.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V10a.75.75 0 00-.75-.75H12zM11.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12a.75.75 0 01-.75-.75V12zM12 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H12zM13.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H14a.75.75 0 01-.75-.75V10zM14 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H14z"></path>
                                                    <path clip-rule="evenodd" fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"></path>
                                                </svg>
                                            </div>
                                            <input
                                                type="date"
                                                wire:model.live="endDate"
                                                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 sm:w-48 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                placeholder="End date"
                                            />
                                        </div>
                                    </div>
                                @endif
                                <button
                                    wire:click="deleteSelected"
                                    wire:confirm="Are you sure you want to delete the selected shifts?"
                                    class="inline-flex justify-center p-1 text-gray-500 rounded {{$selectedShifts ? 'cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white' : 'opacity-50 cursor-not-allowed'}}"
                                    {{$selectedShifts ? '' : 'disabled'}}
                                >
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                            <a href="#" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                                <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path></svg>
                                Export
                            </a>
                            <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                @foreach ([5, 10, 25, 50, 100] as $value)
                                    <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </x-card>
            <x-content-body>
                <x-card key="shift-index-table" class="overflow-x-auto mt-4">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="p-4 w-4">
                                    <input
                                        type="checkbox"
                                        wire:model.live="selectAll"
                                        wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                        aria-label="Select all shifts"
                                    >
                                </th>
                                <th wire:click="sortBy('date')" class="cursor-pointer px-6 py-3">
                                    Date {{ $sortField === 'date' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                </th>
                                <th wire:click="sortBy('user_name')" class="cursor-pointer px-6 py-3">
                                    Agent {{ $sortField === 'user_name' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                </th>
                                <th wire:click="sortBy('start_day')" class="cursor-pointer px-6 py-3">
                                    Day Start {{ $sortField === 'start_day' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                </th>
                                <th wire:click="sortBy('end_day')" class="cursor-pointer px-6 py-3">
                                    Day End {{ $sortField === 'end_day' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                </th>
                                <th wire:click="sortBy('break_duration')" class="cursor-pointer px-6 py-3">
                                    Break duration {{ $sortField === 'break_duration' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                </th>
                                <th wire:click="sortBy('hours_worked')" class="cursor-pointer px-6 py-3">
                                    Hours Worked {{ $sortField === 'hours_worked' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                </th>
                                <th wire:click="sortBy('is_validated')" class="cursor-pointer px-6 py-3">
                                    Status {{ $sortField === 'is_validated' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                </th>
                                <!-- Content action Button (Manager Only) -->
                                @if(auth()->user()->role_id === 3)
                                <th class="px-6 py-3">
                                    Action
                                </th>
                                @endif
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($shifts as $shift)
                                <tr wire:key="shift-{{ $shift->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <td class="p-4 w-4">
                                        <input
                                            type="checkbox"
                                            wire:change="toggleShiftSelection({{ $shift->id }})"
                                            wire:key="checkbox-{{ $shift->id }}-{{ in_array($shift->id, $selectedShifts) ? 'checked' : 'unchecked' }}"
                                            value="{{ $shift->id }}"
                                            {{ in_array($shift->id, $selectedShifts) ? 'checked' : '' }}
                                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                            aria-label="Select shift {{ $shift->user->getFullNameAttribute()}}"
                                        >
                                    </td>
                                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                                        {{ \Carbon\Carbon::parse($shift->date)->format('M d, Y') }}
                                    </td>
                                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8">
                                                <img class="h-8 w-8 rounded-full"  src="{{ $shift->user->getProfilePictureUrl() }}" alt="">
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $shift->user->getFullNameAttribute() }}</p>
                                                @if($shift->user->campaign)
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $shift->user->campaign->name }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                                        {{ $shift->login_time ? \Carbon\Carbon::parse($shift->login_time)->format('H:i') : '-' }}
                                    </td>
                                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                                        {{ $shift->logout_time ? \Carbon\Carbon::parse($shift->logout_time)->format('H:i') : 'In progress' }}
                                    </td>
                                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                                        {{ $shift->break_duration }} min
                                    </td>
                                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                                        @if($shift->hours_worked)
                                            {{ $shift->hours_worked }}h
                                        @elseif($shift->login_time && $shift->logout_time)
                                            {{ \Carbon\Carbon::parse($shift->login_time)->diffInHours(\Carbon\Carbon::parse($shift->logout_time)) }}h
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-agent-presence-show', { shift: {{ $shift->id }} })">
                                        @if($shift->is_validated)
                                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                                Validated
                                            </span>
                                        @else
                                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                                                Pending
                                            </span>
                                        @endif
                                    </td>
                                    <!-- Content action Button (Manager Only) -->
                                    @if(auth()->user()->role_id === 3)
                                    <td
                                        class="px-6 py-4"
                                        x-data="{confirm:true}"
                                    >
                                        <div class="flex items-center">
                                            <button
                                                x-cloak
                                                x-show="confirm"
                                                x-on:click="confirm=!confirm"
                                                wire:click="validateShift({{ $shift->id }})"
                                                type="button"
                                                class="cursor-pointer text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-xs p-2 text-center inline-flex items-center  dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                                                x-text="'Confirm'"
                                            >
                                            </button>
                                            <button
                                                x-cloak
                                                x-show="!confirm"
                                                x-on:click="confirm=!confirm"
                                                wire:click="unvalidateShift({{ $shift->id }})"
                                                type="button"
                                                class="cursor-pointer text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-xs p-2 text-center inline-flex items-center  dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800"
                                                x-text="'Cancel'"
                                            >
                                            </button>
                                        </div>
                                    </td>
                                    @endif
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No shifts found.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                    <div class="p-4">
                        {{ $shifts->links() }}
                    </div>
                </x-card>
            </x-content-body>
        @endif
</x-content>
