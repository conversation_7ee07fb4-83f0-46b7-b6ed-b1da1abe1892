<x-content>
    <div>
        <x-content-header title="Agent Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-agent-edit', { agent: {{ $agent->id }} })"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-agent-delete', { agent: {{ $agent->id }} })"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-agent-index')"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Left Column - Agent Info -->
                <div class="col-span-1">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flex flex-col items-center">
                            <img class="mb-4 rounded-lg w-28 h-28 object-cover" src="{{ $agent->getProfilePictureUrl() }}" alt="{{ $agent->getFullNameAttribute() }} picture">
                            <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">{{ $agent->getFullNameAttribute() }}</h3>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $agent->role->name }}</span>

                            <div class="mt-4 w-full">
                                <div class="flex justify-between mb-1">
                                    <span class="text-base font-medium text-primary-700 dark:text-white">Status</span>
                                    <span class="text-sm font-medium text-primary-700 dark:text-white">{{ ucfirst($agent->status) }}</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    @php
                                        $statusPercentage = match($agent->status) {
                                            'in_training' => 25,
                                            'active' => 100,
                                            'inactive' => 0,
                                            'engaged' => 75,
                                            default => 50
                                        };
                                        $statusColor = match($agent->status) {
                                            'in_training' => 'blue',
                                            'active' => 'green',
                                            'inactive' => 'red',
                                            'engaged' => 'yellow',
                                            default => 'gray'
                                        };
                                    @endphp
                                    <div class="bg-{{ $statusColor }}-600 h-2.5 rounded-full" style="width: {{ $statusPercentage }}%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Contact Information</h4>
                            <div class="mt-2 space-y-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path><path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path></svg>
                                    <span class="text-gray-900 dark:text-white">{{ $agent->email }}</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path></svg>
                                    <span class="text-gray-900 dark:text-white">{{ $agent->phone_number ?: 'Not provided' }}</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-500 dark:text-gray-400 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path></svg>
                                    <span class="text-gray-900 dark:text-white">{{ $agent->address ? "$agent->address, $agent->city, $agent->country" : 'Not provided' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Agent-specific information -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Agent Details</h4>
                        <div class="mt-2 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Registration Number</span>
                                <span class="text-gray-900 dark:text-white">{{ $agent->registration_number ?: 'Not assigned' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Hire Date</span>
                                <span class="text-gray-900 dark:text-white">
                                    @if($agent->hire_date)
                                        @if(is_string($agent->hire_date))
                                            {{ \Carbon\Carbon::parse($agent->hire_date)->format('M d, Y') }}
                                        @else
                                            {{ $agent->hire_date->format('M d, Y') }}
                                        @endif
                                    @else
                                        Not provided
                                    @endif
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Campaign</span>
                                <span class="text-gray-900 dark:text-white">{{ $agent->campaign ? $agent->campaign->name : 'Not assigned' }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Current Status</span>
                                <span class="text-gray-900 dark:text-white">
                                    @php
                                        $statusBadgeClass = match($agent->status) {
                                            'in_training' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                            'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                            'inactive' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                            'validated' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                                            'engaged' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
                                            default => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                                        };
                                    @endphp
                                    <span class="px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusBadgeClass }}">
                                        {{ ucfirst($agent->status) }}
                                    </span>
                                </span>
                            </div>

                            @if($agent->training)
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
                                    <h5 class="text-md font-medium text-gray-900 dark:text-white mb-2">Training Information</h5>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Training Module</span>
                                        <span class="text-gray-900 dark:text-white">
                                            {{ $agent->training->module ? $agent->training->module->name : 'Not assigned' }}
                                        </span>
                                    </div>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Progress</span>
                                        <span class="text-gray-900 dark:text-white">{{ $agent->training->progress }}%</span>
                                    </div>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Rating</span>
                                        <span class="text-gray-900 dark:text-white">
                                            {{ $agent->training->rating ? number_format($agent->training->rating, 1) . '/3' : 'Not rated' }}
                                        </span>
                                    </div>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Validation Status</span>
                                        <span class="text-gray-900 dark:text-white">
                                            @if($agent->training->validated_at)
                                                <span class="px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                    Validated on {{ $agent->training->validated_at->format('M d, Y') }}
                                                </span>
                                            @else
                                                <span class="px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                                                    Not validated
                                                </span>
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            @endif

                            @if($agent->campaign)
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-3 mt-3">
                                    <h5 class="text-md font-medium text-gray-900 dark:text-white mb-2">Campaign Performance</h5>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Performance</span>
                                        <span class="text-gray-900 dark:text-white">
                                            {{ $agent->campaign->performance ? $agent->campaign->performance . '%' : 'Not measured' }}
                                        </span>
                                    </div>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Productivity</span>
                                        <span class="text-gray-900 dark:text-white">
                                            {{ $agent->campaign->productivity ? $agent->campaign->productivity . '%' : 'Not measured' }}
                                        </span>
                                    </div>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Rating</span>
                                        <span class="text-gray-900 dark:text-white">
                                            {{ $agent->campaign->rating ? number_format($agent->campaign->rating, 1) . '/3' : 'Not rated' }}
                                        </span>
                                    </div>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Daily Target</span>
                                        <span class="text-gray-900 dark:text-white">
                                            {{ $agent->daily_appointment_target ?: 'Not set' }} appointments
                                        </span>
                                    </div>

                                    <div class="flex justify-between mt-2">
                                        <span class="text-gray-500 dark:text-gray-400">Weekly Hours</span>
                                        <span class="text-gray-900 dark:text-white">
                                            {{ $agent->weekly_hours_target ?: 'Not set' }} hours
                                        </span>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Agent Documents -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Documents</h4>
                        <div class="mt-4 space-y-4">
                            @if(count($documents) > 0)
                                @if(isset($documents['resume']))
                                    <div>
                                        <h5 class="text-md font-medium text-gray-900 dark:text-white mb-2">Resume/CV</h5>
                                        <ul class="space-y-2">
                                            @foreach($documents['resume'] as $document)
                                                <li class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                                    <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                                    <a href="{{ $document['file_url'] }}" target="_blank" class="text-primary-600 hover:text-primary-700 dark:text-primary-500 dark:hover:text-primary-400">
                                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                                                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                                                        </svg>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                @if(isset($documents['id_card']))
                                    <div>
                                        <h5 class="text-md font-medium text-gray-900 dark:text-white mb-2">ID Card</h5>
                                        <ul class="space-y-2">
                                            @foreach($documents['id_card'] as $document)
                                                <li class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                                    <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                                    <a href="{{ $document['file_url'] }}" target="_blank" class="text-primary-600 hover:text-primary-700 dark:text-primary-500 dark:hover:text-primary-400">
                                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                                                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                                                        </svg>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                @if(isset($documents['certificate']))
                                    <div>
                                        <h5 class="text-md font-medium text-gray-900 dark:text-white mb-2">Certificates</h5>
                                        <ul class="space-y-2">
                                            @foreach($documents['certificate'] as $document)
                                                <li class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                                    <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                                    <a href="{{ $document['file_url'] }}" target="_blank" class="text-primary-600 hover:text-primary-700 dark:text-primary-500 dark:hover:text-primary-400">
                                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                                                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                                                        </svg>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                @if(isset($documents['other_document']))
                                    <div>
                                        <h5 class="text-md font-medium text-gray-900 dark:text-white mb-2">Other Documents</h5>
                                        <ul class="space-y-2">
                                            @foreach($documents['other_document'] as $document)
                                                <li class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                                    <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                                    <a href="{{ $document['file_url'] }}" target="_blank" class="text-primary-600 hover:text-primary-700 dark:text-primary-500 dark:hover:text-primary-400">
                                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                                                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                                                        </svg>
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No documents uploaded yet.</p>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Right Column - Performance Metrics -->
                <div class="col-span-2">
                    <!-- Agent Evolution Timeline -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Agent Evolution History</h4>

                        @if(count($evolutionHistory) > 0)
                            <ol class="relative border-l border-gray-200 dark:border-gray-700">
                                @foreach($evolutionHistory as $historyItem)
                                    <li class="mb-10 ml-6">
                                        <span class="absolute flex items-center justify-center w-6 h-6 bg-{{ $historyItem['color'] }}-100 rounded-full -left-3 ring-8 ring-white dark:ring-gray-900 dark:bg-{{ $historyItem['color'] }}-900">
                                            @if($historyItem['icon'] === 'user-plus')
                                                <svg class="w-3 h-3 text-{{ $historyItem['color'] }}-800 dark:text-{{ $historyItem['color'] }}-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 18">
                                                    <path d="M6.5 9a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9ZM8 10H5a5.006 5.006 0 0 0-5 5v2a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-2a5.006 5.006 0 0 0-5-5Zm11-3h-2V5a1 1 0 0 0-2 0v2h-2a1 1 0 1 0 0 2h2v2a1 1 0 0 0 2 0V9h2a1 1 0 1 0 0-2Z"/>
                                                </svg>
                                            @elseif($historyItem['icon'] === 'briefcase')
                                                <svg class="w-3 h-3 text-{{ $historyItem['color'] }}-800 dark:text-{{ $historyItem['color'] }}-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M19 4h-1a1 1 0 1 0 0 2v11a1 1 0 0 1-2 0V2a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v15a1 1 0 0 1-2 0V6a1 1 0 0 0 0-2H1a1 1 0 0 0 0 2v11a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a1 1 0 1 0 0-2Z"/>
                                                </svg>
                                            @elseif($historyItem['icon'] === 'academic-cap')
                                                <svg class="w-3 h-3 text-{{ $historyItem['color'] }}-800 dark:text-{{ $historyItem['color'] }}-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 16">
                                                    <path d="m19.9 5.211-9-4a1.001 1.001 0 0 0-.8 0l-9 4A1 1 0 0 0 1 6v7a1 1 0 0 0 2 0V6.5l7 3.11V15a1 1 0 0 0 2 0v-5.39l7-3.11v2.38a1 1 0 0 0 2 0V6a1.001 1.001 0 0 0-.1-.789Z"/>
                                                </svg>
                                            @elseif($historyItem['icon'] === 'check-badge')
                                                <svg class="w-3 h-3 text-{{ $historyItem['color'] }}-800 dark:text-{{ $historyItem['color'] }}-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                                                </svg>
                                            @elseif($historyItem['icon'] === 'play')
                                                <svg class="w-3 h-3 text-{{ $historyItem['color'] }}-800 dark:text-{{ $historyItem['color'] }}-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 14 16">
                                                    <path d="M0 .984v14.032a1 1 0 0 0 1.506.845l12.006-7.016a.974.974 0 0 0 0-1.69L1.506.139A1 1 0 0 0 0 .984Z"/>
                                                </svg>
                                            @else
                                                <svg class="w-3 h-3 text-{{ $historyItem['color'] }}-800 dark:text-{{ $historyItem['color'] }}-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M20 4a2 2 0 0 0-2-2h-2V1a1 1 0 0 0-2 0v1h-3V1a1 1 0 0 0-2 0v1H6V1a1 1 0 0 0-2 0v1H2a2 2 0 0 0-2 2v2h20V4ZM0 18a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V8H0v10Zm5-8h10a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z"/>
                                                </svg>
                                            @endif
                                        </span>
                                        <h3 class="flex items-center mb-1 text-lg font-semibold text-gray-900 dark:text-white">
                                            {{ $historyItem['event'] }}
                                            @if($loop->last)
                                                <span class="bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300 ml-3">Latest</span>
                                            @endif
                                        </h3>
                                        <time class="block mb-2 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">{{ $historyItem['formatted_date'] }}</time>
                                        <p class="mb-4 text-base font-normal text-gray-500 dark:text-gray-400">{{ $historyItem['description'] }}</p>

                                        @if(isset($historyItem['details']) && count($historyItem['details']) > 0)
                                            <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                                <dl class="grid grid-cols-2 gap-x-4 gap-y-2">
                                                    @foreach($historyItem['details'] as $label => $value)
                                                        <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $label }}:</dt>
                                                        <dd class="text-sm font-semibold text-gray-900 dark:text-white">{{ $value }}</dd>
                                                    @endforeach
                                                </dl>
                                            </div>
                                        @endif
                                    </li>
                                @endforeach
                            </ol>
                        @else
                            <p class="text-gray-500 dark:text-gray-400">No evolution history available for this agent.</p>
                        @endif
                    </div>

                    <!-- Performance Summary -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Summary</h4>
                        <div class="grid grid-cols-2 gap-4 sm:grid-cols-4">
                            <div class="p-3 bg-gray-100 rounded-lg dark:bg-gray-700">
                                <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Shifts</h5>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $performanceMetrics['total_shifts'] }}</p>
                            </div>
                            <div class="p-3 bg-gray-100 rounded-lg dark:bg-gray-700">
                                <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Hours Worked</h5>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($performanceMetrics['total_hours'], 1) }}</p>
                            </div>
                            <div class="p-3 bg-gray-100 rounded-lg dark:bg-gray-700">
                                <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Appointments</h5>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $performanceMetrics['total_appointments'] }}</p>
                            </div>
                            <div class="p-3 bg-gray-100 rounded-lg dark:bg-gray-700">
                                <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Validated</h5>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $performanceMetrics['validated_appointments'] }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Shifts -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Shifts</h4>
                            <a href="{{ route('agents.presence', ['agent' => $agent->id]) }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                View all
                            </a>
                        </div>
                        @if(count($recentShifts) > 0)
                            <div class="flow-root">
                                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                    @foreach($recentShifts as $shift)
                                        <li class="py-3 sm:py-4">
                                            <div class="flex items-center space-x-4">
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                        {{ \Carbon\Carbon::parse($shift['date'])->format('M d, Y') }}
                                                    </p>
                                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                        {{ \Carbon\Carbon::parse($shift['login_time'])->format('H:i') }} -
                                                        {{ isset($shift['logout_time']) ? \Carbon\Carbon::parse($shift['logout_time'])->format('H:i') : 'In progress' }}
                                                    </p>
                                                </div>
                                                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                                    @if(isset($shift['logout_time']))
                                                        @php
                                                            $duration = \Carbon\Carbon::parse($shift['login_time'])->diffInMinutes(\Carbon\Carbon::parse($shift['logout_time']));
                                                            $hours = floor($duration / 60);
                                                            $minutes = $duration % 60;
                                                        @endphp
                                                        {{ $hours }}h {{ $minutes }}m
                                                    @else
                                                        Active
                                                    @endif
                                                </div>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @else
                            <p class="text-sm text-gray-500 dark:text-gray-400">No shifts recorded yet.</p>
                        @endif
                    </div>

                    <!-- Recent Appointments -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Appointments</h4>
                            <a href="{{ route('appointments.index', ['agent' => $agent->id]) }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                View all
                            </a>
                        </div>
                        @if(count($recentAppointments) > 0)
                            <div class="flow-root">
                                <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                    @foreach($recentAppointments as $appointment)
                                        <li class="py-3 sm:py-4">
                                            <div class="flex items-center space-x-4">
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                        @if(isset($appointment['customer']) && isset($appointment['customer']['first_name']))
                                                            {{ $appointment['customer']['first_name'] }} {{ $appointment['customer']['last_name'] ?? '' }}
                                                        @else
                                                            Customer information unavailable
                                                        @endif
                                                    </p>
                                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                        @if(isset($appointment['scheduled_at']))
                                                            {{ \Carbon\Carbon::parse($appointment['scheduled_at'])->format('M d, Y H:i') }}
                                                        @else
                                                            Schedule unavailable
                                                        @endif
                                                    </p>
                                                </div>
                                                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                                    @if(isset($appointment['status']))
                                                        @php
                                                            $statusClass = match($appointment['status']) {
                                                                'scheduled' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
                                                                'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
                                                                'cancelled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
                                                                'validated' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
                                                                default => 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                                                            };
                                                        @endphp
                                                        <span class="px-2.5 py-0.5 rounded-full text-xs font-medium {{ $statusClass }}">
                                                            {{ ucfirst($appointment['status']) }}
                                                        </span>
                                                    @else
                                                        <span class="px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                            Unknown
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @else
                            <p class="text-sm text-gray-500 dark:text-gray-400">No appointments recorded yet.</p>
                        @endif
                    </div>
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>

