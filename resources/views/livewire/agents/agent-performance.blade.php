<x-content>
    <x-card key="shift-index-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    @if(auth()->user()->role_id !== 6)
                        <div class="lg:pr-3">
                            <label for="agents-search" class="sr-only">Search</label>
                            <div x-data="{ open: false }" class="relative mt-1 lg:w-64 xl:w-96">
                                <input
                                    id="agents-search"
                                    type="text"
                                    x-on:focus="open = true"
                                    x-on:click.away="open = false"
                                    wire:model.live="agentSearch"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                    placeholder="Search for agent"
                                />

                                <ul
                                    x-show="open"
                                    x-cloak
                                    class="absolute z-10 mt-1 w-full text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                >
                                    @forelse($filteredAgents as $agent)
                                        <li
                                            wire:click="selectAgent({{ $agent->id }})"
                                            x-on:click="open = false"
                                            class="px-2 py-1 hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-700"
                                        >
                                            {{ $agent->first_name }} {{ $agent->last_name }}
                                        </li>
                                    @empty
                                        <li class="w-full p-4 text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Aucun agent trouvé.</li>
                                    @endforelse
                                </ul>
                            </div>
                        </div>
                    @else
                        <div class="lg:pr-3">
                            <h3 class="flex items-center text-lg font-semibold text-gray-900 dark:text-white">
                                {{ $selectedAgent->getFullNameAttribute() }}
                            </h3>
                        </div>
                    @endif
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path d="M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V12zM6 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H6zM7.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8a.75.75 0 01-.75-.75V12zM8 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H8zM9.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V10zM10 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H10zM9.25 14a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V14zM12 9.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V10a.75.75 0 00-.75-.75H12zM11.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12a.75.75 0 01-.75-.75V12zM12 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H12zM13.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H14a.75.75 0 01-.75-.75V10zM14 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H14z"></path>
                            <path clip-rule="evenodd" fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"></path>
                          </svg>
                        </div>
                        <input
                              type="date"
                              wire:model.live="startDate"
                              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                              placeholder="From"
                          />
                      </div>
                      <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                            <path d="M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75V12zM6 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H6zM7.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8a.75.75 0 01-.75-.75V12zM8 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H8zM9.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V10zM10 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H10zM9.25 14a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10a.75.75 0 01-.75-.75V14zM12 9.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V10a.75.75 0 00-.75-.75H12zM11.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12a.75.75 0 01-.75-.75V12zM12 13.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14a.75.75 0 00-.75-.75H12zM13.25 10a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H14a.75.75 0 01-.75-.75V10zM14 11.25a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V12a.75.75 0 00-.75-.75H14z"></path>
                            <path clip-rule="evenodd" fill-rule="evenodd" d="M5.75 2a.75.75 0 01.75.75V4h7V2.75a.75.75 0 011.5 0V4h.25A2.75 2.75 0 0118 6.75v8.5A2.75 2.75 0 0115.25 18H4.75A2.75 2.75 0 012 15.25v-8.5A2.75 2.75 0 014.75 4H5V2.75A.75.75 0 015.75 2zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75z"></path>
                          </svg>
                        </div>
                        <input
                              type="date"
                              wire:model.live="endDate"
                              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                              placeholder="To"
                          />
                      </div>
                      <select wire:model.live="period" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                          <option value="day">Daily</option>
                          <option value="week">Weekly</option>
                          <option value="month">Monthly</option>
                          <option value="year">Yearly</option>
                      </select>
                    <button wire:click="exportPerformanceData" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path></svg>
                        Export
                    </button>
                </div>
            </div>
        </div>
    </x-card>
    <x-content-body>
        <div class="mt-4">
            <div class="{{ $selectedAgentId ? 'grid gap-4 xl:grid-cols-2 2xl:grid-cols-3' : ''}}">
                <!-- Main performance chart widget -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex-shrink-0">
                      @if($selectedAgent && isset($performanceData['total_appointments']))
                        <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white">{{ $performanceData['total_appointments'] }}</span>
                        <h3 class="text-base font-light text-gray-500 dark:text-gray-400">Total Appointments</h3>
                      @else
                        <span class="text-xl font-bold leading-none text-gray-900 sm:text-2xl dark:text-white">0</span>
                        <h3 class="text-base font-light text-gray-500 dark:text-gray-400">No data available</h3>
                      @endif
                    </div>
                    <div class="flex items-center justify-end flex-1 text-base font-medium">
                      @if($selectedAgent && isset($performanceData['validation_rate']))
                        <span class="{{ $performanceData['validation_rate'] >= 70 ? 'text-green-500 dark:text-green-400' : ($performanceData['validation_rate'] >= 50 ? 'text-yellow-500 dark:text-yellow-400' : 'text-red-500 dark:text-red-400') }}">
                          {{ $performanceData['validation_rate'] }}%
                          <span class="ml-2 text-gray-500 dark:text-gray-400">Validation Rate</span>
                        </span>
                      @endif
                    </div>
                  </div>

                  <!-- Performance metrics cards -->
                  <div class="grid grid-cols-1 gap-4 mb-4 sm:grid-cols-2 lg:grid-cols-4">
                    <!-- Overall Performance Score Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Performance</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['overall_score'] ?? 0 }}</h3>
                          <p class="text-xs text-gray-500 dark:text-gray-400">{{ $performanceData['performance_rating'] ?? 'Not Rated' }}</p>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full
                          {{ isset($performanceData['overall_score']) && $performanceData['overall_score'] >= 80 ? 'bg-green-100 dark:bg-green-900' :
                            (isset($performanceData['overall_score']) && $performanceData['overall_score'] >= 60 ? 'bg-yellow-100 dark:bg-yellow-900' : 'bg-red-100 dark:bg-red-900') }}">
                          <svg class="w-6 h-6
                            {{ isset($performanceData['overall_score']) && $performanceData['overall_score'] >= 80 ? 'text-green-600 dark:text-green-300' :
                              (isset($performanceData['overall_score']) && $performanceData['overall_score'] >= 60 ? 'text-yellow-600 dark:text-yellow-300' : 'text-red-600 dark:text-red-300') }}"
                            fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Quality Score Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Quality Score</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['quality_score'] ?? 0 }}</h3>
                          <p class="text-xs text-gray-500 dark:text-gray-400">CSAT: {{ $performanceData['customer_satisfaction'] ?? 0 }}</p>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900">
                          <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Call Metrics Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Call Volume</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['total_calls'] ?? 0 }}</h3>
                          <p class="text-xs text-gray-500 dark:text-gray-400">AHT: {{ $performanceData['avg_handle_time'] ?? '00:00:00' }}</p>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900">
                          <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Adherence Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Adherence</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['adherence_rate'] ?? 0 }}%</h3>
                          <p class="text-xs text-gray-500 dark:text-gray-400">Compliance: {{ $performanceData['compliance_score'] ?? 0 }}</p>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900">
                          <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Appointment Metrics Row -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Total Appts</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['total_appointments'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 dark:bg-green-900">
                          <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Validated Appointments Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Validated</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['validated_appointments'] ?? 0 }}</h3>
                          <p class="text-xs text-gray-500 dark:text-gray-400">{{ $performanceData['validation_rate'] ?? 0 }}% rate</p>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-green-100 dark:bg-green-900">
                          <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- First Call Resolution Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">FCR</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['first_call_resolution'] ?? 0 }}%</h3>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900">
                          <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- Daily Average Card -->
                    <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                      <div class="flex items-center justify-between">
                        <div>
                          <span class="text-base font-normal text-gray-500 dark:text-gray-400">Daily Avg</span>
                          <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $performanceData['daily_average'] ?? 0 }}</h3>
                        </div>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900">
                          <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Performance Chart -->
                  <div id="performance-chart" class="w-full h-80" data-chart="{{ json_encode($chartData) }}"></div>

                  <script src="{{ asset('js/agent-performance-chart.js') }}"></script>

                  <!-- Card Footer -->
                  <div class="flex items-center justify-between pt-3 mt-4 border-t border-gray-200 sm:pt-6 dark:border-gray-700">
                    <div>
                      <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Period: {{ ucfirst($period) }}
                      </span>
                    </div>
                    <div class="flex-shrink-0">
                      <div class="inline-flex items-center p-2 text-xs font-medium uppercase rounded-lg text-primary-700 sm:text-sm dark:text-primary-500">
                        <span class="mr-2">Team Average:</span>
                        <span class="font-bold">{{ $teamAverage['daily_average'] ?? 0 }} appts/day</span>
                        <span class="mx-2">|</span>
                        <span class="font-bold">{{ $teamAverage['validation_rate'] ?? 0 }}% validation</span>
                      </div>
                    </div>
                  </div>
                </div>
                @if($selectedAgentId)
                <!--Tabs widget -->
                <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                  <h3 class="flex items-center mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                    Agent Details
                    <button data-popover-target="popover-description" data-popover-placement="bottom-end" type="button">
                      <svg class="w-4 h-4 ml-2 text-gray-400 hover:text-gray-500" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="sr-only">Show information</span>
                    </button>
                  </h3>
                  <div data-popover id="popover-description" role="tooltip" class="absolute z-10 invisible inline-block text-sm font-light text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 w-72 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400">
                    <div class="p-3 space-y-2">
                        <h3 class="font-semibold text-gray-900 dark:text-white">Agent Details</h3>
                        <p>View detailed information about the agent's appointments and presence records.</p>
                    </div>
                    <div data-popper-arrow></div>
                  </div>

                  <div class="sm:hidden">
                      <label for="tabs" class="sr-only">Select tab</label>
                      <select id="tabs" class="bg-gray-50 border-0 border-b border-gray-200 text-gray-900 text-sm rounded-t-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                          <option>Appointments</option>
                          <option>Presence</option>
                      </select>
                  </div>

                  <ul class="hidden text-sm font-medium text-center text-gray-500 divide-x divide-gray-200 rounded-lg sm:flex dark:divide-gray-600 dark:text-gray-400" id="fullWidthTab" data-tabs-toggle="#fullWidthTabContent" role="tablist">
                      <li class="w-full">
                          <button id="appointments-tab" data-tabs-target="#appointments" type="button" role="tab" aria-controls="appointments" aria-selected="true" class="inline-block w-full p-4 rounded-tl-lg bg-gray-50 hover:bg-gray-100 focus:outline-none dark:bg-gray-700 dark:hover:bg-gray-600">Appointments</button>
                      </li>
                      <li class="w-full">
                          <button id="presence-tab" data-tabs-target="#presence" type="button" role="tab" aria-controls="presence" aria-selected="false" class="inline-block w-full p-4 rounded-tr-lg bg-gray-50 hover:bg-gray-100 focus:outline-none dark:bg-gray-700 dark:hover:bg-gray-600">Presence</button>
                      </li>
                  </ul>

                  <div id="fullWidthTabContent" class="border-t border-gray-200 dark:border-gray-600">
                      <!-- Appointments Tab -->
                      <div class="pt-4" id="appointments" role="tabpanel" aria-labelledby="appointments-tab">
                        @if($selectedAgent && $selectedAgent->appointments && $selectedAgent->appointments->count() > 0)
                            <div class="relative overflow-x-auto">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-4 py-3">Date</th>
                                            <th scope="col" class="px-4 py-3">Client</th>
                                            <th scope="col" class="px-4 py-3">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($selectedAgent->appointments->sortByDesc('scheduled_at')->take(5) as $appointment)
                                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                                <td class="px-4 py-3">
                                                    {{ \Carbon\Carbon::parse($appointment->scheduled_at)->format('d M Y H:i') }}
                                                </td>
                                                <td class="px-4 py-3">
                                                    {{ $appointment->client_name ?? 'N/A' }}
                                                </td>
                                                <td class="px-4 py-3">
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                                        {{ $appointment->status === 'validated' ? 'text-green-700 bg-green-100 dark:bg-green-900 dark:text-green-300' :
                                                           ($appointment->status === 'rejected' ? 'text-red-700 bg-red-100 dark:bg-red-900 dark:text-red-300' :
                                                           'text-yellow-700 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                                        {{ ucfirst($appointment->status) }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if($selectedAgent->appointments->count() > 5)
                                <div class="flex justify-center mt-4">
                                    <a href="#" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                        View all {{ $selectedAgent->appointments->count() }} appointments
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="flex items-center justify-center h-40">
                                <p class="text-gray-500 dark:text-gray-400">No appointments found for this agent.</p>
                            </div>
                        @endif
                      </div>

                      <!-- Presence Tab -->
                      <div class="hidden pt-4" id="presence" role="tabpanel" aria-labelledby="presence-tab">
                        <div class="mb-4">
                            <h4 class="text-base font-semibold text-gray-900 dark:text-white">Presence Summary</h4>
                            <div class="grid grid-cols-1 gap-4 mt-3 sm:grid-cols-3">
                                <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">Present Days</span>
                                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">{{ $selectedAgent->shifts->count() ?? 0 }}</h3>
                                        </div>
                                        <div class="flex items-center justify-center w-8 h-8 rounded-full bg-green-100 dark:bg-green-900">
                                            <svg class="w-4 h-4 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">Total Hours</span>
                                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">
                                                @php
                                                    $totalHours = 0;
                                                    if ($selectedAgent && $selectedAgent->shifts) {
                                                        foreach ($selectedAgent->shifts as $shift) {
                                                            $start = \Carbon\Carbon::parse($shift->start_time);
                                                            $end = \Carbon\Carbon::parse($shift->end_time);
                                                            $totalHours += $end->diffInHours($start);
                                                        }
                                                    }
                                                @endphp
                                                {{ $totalHours }}
                                            </h3>
                                        </div>
                                        <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900">
                                            <svg class="w-4 h-4 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">Avg Hours/Day</span>
                                            <h3 class="text-lg font-bold text-gray-900 dark:text-white">
                                                {{ $selectedAgent->shifts->count() > 0 ? round($totalHours / $selectedAgent->shifts->count(), 1) : 0 }}
                                            </h3>
                                        </div>
                                        <div class="flex items-center justify-center w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900">
                                            <svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($selectedAgent && $selectedAgent->shifts && $selectedAgent->shifts->count() > 0)
                            <div class="relative overflow-x-auto">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-4 py-3">Date</th>
                                            <th scope="col" class="px-4 py-3">Start Time</th>
                                            <th scope="col" class="px-4 py-3">End Time</th>
                                            <th scope="col" class="px-4 py-3">Duration</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($selectedAgent->shifts->sortByDesc('start_time')->take(5) as $shift)
                                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                                <td class="px-4 py-3">
                                                    {{ \Carbon\Carbon::parse($shift->start_time)->format('d M Y') }}
                                                </td>
                                                <td class="px-4 py-3">
                                                    {{ \Carbon\Carbon::parse($shift->start_time)->format('H:i') }}
                                                </td>
                                                <td class="px-4 py-3">
                                                    {{ \Carbon\Carbon::parse($shift->end_time)->format('H:i') }}
                                                </td>
                                                <td class="px-4 py-3">
                                                    @php
                                                        $start = \Carbon\Carbon::parse($shift->start_time);
                                                        $end = \Carbon\Carbon::parse($shift->end_time);
                                                        $hours = $end->diffInHours($start);
                                                        $minutes = $end->diffInMinutes($start) % 60;
                                                    @endphp
                                                    {{ $hours }}h {{ $minutes }}m
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if($selectedAgent->shifts->count() > 5)
                                <div class="flex justify-center mt-4">
                                    <a href="#" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                        View all {{ $selectedAgent->shifts->count() }} shifts
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="flex items-center justify-center h-40">
                                <p class="text-gray-500 dark:text-gray-400">No presence records found for this agent.</p>
                            </div>
                        @endif
                      </div>
                  </div>
                </div>
                @endif
            </div>
        </div>
    </x-content-body>
</x-content>

