<x-content>
    <div>
        <x-content-header title="{{ $report->title ?? 'Report Details' }}">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-report-edit', { report: {{ $report->id }} })"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-report-delete', { report: {{ $report->id }} })"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-report-index')"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Right Content - Creator and Report Info -->
                <div class="col-span-full xl:col-auto">
                    <!-- Creator Info -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Creator Information</h3>
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <img class="mb-4 rounded-lg w-28 h-28 sm:mb-0 xl:mb-4 2xl:mb-0" src="https://avatar.iran.liara.run/public/{{$report->creator->id}}" alt="{{ $report->creator->getFullNameAttribute() }}">
                            <div>
                                <h3 class="mb-1 text-xl font-semibold dark:text-white">{{$report->creator->getFullNameAttribute()}}</h3>
                                <div class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                                    Created on {{ $report->created_at->format('M d, Y \a\t h:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Report Metadata -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Report Information</h3>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Campaign</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $report->campaign->name ?? 'N/A' }}</p>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Status</h4>
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                @if($report->status == 'pending') bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                @elseif($report->status == 'submitted') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                @elseif($report->status == 'approved') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                @elseif($report->status == 'rejected') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                @elseif($report->status == 'in_review') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                @endif">
                                {{ ucfirst(str_replace('_', ' ', $report->status ?? 'pending')) }}
                            </span>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Priority</h4>
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                @if($report->priority == 'low') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                @elseif($report->priority == 'normal') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                @elseif($report->priority == 'high') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                @elseif($report->priority == 'urgent') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                @endif">
                                {{ ucfirst($report->priority ?? 'normal') }}
                            </span>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Type</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $report->type ?? 'N/A' }}</p>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Category</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $report->category ?? 'N/A' }}</p>
                        </div>

                        @if($report->tags && count($report->tags) > 0)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Tags</h4>
                            <div class="flex flex-wrap gap-2 mt-2">
                                @foreach($report->tags as $tag)
                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full dark:bg-gray-700 dark:text-gray-300">
                                    {{ $tag }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        @if($report->file_path)
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Attachments</h4>
                            <div class="flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                              <div class="flex items-center justify-center w-10 h-10 mr-3 rounded-lg bg-primary-100 dark:bg-primary-900">
                                <svg class="w-5 h-5 text-primary-600 lg:w-6 lg:h-6 dark:text-primary-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                  <path clip-rule="evenodd" fill-rule="evenodd" d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625zM7.5 15a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5A.75.75 0 017.5 15zm.75 2.25a.75.75 0 000 1.5H12a.75.75 0 000-1.5H8.25z"></path>
                                  <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z"></path>
                                </svg>
                              </div>
                              <div class="mr-4">
                                  <p class="text-sm font-semibold text-gray-900 dark:text-white">{{ basename($report->file_path) }}</p>
                                  <p class="text-sm text-gray-500 dark:text-gray-400">Report Document</p>
                              </div>
                              <div class="flex items-center ml-auto">
                                <a href="{{ asset('storage/' . $report->file_path) }}" download class="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700">
                                  <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <path clip-rule="evenodd" fill-rule="evenodd" d="M12 2.25a.75.75 0 01.75.75v11.69l3.22-3.22a.75.75 0 111.06 1.06l-4.5 4.5a.75.75 0 01-1.06 0l-4.5-4.5a.75.75 0 111.06-1.06l3.22 3.22V3a.75.75 0 01.75-.75zm-9 13.5a.75.75 0 01.75.75v2.25a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5V16.5a.75.75 0 011.5 0v2.25a3 3 0 01-3 3H5.25a3 3 0 01-3-3V16.5a.75.75 0 01.75-.75z"></path>
                                  </svg>
                                  <span class="sr-only">Download</span>
                                </a>
                              </div>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Performance Scores -->
                    @if($report->performance_score || $report->quality_score || $report->compliance_score)
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Performance Metrics</h3>

                        @if($report->performance_score)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Performance Score</h4>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ $report->performance_score }}%"></div>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ $report->performance_score }}%</p>
                        </div>
                        @endif

                        @if($report->quality_score)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Quality Score</h4>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2">
                                <div class="bg-green-600 h-2.5 rounded-full" style="width: {{ $report->quality_score }}%"></div>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ $report->quality_score }}%</p>
                        </div>
                        @endif

                        @if($report->compliance_score)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Compliance Score</h4>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2">
                                <div class="bg-yellow-600 h-2.5 rounded-full" style="width: {{ $report->compliance_score }}%"></div>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ $report->compliance_score }}%</p>
                        </div>
                        @endif

                        @if($report->getOverallScoreAttribute())
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Overall Score</h4>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mt-2">
                                <div class="bg-purple-600 h-2.5 rounded-full" style="width: {{ $report->getOverallScoreAttribute() }}%"></div>
                            </div>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ $report->getOverallScoreAttribute() }}%</p>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>

                <!-- Left Content - Report Content -->
                <div class="col-span-2">
                    <!-- Report Content -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Report Content</h3>

                        @if($report->description)
                        <div class="mb-6">
                            <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white">Description</h4>
                            <p class="text-gray-500 dark:text-gray-400">{{ $report->description }}</p>
                        </div>
                        @endif

                        <div class="mb-6">
                            <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white">Content</h4>
                            <div class="p-4 bg-gray-50 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                {!! nl2br(e($report->content)) !!}
                            </div>
                        </div>

                        @if($report->response)
                        <div>
                            <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white">Response</h4>
                            <div class="p-4 bg-gray-50 border border-gray-300 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                {!! nl2br(e($report->response)) !!}
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Approval Information -->
                    @if($report->approval_status && $report->approval_status != 'pending')
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Approval Information</h3>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Approval Status</h4>
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                @if($report->approval_status == 'pending') bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                @elseif($report->approval_status == 'approved') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                @elseif($report->approval_status == 'rejected') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                @endif">
                                {{ ucfirst($report->approval_status) }}
                            </span>
                        </div>

                        @if($report->approved_by)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Approved By</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $report->approver->getFullNameAttribute() ?? 'N/A' }}</p>
                        </div>
                        @endif

                        @if($report->approved_at)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Approved On</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $report->approved_at->format('M d, Y \a\t h:i A') }}</p>
                        </div>
                        @endif

                        @if($report->approval_notes)
                        <div>
                            <h4 class="mb-2 text-sm font-medium text-gray-900 dark:text-white">Approval Notes</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $report->approval_notes }}</p>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>
