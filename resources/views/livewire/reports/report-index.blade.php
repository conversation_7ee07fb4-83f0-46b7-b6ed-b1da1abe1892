<x-content>
    <x-card key="report-index-header" class="p-4">
        <div class="w-full">
            <!-- Main filters and actions -->
            <div class="flex flex-col space-y-4">
                <!-- Search and primary filters -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                    <!-- Search input -->
                    <div>
                        <label for="search" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Search</label>
                        <input
                            id="search"
                            type="text"
                            wire:model.live.debounce.300ms="search"
                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            placeholder="Search reports..."
                        />
                    </div>

                    <!-- Manager filter -->
                    <div>
                        <label for="manager-search" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Manager</label>
                        <div x-data="{ open: false }" class="relative">
                            <input
                                id="manager-search"
                                type="text"
                                x-on:focus="open = true"
                                x-on:click.away="open = false"
                                wire:model.live="managerSearch"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Filter by manager"
                            />
                            <ul
                                x-show="open"
                                x-cloak
                                class="absolute z-10 mt-1 w-full text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                            >
                                @forelse($filteredManagers as $manager)
                                    <li
                                        wire:click="selectManager({{ $manager->id }})"
                                        x-on:click="open = false"
                                        class="px-2 py-1 hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-700"
                                    >
                                        {{ $manager->getFullNameAttribute() }}
                                    </li>
                                @empty
                                    <li class="w-full p-4 text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                                        No managers found.
                                    </li>
                                @endforelse
                            </ul>
                        </div>
                    </div>

                    <!-- Campaign filter -->
                    <div>
                        <label for="campaign-search" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Campaign</label>
                        <div x-data="{ open: false }" class="relative">
                            <input
                                id="campaign-search"
                                type="text"
                                x-on:focus="open = true"
                                x-on:click.away="open = false"
                                wire:model.live="campaignSearch"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Filter by campaign"
                            />
                            <ul
                                x-show="open"
                                x-cloak
                                class="absolute z-10 mt-1 w-full text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                            >
                                @forelse($filteredCampaigns as $campaign)
                                    <li
                                        wire:click="selectCampaign({{ $campaign->id }})"
                                        x-on:click="open = false"
                                        class="px-2 py-1 hover:bg-gray-100 cursor-pointer dark:hover:bg-gray-700"
                                    >
                                        {{ $campaign->name }}
                                    </li>
                                @empty
                                    <li class="w-full p-4 text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                                        No campaigns found.
                                    </li>
                                @endforelse
                            </ul>
                        </div>
                    </div>

                    <!-- Status filter -->
                    <div>
                        <label for="status-filter" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                        <select
                            id="status-filter"
                            wire:model.live="statusFilter"
                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        >
                            <option value="">All Statuses</option>
                            @foreach ($statusOptions as $value => $label)
                                <option value="{{ $value }}">{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>

                <!-- Secondary filters -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                    <!-- Priority filter -->
                    <div>
                        <label for="priority-filter" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Priority</label>
                        <select
                            id="priority-filter"
                            wire:model.live="priorityFilter"
                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        >
                            <option value="">All Priorities</option>
                            @foreach ($priorityOptions as $value => $label)
                                <option value="{{ $value }}">{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Type filter -->
                    <div>
                        <label for="type-filter" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">Type</label>
                        <select
                            id="type-filter"
                            wire:model.live="typeFilter"
                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        >
                            <option value="">All Types</option>
                            @foreach ($typeOptions as $type)
                                <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Date From -->
                    <div>
                        <label for="date-from" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">From Date</label>
                        <input
                            id="date-from"
                            type="date"
                            wire:model.live="dateFrom"
                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        >
                    </div>

                    <!-- Date To -->
                    <div>
                        <label for="date-to" class="block mb-1 text-sm font-medium text-gray-900 dark:text-white">To Date</label>
                        <input
                            id="date-to"
                            type="date"
                            wire:model.live="dateTo"
                            class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        >
                    </div>
                </div>

                <!-- Actions row -->
                <div class="flex flex-wrap items-center justify-between gap-3 pt-2">
                    <!-- Left side - Delete button -->
                    <div>
                        <button
                            wire:click="deleteSelected"
                            wire:confirm="Are you sure you want to delete the selected reports?"
                            class="inline-flex items-center justify-center p-2 text-gray-500 rounded {{count($selectedReports) ? 'cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white' : 'opacity-50 cursor-not-allowed'}}"
                            {{count($selectedReports) ? '' : 'disabled'}}
                        >
                            <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                            Delete Selected
                        </button>
                    </div>

                    <!-- Right side - Action buttons -->
                    <div class="flex flex-wrap items-center gap-2">
                        <button type="button" wire:click="$dispatch('to-report-create')" class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" wire:navigate>
                            <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                            Add Report
                        </button>
                        <button wire:click="exportReports" class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                            <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path></svg>
                            Export
                        </button>
                        <div class="flex items-center">
                            <label for="per-page" class="mr-2 text-sm font-medium text-gray-900 dark:text-white">Per page:</label>
                            <select
                                id="per-page"
                                wire:model.live="perPage"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                                @foreach ([5, 10, 25, 50, 100] as $value)
                                    <option value="{{ $value }}">{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-card>
    <x-card key="report-index-table" class="overflow-x-auto mt-4">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="p-4 w-4">
                        <input
                            type="checkbox"
                            wire:model.live="selectAll"
                            wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            aria-label="Select all reports"
                        >
                    </th>
                    <th wire:click="sortBy('title')" class="cursor-pointer px-6 py-3">
                        Title {{ $sortField === 'title' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('created_by')" class="cursor-pointer px-6 py-3">
                        Created By {{ $sortField === 'created_by' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('campaign_id')" class="cursor-pointer px-6 py-3">
                        Campaign {{ $sortField === 'campaign_id' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('date')" class="cursor-pointer px-6 py-3">
                        Date {{ $sortField === 'date' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('status')" class="cursor-pointer px-6 py-3">
                        Status {{ $sortField === 'status' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                    <th wire:click="sortBy('priority')" class="cursor-pointer px-6 py-3">
                        Priority {{ $sortField === 'priority' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                    </th>
                </tr>
            </thead>
            <tbody>
                @forelse ($reports as $report)
                    <tr wire:key="report-{{ $report->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="p-4 w-4">
                            <input
                                type="checkbox"
                                wire:change="toggleReportSelection({{ $report->id }})"
                                wire:key="checkbox-{{ $report->id }}-{{ in_array($report->id, $selectedReports) ? 'checked' : 'unchecked' }}"
                                value="{{ $report->id }}"
                                {{ in_array($report->id, $selectedReports) ? 'checked' : '' }}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                aria-label="Select report {{ $report->created_by }}"
                            >
                        </td>
                        <td class="px-6 py-4 cursor-pointer font-medium" wire:click="$dispatch('to-report-show', { report: {{ $report->id }} })">
                            {{ $report->title ?? 'Untitled Report' }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-report-show', { report: {{ $report->id }} })">
                            <div class="flex items-center">
                                <img class="w-8 h-8 rounded-full mr-2" src="https://avatar.iran.liara.run/public/{{$report->creator->id}}" alt="{{ $report->creator->getFullNameAttribute() }}">
                                <div>
                                    <div class="font-medium">{{ $report->creator->getFullNameAttribute() }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-report-show', { report: {{ $report->id }} })">
                            {{ $report->campaign->name ?? 'N/A' }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-report-show', { report: {{ $report->id }} })">
                            {{ $report->date ? $report->date->format('M d, Y') : 'N/A' }}
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-report-show', { report: {{ $report->id }} })">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                @if($report->status == 'pending') bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                @elseif($report->status == 'submitted') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                @elseif($report->status == 'approved') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                @elseif($report->status == 'rejected') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                @elseif($report->status == 'in_review') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                @endif">
                                {{ ucfirst(str_replace('_', ' ', $report->status ?? 'pending')) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 cursor-pointer" wire:click="$dispatch('to-report-show', { report: {{ $report->id }} })">
                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                @if($report->priority == 'low') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                @elseif($report->priority == 'normal') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                @elseif($report->priority == 'high') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                @elseif($report->priority == 'urgent') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                @endif">
                                {{ ucfirst($report->priority ?? 'normal') }}
                            </span>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No reports found.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
        <div class="p-4">
            {{ $reports->links() }}
        </div>
    </x-card>
</x-content>
