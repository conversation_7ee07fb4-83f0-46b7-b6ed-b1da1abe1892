<x-content>
    <x-content-header title="Performance Monitoring">
        <div class="flex flex-wrap items-center gap-2">
            <!-- Date Range Filter -->
            <select wire:model="dateRange" wire:change="updatedDateRange" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="week">Last Week</option>
                <option value="month">Last Month</option>
                <option value="quarter">Last Quarter</option>
                <option value="year">Last Year</option>
            </select>

            <!-- Agent Filter -->
            <select wire:model="selectedAgentId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="">All Agents</option>
                @foreach($agents as $agent)
                    <option value="{{ $agent['id'] }}">{{ $agent['name'] }}</option>
                @endforeach
            </select>

            <!-- Campaign Filter -->
            <select wire:model="selectedCampaignId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <option value="">All Campaigns</option>
                @foreach($campaigns as $campaign)
                    <option value="{{ $campaign['id'] }}">{{ $campaign['name'] }}</option>
                @endforeach
            </select>

            <!-- Print Button -->
            <button id="print-performance-stats" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                <svg class="w-4 h-4 mr-2 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"></path>
                </svg>
                Print Report
            </button>
        </div>
    </x-content-header>

    <x-content-body>
        <!-- Performance Overview -->
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 mb-4">
            <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white mb-4">Performance Overview</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Average Performance -->
                <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Average Performance</h4>
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-4">
                            <span class="text-2xl font-bold text-blue-600 dark:text-blue-300">{{ $averagePerformance }}%</span>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Overall average performance score across all agents</p>
                        </div>
                    </div>
                </div>

                <!-- Top Performers -->
                <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Top Performers</h4>
                    <ul class="space-y-2">
                        @foreach($topPerformers as $performer)
                            <li class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $performer['name'] }}</span>
                                <span class="text-sm font-semibold text-green-600 dark:text-green-400">{{ $performer['performance'] }}%</span>
                            </li>
                        @endforeach
                    </ul>
                </div>

                <!-- Low Performers -->
                <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Low Performers</h4>
                    <ul class="space-y-2">
                        @foreach($lowPerformers as $performer)
                            <li class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $performer['name'] }}</span>
                                <span class="text-sm font-semibold text-red-600 dark:text-red-400">{{ $performer['performance'] }}%</span>
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Agent Performance Chart -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Agent Performance</h3>
                </div>
                <div id="agent-performance-bar-chart" class="h-80" data-chart="{{ $agentPerformanceData }}"></div>
            </div>

            <!-- Campaign Performance Chart -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Campaign Performance</h3>
                </div>
                <div id="campaign-performance-line-chart" class="h-80" data-chart="{{ $campaignPerformanceData }}"></div>
            </div>
        </div>

        <div class="grid grid-cols-1 gap-4 mb-4">
            <!-- Performance Comparison Chart -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Performance Comparison</h3>
                </div>
                <div id="performance-comparison-chart" class="h-80" data-chart="{{ $performanceComparisonData }}"></div>
            </div>
        </div>
    </x-content-body>
</x-content>

<!-- Include the statistics charts JavaScript -->
<script src="{{ asset('js/statistics-charts.js') }}"></script>
