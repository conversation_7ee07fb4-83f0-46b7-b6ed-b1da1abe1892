<x-content>
    <x-content-header title="Add Agents to Training">
        <x-page-button type="save" label="Add Selected" action="addToTraining"/>
        <x-page-button type="cancel" label="Cancel" action="cancel"/>
    </x-content-header>

    <x-card key="add-agents-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <div class="lg:pr-3">
                        <label for="agents-search" class="sr-only">Search</label>
                        <div class="relative mt-1 lg:w-64 xl:w-96">
                            <input
                                type="text"
                                wire:model.live.debounce.300ms="search"
                                id="agents-search"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Search for agent">
                        </div>
                    </div>
                    <div class="flex pl-0 mt-3 space-x-1 sm:pl-2 sm:mt-0">
                        <div class="flex items-center">
                            <input
                                id="select-all"
                                type="checkbox"
                                wire:click="$toggle('selectAll')"
                                wire:change="updatedSelectAll"
                                {{ $selectAll ? 'checked' : '' }}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                            >
                            <label for="select-all" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Select All</label>
                        </div>
                    </div>
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <select wire:model.live="statusFilter" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 mr-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        @foreach ($statusOptions as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>

                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        @foreach ([8, 12, 24, 36, 48] as $value)
                            <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
    </x-card>

    @if (session()->has('message'))
        <div class="p-4 mt-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            {{ session('message') }}
        </div>
    @endif

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mt-4">
        @forelse ($agents as $agent)
            <x-card key="agent-{{ $agent->id }}" class="max-w-sm">
                <div class="flex justify-between px-4 pt-4">
                    <div class="flex items-center">
                        <input
                            id="checkbox-{{ $agent->id }}"
                            type="checkbox"
                            wire:click="toggleAgentSelection({{ $agent->id }})"
                            {{ in_array((string)$agent->id, $selectedAgents) ? 'checked' : '' }}
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                        >
                        <label for="checkbox-{{ $agent->id }}" class="sr-only">Select agent</label>
                    </div>
                </div>
                <div class="flex flex-col items-center pb-10">
                    <img class="w-24 h-24 mb-3 rounded-full shadow-lg" src="https://avatar.iran.liara.run/public/{{$agent->id}}" alt="Agent image"/>
                    <a href="{{ route('agents.show', $agent->id)}}" wire:navigate>
                        <h5 class="mb-1 text-xl font-medium text-gray-900 dark:text-white">{{ $agent->getFullNameAttribute() }}</h5>
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ $agent->email }}</span>
                    </a>

                    @if($agent->status === 'inactive')
                    <div class="mt-2">
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                            Recycled from production
                        </span>
                    </div>
                    @endif

                    <div class="flex mt-4 space-x-2">
                        <button
                            type="button"
                            wire:click="toggleAgentSelection({{ $agent->id }})"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                        >
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                @if(in_array((string)$agent->id, $selectedAgents))
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                @else
                                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                                @endif
                            </svg>
                            {{ in_array((string)$agent->id, $selectedAgents) ? 'Deselect' : 'Select' }}
                        </button>
                    </div>
                </div>
            </x-card>
        @empty
            <div class="col-span-full p-4 text-center text-gray-500 dark:text-gray-400">
                No active agents found matching your criteria.
            </div>
        @endforelse
    </div>

    <div class="mt-4">
        {{ $agents->links() }}
    </div>
</x-content>
