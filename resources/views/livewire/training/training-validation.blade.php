<x-content>
    <form wire:submit="submit" class="space-y-8">
        <x-content-header title="Agent validation">
            <x-page-button type="save" label="Save" action="submit"/>
            <x-page-button type="cancel" label="Cancel" :href="route('training.agents')" wire:navigate/>
        </x-content-header>
        <x-content-body>
            {{-- general information --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="campaign_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign (Optional)</label>
                        <select id="campaign_id" wire:model="campaign_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Validate without assigning to campaign</option>
                            @foreach($campaigns as $campaign)
                                <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                            @endforeach
                        </select>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">If no campaign is selected, the agent will be validated but remain available for future campaign assignment.</p>
                        @error('campaign_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="agent_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Agent Name</label>
                        <input type="text" id="agent_name" value="{{ $agent->getFullNameAttribute() }}" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" disabled>
                    </div>
                    <div class="col-span-6">
                        <label for="notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                        <textarea id="notes" wire:model="notes" rows="4" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Add any additional notes about the agent's validation"></textarea>
                        @error('notes') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
