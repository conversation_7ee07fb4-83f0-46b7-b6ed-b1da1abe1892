<x-content>
    <form wire:submit="deleteModule">
        <x-content-header title="Delete Training Module">
            @if($sessionsCount == 0)
                <x-page-button type="delete" label="Yes I'm sure" action="deleteModule"/>
            @endif
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-training-modules')"/>
        </x-content-header>
        <x-content-body>
            <div class="p-4 text-center bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <svg class="w-16 h-16 mx-auto text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <h3 class="mt-5 mb-6 text-lg text-gray-500 dark:text-gray-400">Are you sure you want to delete the training module "{{ $module->name }}"?</h3>

                @if($sessionsCount > 0)
                    <div class="flex items-center p-4 mb-6 text-sm text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300 dark:border-yellow-800" role="alert">
                        <svg class="flex-shrink-0 inline w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                        </svg>
                        <span class="sr-only">Warning</span>
                        <div>
                            <span class="font-medium">Cannot delete!</span> This module is currently used in {{ $sessionsCount }} training session(s). You must remove it from all sessions before deleting.
                        </div>
                    </div>
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                    <div class="text-left">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Module Name</h4>
                        <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{{ $module->name }}</p>
                    </div>
                    <div class="text-left">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h4>
                        <p class="mt-1">
                            <span class="px-2.5 py-0.5 text-xs font-medium rounded {{ $module->status_badge }}">{{ ucfirst($module->status) }}</span>
                        </p>
                    </div>
                    <div class="text-left">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</h4>
                        <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{{ $module->duration }}</p>
                    </div>
                    <div class="text-left">
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Credits</h4>
                        <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{{ $module->credits }}</p>
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
