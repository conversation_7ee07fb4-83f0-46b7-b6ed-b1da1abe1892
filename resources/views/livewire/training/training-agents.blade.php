<x-content>
    <x-card key="training-agents-header" class="p-4">
        <div class="w-full">
            <!-- Search and Actions Row -->
            <div class="flex flex-col md:flex-row justify-between gap-4 mb-4">
                <!-- Search Box -->
                <div class="w-full md:w-1/3">
                    <label for="agents-search" class="sr-only">Search</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                            </svg>
                        </div>
                        <input
                            type="text"
                            wire:model.live.debounce.300ms="search"
                            id="agents-search"
                            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            placeholder="Search for agent">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-2">
                    <button
                        type="button"
                        wire:click="$dispatch('to-agent-create')"
                        class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                    >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                        Create New Agent
                    </button>

                    <button
                        type="button"
                        wire:click="$dispatch('addToTraining')"
                        class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-300 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                    >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                        Add to Training
                    </button>

                    <button
                        wire:click="$dispatch('removeFromTraining')"
                        wire:confirm="Are you sure you want to remove the selected agents from training?"
                        class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg {{count($selectedAgents) ? 'bg-red-600 hover:bg-red-700 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800' : 'bg-gray-400 cursor-not-allowed'}}"
                        {{count($selectedAgents) ? '' : 'disabled'}}
                    >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                        Remove Selected
                    </button>
                </div>
            </div>

            <!-- Filters Row -->
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mt-4">
                <!-- Select All Checkbox -->
                <div class="flex items-center mb-3 sm:mb-0">
                    <input
                        id="select-all"
                        type="checkbox"
                        wire:click="toggleSelectAll"
                        {{ $selectAll ? 'checked' : '' }}
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                    >
                    <label for="select-all" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Select All</label>
                </div>

                <!-- Filtering Controls -->
                <div class="flex flex-wrap gap-2 items-center">
                    <select wire:model.live="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="in_training">In Training</option>
                        <option value="inactive">Recycled</option>
                        <option value="">All Statuses</option>
                    </select>

                    <select wire:model.live="moduleFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Modules</option>
                        @foreach($trainingModules as $module)
                            <option value="{{ $module->id }}">{{ $module->name }}</option>
                        @endforeach
                    </select>

                    <select wire:model.live="ratingFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Ratings</option>
                        <option value="low">Low (0-1)</option>
                        <option value="medium">Medium (1-2)</option>
                        <option value="high">High (2-3)</option>
                    </select>

                    <select wire:model.live="progressFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Progress</option>
                        <option value="beginner">Beginner (0-33%)</option>
                        <option value="intermediate">Intermediate (34-66%)</option>
                        <option value="advanced">Advanced (67-100%)</option>
                    </select>

                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        @foreach ([8, 12, 24, 36, 48] as $value)
                            <option value="{{ $value }}">{{ $value }} per page</option>
                        @endforeach
                    </select>

                    @if($ratingFilter || $progressFilter || $moduleFilter)
                    <button
                        wire:click="clearFilters"
                        type="button"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900"
                    >
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear Filters
                    </button>
                    @endif
                </div>
            </div>
        </div>
    </x-card>

    @if (session()->has('message'))
        <div class="p-4 mt-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            {{ session('message') }}
        </div>
    @endif

    @if($ratingFilter || $progressFilter || $moduleFilter)
        <div class="p-3 mt-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
            <div class="flex flex-wrap items-center gap-2">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="font-medium">Active filters:</span>
                </div>

                @if($ratingFilter)
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{
                        $ratingFilter === 'low' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                        ($ratingFilter === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300')
                    }}">
                        Rating: {{ ucfirst($ratingFilter) }}
                        <button type="button" wire:click="$set('ratingFilter', '')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">Remove filter</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endif

                @if($progressFilter)
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                        Progress: {{ ucfirst($progressFilter) }}
                        <button type="button" wire:click="$set('progressFilter', '')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">Remove filter</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endif

                @if($moduleFilter)
                    <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                        Module: {{ $trainingModules->firstWhere('id', $moduleFilter)->name ?? 'Unknown' }}
                        <button type="button" wire:click="$set('moduleFilter', '')" class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-xs rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                            <span class="sr-only">Remove filter</span>
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                @endif

                <button
                    wire:click="clearFilters"
                    type="button"
                    class="ml-auto text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                >
                    Clear all filters
                </button>
            </div>
        </div>
    @endif

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 mt-4">
        @forelse ($agents as $agent)
            <x-card key="agent-{{ $agent->id }}" class="h-full flex flex-col">
                <div class="flex justify-between px-4 pt-4">
                    <div class="flex items-center">
                        <input
                            id="checkbox-{{ $agent->id }}"
                            type="checkbox"
                            wire:click="toggleAgentSelection({{ $agent->id }})"
                            {{ in_array((string)$agent->id, $selectedAgents) ? 'checked' : '' }}
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 cursor-pointer"
                        >
                        <label for="checkbox-{{ $agent->id }}" class="sr-only">Select agent</label>
                    </div>
                    <button id="dropdownButton-{{ $agent->id }}" data-dropdown-toggle="dropdown-{{ $agent->id }}" class="inline-block text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-1.5" type="button">
                        <span class="sr-only">Open dropdown</span>
                        <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 3">
                            <path d="M2 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm6.041 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM14 0a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Z"/>
                        </svg>
                    </button>
                    <!-- Dropdown menu -->
                    <div id="dropdown-{{ $agent->id }}" class="z-10 hidden text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow-sm w-44 dark:bg-gray-700">
                        <ul class="py-2" aria-labelledby="dropdownButton-{{ $agent->id }}">
                            <li>
                                <a
                                    href="{{ route('training.validation', $agent->id)}}"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
                                    wire:navigate
                                >
                                    Validation
                                </a>
                            </li>
                            <li>
                                <a
                                    href="{{ route('training.observation', $agent->id)}}"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
                                    wire:navigate
                                >
                                    Observation
                                </a>
                            </li>
                            <li>
                                <a
                                    href="{{ route('training.report', $agent->id)}}"
                                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
                                    wire:navigate
                                >
                                    Reports
                                </a>
                            </li>
                            <li>
                                <a
                                    href="{{ route('training.remove', $agent->id)}}"
                                    class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white"
                                    wire:navigate
                                >
                                    Remove
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="flex flex-col items-center flex-grow">
                    <img class="w-20 h-20 mb-3 rounded-full shadow-lg" src="https://avatar.iran.liara.run/public/{{$agent->id}}" alt="Agent image"/>
                    <a href="{{ route('agents.show', $agent->id)}}" wire:navigate class="text-center">
                        <h5 class="mb-1 text-lg font-medium text-gray-900 dark:text-white">{{ $agent->getFullNameAttribute() }}</h5>
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ $agent->email }}</span>
                    </a>

                    @if($agent->status === 'inactive')
                    <div class="mt-2">
                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                            Recycled from production
                        </span>
                    </div>
                    @endif

                    @if($agent->training && $agent->training->module)
                    <div class="mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                            {{ $agent->training->module->name }}
                        </span>
                    </div>
                    @endif

                    <div class="w-full px-4 mt-3">
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-blue-700 dark:text-white">Training Progress</span>
                            <span class="text-sm font-medium text-blue-700 dark:text-white">
                                {{ $agent->training ? $agent->training->progress : 0 }}%
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="bg-blue-600 h-2.5 rounded-full"
                                 style="width: {{ $agent->training ? $agent->training->progress : 0 }}%">
                            </div>
                        </div>

                        <div class="flex justify-between mt-3 mb-1">
                            <span class="text-sm font-medium text-blue-700 dark:text-white">Rating</span>
                            @php
                                $rating = $agent->training && isset($agent->training->rating) ? $agent->training->rating : 0;
                            @endphp
                            <span class="text-sm font-medium
                                {{ $rating <= 1 ? 'text-red-600 dark:text-red-400' :
                                   ($rating <= 2 ? 'text-yellow-600 dark:text-yellow-400' :
                                   'text-green-600 dark:text-green-400') }}">
                                {{ number_format($rating, 1) }}/3
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $rating <= 1 ? 'bg-red-600' :
                                          ($rating <= 2 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ ($rating / 3) * 100 }}%">
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-wrap gap-2 justify-center mt-auto pt-4 pb-4 w-full px-4">
                        <a
                            href="{{ route('training.validation', $agent->id)}}"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                            Validate
                        </a>
                        <a
                            href="{{ route('training.observation', $agent->id)}}"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
                            Observation
                        </a>
                        <a
                            href="{{ route('training.report', $agent->id)}}"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm2 10a1 1 0 10-2 0v3a1 1 0 102 0v-3zm2-3a1 1 0 011 1v5a1 1 0 11-2 0v-5a1 1 0 011-1zm4-1a1 1 0 10-2 0v7a1 1 0 102 0V8z" clip-rule="evenodd"></path></svg>
                            Reports
                        </a>
                    </div>
                </div>
            </x-card>
        @empty
            <div class="col-span-full p-4 text-center text-gray-500 dark:text-gray-400">
                No agents found matching your criteria.
            </div>
        @endforelse
    </div>

    <div class="mt-4">
        {{ $agents->links() }}
    </div>
</x-content>
