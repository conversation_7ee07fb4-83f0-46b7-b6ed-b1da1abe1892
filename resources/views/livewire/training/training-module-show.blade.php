<x-content>
    <x-content-header title="{{ $module->name }}">
        <x-page-button type="edit" label="Edit" :href="route('training.modules.edit', $module)" wire:navigate/>
        <x-page-button type="delete" label="Delete" :href="route('training.modules.delete', $module)" wire:navigate/>
        <x-page-button type="back" label="Back" action="$dispatch('to-training-modules')"/>
    </x-content-header>
    <x-content-body>
        {{-- Module Information --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Module Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h4>
                    <p class="mt-1">
                        <span class="px-2.5 py-0.5 text-xs font-medium rounded {{ $module->status_badge }}">{{ ucfirst($module->status) }}</span>
                    </p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</h4>
                    <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{{ $module->duration }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Credits</h4>
                    <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{{ $module->credits }}</p>
                </div>
                <div>
                    <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</h4>
                    <p class="mt-1 text-sm font-medium text-gray-900 dark:text-white">{{ $module->created_at->format('M d, Y') }}</p>
                </div>
            </div>
            <div class="mt-4">
                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h4>
                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $module->description }}</p>
            </div>
        </div>

        {{-- Prerequisites --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Prerequisites</h3>
            @if(!empty($module->prerequisites) && count($module->prerequisites) > 0)
                <ul class="space-y-1 list-disc list-inside text-gray-900 dark:text-white">
                    @foreach($module->prerequisites as $prerequisite)
                        <li>{{ $prerequisite }}</li>
                    @endforeach
                </ul>
            @else
                <p class="text-sm text-gray-500 dark:text-gray-400">No prerequisites for this module.</p>
            @endif
        </div>

        {{-- Module Content --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Module Content</h3>
            <div class="prose prose-gray max-w-none text-gray-900 dark:prose-invert dark:text-gray-100">
                @if(!empty($module->content))
                    {!! \Illuminate\Support\Str::markdown($module->content) !!}
                @else
                    <p class="text-sm text-gray-500 dark:text-gray-400">No content available for this module.</p>
                @endif
            </div>
        </div>

        {{-- Training Sessions Using This Module --}}
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold dark:text-white">Training Sessions Using This Module</h3>
            @if(count($sessions) > 0)
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-4 py-3">Session Name</th>
                                <th scope="col" class="px-4 py-3">Instructor</th>
                                <th scope="col" class="px-4 py-3">Dates</th>
                                <th scope="col" class="px-4 py-3">Status</th>
                                <th scope="col" class="px-4 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($sessions as $session)
                                <tr class="border-b dark:border-gray-700">
                                    <td class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">{{ $session->name }}</td>
                                    <td class="px-4 py-3">{{ $session->instructor->name ?? 'N/A' }}</td>
                                    <td class="px-4 py-3">{{ $session->start_date->format('M d, Y') }} - {{ $session->end_date->format('M d, Y') }}</td>
                                    <td class="px-4 py-3">
                                        <span class="bg-{{ $session->status === 'active' ? 'green' : ($session->status === 'completed' ? 'blue' : 'yellow') }}-100 text-{{ $session->status === 'active' ? 'green' : ($session->status === 'completed' ? 'blue' : 'yellow') }}-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-{{ $session->status === 'active' ? 'green' : ($session->status === 'completed' ? 'blue' : 'yellow') }}-900 dark:text-{{ $session->status === 'active' ? 'green' : ($session->status === 'completed' ? 'blue' : 'yellow') }}-300">
                                            {{ ucfirst($session->status) }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3">
                                        <a href="{{ route('training.sessions.show', $session) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline" wire:navigate>View</a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <p class="text-sm text-gray-500 dark:text-gray-400">This module is not currently used in any training sessions.</p>
            @endif
        </div>
    </x-content-body>
</x-content>
