<x-content>
    <x-card key="training-session-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <div class="flex items-center">
                        <h1 class="text-xl font-semibold text-gray-900 dark:text-white">{{ $session->name }}</h1>
                        <span class="ml-2 px-2.5 py-0.5 text-xs font-medium rounded-full {{ $session->status_badge }}">
                            {{ ucfirst($session->status) }}
                        </span>
                    </div>
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <button 
                        type="button" 
                        wire:click="$dispatch('to-training-session-edit', { session: {{ $session->id }} })"
                        class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                    >
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"></path><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>
                        Edit Session
                    </button>
                    <button 
                        type="button" 
                        wire:click="$dispatch('to-training-sessions')"
                        class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    >
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                        Back to Sessions
                    </button>
                </div>
            </div>
        </div>
    </x-card>

    @if (session()->has('message'))
        <div class="p-4 mt-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="p-4 mt-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
            {{ session('error') }}
        </div>
    @endif

    <div class="grid grid-cols-1 gap-4 mt-4 xl:grid-cols-3">
        <!-- Session Details -->
        <div class="xl:col-span-1">
            <x-card key="session-details" class="p-4">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Session Details</h3>
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $session->start_date->format('M d, Y') }} - {{ $session->end_date->format('M d, Y') }}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $session->duration }} days</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Location</h4>
                        <p class="text-base text-gray-900 dark:text-white">{{ $session->location ?? 'Not specified' }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Instructor</h4>
                        <p class="text-base text-gray-900 dark:text-white">
                            @if ($session->instructor)
                                {{ $session->instructor->getFullNameAttribute() }}
                            @else
                                <span class="text-gray-500 dark:text-gray-400">Not assigned</span>
                            @endif
                        </p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Capacity</h4>
                        <div class="flex items-center mt-1">
                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ min(100, ($session->trainees->count() / max(1, $session->capacity)) * 100) }}%"></div>
                            </div>
                            <span>{{ $session->trainees->count() }}/{{ $session->capacity }}</span>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Progress</h4>
                        <div class="flex items-center mt-1">
                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                <div class="bg-green-600 h-2.5 rounded-full" style="width: {{ $session->progress }}%"></div>
                            </div>
                            <span>{{ $session->progress }}%</span>
                        </div>
                    </div>
                    @if ($session->description)
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</h4>
                            <p class="text-sm text-gray-900 dark:text-white">{{ $session->description }}</p>
                        </div>
                    @endif
                </div>
            </x-card>

            <!-- Training Modules -->
            <x-card key="session-modules" class="p-4 mt-4">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Training Modules</h3>
                @if ($modules->count() > 0)
                    <ol class="relative border-l border-gray-200 dark:border-gray-700">
                        @foreach ($modules as $module)
                            <li class="mb-6 ml-4">
                                <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-900 dark:bg-gray-700"></div>
                                <h3 class="text-base font-semibold text-gray-900 dark:text-white">{{ $module->name }}</h3>
                                <p class="mb-1 text-sm font-normal text-gray-500 dark:text-gray-400">{{ $module->description }}</p>
                                <div class="flex items-center space-x-2">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">{{ $module->duration }}</span>
                                    <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-purple-900 dark:text-purple-300">{{ $module->credits }} credits</span>
                                </div>
                            </li>
                        @endforeach
                    </ol>
                @else
                    <p class="text-sm text-gray-500 dark:text-gray-400">No modules assigned to this session.</p>
                @endif
            </x-card>
        </div>

        <!-- Enrolled Trainees -->
        <div class="xl:col-span-2">
            <x-card key="session-trainees" class="p-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Enrolled Trainees</h3>
                    <button 
                        type="button" 
                        data-modal-target="add-trainee-modal" 
                        data-modal-toggle="add-trainee-modal"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                    >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                        Add Trainee
                    </button>
                </div>

                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th scope="col" class="px-6 py-3">Name</th>
                                <th scope="col" class="px-6 py-3">Status</th>
                                <th scope="col" class="px-6 py-3">Enrollment Date</th>
                                <th scope="col" class="px-6 py-3">Completion Date</th>
                                <th scope="col" class="px-6 py-3">Grade</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($trainees as $trainee)
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                        {{ $trainee->getFullNameAttribute() }}
                                    </th>
                                    <td class="px-6 py-4">
                                        <span class="px-2.5 py-0.5 text-xs font-medium rounded-full 
                                            @if($trainee->pivot->status === 'enrolled') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                            @elseif($trainee->pivot->status === 'in_progress') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                            @elseif($trainee->pivot->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @else bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                            @endif">
                                            {{ ucwords(str_replace('_', ' ', $trainee->pivot->status)) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ $trainee->pivot->enrollment_date ? $trainee->pivot->enrollment_date->format('M d, Y') : 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ $trainee->pivot->completion_date ? $trainee->pivot->completion_date->format('M d, Y') : 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4">
                                        {{ $trainee->pivot->grade ?? 'N/A' }}
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center space-x-2">
                                            <button 
                                                type="button" 
                                                data-dropdown-toggle="trainee-status-{{ $trainee->id }}"
                                                class="text-blue-600 dark:text-blue-500 hover:underline"
                                            >
                                                Update Status
                                            </button>
                                            <div id="trainee-status-{{ $trainee->id }}" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownDefaultButton">
                                                    <li>
                                                        <a wire:click="updateTraineeStatus({{ $trainee->id }}, 'enrolled')" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white cursor-pointer">Enrolled</a>
                                                    </li>
                                                    <li>
                                                        <a wire:click="updateTraineeStatus({{ $trainee->id }}, 'in_progress')" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white cursor-pointer">In Progress</a>
                                                    </li>
                                                    <li>
                                                        <a wire:click="updateTraineeStatus({{ $trainee->id }}, 'completed')" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white cursor-pointer">Completed</a>
                                                    </li>
                                                    <li>
                                                        <a wire:click="updateTraineeStatus({{ $trainee->id }}, 'dropped')" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white cursor-pointer">Dropped</a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <button 
                                                type="button" 
                                                wire:click="removeTrainee({{ $trainee->id }})"
                                                class="text-red-600 dark:text-red-500 hover:underline"
                                            >
                                                Remove
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                    <td colspan="6" class="px-6 py-4 text-center">No trainees enrolled in this session.</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    {{ $trainees->links() }}
                </div>
            </x-card>
        </div>
    </div>

    <!-- Add Trainee Modal -->
    <div id="add-trainee-modal" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
        <div class="relative w-full max-w-md max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-medium text-gray-900 dark:text-white">
                        Add Trainee to Session
                    </h3>
                    <button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="add-trainee-modal">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="p-4 md:p-5">
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Select an agent to add to this training session:</p>
                    <div class="space-y-4 max-h-60 overflow-y-auto">
                        @forelse ($availableAgents as $agent)
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg dark:border-gray-600">
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $agent->getFullNameAttribute() }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $agent->email }}</p>
                                </div>
                                <button 
                                    type="button" 
                                    wire:click="addTrainee({{ $agent->id }})"
                                    class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                                >
                                    Add
                                </button>
                            </div>
                        @empty
                            <p class="text-sm text-gray-500 dark:text-gray-400">No available agents to add.</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-content>
