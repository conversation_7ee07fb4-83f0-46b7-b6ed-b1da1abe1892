<x-content>
    <x-card key="training-statistics-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Training Statistics</h2>
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <button type="button" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path></svg>
                        Export Report
                    </button>
                </div>
            </div>
        </div>
    </x-card>

    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 mt-4">
        <!-- Agents in Training Card -->
        <x-card key="stat-agents" class="p-4">
            <div class="flex items-center">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-12 h-12 text-blue-500 bg-blue-100 rounded-lg dark:bg-blue-900 dark:text-blue-300">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                </div>
                <div class="ms-4">
                    <h3 class="mb-1 text-xl font-semibold text-gray-900 dark:text-white">{{ $statistics['agentsInTraining'] }}</h3>
                    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">Agents in Training</p>
                </div>
            </div>
        </x-card>

        <!-- Completed Trainings Card -->
        <x-card key="stat-completed" class="p-4">
            <div class="flex items-center">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-12 h-12 text-green-500 bg-green-100 rounded-lg dark:bg-green-900 dark:text-green-300">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ms-4">
                    <h3 class="mb-1 text-xl font-semibold text-gray-900 dark:text-white">{{ $statistics['completedTrainings'] }}</h3>
                    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">Completed Trainings</p>
                </div>
            </div>
        </x-card>

        <!-- Ongoing Trainings Card -->
        <x-card key="stat-ongoing" class="p-4">
            <div class="flex items-center">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-12 h-12 text-yellow-500 bg-yellow-100 rounded-lg dark:bg-yellow-900 dark:text-yellow-300">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ms-4">
                    <h3 class="mb-1 text-xl font-semibold text-gray-900 dark:text-white">{{ $statistics['ongoingTrainings'] }}</h3>
                    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">Ongoing Trainings</p>
                </div>
            </div>
        </x-card>

        <!-- Success Rate Card -->
        <x-card key="stat-success" class="p-4">
            <div class="flex items-center">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-12 h-12 text-purple-500 bg-purple-100 rounded-lg dark:bg-purple-900 dark:text-purple-300">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                        <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                    </svg>
                </div>
                <div class="ms-4">
                    <h3 class="mb-1 text-xl font-semibold text-gray-900 dark:text-white">{{ $statistics['successRate'] }}%</h3>
                    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">Success Rate</p>
                </div>
            </div>
        </x-card>
    </div>

    <!-- Training Progress Chart -->
    <div class="grid grid-cols-1 gap-4 mt-4">
        <x-card key="training-progress-chart" class="p-4">
            <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Training Progress Over Time</h3>
            <div class="relative w-full h-80">
                <!-- Placeholder for chart -->
                <div class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p class="text-gray-500 dark:text-gray-400">Chart visualization would be displayed here</p>
                </div>
            </div>
        </x-card>
    </div>

    <!-- Training Metrics -->
    <div class="grid grid-cols-1 gap-4 mt-4 lg:grid-cols-2">
        <!-- Average Training Duration -->
        <x-card key="avg-duration" class="p-4">
            <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Average Training Duration</h3>
            <div class="flex items-center">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-12 h-12 text-blue-500 bg-blue-100 rounded-lg dark:bg-blue-900 dark:text-blue-300">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ms-4">
                    <h3 class="mb-1 text-xl font-semibold text-gray-900 dark:text-white">{{ $statistics['avgDuration'] }} days</h3>
                    <p class="text-sm font-normal text-gray-500 dark:text-gray-400">Average time to complete training</p>
                </div>
            </div>
        </x-card>

        <!-- Training Completion by Module -->
        <x-card key="completion-by-module" class="p-4">
            <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Training Completion by Module</h3>
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-white">Introduction to Call Center</span>
                        <span class="text-sm font-medium text-blue-700 dark:text-white">85%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: 85%"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-white">Product Knowledge</span>
                        <span class="text-sm font-medium text-blue-700 dark:text-white">70%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: 70%"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-white">Call Handling Techniques</span>
                        <span class="text-sm font-medium text-blue-700 dark:text-white">60%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: 60%"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm font-medium text-blue-700 dark:text-white">CRM System Training</span>
                        <span class="text-sm font-medium text-blue-700 dark:text-white">45%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                    </div>
                </div>
            </div>
        </x-card>
    </div>
</x-content>
