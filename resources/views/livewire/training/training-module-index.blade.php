<x-content>
    <x-card key="training-modules-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <div class="lg:pr-3">
                        <label for="modules-search" class="sr-only">Search</label>
                        <div class="relative mt-1 lg:w-64 xl:w-96">
                            <input
                                type="text"
                                wire:model.live.debounce.300ms="search"
                                id="modules-search"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Search for modules">
                        </div>
                    </div>
                </div>
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <div class="lg:pr-3">
                        <button
                            wire:click="deleteSelected"
                            wire:confirm="Are you sure you want to delete the selected modules? This action cannot be undone."
                            class="inline-flex justify-center p-1 text-gray-500 rounded {{count($selectedModules) > 0 ? 'cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white' : 'opacity-50 cursor-not-allowed'}}"
                            {{count($selectedModules) > 0 ? '' : 'disabled'}}
                        >
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                        </button>
                    </div>
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <a
                        href="{{ route('training.modules.create') }}"
                        class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                        wire:navigate
                    >
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                        Add Module
                    </a>
                    <select wire:model.live="status" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Statuses</option>
                        <option value="active">Active</option>
                        <option value="draft">Draft</option>
                        <option value="archived">Archived</option>
                    </select>
                    <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        @foreach ([5, 10, 25, 50, 100] as $value)
                            <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
    </x-card>

    @if (session()->has('message'))
        <div class="p-4 mt-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            {{ session('message') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="p-4 mt-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
            {{ session('error') }}
        </div>
    @endif

    <x-card key="module-index-table" class="overflow-x-auto mt-4">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="p-4 w-4">
                        <input
                            type="checkbox"
                            wire:model.live="selectAll"
                            wire:key="select-all-{{ $selectAll ? 'checked' : 'unchecked' }}"
                            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                            aria-label="Select all modules"
                        >
                    </th>
                    <th scope="col" class="px-4 py-3">Name</th>
                    <th scope="col" class="px-4 py-3">Duration</th>
                    <th scope="col" class="px-4 py-3">Credits</th>
                    <th scope="col" class="px-4 py-3">Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($modules as $module)
                    <tr wire:key="module-{{ $module->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                        <td class="p-4 w-4">
                            <input
                                type="checkbox"
                                wire:change="toggleModuleSelection({{ $module->id }})"
                                wire:key="checkbox-{{ $module->id }}-{{ in_array((string)$module->id, $selectedModules) ? 'checked' : 'unchecked' }}"
                                {{ in_array((string)$module->id, $selectedModules) ? 'checked' : '' }}
                                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                aria-label="Select module {{ $module->name }}"
                            >
                        </td>
                        <th scope="row" class="flex items-center px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white cursor-pointer" wire:click="showModule({{ $module->id }})">
                            <div class="ps-3">
                                <div class="text-base font-semibold">{{ $module->name }}</div>
                                <div class="font-normal text-gray-500">{{ \Illuminate\Support\Str::limit($module->description, 60) }}</div>
                            </div>
                        </th>
                        <td class="px-4 py-3 cursor-pointer" wire:click="showModule({{ $module->id }})">{{ $module->duration }}</td>
                        <td class="px-4 py-3 cursor-pointer" wire:click="showModule({{ $module->id }})">{{ $module->credits }}</td>
                        <td class="px-4 py-3 cursor-pointer" wire:click="showModule({{ $module->id }})">
                            <div class="flex items-center">
                                <div class="h-2.5 w-2.5 rounded-full {{ $module->status === 'active' ? 'bg-green-500' : 'bg-red-500' }} me-2"></div>
                                {{ ucfirst($module->status) }}
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </x-card>

    <div class="mt-4">
        {{ $modules->links() }}
    </div>
</x-content>
