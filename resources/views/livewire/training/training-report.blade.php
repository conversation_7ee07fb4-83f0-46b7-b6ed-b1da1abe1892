<x-content>
    <div class="space-y-8">
        <x-content-header title="Agent Training Report">
            <x-page-button type="back" label="Back" :href="route('training.agents')" wire:navigate/>
        </x-content-header>

        @if (session()->has('message'))
        <div class="p-4 mt-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
            {{ session('message') }}
        </div>
        @endif
        <x-content-body>
            @if($training)
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <x-card key="performance-card" class="p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Training Metrics</h3>

                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Progress</h4>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $training->progress <= 60 ? 'bg-red-600' :
                                          ($training->progress <= 80 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ $training->progress }}%">
                            </div>
                        </div>
                        <p class="mt-1 text-sm {{ $training->progress <= 60 ? 'text-red-600 dark:text-red-400' :
                                                  ($training->progress <= 80 ? 'text-yellow-600 dark:text-yellow-400' :
                                                  'text-green-600 dark:text-green-400') }}">
                            {{ $training->progress }}% Complete
                        </p>
                    </div>

                    @if(isset($training->rating))
                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Rating</h4>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="{{ $training->rating <= 1 ? 'bg-red-600' :
                                          ($training->rating <= 2 ? 'bg-yellow-500' :
                                          'bg-green-600') }} h-2.5 rounded-full"
                                 style="width: {{ ($training->rating / 3) * 100 }}%">
                            </div>
                        </div>
                        <p class="mt-1 text-sm
                            {{ $training->rating <= 1 ? 'text-red-600 dark:text-red-400' :
                               ($training->rating <= 2 ? 'text-yellow-600 dark:text-yellow-400' :
                               'text-green-600 dark:text-green-400') }}">
                            {{ number_format($training->rating, 1) }}/3 Rating
                        </p>
                    </div>
                    @endif

                    <div class="mb-4">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Validation Status</h4>
                        <div class="flex items-center">
                            @if($training->validated_at)
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span class="text-sm text-green-600 dark:text-green-400">Validated on {{ $training->validated_at->format('M d, Y') }}</span>
                            @else
                                <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                                <span class="text-sm text-yellow-600 dark:text-yellow-400">Not Validated</span>
                            @endif
                        </div>
                    </div>
                </x-card>

                <x-card key="agent-info" class="p-4">
                    <div class="flex flex-col items-center">
                        <img class="w-24 h-24 mb-3 rounded-full shadow-lg" src="https://avatar.iran.liara.run/public/{{$agent->id}}" alt="Agent image"/>
                        <h5 class="mb-1 text-xl font-medium text-gray-900 dark:text-white">{{ $agent->getFullNameAttribute() }}</h5>
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ $agent->email }}</span>

                        <div class="mt-4 flex flex-col w-full">
                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                                <span class="text-gray-500 dark:text-gray-400">Status</span>
                                <span class="font-medium {{ $agent->status === 'active' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                    {{ ucfirst($agent->status) }}
                                </span>
                            </div>
                            <div class="flex justify-between py-2 border-b border-gray-200 dark:border-gray-700">
                                <span class="text-gray-500 dark:text-gray-400">Start Date</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $training->start_date ? $training->start_date->format('M d, Y') : 'N/A' }}</span>
                            </div>
                            <div class="flex justify-between py-2">
                                <span class="text-gray-500 dark:text-gray-400">Last Observation</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ $training->updated_at ? $training->updated_at->format('M d, Y') : 'N/A' }}</span>
                            </div>
                        </div>
                    </div>
                </x-card>

                <x-card key="action-buttons" class="p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
                    <div class="flex flex-col gap-3">
                        <a
                            href="{{ route('training.observation', $agent->id)}}"
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
                            New Observation
                        </a>
                        <a
                            href="{{ route('training.validation', $agent->id)}}"
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-center {{ $training->validated_at ? 'text-gray-900 bg-white border border-gray-300' : 'text-white bg-green-600' }} rounded-lg hover:bg-{{ $training->validated_at ? 'gray-100' : 'green-700' }} focus:ring-4 focus:outline-none focus:ring-{{ $training->validated_at ? 'gray-200' : 'green-300' }} dark:bg-{{ $training->validated_at ? 'gray-800' : 'green-600' }} dark:text-white dark:border-{{ $training->validated_at ? 'gray-600' : 'green-600' }} dark:hover:bg-{{ $training->validated_at ? 'gray-700' : 'green-700' }} dark:hover:border-{{ $training->validated_at ? 'gray-700' : 'green-700' }} dark:focus:ring-{{ $training->validated_at ? 'gray-700' : 'green-800' }}"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
                            {{ $training->validated_at ? 'Validated' : 'Validate Training' }}
                        </a>
                        <button
                            onclick="window.print()"
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700"
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5 4v3H4a2 2 0 00-2 2v3a2 2 0 002 2h1v2a2 2 0 002 2h6a2 2 0 002-2v-2h1a2 2 0 002-2V9a2 2 0 00-2-2h-1V4a2 2 0 00-2-2H7a2 2 0 00-2 2zm8 0H7v3h6V4zm0 8H7v4h6v-4z" clip-rule="evenodd"></path></svg>
                            Print Report
                        </a>
                        <a
                            href="{{ route('training.agents')}}"
                            class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-700 dark:focus:ring-gray-700"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path></svg>
                            Back to Agents
                        </a>
                    </div>
                </x-card>
            </div>

            <div class="flex flex-col gap-4">
                <x-card key="training-notes" class="flex flex-col gap-4 p-6 w-full">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Training Notes</h3>
                    </div>

                    @if(!empty($training->notes))
                    <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">{{ Str::before($training->notes, ':') }}</h4>
                        <p class="text-sm text-gray-900 dark:text-white">{{ Str::after($training->notes, ': ') }}</p>
                    </div>
                    @else
                    <div class="flex flex-col items-center justify-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-gray-500 dark:text-gray-400 mb-2">No notes available</p>
                        <a
                            href="{{ route('training.observation', $agent->id)}}"
                            class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                            wire:navigate
                        >
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
                            Add Observation
                        </a>
                    </div>
                    @endif
                </x-card>
            </div>
            @else
            <x-card key="no-data" class="p-6">
                <div class="flex flex-col items-center justify-center py-8">
                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Training Data Available</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4 text-center">There is no training data available for this agent.</p>
                    <a
                        href="{{ route('training.observation', $agent->id)}}"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                        wire:navigate
                    >
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
                        Create First Observation
                    </a>
                </div>
            </x-card>
            @endif
        </x-content-body>
    </div>
</x-content>
