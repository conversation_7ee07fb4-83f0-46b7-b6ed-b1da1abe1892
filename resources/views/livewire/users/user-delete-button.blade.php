<div>
    <div class="flex flex-col space-y-2">
        <button wire:click="deleteUser" type="button" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"></path>
            </svg>
            Yes I'm sure
        </button>

        <div class="flex items-center">
            <input wire:model="forceDelete" wire:click="toggleForceDelete" id="force-delete-checkbox" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
            <label for="force-delete-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Force delete (removes all relationships)</label>
        </div>

        @if($forceDelete)
            <div class="p-2 text-xs text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                Warning: Force delete will remove all relationships associated with this user before deletion. This action cannot be undone.
            </div>
        @endif
    </div>
</div>
