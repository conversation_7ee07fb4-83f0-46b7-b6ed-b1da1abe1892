<x-content>
    <form wire:submit.prevent="createPermission">
        <x-content-header title="Create Permission">
            <button type="submit" class="inline-flex items-center px-5 py-2.5 text-sm font-medium text-center text-white bg-primary-700 rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800" wire:loading.attr="disabled">
                <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                </svg>
                <span wire:loading.remove>Create Permission</span>
                <span wire:loading>Creating...</span>
            </button>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-user-permission-index')" data-action="$dispatch('to-user-permission-index')" class="page-button"/>
        </x-content-header>

        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        @if ($errors->any())
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <ul class="list-disc pl-5 space-y-1">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <x-content-body>
            {{-- Permission Information --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Permission Information</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                            Permission Name <span class="text-red-500">*</span>
                        </label>
                        <div class="flex rounded-md shadow-sm">
                            <input 
                                type="text" 
                                id="name-prefix" 
                                value="{{ $group ? $group . '.' : '' }}" 
                                class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300" 
                                readonly
                            >
                            <input 
                                type="text" 
                                id="name" 
                                wire:model.live="name" 
                                class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-r-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                                placeholder="e.g., create, edit, delete"
                                autofocus
                            >
                        </div>
                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Full permission name will be: <span class="font-mono">{{ $group ? $group . '.' : '' }}<span x-text="document.getElementById('name').value"></span></span>
                        </p>
                        @error('name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="group" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                            Group <span class="text-red-500">*</span>
                        </label>
                        <div class="flex space-x-2">
                            <select 
                                id="group" 
                                wire:model.live="group" 
                                class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                            >
                                <option value="">Select a group</option>
                                @foreach($availableGroups as $availableGroup)
                                    <option value="{{ $availableGroup }}">{{ ucfirst($availableGroup) }}</option>
                                @endforeach
                                <option value="custom">Custom...</option>
                            </select>
                            @if($group === 'custom')
                                <input 
                                    type="text" 
                                    wire:model.live="customGroup" 
                                    placeholder="Enter group name"
                                    class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                >
                            @endif
                        </div>
                        @error('group') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6">
                        <label for="description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                            Description
                        </label>
                        <textarea 
                            id="description" 
                            wire:model.live="description" 
                            rows="3" 
                            class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                            placeholder="A brief description of what this permission allows"
                        ></textarea>
                        @error('description') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="guard_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                            Guard Name <span class="text-red-500">*</span>
                        </label>
                        <select 
                            id="guard_name" 
                            wire:model.live="guard_name" 
                            class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        >
                            <option value="web">Web</option>
                            <option value="api">API</option>
                        </select>
                        @error('guard_name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            {{-- Permission Preview --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Permission Preview</h3>
                <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Permission Name</p>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white font-mono">{{ $group ? $group . '.' : '' }}{{ $name ?: 'permission' }}</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Guard Name</p>
                            <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $guard_name }}</p>
                        </div>
                        @if($description)
                            <div class="col-span-2">
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</p>
                                <p class="mt-1 text-sm text-gray-900 dark:text-white">{{ $description }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>

@push('scripts')
<script>
    document.addEventListener('livewire:initialized', () => {
        // Auto-update the permission name when group changes
        Livewire.on('group-updated', (group) => {
            const nameInput = document.getElementById('name');
            if (nameInput && nameInput.value === '' && group) {
                nameInput.focus();
            }
        });
    });
</script>
@endpush
