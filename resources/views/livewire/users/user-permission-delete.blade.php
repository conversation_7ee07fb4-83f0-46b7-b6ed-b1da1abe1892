<x-content>
    <form action="#">
        <x-content-header title="Delete Permission">
            <x-page-button type="delete" label="Yes I'm sure" action="$dispatch('destroy-permission')" />
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-user-permission-show', { permission: {{ $permission->id }} })" />
        </x-content-header>

        <x-content-body>
            <div class="p-4 text-center bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <svg class="w-16 h-16 mx-auto text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-5 mb-6 text-lg text-gray-500 dark:text-gray-400">Are you sure you want to delete the permission "{{ $permission->name }}"?</h3>
                <p class="mb-5 text-sm text-gray-500 dark:text-gray-400">
                    This action cannot be undone. The permission will be permanently removed from the system and any roles or users assigned this permission will lose access.
                </p>

                @if($rolesCount > 0)
                    <div class="p-4 mb-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-400" role="alert">
                        <div class="flex">
                            <svg class="flex-shrink-0 w-4 h-4 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                            </svg>
                            <div class="ml-3">
                                <span class="font-medium">Warning!</span> This permission is assigned to {{ $rolesCount }} role(s) and {{ $usersCount }} user(s).
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-center mb-4">
                        <input wire:model="forceDelete" wire:click="toggleForceDelete" id="force-delete-checkbox" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                        <label for="force-delete-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                            Force delete (remove from all roles and users)
                        </label>
                    </div>

                    @if($forceDelete)
                        <div class="p-2 mb-4 text-xs text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                            <span class="font-medium">Warning:</span> Roles and users will lose this permission.
                        </div>
                    @endif
                @endif

                @error('delete_error')
                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        <span class="font-medium">Error!</span> {{ $message }}
                    </div>
                @enderror

                <div class="mt-6 p-4 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white">Permission Information</h4>
                    <ul class="space-y-2 text-sm text-left">
                        <li><strong>ID:</strong> {{ $permission->id }}</li>
                        <li><strong>Name:</strong> {{ $permission->name }}</li>
                        <li><strong>Guard:</strong> {{ $permission->guard_name }}</li>
                        <li><strong>Assigned to Roles:</strong> {{ $rolesCount }}</li>
                        <li><strong>Assigned to Users:</strong> {{ $usersCount }}</li>
                        <li><strong>Created:</strong> {{ $permission->created_at->format('M d, Y') }}</li>
                        @if(isset($permission->description))
                            <li class="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                                <strong>Description:</strong>
                                <p class="mt-1 text-gray-600 dark:text-gray-400">{{ $permission->description }}</p>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
