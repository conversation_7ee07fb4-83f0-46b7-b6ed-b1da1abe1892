<x-content>
    <form wire:submit.prevent="createRole">
        <x-content-header title="Create Role">
            <button type="submit" class="inline-flex items-center px-5 py-2.5 text-sm font-medium text-center text-white bg-primary-700 rounded-lg focus:ring-4 focus:ring-primary-200 dark:focus:ring-primary-900 hover:bg-primary-800" wire:loading.attr="disabled">
                <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                </svg>
                <span wire:loading.remove>Create Role</span>
                <span wire:loading>Creating...</span>
            </button>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-user-role-index')" data-action="$dispatch('to-user-role-index')" class="page-button"/>
        </x-content-header>

        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        @if ($errors->any())
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <ul class="list-disc pl-5 space-y-1">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <x-content-body>
            {{-- Role Information --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Role Information</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                            Role Name <span class="text-red-500">*</span>
                        </label>
                        <input 
                            type="text" 
                            id="name" 
                            wire:model.live="name" 
                            class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                            placeholder="e.g., manager, supervisor"
                            autofocus
                        >
                        @error('name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="guard_name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                            Guard Name <span class="text-red-500">*</span>
                        </label>
                        <select 
                            id="guard_name" 
                            wire:model.live="guard_name" 
                            class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        >
                            <option value="web">Web</option>
                            <option value="api">API</option>
                        </select>
                        @error('guard_name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            {{-- Permissions --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold dark:text-white">Permissions</h3>
                    <div class="flex items-center">
                        <input 
                            type="text" 
                            wire:model.live.debounce.300ms="search" 
                            placeholder="Search permissions..." 
                            class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                        >
                    </div>
                </div>

                <div class="space-y-4">
                    @forelse($permissionGroups as $group => $permissions)
                        <div class="border rounded-lg overflow-hidden dark:border-gray-700">
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700">
                                <div class="flex items-center">
                                    <h4 class="font-medium text-gray-900 dark:text-white capitalize">{{ $group }}</h4>
                                    <button 
                                        type="button" 
                                        class="ml-auto text-sm text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                                        wire:click="$set('expandedGroups.{{ $group }}', {{ !($expandedGroups[$group] ?? true) ? 'true' : 'false' }})"
                                    >
                                        {{ $expandedGroups[$group] ?? true ? 'Hide' : 'Show' }}
                                    </button>
                                </div>
                            </div>
                            @if($expandedGroups[$group] ?? true)
                                <div class="p-4 bg-white dark:bg-gray-800">
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach($permissions as $permission)
                                            <div class="flex items-center">
                                                <input 
                                                    type="checkbox" 
                                                    id="permission-{{ $permission['id'] }}"
                                                    wire:model.live="selectedPermissions"
                                                    value="{{ $permission['name'] }}"
                                                    class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                >
                                                <label for="permission-{{ $permission['id'] }}" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                                    {{ $permission['name'] }}
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                        </div>
                    @empty
                        <div class="col-span-full text-center py-4 text-gray-500 dark:text-gray-400">
                            No permissions found. Try adjusting your search.
                        </div>
                    @endforelse
                    @if(empty($permissionGroups) && !empty($search))
                        <div class="col-span-full text-center py-4 text-gray-500 dark:text-gray-400">
                            No permissions match your search criteria.
                        </div>
                    @endif
                </div>

                @error('selectedPermissions') 
                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>
        </x-content-body>
    </form>
</x-content>

@push('scripts')
<script>
    document.addEventListener('livewire:initialized', () => {
        // Toggle all permissions in a group when clicking the group header
        document.querySelectorAll('[data-group-toggle]').forEach(button => {
            button.addEventListener('click', (e) => {
                const group = button.dataset.group;
                const checkboxes = document.querySelectorAll(`input[type="checkbox"][data-group="${group}"]`);
                const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
                
                checkboxes.forEach(checkbox => {
                    checkbox.checked = !allChecked;
                    checkbox.dispatchEvent(new Event('change'));
                });
            });
        });
    });
</script>
@endpush
