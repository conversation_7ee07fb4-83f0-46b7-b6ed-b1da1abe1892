<x-content>
    <form action="#">
        <x-content-header title="Delete Role">
            <x-page-button type="delete" label="Yes I'm sure" action="$dispatch('destroy-role')" />
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-user-role-show', { role: {{ $role->id }} })" />
        </x-content-header>

        <x-content-body>
            <div class="p-4 text-center bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <svg class="w-16 h-16 mx-auto text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-5 mb-6 text-lg text-gray-500 dark:text-gray-400">Are you sure you want to delete the role "{{ $role->name }}"?</h3>
                <p class="mb-5 text-sm text-gray-500 dark:text-gray-400">
                    This action cannot be undone. The role and all its permission assignments will be permanently removed from the system.
                </p>

                @if($usersCount > 0)
                    <div class="p-4 mb-4 text-sm text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-400" role="alert">
                        <div class="flex">
                            <svg class="flex-shrink-0 w-4 h-4 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM10 15a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm1-4a1 1 0 0 1-2 0V6a1 1 0 0 1 2 0v5Z"/>
                            </svg>
                            <div class="ml-3">
                                <span class="font-medium">Warning!</span> This role is assigned to {{ $usersCount }} user(s).
                            </div>
                        </div>
                    </div>
                @endif

                <div class="flex items-center justify-center mb-4">
                    <input wire:model="forceDelete" wire:click="toggleForceDelete" id="force-delete-checkbox" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                    <label for="force-delete-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Force delete (removes all relationships)</label>
                </div>

                @if($forceDelete)
                    <div class="p-2 mb-4 text-xs text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        Warning: Force delete will remove all relationships associated with this role before deletion. This action cannot be undone.
                    </div>
                @endif

                @if (session()->has('error'))
                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        <span class="font-medium">Error!</span> {{ session('error') }}
                    </div>
                @endif

                <div class="mt-6 p-4 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white">Role Information</h4>
                    <ul class="space-y-2 text-sm text-left">
                        <li><strong>ID:</strong> {{ $role->id }}</li>
                        <li><strong>Name:</strong> {{ $role->name }}</li>
                        <li><strong>Guard:</strong> {{ $role->guard_name }}</li>
                        <li><strong>Users:</strong> {{ $usersCount }}</li>
                        <li><strong>Permissions:</strong> {{ $permissionsCount }}</li>
                        <li><strong>Created:</strong> {{ $role->created_at->format('M d, Y') }}</li>
                        @if($role->description)
                            <li class="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                                <strong>Description:</strong>
                                <p class="text-gray-600 dark:text-gray-300">{{ $role->description }}</p>
                            </li>
                        @endif
                    </ul>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
