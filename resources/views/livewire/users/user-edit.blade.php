<x-content>
    <form wire:submit="$dispatch('update-user')" wire:key="user-edit-form-{{ $user->id }}">
        <x-content-header title="Edit user">
            <x-page-button type="save" label="Save" action="submit" class="page-button"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-user-show', { user: {{ $user->id }} })" data-action="$dispatch('to-user-show', { user: {{ $user->id }} })" class="page-button"/>
        </x-content-header>

        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message') }}
            </div>
        @endif

        @if (session()->has('error'))
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <span class="font-medium">Error!</span> {{ session('error') }}
            </div>
        @endif
        <x-content-body>
            {{-- Profile picture --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800"
                 wire:key="profile-picture-section-{{ $user->id }}"
                 wire:loading.class="opacity-50"
                 wire:target="removeProfilePicture,form.profile_picture">
                <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                    <div class="relative mb-4 sm:mb-0 xl:mb-4 2xl:mb-0">
                        <label for="profilePictureInput" class="cursor-pointer block relative group">
                            @if(isset($form->profile_picture))
                                <img class="object-cover rounded-lg w-28 h-28 border-2 border-gray-200 dark:border-gray-700 group-hover:opacity-75 transition-opacity"
                                    src="{{ $form->profile_picture->temporaryUrl() }}"
                                    alt="Profile picture">
                            @else
                                <img class="object-cover rounded-lg w-28 h-28 border-2 border-gray-200 dark:border-gray-700 group-hover:opacity-75 transition-opacity"
                                    src="{{ $profilePictureUrl }}"
                                    alt="Profile picture">
                            @endif
                            <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                <div class="bg-primary-700 rounded-full p-2 text-white">
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.5 13a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 13H11V9.413l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13H5.5z"></path>
                                        <path d="M9 13h2v5a1 1 0 11-2 0v-5z"></path>
                                    </svg>
                                </div>
                            </div>
                        </label>
                        <input
                            wire:model.live="form.profile_picture"
                            type="file"
                            id="profilePictureInput"
                            class="hidden"
                            accept="image/jpeg,image/png"
                        >
                        @if(isset($form->profile_picture) || $profilePictureUrl != '/images/users/default.png')
                            <button type="button" wire:click="removeProfilePicture" class="absolute top-0 right-0 -mt-2 -mr-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                        @endif
                    </div>
                    <div>
                        <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">Profile picture</h3>
                        <div class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                            JPG, JPEG or PNG. Max size of 2MB
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Click on the image to upload or change
                        </div>

                        @error('form.profile_picture') <span class="text-red-500 text-sm mt-2 block">{{ $message }}</span> @enderror

                        @if(isset($form->profile_picture))
                            <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                {{ $form->profile_picture->getClientOriginalName() }} ({{ round($form->profile_picture->getSize() / 1024) }} KB)
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- General information -->
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">General information</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="first-name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">First Name</label>
                        <input type="text" wire:model="form.first_name" name="first-name" id="first-name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Jonathan">
                        @error('form.first_name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="last-name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Last Name</label>
                        <input type="text" wire:model="form.last_name" name="last-name" id="last-name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Wamba">
                        @error('form.last_name') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="birth-date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Birth Date</label>
                        <input type="date" wire:model="form.birth_date" name="birth-date" id="birth-date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                        @error('form.birth_date') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="country" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Country</label>
                        <input type="text" wire:model="form.country" name="country" id="country" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Cameroon">
                        @error('form.country') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="city" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">City</label>
                        <input type="text" wire:model="form.city" name="city" id="city" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Yaounde">
                        @error('form.city') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="address" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Address</label>
                        <input type="text" wire:model="form.address" name="address" id="address" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Biyem-Assi">
                        @error('form.address') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                        <input type="email" wire:model="form.email" name="email" id="email" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<EMAIL>">
                        @error('form.email') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="phone-number" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Phone Number</label>
                        <input type="text" wire:model="form.phone_number" name="phone-number" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="(237) 6 93 76 64 40">
                        @error('form.phone_number') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="role_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Role</label>
                        <select wire:model.live="form.role_id" id="role_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select a role</option>
                            @foreach ($roles as $role)
                                <option value="{{ $role->id }}">{{ $role->name }}</option>
                            @endforeach
                        </select>
                        @error('form.role_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div wire:show="form.canDisplayCampaign" class="col-span-6 sm:col-span-3">
                        <label for="campaign_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign</label>
                        <select wire:model="form.campaign_id" id="campaign_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">No Campaign</option>
                            @foreach ($campaigns as $campaign)
                                <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                            @endforeach
                        </select>
                        @error('form.campaign_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="hire-date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Hire Date</label>
                        <input type="date" wire:model="form.hire_date" name="hire-date" id="hire-date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                        @error('form.hire_date') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                        <select wire:model="form.status" id="status" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="in_training">In Training</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="engaged">Engaged</option>
                        </select>
                        @error('form.status') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            <!-- Organization Information -->
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Organization Information</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="department_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Department</label>
                        <select wire:model="form.department_id" id="department_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">No Department</option>
                            @foreach ($departments as $department)
                                <option value="{{ $department->id }}">{{ $department->name }}</option>
                            @endforeach
                        </select>
                        @error('form.department_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="manager_id" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Manager</label>
                        <select wire:model="form.manager_id" id="manager_id" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">No Manager</option>
                            @foreach ($managers as $manager)
                                <option value="{{ $manager->id }}">{{ $manager->first_name }} {{ $manager->last_name }}</option>
                            @endforeach
                        </select>
                        @error('form.manager_id') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="job_title" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Job Title</label>
                        <input type="text" wire:model="form.job_title" name="job_title" id="job_title" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Call Center Agent">
                        @error('form.job_title') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="employment_type" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Employment Type</label>
                        <select wire:model="form.employment_type" id="employment_type" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select Type</option>
                            <option value="full-time">Full-time</option>
                            <option value="part-time">Part-time</option>
                            <option value="contractor">Contractor</option>
                            <option value="intern">Intern</option>
                            <option value="temporary">Temporary</option>
                        </select>
                        @error('form.employment_type') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>

                    <div class="col-span-6 sm:col-span-3">
                        <label for="hierarchy_level" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Hierarchy Level</label>
                        <input type="number" wire:model="form.hierarchy_level" name="hierarchy_level" id="hierarchy_level" min="1" max="10" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="1-10">
                        @error('form.hierarchy_level') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            <!-- Password -->
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Password information</h3>
                <div class="grid grid-cols-6 gap-6">
                    <div class="col-span-6 sm:col-span-3">
                        <label for="password" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">New password</label>
                        <input type="password" wire:model.live="form.password" id="password" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" placeholder="••••••••">
                        @error('form.password') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                    <div class="col-span-6 sm:col-span-3">
                        <label for="password-confirmation" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Confirm password</label>
                        <input type="password" wire:model.live="form.password_confirmation" name="password-confirmation" id="password-confirmation" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="••••••••">
                        @error('form.password_confirmation') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>

            {{-- Documents section --}}
            <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <h3 class="mb-4 text-xl font-semibold dark:text-white">Employee Documents</h3>
                <p class="mb-4 text-sm text-gray-500 dark:text-gray-400">Upload important employee documents such as resume, ID card, certificates, and other relevant documents.</p>

                {{-- Document Department --}}
                <div class="mb-4">
                    <label for="document_department" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Document Department</label>
                    <select wire:model.live="form.document_department" id="document_department" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        @foreach($departments as $department)
                            <option value="{{ $department->name }}">{{ $department->name }}</option>
                        @endforeach
                        <option value="Other">Other</option>
                    </select>
                    @error('form.document_department') <span class="text-sm text-red-500">{{ $message }}</span> @enderror
                </div>

                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                    {{-- Resume upload --}}
                    <div>
                        <label for="resume" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Resume/CV</label>
                        <div class="flex items-center justify-center w-full">
                            <label for="resume-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, DOCX (MAX. 5MB)</p>
                                </div>
                                <input id="resume-upload" wire:model="form.resume" type="file" class="hidden" accept=".pdf,.doc,.docx" />
                            </label>
                        </div>
                        @error('form.resume') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                        @if($form->resume)
                            <div class="mt-2 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                <span class="text-sm truncate">{{ $form->resume->getClientOriginalName() }}</span>
                                <button type="button" wire:click="$set('form.resume', null)" class="text-red-500 hover:text-red-700">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif

                        @if(isset($form->current_documents['resume']))
                            <div class="mt-2">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current Resume:</h4>
                                @foreach($form->current_documents['resume'] as $document)
                                    <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                        <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                        <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'resume')" class="text-red-500 hover:text-red-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    {{-- ID Card upload --}}
                    <div>
                        <label for="id_card" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">ID Card</label>
                        <div class="flex items-center justify-center w-full">
                            <label for="id-card-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                    </svg>
                                    <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">PDF, JPG, JPEG, PNG (MAX. 2MB)</p>
                                </div>
                                <input id="id-card-upload" wire:model="form.id_card" type="file" class="hidden" accept=".pdf,.jpg,.jpeg,.png" />
                            </label>
                        </div>
                        @error('form.id_card') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                        @if($form->id_card)
                            <div class="mt-2 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                <span class="text-sm truncate">{{ $form->id_card->getClientOriginalName() }}</span>
                                <button type="button" wire:click="$set('form.id_card', null)" class="text-red-500 hover:text-red-700">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        @endif

                        @if(isset($form->current_documents['id_card']))
                            <div class="mt-2">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current ID Card:</h4>
                                @foreach($form->current_documents['id_card'] as $document)
                                    <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                        <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                        <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'id_card')" class="text-red-500 hover:text-red-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>

                {{-- Certificates upload --}}
                <div class="mt-6">
                    <label for="certificates" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Certificates</label>
                    <div class="flex items-center justify-center w-full">
                        <label for="certificates-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">PDF, JPG, JPEG, PNG (MAX. 5MB each) - Multiple files allowed</p>
                            </div>
                            <input id="certificates-upload" wire:model="form.certificates" type="file" class="hidden" accept=".pdf,.jpg,.jpeg,.png" multiple />
                        </label>
                    </div>
                    @error('form.certificates.*') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                    @if(!empty($form->certificates))
                        <div class="mt-2 space-y-2">
                            @foreach($form->certificates as $index => $certificate)
                                <div class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                    <span class="text-sm truncate">{{ $certificate->getClientOriginalName() }}</span>
                                    <button type="button" wire:click="removeCertificate({{ $index }})" class="text-red-500 hover:text-red-700">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    @if(isset($form->current_documents['certificate']))
                        <div class="mt-2">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current Certificates:</h4>
                            @foreach($form->current_documents['certificate'] as $document)
                                <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                    <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                    <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'certificate')" class="text-red-500 hover:text-red-700">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

                {{-- Other documents upload --}}
                <div class="mt-6">
                    <label for="other_documents" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Other Documents</label>
                    <div class="flex items-center justify-center w-full">
                        <label for="other-documents-upload" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">PDF, DOC, DOCX, JPG, JPEG, PNG, XLS, XLSX (MAX. 10MB each) - Multiple files allowed</p>
                            </div>
                            <input id="other-documents-upload" wire:model="form.other_documents" type="file" class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.xls,.xlsx" multiple />
                        </label>
                    </div>
                    @error('form.other_documents.*') <span class="text-sm text-red-500">{{ $message }}</span> @enderror

                    @if(!empty($form->other_documents))
                        <div class="mt-2 space-y-2">
                            @foreach($form->other_documents as $index => $document)
                                <div class="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                    <span class="text-sm truncate">{{ $document->getClientOriginalName() }}</span>
                                    <button type="button" wire:click="removeOtherDocument({{ $index }})" class="text-red-500 hover:text-red-700">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    @if(isset($form->current_documents['other_document']))
                        <div class="mt-2">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Current Other Documents:</h4>
                            @foreach($form->current_documents['other_document'] as $document)
                                <div class="mt-1 flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                                    <span class="text-sm truncate">{{ $document['file_name'] }}</span>
                                    <button type="button" wire:click="removeExistingDocument({{ $document['id'] }}, 'other_document')" class="text-red-500 hover:text-red-700">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
