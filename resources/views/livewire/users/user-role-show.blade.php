<x-content>
    <div>
        <x-content-header title="Role Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-user-role-edit', { role: {{ $role->id }} })" data-action="$dispatch('to-user-role-edit', { role: {{ $role->id }} })" class="page-button"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-user-role-delete', { role: {{ $role->id }} })" data-action="$dispatch('to-user-role-delete', { role: {{ $role->id }} })" class="page-button"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-role-index')" data-action="$dispatch('to-role-index')" class="page-button"/>
        </x-content-header>
        
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Left Column -->
                <div class="col-span-full xl:col-auto">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <div class="flex items-center justify-center w-20 h-20 mb-4 rounded-lg bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300">
                                <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">{{ $role->name }}</h3>
                                <div class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                                    <p>Created: {{ $role->created_at->format('M d, Y') }}</p>
                                    <p>Last Updated: {{ $role->updated_at->format('M d, Y') }}</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-3 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                        {{ $role->permissions->count() }} Permissions
                                    </span>
                                    <span class="px-3 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300">
                                        {{ $role->users->count() }} Users
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permissions Summary -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Permissions Summary</h3>
                        <div class="space-y-3">
                            @php
                                $groupedPermissions = $role->permissions->groupBy(function($permission) {
                                    return explode('.', $permission->name)[0] ?? 'other';
                                });
                            @endphp
                            
                            @foreach($groupedPermissions as $group => $permissions)
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-white">{{ ucfirst($group) }}</h4>
                                    <div class="flex flex-wrap gap-2 mt-1">
                                        @foreach($permissions as $permission)
                                            <span class="px-2 py-1 text-xs font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">
                                                {{ last(explode('.', $permission->name)) }}
                                            </span>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-span-2">
                    <!-- Role Details -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Role Information</h3>
                        <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
                            <div class="col-span-1 sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Role Name</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $role->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Guard Name</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $role->guard_name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created At</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $role->created_at->format('M d, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $role->updated_at->format('M d, Y') }}</dd>
                            </div>
                        </dl>
                    </div>

                    <!-- Assigned Users -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold dark:text-white">Assigned Users ({{ $role->users->count() }})</h3>
                        </div>
                        
                        @if($role->users->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">Name</th>
                                            <th scope="col" class="px-6 py-3">Email</th>
                                            <th scope="col" class="px-6 py-3">Status</th>
                                            <th scope="col" class="px-6 py-3 text-right">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($role->users as $user)
                                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {{ $user->name }}
                                                </td>
                                                <td class="px-6 py-4">{{ $user->email }}</td>
                                                <td class="px-6 py-4">
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $user->status === 'active' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' : 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' }}">
                                                        {{ ucfirst($user->status) }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-right">
                                                    <a href="#" wire:click.prevent="$dispatch('to-user-show', { user: {{ $user->id }} })" class="font-medium text-primary-600 hover:text-primary-800 dark:text-primary-500 dark:hover:text-primary-400">View</a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="p-4 text-center text-gray-500 bg-gray-50 rounded-lg dark:bg-gray-700 dark:text-gray-400">
                                No users assigned to this role.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>
