<x-content>
    <form action="#">
        <!-- <x-content-header title="Delete user">
            <button wire:click="destroyUser" type="button" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-red-600 rounded-lg hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:focus:ring-red-900">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"></path>
                </svg>
                Yes I'm sure
            </button>
            <button wire:click="$dispatch('to-user-show', { user: {{ $user->id }} })" type="button" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Cancel
            </button>
        </x-content-header> -->
        <x-content-header title="Delete user">
            <x-page-button type="delete" label="Yes I'm sure" action="$dispatch('destroy-user')"/>
            <x-page-button type="cancel" label="Cancel" action="$dispatch('to-user-show', { user: {{ $user->id }} })"/>
        </x-content-header>
        <x-content-body>
            <div class="p-4 text-center bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                <svg class="w-16 h-16 mx-auto text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                <h3 class="mt-5 mb-6 text-lg text-gray-500 dark:text-gray-400">Are you sure you want to delete this user {{ $user->name }}?</h3>
                <p class="mb-5 text-sm text-gray-500 dark:text-gray-400">This action cannot be undone. All user data, including profile information, documents, and relationships will be permanently removed from the system.</p>

                <div class="flex items-center justify-center mb-4">
                    <input wire:model="forceDelete" wire:click="toggleForceDelete" id="force-delete-checkbox" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                    <label for="force-delete-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Force delete (removes all relationships)</label>
                </div>

                @if($forceDelete)
                    <div class="p-2 mb-4 text-xs text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        Warning: Force delete will remove all relationships associated with this user before deletion. This action cannot be undone.
                    </div>
                @endif

                @if (session()->has('error'))
                    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                        <span class="font-medium">Error!</span> {{ session('error') }}
                    </div>
                @endif

                <div class="mt-6 p-4 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                    <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white">User Information</h4>
                    <ul class="space-y-2 text-sm text-left">
                        <li><strong>ID:</strong> {{ $user->id }}</li>
                        <li><strong>Name:</strong> {{ $user->first_name }} {{ $user->last_name }}</li>
                        <li><strong>Email:</strong> {{ $user->email }}</li>
                        <li><strong>Role:</strong> {{ $user->role->name ?? 'None' }}</li>
                        <li><strong>Created:</strong> {{ $user->created_at->format('M d, Y') }}</li>
                    </ul>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>