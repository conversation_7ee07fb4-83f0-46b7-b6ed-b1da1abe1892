<x-content>
    <div>
        <x-content-header title="User Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-user-edit', { user: {{ $user->id }} })" data-action="$dispatch('to-user-edit', { user: {{ $user->id }} })" class="page-button"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-user-delete', { user: {{ $user->id }} })" data-action="$dispatch('to-user-delete', { user: {{ $user->id }} })" class="page-button"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-user-index')" data-action="$dispatch('to-user-index')" class="page-button"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Right Content -->
                <div class="col-span-full xl:col-auto">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <img class="mb-4 rounded-lg w-28 h-28 sm:mb-0 xl:mb-4 2xl:mb-0 object-cover"
                                 src="{{ $profilePictureUrl }}"
                                 alt="{{ $user->full_name }}'s profile picture">
                            <div>
                                <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">Profile picture</h3>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flow-root">
                            <h3 class="text-xl font-semibold dark:text-white">Historique</h3>
                            <ol class="relative mt-4 border-l border-gray-200 dark:border-gray-700">
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Date d'enregistrement</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $user->created_at->format('d M Y, H:i') }}</h3>
                                </li>
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Role</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $user->role->name }}</h3>
                                    <p class="mb-4 text-base font-normal text-gray-500 dark:text-gray-400">En activité</p>
                                </li>
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Dernière mise à jour</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $user->updated_at->format('d M Y, H:i') }}</h3>
                                </li>
                                @if($user->hire_date)
                                <li class="mb-10 ml-4">
                                    <div class="absolute w-3 h-3 bg-gray-200 rounded-full mt-1.5 -left-1.5 border border-white dark:border-gray-800 dark:bg-gray-700"></div>
                                    <time class="mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">Date d'embauche</time>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($user->hire_date)->format('d M Y') }}</h3>
                                </li>
                                @endif
                            </ol>
                        </div>
                    </div>

                    <!-- Onboarding Status -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Onboarding Status</h3>

                        @if($user->activeOnboarding())
                            @php
                                $onboarding = $user->activeOnboarding();
                            @endphp
                            <div class="mb-4">
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Progress:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $onboarding->progress_percentage }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    <div class="bg-primary-600 h-2.5 rounded-full" style="width: {{ $onboarding->progress_percentage }}%"></div>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                    <span class="block text-sm font-medium text-gray-500 dark:text-gray-400">Status:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            @if($onboarding->status === 'pending') bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                            @elseif($onboarding->status === 'in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                            @elseif($onboarding->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                            @elseif($onboarding->status === 'cancelled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                            @endif">
                                            {{ ucfirst(str_replace('_', ' ', $onboarding->status)) }}
                                        </span>
                                    </span>
                                </div>
                                <div>
                                    <span class="block text-sm font-medium text-gray-500 dark:text-gray-400">Start Date:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $onboarding->start_date->format('M d, Y') }}</span>
                                </div>
                                <div>
                                    <span class="block text-sm font-medium text-gray-500 dark:text-gray-400">Target Completion:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $onboarding->target_completion_date ? $onboarding->target_completion_date->format('M d, Y') : 'Not set' }}
                                    </span>
                                </div>
                                <div>
                                    <span class="block text-sm font-medium text-gray-500 dark:text-gray-400">Assigned To:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $onboarding->assignedTo ? $onboarding->assignedTo->name : 'Not assigned' }}
                                    </span>
                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="{{ route('hr.onboarding.show', ['onboarding' => $onboarding->id]) }}" class="text-sm font-medium text-primary-600 dark:text-primary-500 hover:underline">
                                    View Onboarding Details
                                </a>
                            </div>
                        @elseif($user->onboardings()->where('status', 'completed')->exists())
                            <div class="flex items-center text-green-500 dark:text-green-400">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium">Onboarding completed</span>
                            </div>

                            @php
                                $completedOnboarding = $user->onboardings()->where('status', 'completed')->latest()->first();
                            @endphp

                            @if($completedOnboarding)
                                <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                                    Completed on {{ $completedOnboarding->actual_completion_date->format('M d, Y') }}
                                </div>

                                <div class="mt-4">
                                    <a href="{{ route('hr.onboarding.show', ['onboarding' => $completedOnboarding->id]) }}" class="text-sm font-medium text-primary-600 dark:text-primary-500 hover:underline">
                                        View Onboarding Details
                                    </a>
                                </div>
                            @endif
                        @else
                            <div class="flex items-center text-gray-500 dark:text-gray-400">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-sm font-medium">No onboarding process found</span>
                            </div>

                            <div class="mt-4">
                                <a href="{{ route('hr.onboarding.create') }}" class="text-sm font-medium text-primary-600 dark:text-primary-500 hover:underline">
                                    Start Onboarding Process
                                </a>
                            </div>
                        @endif
                    </div>
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flow-root">
                            <h3 class="text-xl font-semibold dark:text-white">Sessions</h3>
                            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                                @forelse($user->sessions ?? [] as $session)
                                <li class="py-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex-shrink-0">
                                            <svg class="w-6 h-6 dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-base font-semibold text-gray-900 truncate dark:text-white">
                                                {{ $session->ip_address ?? 'Unknown' }}
                                            </p>
                                            <p class="text-sm font-normal text-gray-500 truncate dark:text-gray-400">
                                                {{ $session->user_agent ?? 'Unknown browser' }}
                                            </p>
                                        </div>
                                        <div class="inline-flex items-center">
                                            <p class="text-sm font-normal text-gray-500 dark:text-gray-400">
                                                {{ isset($session->last_activity) ? \Carbon\Carbon::createFromTimestamp($session->last_activity)->diffForHumans() : 'Unknown' }}
                                            </p>
                                        </div>
                                    </div>
                                </li>
                                @empty
                                <li class="py-4">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">No active sessions found</p>
                                </li>
                                @endforelse
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- Left Content -->
                <div class="col-span-2">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">General information</h3>
                        <div class="grid grid-cols-6 gap-6">
                            <div class="col-span-6 sm:col-span-3">
                                <label for="first-name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">First Name</label>
                                <input readonly type="text" value="{{ $user->first_name }}" name="first-name" id="first-name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Jonathan">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="last-name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Last Name</label>
                                <input readonly type="text" value="{{ $user->last_name }}" name="last-name" id="last-name" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Wamba">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="birth-date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Birth Date</label>
                                <input readonly type="date" value="{{ $user->birth_date }}" name="birth-date" id="birth-date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="country" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Country</label>
                                <input readonly type="text" value="{{ $user->country }}" name="country" id="country" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Cameroon">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="city" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">City</label>
                                <input readonly type="text" value="{{ $user->city }}" name="city" id="city" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Yaounde">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="address" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Address</label>
                                <input readonly type="text" value="{{ $user->address }}" name="address" id="address" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Biyem-Assi">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Email</label>
                                <input readonly type="email" value="{{ $user->email }}" name="email" id="email" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<EMAIL>">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="phone-number" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Phone Number</label>
                                <input readonly type="text" value="{{ $user->phone_number }}" name="phone-number" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="(237) 6 93 76 64 40">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="hire-date" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Hire Date</label>
                                <input readonly type="date" value="{{ $user->hire_date }}" name="hire-date" id="hire-date" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="01/01/1990">
                            </div>
                            <div class="col-span-6 sm:col-span-3">
                                <label for="role" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Role</label>
                                <input readonly type="role" value="{{ $user->role->name }}" name="role" id="role" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<EMAIL>">
                            </div>
                            @if($user->campaign_id)
                            <div class="col-span-6 sm:col-span-3">
                                <label for="campaign" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Campaign</label>
                                <input readonly type="text" value="{{ $user->campaign->name }}" name="campaign" id="campaign" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            @endif

                            @if($user->department_id)
                            <div class="col-span-6 sm:col-span-3">
                                <label for="department" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Department</label>
                                <input readonly type="text" value="{{ $user->department->name }}" name="department" id="department" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            @endif

                            @if($user->job_title)
                            <div class="col-span-6 sm:col-span-3">
                                <label for="job-title" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Job Title</label>
                                <input readonly type="text" value="{{ $user->job_title }}" name="job-title" id="job-title" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            @endif

                            @if($user->manager_id)
                            <div class="col-span-6 sm:col-span-3">
                                <label for="manager" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Manager</label>
                                <input readonly type="text" value="{{ $user->manager->first_name }} {{ $user->manager->last_name }}" name="manager" id="manager" class="shadow-sm bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Team Memberships -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Team Memberships</h3>

                        @if($user->activeTeams && $user->activeTeams->count() > 0)
                            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                                @foreach($user->activeTeams as $team)
                                    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                        <div class="flex items-center justify-between mb-2">
                                            <h5 class="text-lg font-medium text-gray-900 dark:text-white">{{ $team->name }}</h5>
                                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">{{ $team->code }}</span>
                                        </div>
                                        <p class="mb-3 text-sm text-gray-500 dark:text-gray-400">{{ $team->description }}</p>
                                        <div class="flex flex-col space-y-2">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>
                                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                                    Role: {{ ucfirst($team->pivot->role ?? 'Member') }}
                                                </span>
                                            </div>
                                            @if($team->leader)
                                                <div class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path></svg>
                                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                                        Leader: {{ $team->leader->first_name }} {{ $team->leader->last_name }}
                                                    </span>
                                                </div>
                                            @endif
                                            @if($team->pivot->joined_at)
                                                <div class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg>
                                                    <span class="text-sm text-gray-500 dark:text-gray-400">
                                                        Joined: {{ \Carbon\Carbon::parse($team->pivot->joined_at)->format('M d, Y') }}
                                                    </span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <p class="text-sm text-gray-500 dark:text-gray-400">This user is not a member of any active teams.</p>
                        @endif
                    </div>

                    <!-- Documents section -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800" id="documents-section">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Documents</h3>

                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {{-- Resume --}}
                            <div>
                                <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Resume/CV</h4>
                                @if(isset($userDocuments['resume']))
                                    <div class="space-y-2">
                                        @foreach($userDocuments['resume'] as $document)
                                            <div class="flex items-center p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                                <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                        {{ $document['file_name'] }}
                                                    </p>
                                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                        Uploaded: {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                    </p>
                                                </div>
                                                <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                    View
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-sm text-gray-500 dark:text-gray-400">No resume uploaded</p>
                                @endif
                            </div>

                            {{-- ID Card --}}
                            <div>
                                <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">ID Card</h4>
                                @if(isset($userDocuments['id_card']))
                                    <div class="space-y-2">
                                        @foreach($userDocuments['id_card'] as $document)
                                            <div class="flex items-center p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                                <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                        {{ $document['file_name'] }}
                                                    </p>
                                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                        Uploaded: {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                    </p>
                                                </div>
                                                <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                    View
                                                </a>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-sm text-gray-500 dark:text-gray-400">No ID card uploaded</p>
                                @endif
                            </div>
                        </div>

                        {{-- Certificates --}}
                        <div class="mt-6" id="certificates-section">
                            <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Certificates</h4>
                            @if(isset($userDocuments['certificate']))
                                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                    @foreach($userDocuments['certificate'] as $document)
                                        <div class="flex items-center p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                            <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                            </svg>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                    {{ $document['file_name'] }}
                                                </p>
                                                <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                    {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                </p>
                                            </div>
                                            <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                View
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No certificates uploaded</p>
                            @endif
                        </div>

                        {{-- Other Documents --}}
                        <div class="mt-6">
                            <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Other Documents</h4>
                            @if(isset($userDocuments['other_document']))
                                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                    @foreach($userDocuments['other_document'] as $document)
                                        <div class="flex items-center p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                            <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                            </svg>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                    {{ $document['file_name'] }}
                                                </p>
                                                <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                    {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                </p>
                                            </div>
                                            <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                View
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No other documents uploaded</p>
                            @endif
                        </div>
                    </div>

                    @if($user->role_id === 3) {{-- Only show performance section for agents --}}
                    <!-- Performance Management Section -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Performance Management</h3>

                        <!-- Period Selector -->
                        <div class="flex items-center mb-4 space-x-4">
                            <label for="period" class="text-sm font-medium text-gray-900 dark:text-white">Period:</label>
                            <select id="period" wire:model.live="period" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="quarter">This Quarter</option>
                                <option value="year">This Year</option>
                            </select>
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ \Carbon\Carbon::parse($startDate)->format('M d, Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('M d, Y') }}</span>
                        </div>

                        <!-- Performance Overview Cards -->
                        <div class="grid grid-cols-1 gap-4 mb-4 sm:grid-cols-2 lg:grid-cols-4">
                            <!-- Call Volume -->
                            <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Call Volume</h5>
                                    <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M2 3a1 1 0 011-1h14a1 1 0 011 1v14a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 2v10h14V5H4z" clip-rule="evenodd"></path></svg>
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $callStats['total_calls'] ?? 0 }}</div>
                                <div class="flex items-center mt-2">
                                    <div class="text-xs font-medium text-gray-500 dark:text-gray-400">
                                        <span class="inline-block mr-2">Inbound: {{ $callStats['calls_handled'] ?? 0 }}</span>
                                        <span class="inline-block">Outbound: {{ $callStats['outbound_calls'] ?? 0 }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Quality Score -->
                            <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Quality Score</h5>
                                    <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($avgQualityScore, 1) }}</div>
                                <div class="flex items-center mt-2">
                                    <div class="text-xs font-medium text-gray-500 dark:text-gray-400">
                                        <span class="inline-block">Based on {{ count($callEvaluations) }} evaluations</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Adherence Rate -->
                            <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Adherence Rate</h5>
                                    <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($adherenceRate, 1) }}%</div>
                                <div class="flex items-center mt-2">
                                    <div class="text-xs font-medium text-gray-500 dark:text-gray-400">
                                        <span class="inline-block">Schedule adherence</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Average Handle Time -->
                            <div class="p-3 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                                <div class="flex items-center justify-between mb-2">
                                    <h5 class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg Handle Time</h5>
                                    <svg class="w-5 h-5 text-gray-400 dark:text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path></svg>
                                </div>
                                <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ $callStats['avg_handle_time'] ?? '00:00:00' }}</div>
                                <div class="flex items-center mt-2">
                                    <div class="text-xs font-medium text-gray-500 dark:text-gray-400">
                                        <span class="inline-block">Total talk time: {{ $callStats['total_talk_time'] ?? '00:00:00' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- KPI Targets -->
                        <div class="mb-6">
                            <h4 class="mb-3 text-lg font-medium text-gray-900 dark:text-white">KPI Targets</h4>
                            @if(count($kpiTargets) > 0)
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-4 py-3">Metric</th>
                                                <th scope="col" class="px-4 py-3">Target</th>
                                                <th scope="col" class="px-4 py-3">Current</th>
                                                <th scope="col" class="px-4 py-3">Achievement</th>
                                                <th scope="col" class="px-4 py-3">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($kpiTargets as $target)
                                                @php
                                                    $metricName = $target->metric_name;
                                                    $currentValue = 0;

                                                    // Get current value based on metric name
                                                    switch($metricName) {
                                                        case 'calls_handled':
                                                            $currentValue = $callStats['calls_handled'] ?? 0;
                                                            break;
                                                        case 'quality_score':
                                                            $currentValue = $avgQualityScore;
                                                            break;
                                                        case 'adherence_rate':
                                                            $currentValue = $adherenceRate;
                                                            break;
                                                        case 'average_handle_time':
                                                            $currentValue = $performanceMetrics->avg('average_handle_time') ?? 0;
                                                            break;
                                                        case 'conversion_rate':
                                                            $currentValue = $callStats['conversion_rate'] ?? 0;
                                                            break;
                                                        default:
                                                            $currentValue = $performanceMetrics->avg($metricName) ?? 0;
                                                    }

                                                    // Calculate achievement percentage
                                                    $achievement = 0;
                                                    if ($target->target_value > 0) {
                                                        $achievement = ($currentValue / $target->target_value) * 100;
                                                    }

                                                    // Determine status
                                                    $status = 'Needs Improvement';
                                                    $statusColor = 'text-red-500';

                                                    if ($achievement >= 100) {
                                                        $status = 'Achieved';
                                                        $statusColor = 'text-green-500';
                                                    } elseif ($achievement >= 80) {
                                                        $status = 'On Track';
                                                        $statusColor = 'text-yellow-500';
                                                    }
                                                @endphp
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3">{{ $target->description }}</td>
                                                    <td class="px-4 py-3">{{ $target->target_value }} {{ $target->unit }}</td>
                                                    <td class="px-4 py-3">{{ number_format($currentValue, 1) }} {{ $target->unit }}</td>
                                                    <td class="px-4 py-3">{{ number_format($achievement, 1) }}%</td>
                                                    <td class="px-4 py-3 font-medium {{ $statusColor }}">{{ $status }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No KPI targets set for this period</p>
                            @endif
                        </div>

                        <!-- Recent Call Evaluations -->
                        <div class="mb-6">
                            <h4 class="mb-3 text-lg font-medium text-gray-900 dark:text-white">Recent Call Evaluations</h4>
                            @if(count($callEvaluations) > 0)
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-4 py-3">Date</th>
                                                <th scope="col" class="px-4 py-3">Evaluator</th>
                                                <th scope="col" class="px-4 py-3">Score</th>
                                                <th scope="col" class="px-4 py-3">Strengths</th>
                                                <th scope="col" class="px-4 py-3">Areas for Improvement</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($callEvaluations as $evaluation)
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3">{{ $evaluation->evaluation_date->format('M d, Y') }}</td>
                                                    <td class="px-4 py-3">{{ $evaluation->evaluator->first_name }} {{ $evaluation->evaluator->last_name }}</td>
                                                    <td class="px-4 py-3 font-medium
                                                        @if($evaluation->overall_score >= 4) text-green-500
                                                        @elseif($evaluation->overall_score >= 3) text-yellow-500
                                                        @else text-red-500 @endif">
                                                        {{ number_format($evaluation->overall_score, 1) }}
                                                    </td>
                                                    <td class="px-4 py-3">{{ \Illuminate\Support\Str::limit($evaluation->strengths, 50) }}</td>
                                                    <td class="px-4 py-3">{{ \Illuminate\Support\Str::limit($evaluation->areas_for_improvement, 50) }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <a href="{{ route('call-quality.evaluations') }}?agent={{ $user->id }}" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                        View all evaluations
                                    </a>
                                </div>
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No call evaluations found for this period</p>
                            @endif
                        </div>

                        <!-- Performance Reviews -->
                        <div>
                            <h4 class="mb-3 text-lg font-medium text-gray-900 dark:text-white">Performance Reviews</h4>
                            @if(count($performanceReviews) > 0)
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-4 py-3">Review Date</th>
                                                <th scope="col" class="px-4 py-3">Reviewer</th>
                                                <th scope="col" class="px-4 py-3">Period</th>
                                                <th scope="col" class="px-4 py-3">Overall Score</th>
                                                <th scope="col" class="px-4 py-3">Rating</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($performanceReviews as $review)
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3">{{ $review->review_date->format('M d, Y') }}</td>
                                                    <td class="px-4 py-3">{{ $review->reviewer->first_name }} {{ $review->reviewer->last_name }}</td>
                                                    <td class="px-4 py-3">{{ $review->review_period_start->format('M d') }} - {{ $review->review_period_end->format('M d, Y') }}</td>
                                                    <td class="px-4 py-3 font-medium
                                                        @if($review->overall_score >= 4) text-green-500
                                                        @elseif($review->overall_score >= 3) text-yellow-500
                                                        @else text-red-500 @endif">
                                                        {{ number_format($review->overall_score, 1) }}
                                                    </td>
                                                    <td class="px-4 py-3">{{ $review->getRatingDescription() }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No performance reviews found for this period</p>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>

