<x-content>
    <div class="flex items-center justify-between mb-4">
        <div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">User Hierarchy & Team Management</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400">Manage organizational structure, reporting relationships, and team assignments</p>
        </div>
        <div class="flex items-center space-x-2">
            <button wire:click="viewOrgChart" class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z"></path>
                </svg>
                View Org Chart
            </button>
        </div>
    </div>

    <x-card class="p-4 mb-4">
        <!-- View Mode Tabs -->
        <div class="flex flex-col sm:flex-row justify-between items-center mb-4">
            <div class="mb-4 sm:mb-0">
                <ul class="flex flex-wrap text-sm font-medium text-center text-gray-500 border-b border-gray-200 dark:border-gray-700 dark:text-gray-400">
                    <li class="mr-2">
                        <button wire:click="$set('viewMode', 'hierarchy')" type="button" class="inline-block p-4 {{ $viewMode === 'hierarchy' ? 'text-primary-600 border-b-2 border-primary-600 rounded-t-lg active dark:text-primary-500 dark:border-primary-500' : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' }}">Hierarchy View</button>
                    </li>
                    <li class="mr-2">
                        <button wire:click="$set('viewMode', 'department')" type="button" class="inline-block p-4 {{ $viewMode === 'department' ? 'text-primary-600 border-b-2 border-primary-600 rounded-t-lg active dark:text-primary-500 dark:border-primary-500' : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' }}">Department View</button>
                    </li>
                    <li>
                        <button wire:click="$set('viewMode', 'team')" type="button" class="inline-block p-4 {{ $viewMode === 'team' ? 'text-primary-600 border-b-2 border-primary-600 rounded-t-lg active dark:text-primary-500 dark:border-primary-500' : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' }}">Team View</button>
                    </li>
                </ul>
            </div>

            <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                        </svg>
                    </div>
                    <input wire:model.live.debounce.300ms="searchQuery" type="search" id="search" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search users...">
                </div>

                @if($viewMode === 'department')
                <select wire:model.live="selectedDepartmentId" id="department-filter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    <option value="">All Departments</option>
                    @foreach($departments as $department)
                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                    @endforeach
                </select>
                @endif

                @if($viewMode === 'team')
                <select wire:model.live="selectedTeamId" id="team-filter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    <option value="">All Teams</option>
                    @foreach($teams as $team)
                        <option value="{{ $team->id }}">{{ $team->name }}</option>
                    @endforeach
                </select>
                @endif
            </div>
        </div>

        <!-- Department or Team Info -->
        @if($viewMode === 'department' && $selectedDepartment)
            <div class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex flex-col md:flex-row justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $selectedDepartment->name }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $selectedDepartment->description }}</p>
                        <div class="mt-2 flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">{{ $selectedDepartment->code }}</span>
                            @if($selectedDepartment->manager)
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                    Manager: {{ $selectedDepartment->manager->first_name }} {{ $selectedDepartment->manager->last_name }}
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="mt-3 md:mt-0">
                        <span class="bg-gray-100 text-gray-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300">
                            <svg class="w-3 h-3 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 18a8 8 0 1 1 8-8 8.009 8.009 0 0 1-8 8Z"/>
                                <path d="M10 12.5a1 1 0 0 0-1 1v1a1 1 0 0 0 2 0v-1a1 1 0 0 0-1-1Zm0-1.5a1.5 1.5 0 1 0-1.5-1.5A1.5 1.5 0 0 0 10 11Z"/>
                            </svg>
                            {{ $users->where('department_id', $selectedDepartment->id)->count() }} Users
                        </span>
                    </div>
                </div>
            </div>
        @endif

        @if($viewMode === 'team' && $selectedTeam)
            <div class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex flex-col md:flex-row justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $selectedTeam->name }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ $selectedTeam->description }}</p>
                        <div class="mt-2 flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">{{ $selectedTeam->code }}</span>
                            @if($selectedTeam->leader)
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                    Leader: {{ $selectedTeam->leader->first_name }} {{ $selectedTeam->leader->last_name }}
                                </span>
                            @endif
                            @if($selectedTeam->department)
                                <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-purple-900 dark:text-purple-300">
                                    Department: {{ $selectedTeam->department->name }}
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="mt-3 md:mt-0 flex items-center gap-2">
                        <span class="bg-gray-100 text-gray-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300">
                            <svg class="w-3 h-3 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 18a8 8 0 1 1 8-8 8.009 8.009 0 0 1-8 8Z"/>
                                <path d="M10 12.5a1 1 0 0 0-1 1v1a1 1 0 0 0 2 0v-1a1 1 0 0 0-1-1Zm0-1.5a1.5 1.5 0 1 0-1.5-1.5A1.5 1.5 0 0 0 10 11Z"/>
                            </svg>
                            {{ $selectedTeam->members->count() }} Members
                        </span>
                        <button wire:click="manageTeamMembers({{ $selectedTeam->id }})" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"></path>
                            </svg>
                            Manage Members
                        </button>
                    </div>
                </div>
            </div>
        @endif
    </x-card>

    <x-card class="p-4 mt-4">
        <div class="relative overflow-x-auto">
            <div class="flex items-center justify-between pb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    {{ $viewMode === 'hierarchy' ? 'All Users' : ($viewMode === 'department' ? 'Department Users' : 'Team Members') }}
                </h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $users->total() }} {{ $users->total() === 1 ? 'user' : 'users' }}</span>
                </div>
            </div>

            <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">Job Title</th>
                            <th scope="col" class="px-6 py-3">Department</th>
                            <th scope="col" class="px-6 py-3">Manager</th>
                            <th scope="col" class="px-6 py-3">Teams</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                            <tr wire:click="editUser({{ $user->id }})" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer">
                                <th scope="row" class="flex items-center px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    @if($user->profilePicture()->exists())
                                        <img class="w-10 h-10 rounded-full" src="{{ asset('storage/' . $user->profilePicture()->first()->file_path) }}" alt="{{ $user->first_name }} {{ $user->last_name }}">
                                    @else
                                        <div class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                                            <svg class="absolute w-12 h-12 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>
                                        </div>
                                    @endif
                                    <div class="ps-3">
                                        <div class="text-base font-semibold text-gray-900 dark:text-white">{{ $user->first_name }} {{ $user->last_name }}</div>
                                        <div class="font-normal text-gray-500 dark:text-gray-400">{{ $user->email }}</div>
                                    </div>
                                </th>
                                <td class="px-6 py-4 text-gray-900 dark:text-white">
                                    {{ $user->job_title ?: 'Not set' }}
                                </td>
                                <td class="px-6 py-4">
                                    @if($user->department)
                                        <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-purple-900 dark:text-purple-300">
                                            {{ $user->department->name }}
                                        </span>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">Not assigned</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    @if($user->manager)
                                        <div class="flex items-center">
                                            <div class="relative w-7 h-7 mr-2 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                                                <svg class="absolute w-9 h-9 text-gray-400 -left-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg>
                                            </div>
                                            <span class="text-gray-900 dark:text-white">{{ $user->manager->first_name }} {{ $user->manager->last_name }}</span>
                                        </div>
                                    @else
                                        <span class="text-gray-500 dark:text-gray-400">None</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-wrap gap-1">
                                        @forelse($user->activeTeams as $team)
                                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                                                {{ $team->name }}
                                            </span>
                                        @empty
                                            <span class="text-gray-500 dark:text-gray-400">No teams</span>
                                        @endforelse
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">No users found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="p-4 bg-white dark:bg-gray-800">
                {{ $users->links() }}
            </div>
        </div>
    </x-card>


</x-content>

<script>
    // Initialize dark mode on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Check if dark mode is enabled
        if (localStorage.getItem("color-theme") === "dark" ||
            (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }

        // Initialize theme toggle icons
        var themeToggleDarkIcon = document.getElementById("theme-toggle-dark-icon");
        var themeToggleLightIcon = document.getElementById("theme-toggle-light-icon");
        var themeToggleBtn = document.getElementById("theme-toggle");

        if (themeToggleDarkIcon && themeToggleLightIcon) {
            // Reset icon states
            themeToggleDarkIcon.classList.add("hidden");
            themeToggleLightIcon.classList.add("hidden");

            // Change the icons inside the button based on previous settings
            if (localStorage.getItem("color-theme") === "dark" ||
                (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
                themeToggleLightIcon.classList.remove("hidden");
            } else {
                themeToggleDarkIcon.classList.remove("hidden");
            }
        }

        // Add click event to theme toggle button
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener("click", function() {
                // Toggle icons
                themeToggleDarkIcon.classList.toggle("hidden");
                themeToggleLightIcon.classList.toggle("hidden");

                // Toggle dark mode class
                if (localStorage.getItem("color-theme")) {
                    if (localStorage.getItem("color-theme") === "light") {
                        document.documentElement.classList.add("dark");
                        localStorage.setItem("color-theme", "dark");
                    } else {
                        document.documentElement.classList.remove("dark");
                        localStorage.setItem("color-theme", "light");
                    }
                } else {
                    if (document.documentElement.classList.contains("dark")) {
                        document.documentElement.classList.remove("dark");
                        localStorage.setItem("color-theme", "light");
                    } else {
                        document.documentElement.classList.add("dark");
                        localStorage.setItem("color-theme", "dark");
                    }
                }

                // Dispatch event for charts to update
                document.dispatchEvent(new CustomEvent("dark-mode"));
            });
        }
    });

    // Re-initialize dark mode when navigating to this page
    document.addEventListener('livewire:navigated', function() {
        // Check if dark mode is enabled
        if (localStorage.getItem("color-theme") === "dark" ||
            (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }

        // Initialize theme toggle icons
        var themeToggleDarkIcon = document.getElementById("theme-toggle-dark-icon");
        var themeToggleLightIcon = document.getElementById("theme-toggle-light-icon");
        var themeToggleBtn = document.getElementById("theme-toggle");

        if (themeToggleDarkIcon && themeToggleLightIcon) {
            // Reset icon states
            themeToggleDarkIcon.classList.add("hidden");
            themeToggleLightIcon.classList.add("hidden");

            // Change the icons inside the button based on previous settings
            if (localStorage.getItem("color-theme") === "dark" ||
                (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
                themeToggleLightIcon.classList.remove("hidden");
            } else {
                themeToggleDarkIcon.classList.remove("hidden");
            }
        }

        // Add click event to theme toggle button
        if (themeToggleBtn) {
            // Remove existing event listeners
            var newThemeToggleBtn = themeToggleBtn.cloneNode(true);
            themeToggleBtn.parentNode.replaceChild(newThemeToggleBtn, themeToggleBtn);
            themeToggleBtn = newThemeToggleBtn;

            themeToggleBtn.addEventListener("click", function() {
                // Toggle icons
                themeToggleDarkIcon = document.getElementById("theme-toggle-dark-icon");
                themeToggleLightIcon = document.getElementById("theme-toggle-light-icon");

                themeToggleDarkIcon.classList.toggle("hidden");
                themeToggleLightIcon.classList.toggle("hidden");

                // Toggle dark mode class
                if (localStorage.getItem("color-theme")) {
                    if (localStorage.getItem("color-theme") === "light") {
                        document.documentElement.classList.add("dark");
                        localStorage.setItem("color-theme", "dark");
                    } else {
                        document.documentElement.classList.remove("dark");
                        localStorage.setItem("color-theme", "light");
                    }
                } else {
                    if (document.documentElement.classList.contains("dark")) {
                        document.documentElement.classList.remove("dark");
                        localStorage.setItem("color-theme", "light");
                    } else {
                        document.documentElement.classList.add("dark");
                        localStorage.setItem("color-theme", "dark");
                    }
                }

                // Dispatch event for charts to update
                document.dispatchEvent(new CustomEvent("dark-mode"));
            });
        }
    });
</script>
