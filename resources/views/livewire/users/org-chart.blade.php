<x-content>
    <div class="flex items-center justify-between mb-4">
        <div>
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Organization Chart</h2>
            <p class="text-sm text-gray-500 dark:text-gray-400">Visual representation of the organizational hierarchy showing reporting relationships</p>
        </div>
        <div class="flex items-center space-x-2">
            <button wire:click="$dispatch('to-user-hierarchy')" class="inline-flex items-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                </svg>
                Back to Hierarchy
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 gap-4">
        <!-- Filters -->
        <x-card class="p-4">
            <div class="flex flex-col sm:flex-row justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3 sm:mb-0">Chart Filters</h3>

                <div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                            </svg>
                        </div>
                        <input wire:model.live.debounce.300ms="searchQuery" type="search" id="search" class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search users...">
                    </div>

                    <select wire:model.live="selectedDepartmentId" id="department-filter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        <option value="">All Departments</option>
                        @foreach($departments as $department)
                            <option value="{{ $department->id }}">{{ $department->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Department Info -->
            @if($selectedDepartment)
                <div class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <div class="flex flex-col md:flex-row justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $selectedDepartment->name }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $selectedDepartment->description }}</p>
                            <div class="mt-2 flex flex-wrap gap-2">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">{{ $selectedDepartment->code }}</span>
                                @if($selectedDepartment->manager)
                                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                        Manager: {{ $selectedDepartment->manager->first_name }} {{ $selectedDepartment->manager->last_name }}
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="mt-3 md:mt-0">
                            <span class="bg-gray-100 text-gray-800 text-xs font-medium inline-flex items-center px-2.5 py-0.5 rounded dark:bg-gray-700 dark:text-gray-300">
                                <svg class="w-3 h-3 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm0 18a8 8 0 1 1 8-8 8.009 8.009 0 0 1-8 8Z"/>
                                    <path d="M10 12.5a1 1 0 0 0-1 1v1a1 1 0 0 0 2 0v-1a1 1 0 0 0-1-1Zm0-1.5a1.5 1.5 0 1 0-1.5-1.5A1.5 1.5 0 0 0 10 11Z"/>
                                </svg>
                                {{ $users->where('department_id', $selectedDepartment->id)->count() }} Users
                            </span>
                        </div>
                    </div>
                </div>
            @endif
        </x-card>

        <!-- Organization Chart -->
        <x-card class="p-4">
            <div class="flex items-center justify-between pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Organization Chart</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Interactive visualization of reporting relationships</p>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ count($users) }} {{ count($users) === 1 ? 'user' : 'users' }} displayed</span>
                </div>
            </div>

            <div id="org-chart-container" class="w-full overflow-auto" style="min-height: 600px;">
                <!-- The chart will be rendered here by the JavaScript library -->
            </div>
        </x-card>
    </div>

    <!-- Inline styles for the org chart to ensure they're applied -->
    <style>
        .simple-org-chart {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            overflow-x: auto;
        }

        .org-level {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            width: 100%;
        }

        .org-node {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            background-color: white;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .dark .org-node {
            border-color: #4b5563;
            background-color: #374151;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        .org-node-header {
            background-color: #f3f4f6;
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 600;
            color: #111827;
        }

        .dark .org-node-header {
            background-color: #4b5563;
            border-bottom-color: #6b7280;
            color: #f3f4f6;
        }

        .org-node-content {
            padding: 12px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .dark .org-node-content {
            color: #d1d5db;
        }

        .org-node-title {
            font-size: 0.875rem;
            color: #4b5563;
            margin-bottom: 8px;
        }

        .dark .org-node-title {
            color: #9ca3af;
        }

        .org-node-department {
            font-size: 0.75rem;
            color: #6b7280;
            background-color: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        .dark .org-node-department {
            color: #d1d5db;
            background-color: #1f2937;
        }

        .org-connector {
            width: 2px;
            height: 30px;
            background-color: #9ca3af;
            margin: 0 auto 20px;
        }

        .dark .org-connector {
            background-color: #6b7280;
        }

        .org-level-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #4b5563;
            margin-bottom: 15px;
            text-align: center;
            width: 100%;
            padding: 8px;
            background-color: #f9fafb;
            border-radius: 4px;
        }

        .dark .org-level-label {
            color: #d1d5db;
            background-color: #1f2937;
        }

        /* Pagination for levels with many users */
        .org-level-pagination {
            display: flex;
            justify-content: center;
            margin-top: 10px;
            margin-bottom: 20px;
            width: 100%;
        }

        .org-level-pagination button {
            padding: 5px 10px;
            margin: 0 5px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            background-color: #f9fafb;
            color: #4b5563;
            cursor: pointer;
        }

        .dark .org-level-pagination button {
            border-color: #4b5563;
            background-color: #374151;
            color: #d1d5db;
        }

        .org-level-pagination button:hover {
            background-color: #f3f4f6;
        }

        .dark .org-level-pagination button:hover {
            background-color: #4b5563;
        }
    </style>

    <script>
        document.addEventListener('livewire:initialized', function() {
            renderSimpleOrgChart();

            // Re-render when Livewire updates
            Livewire.hook('message.processed', (message, component) => {
                if (component.fingerprint.name === 'users.org-chart') {
                    renderSimpleOrgChart();
                }
            });
        });

        function renderSimpleOrgChart() {
            console.log("Rendering simple org chart...");

            // Get the chart data from the Livewire component
            const chartData = @json($orgData);
            console.log("Chart data:", chartData);
            console.log("Chart data length:", chartData ? chartData.length : 0);

            const container = document.getElementById('org-chart-container');
            if (!container) {
                console.error("Container element not found");
                return;
            }

            // Clear previous chart
            container.innerHTML = '';

            // Create the chart container
            const chartElement = document.createElement('div');
            chartElement.className = 'simple-org-chart';
            // Apply inline styles
            chartElement.style.display = 'flex';
            chartElement.style.flexDirection = 'column';
            chartElement.style.alignItems = 'center';
            chartElement.style.padding = '20px';
            chartElement.style.overflowX = 'auto';
            chartElement.style.width = '100%';
            container.appendChild(chartElement);

            // Process the hierarchy data
            const hierarchyData = processHierarchyData(chartData);
            console.log("Processed hierarchy data:", hierarchyData);

            if (!hierarchyData || hierarchyData.length === 0) {
                console.log("No hierarchy data available after processing");
                chartElement.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">No organization data available. Please select a department or adjust your search criteria.</p>';
                return;
            }

            // Get all users from the Livewire component as a fallback
            const allUsers = @json($usersForJs);

            // Check if we have a flat structure (all users at one level)
            const isFlatStructure = hierarchyData.length === 1 && hierarchyData[0].length > 10;

            if (isFlatStructure) {
                // For flat structures with many users, create a relational grid
                console.log("Rendering flat structure");
                renderFlatStructure(chartElement, hierarchyData[0]);
            } else if (hierarchyData.length > 0) {
                // Render hierarchical structure
                console.log("Rendering hierarchical structure");
                renderHierarchicalStructure(chartElement, hierarchyData);
            } else if (allUsers && allUsers.length > 0) {
                // Fallback to all users if hierarchy data is empty
                console.log("Falling back to all users");
                renderFlatStructure(chartElement, allUsers);
            } else {
                // No data available
                console.log("No data available");
                chartElement.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">No organization data available. Please select a department or adjust your search criteria.</p>';
            }

            console.log("Simple org chart rendered successfully");
        }

        function renderFlatStructure(chartElement, users) {
            console.log("Rendering flat structure with users:", users);

            // Ensure users is an array
            if (!Array.isArray(users)) {
                console.error("Users is not an array:", users);
                chartElement.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">Error: Invalid user data format.</p>';
                return;
            }

            // Create a header explaining the structure
            const headerElement = document.createElement('div');
            headerElement.className = 'p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400';
            headerElement.style.padding = '16px';
            headerElement.style.marginBottom = '16px';
            headerElement.style.borderRadius = '8px';
            headerElement.style.backgroundColor = '#eff6ff';
            headerElement.style.color = '#1e40af';

            // Check if we're filtering by department
            const selectedDepartmentId = @json($selectedDepartmentId);
            const selectedDepartment = @json($selectedDepartment ? $selectedDepartment->name : null);

            if (selectedDepartmentId) {
                headerElement.innerHTML = `
                    <span style="font-weight: 600;">Department Structure:</span>
                    Showing ${users.length} users in the ${selectedDepartment} department.
                    Users are grouped by role within the department.
                `;
            } else {
                headerElement.innerHTML = `
                    <span style="font-weight: 600;">Organization Structure:</span>
                    Your organization has ${users.length} users.
                    Users are grouped by department for better visualization.
                `;
            }

            chartElement.appendChild(headerElement);

            // Create a container for the organization grid
            const gridContainer = document.createElement('div');
            gridContainer.id = 'org-grid-container';
            gridContainer.style.width = '100%';
            gridContainer.style.overflowX = 'auto';
            chartElement.appendChild(gridContainer);

            try {
                // Create the organization grid
                renderOrganizationGrid(gridContainer, users);
            } catch (error) {
                console.error("Error rendering organization grid:", error);
                gridContainer.innerHTML = `
                    <p class="text-center text-red-500 dark:text-red-400">
                        Error rendering organization chart: ${error.message}
                    </p>
                    <p class="text-center text-gray-500 dark:text-gray-400 mt-2">
                        Please try refreshing the page or contact support if the issue persists.
                    </p>
                `;
            }
        }

        function renderOrganizationGrid(container, users) {
            console.log("Rendering organization grid with users:", users);

            // Validate users array
            if (!Array.isArray(users)) {
                console.error("Users is not an array in renderOrganizationGrid");
                container.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">Error: Invalid user data format.</p>';
                return;
            }

            if (users.length === 0) {
                console.log("No users to display in the grid");
                container.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">No users found with the current filters.</p>';
                return;
            }

            // Check if we're filtering by a specific department
            const selectedDepartmentId = @json($selectedDepartmentId);

            // Create the grid container
            const gridElement = document.createElement('div');
            gridElement.style.display = 'flex';
            gridElement.style.flexDirection = 'column';
            gridElement.style.gap = '30px';
            gridElement.style.width = '100%';
            gridElement.style.marginBottom = '30px';
            container.appendChild(gridElement);

            try {
                // If we're filtering by department, display users by role/position
                if (selectedDepartmentId) {
                    renderDepartmentByRoles(gridElement, users);
                } else {
                    // Otherwise, group users by department
                    renderUsersByDepartment(gridElement, users);
                }
            } catch (error) {
                console.error("Error in renderOrganizationGrid:", error);
                gridElement.innerHTML = `
                    <p class="text-center text-red-500 dark:text-red-400">
                        Error rendering organization grid: ${error.message}
                    </p>
                `;
            }
        }

        function renderDepartmentByRoles(container, users) {
            console.log("Rendering department by roles:", users);

            // Validate users
            if (!Array.isArray(users)) {
                console.error("Users is not an array in renderDepartmentByRoles");
                container.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">Error: Invalid user data format.</p>';
                return;
            }

            if (users.length === 0) {
                console.log("No users to display by roles");
                container.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">No users found in this department.</p>';
                return;
            }

            try {
                // Group users by role/position
                const managerUsers = [];
                const teamLeadUsers = [];
                const regularUsers = [];

                // Categorize users by their job title or position
                users.forEach(user => {
                    // Check if user object is valid
                    if (!user || typeof user !== 'object') {
                        console.warn("Invalid user object:", user);
                        return; // Skip this user
                    }

                    const title = ((user.title || '')).toLowerCase();
                    if (title.includes('manager') || title.includes('director') || title.includes('head')) {
                        managerUsers.push(user);
                    } else if (title.includes('lead') || title.includes('senior') || title.includes('supervisor')) {
                        teamLeadUsers.push(user);
                    } else {
                        regularUsers.push(user);
                    }
                });

                // Check if we have any users in any category
                if (managerUsers.length === 0 && teamLeadUsers.length === 0 && regularUsers.length === 0) {
                    console.log("No users in any role category");
                    container.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">No users with role information found in this department.</p>';
                    return;
                }

                // Add managers section if any
                if (managerUsers.length > 0) {
                    const managerSection = createRoleSection('Department Management', managerUsers);
                    container.appendChild(managerSection);
                }

                // Add team leads section if any
                if (teamLeadUsers.length > 0) {
                    const teamLeadSection = createRoleSection('Team Leads', teamLeadUsers);
                    container.appendChild(teamLeadSection);
                }

                // Add regular employees section
                if (regularUsers.length > 0) {
                    const regularSection = createRoleSection('Team Members', regularUsers);
                    container.appendChild(regularSection);
                }
            } catch (error) {
                console.error("Error in renderDepartmentByRoles:", error);
                container.innerHTML = `
                    <p class="text-center text-red-500 dark:text-red-400">
                        Error grouping users by roles: ${error.message}
                    </p>
                `;
            }
        }

        function createRoleSection(roleName, users) {
            // Create role container with the same styling as department sections
            const roleContainer = document.createElement('div');
            roleContainer.style.border = '1px solid #e5e7eb';
            roleContainer.style.borderRadius = '8px';
            roleContainer.style.overflow = 'hidden';
            roleContainer.style.backgroundColor = '#ffffff';
            roleContainer.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';

            // Create role header
            const roleHeader = document.createElement('div');
            roleHeader.style.backgroundColor = '#f3f4f6';
            roleHeader.style.padding = '12px 16px';
            roleHeader.style.borderBottom = '1px solid #e5e7eb';
            roleHeader.style.display = 'flex';
            roleHeader.style.justifyContent = 'space-between';
            roleHeader.style.alignItems = 'center';

            const roleTitle = document.createElement('h3');
            roleTitle.style.fontSize = '1rem';
            roleTitle.style.fontWeight = '600';
            roleTitle.style.color = '#111827';
            roleTitle.textContent = roleName;

            const roleCount = document.createElement('span');
            roleCount.style.fontSize = '0.875rem';
            roleCount.style.color = '#6b7280';
            roleCount.style.backgroundColor = '#f9fafb';
            roleCount.style.padding = '2px 8px';
            roleCount.style.borderRadius = '9999px';
            roleCount.textContent = `${users.length} ${users.length === 1 ? 'user' : 'users'}`;

            roleHeader.appendChild(roleTitle);
            roleHeader.appendChild(roleCount);
            roleContainer.appendChild(roleHeader);

            // Create users grid
            const usersGrid = document.createElement('div');
            usersGrid.style.display = 'grid';
            usersGrid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';
            usersGrid.style.gap = '16px';
            usersGrid.style.padding = '16px';

            // Add users to grid
            users.forEach(user => {
                const userCard = createCompactUserCard(user);
                usersGrid.appendChild(userCard);
            });

            roleContainer.appendChild(usersGrid);
            return roleContainer;
        }

        function renderUsersByDepartment(container, users) {
            console.log("Rendering users by department:", users);

            // Validate users
            if (!Array.isArray(users)) {
                console.error("Users is not an array in renderUsersByDepartment");
                container.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">Error: Invalid user data format.</p>';
                return;
            }

            // Create departments map to group users by department
            const departmentsMap = new Map();
            const noDepUsers = [];

            try {
                // Group users by department
                users.forEach(user => {
                    // Check if user object is valid
                    if (!user || typeof user !== 'object') {
                        console.warn("Invalid user object:", user);
                        return; // Skip this user
                    }

                    if (user.department && user.department !== 'Not assigned') {
                        if (!departmentsMap.has(user.department)) {
                            departmentsMap.set(user.department, []);
                        }
                        departmentsMap.get(user.department).push(user);
                    } else {
                        noDepUsers.push(user);
                    }
                });

                // Check if we have any departments
                if (departmentsMap.size === 0 && noDepUsers.length === 0) {
                    console.log("No departments or users to display");
                    container.innerHTML = '<p class="text-center text-gray-500 dark:text-gray-400">No departments or users found with the current filters.</p>';
                    return;
                }

                // Add departments to the grid
                if (departmentsMap.size > 0) {
                    departmentsMap.forEach((depUsers, depName) => {
                        // Create department section
                        const depSection = createDepartmentSection(depName, depUsers);
                        container.appendChild(depSection);
                    });
                }

                // Add users without department
                if (noDepUsers.length > 0) {
                    const noDepSection = createDepartmentSection('Unassigned', noDepUsers);
                    container.appendChild(noDepSection);
                }
            } catch (error) {
                console.error("Error in renderUsersByDepartment:", error);
                container.innerHTML = `
                    <p class="text-center text-red-500 dark:text-red-400">
                        Error grouping users by department: ${error.message}
                    </p>
                `;
            }
        }

        function createDepartmentSection(departmentName, users) {
            // Create department container
            const depContainer = document.createElement('div');
            depContainer.style.border = '1px solid #e5e7eb';
            depContainer.style.borderRadius = '8px';
            depContainer.style.overflow = 'hidden';
            depContainer.style.backgroundColor = '#ffffff';
            depContainer.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';

            // Create department header
            const depHeader = document.createElement('div');
            depHeader.style.backgroundColor = '#f3f4f6';
            depHeader.style.padding = '12px 16px';
            depHeader.style.borderBottom = '1px solid #e5e7eb';
            depHeader.style.display = 'flex';
            depHeader.style.justifyContent = 'space-between';
            depHeader.style.alignItems = 'center';

            const depTitle = document.createElement('h3');
            depTitle.style.fontSize = '1rem';
            depTitle.style.fontWeight = '600';
            depTitle.style.color = '#111827';
            depTitle.textContent = departmentName;

            const depCount = document.createElement('span');
            depCount.style.fontSize = '0.875rem';
            depCount.style.color = '#6b7280';
            depCount.style.backgroundColor = '#f9fafb';
            depCount.style.padding = '2px 8px';
            depCount.style.borderRadius = '9999px';
            depCount.textContent = `${users.length} ${users.length === 1 ? 'user' : 'users'}`;

            depHeader.appendChild(depTitle);
            depHeader.appendChild(depCount);
            depContainer.appendChild(depHeader);

            // Create users grid
            const usersGrid = document.createElement('div');
            usersGrid.style.display = 'grid';
            usersGrid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(200px, 1fr))';
            usersGrid.style.gap = '16px';
            usersGrid.style.padding = '16px';

            // Add users to grid
            users.forEach(user => {
                const userCard = createCompactUserCard(user);
                usersGrid.appendChild(userCard);
            });

            depContainer.appendChild(usersGrid);
            return depContainer;
        }

        function createCompactUserCard(user) {
            const card = document.createElement('div');
            card.style.border = '1px solid #e5e7eb';
            card.style.borderRadius = '6px';
            card.style.padding = '12px';
            card.style.backgroundColor = '#ffffff';
            card.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
            card.style.gap = '8px';

            // User name
            const userName = document.createElement('div');
            userName.style.fontWeight = '600';
            userName.style.fontSize = '0.875rem';
            userName.style.color = '#111827';
            userName.textContent = user.name;
            card.appendChild(userName);

            // User email
            if (user.email) {
                const userEmail = document.createElement('div');
                userEmail.style.fontSize = '0.75rem';
                userEmail.style.color = '#6b7280';
                userEmail.style.whiteSpace = 'nowrap';
                userEmail.style.overflow = 'hidden';
                userEmail.style.textOverflow = 'ellipsis';
                userEmail.textContent = user.email;
                card.appendChild(userEmail);
            }

            // User job title
            const userTitle = document.createElement('div');
            userTitle.style.fontSize = '0.75rem';
            userTitle.style.color = '#4b5563';
            userTitle.style.marginTop = '4px';
            userTitle.textContent = user.title || 'Not set';
            card.appendChild(userTitle);

            return card;
        }

        function renderHierarchicalStructure(chartElement, hierarchyData) {
            // Render each level of the hierarchy
            hierarchyData.forEach((level, index) => {
                // Add connector if not the first level
                if (index > 0) {
                    const connector = document.createElement('div');
                    connector.className = 'org-connector';
                    // Apply inline styles
                    connector.style.width = '2px';
                    connector.style.height = '30px';
                    connector.style.backgroundColor = '#9ca3af';
                    connector.style.margin = '0 auto 20px';
                    chartElement.appendChild(connector);
                }

                // Add level label
                const levelLabel = document.createElement('div');
                levelLabel.className = 'org-level-label';
                // Apply inline styles
                levelLabel.style.fontSize = '0.875rem';
                levelLabel.style.fontWeight = '600';
                levelLabel.style.color = '#4b5563';
                levelLabel.style.marginBottom = '15px';
                levelLabel.style.textAlign = 'center';
                levelLabel.style.width = '100%';
                levelLabel.style.padding = '8px';
                levelLabel.style.backgroundColor = '#f9fafb';
                levelLabel.style.borderRadius = '4px';
                levelLabel.textContent = `Level ${index + 1}: ${getLevelDescription(index, level.length)}`;
                chartElement.appendChild(levelLabel);

                // Create a level element
                const levelElement = document.createElement('div');
                levelElement.className = 'org-level';
                // Apply inline styles
                levelElement.style.display = 'grid';
                levelElement.style.gridTemplateColumns = 'repeat(auto-fill, minmax(250px, 1fr))';
                levelElement.style.gap = '20px';
                levelElement.style.marginBottom = '30px';
                levelElement.style.width = '100%';

                // Add nodes for this level
                level.forEach(user => {
                    const nodeElement = createNodeElement(user);
                    levelElement.appendChild(nodeElement);
                });

                chartElement.appendChild(levelElement);
            });
        }

        function getLevelDescription(levelIndex, userCount) {
            if (levelIndex === 0) {
                return `Top Management (${userCount} ${userCount === 1 ? 'user' : 'users'})`;
            } else if (levelIndex === 1) {
                return `Middle Management (${userCount} ${userCount === 1 ? 'user' : 'users'})`;
            } else {
                return `Team Members (${userCount} ${userCount === 1 ? 'user' : 'users'})`;
            }
        }

        function processHierarchyData(chartData) {
            console.log("Processing hierarchy data:", chartData);

            // This function processes the chart data into levels
            const levels = [];

            // If chartData is empty or not an array, return an empty array
            if (!chartData || !Array.isArray(chartData) || chartData.length === 0) {
                console.log("Chart data is empty or invalid");
                // Create a single level with all users from the Livewire component
                const allUsers = @json($usersForJs);

                if (allUsers && allUsers.length > 0) {
                    console.log("Using all users from Livewire component:", allUsers);
                    return [allUsers];
                }

                return [];
            }

            // Process the first level (top-level managers)
            levels.push(chartData.map(node => ({
                id: node.id,
                name: node.name,
                title: node.title,
                department: node.department,
                email: node.email
            })));

            // Process subsequent levels
            let currentLevelNodes = chartData;

            while (currentLevelNodes.some(node => node.children && node.children.length > 0)) {
                const nextLevelNodes = [];

                currentLevelNodes.forEach(node => {
                    if (node.children && node.children.length > 0) {
                        node.children.forEach(child => {
                            nextLevelNodes.push({
                                id: child.id,
                                name: child.name,
                                title: child.title,
                                department: child.department,
                                email: child.email,
                                children: child.children
                            });
                        });
                    }
                });

                if (nextLevelNodes.length > 0) {
                    levels.push(nextLevelNodes);
                    currentLevelNodes = nextLevelNodes;
                } else {
                    break;
                }
            }

            console.log("Processed hierarchy levels:", levels);
            return levels;
        }

        function createNodeElement(user) {
            const nodeElement = document.createElement('div');
            nodeElement.className = 'org-node';
            // Apply inline styles
            nodeElement.style.border = '2px solid #e5e7eb';
            nodeElement.style.borderRadius = '8px';
            nodeElement.style.overflow = 'hidden';
            nodeElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
            nodeElement.style.backgroundColor = 'white';
            nodeElement.style.position = 'relative';
            nodeElement.style.height = '100%';
            nodeElement.style.display = 'flex';
            nodeElement.style.flexDirection = 'column';

            // Create header with user name
            const headerElement = document.createElement('div');
            headerElement.className = 'org-node-header';
            // Apply inline styles
            headerElement.style.backgroundColor = '#f3f4f6';
            headerElement.style.padding = '12px';
            headerElement.style.borderBottom = '1px solid #e5e7eb';
            headerElement.style.fontWeight = '600';
            headerElement.style.color = '#111827';
            headerElement.textContent = user.name;

            // Create content container
            const contentElement = document.createElement('div');
            contentElement.className = 'org-node-content';
            // Apply inline styles
            contentElement.style.padding = '12px';
            contentElement.style.flexGrow = '1';
            contentElement.style.display = 'flex';
            contentElement.style.flexDirection = 'column';
            contentElement.style.justifyContent = 'space-between';

            // Create job title element
            const titleElement = document.createElement('div');
            titleElement.className = 'org-node-title';
            // Apply inline styles
            titleElement.style.fontSize = '0.875rem';
            titleElement.style.color = '#4b5563';
            titleElement.style.marginBottom = '8px';
            titleElement.innerHTML = `<strong>Job Title:</strong> ${user.title || 'Not set'}`;

            // Create department element
            const departmentElement = document.createElement('div');
            departmentElement.className = 'org-node-department';
            // Apply inline styles
            departmentElement.style.fontSize = '0.75rem';
            departmentElement.style.color = '#6b7280';
            departmentElement.style.backgroundColor = '#f3f4f6';
            departmentElement.style.padding = '4px 8px';
            departmentElement.style.borderRadius = '4px';
            departmentElement.style.display = 'inline-block';
            departmentElement.textContent = user.department || 'Not assigned';

            // Create email element if available
            if (user.email) {
                const emailElement = document.createElement('div');
                emailElement.className = 'text-xs text-gray-500 dark:text-gray-400 mt-2';
                // Apply inline styles
                emailElement.style.fontSize = '0.75rem';
                emailElement.style.color = '#6b7280';
                emailElement.style.marginTop = '8px';
                emailElement.textContent = user.email;
                contentElement.appendChild(emailElement);
            }

            // Add elements to content container
            contentElement.appendChild(titleElement);
            contentElement.appendChild(departmentElement);

            // Add header and content to node
            nodeElement.appendChild(headerElement);
            nodeElement.appendChild(contentElement);

            return nodeElement;
        }
    </script>
</x-content>
