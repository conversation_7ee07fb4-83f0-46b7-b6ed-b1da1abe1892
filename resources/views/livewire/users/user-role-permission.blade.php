<!-- Role & Permission Main Page -->
<div>
    <div class="flex items-start max-md:flex-col w-full">
        {{-- Page content --}}
        <div class="flex-1 self-stretch">
            <div class="w-full md:px-0">
                <div class="grid grid-cols-1 xl:grid-cols-12 xl:gap-4 dark:bg-gray-900">
                    <div class="xl:col-span-12">
                        {{-- Tab Navigation --}}
                        <div class="mb-4 text-gray-900 border-b border-gray-200 dark:border-gray-700 dark:text-white">
                            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
                                <li class="mr-2" role="presentation">
                                    <button class="inline-block p-4 border-b-2 rounded-t-lg"
                                        :class="{'text-primary-600 border-primary-600 active dark:text-primary-500 dark:border-primary-500': $tab === 'roles', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 border-transparent': $tab !== 'roles'}"
                                        wire:click="selectTab('roles')"
                                        type="button"
                                        role="tab"
                                        aria-selected="@js($tab === 'roles')">
                                        <i class="fas fa-user-tag mr-2"></i>Roles
                                    </button>
                                </li>
                                <li class="mr-2" role="presentation">
                                    <button class="inline-block p-4 border-b-2 rounded-t-lg"
                                        :class="{'text-primary-600 border-primary-600 active dark:text-primary-500 dark:border-primary-500': $tab === 'permissions', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 border-transparent': $tab !== 'permissions'}"
                                        wire:click="selectTab('permissions')"
                                        type="button"
                                        role="tab"
                                        aria-selected="@js($tab === 'permissions')">
                                        <i class="fas fa-key mr-2"></i>Permissions
                                    </button>
                                </li>
                            </ul>
                        </div>

                        {{-- Main Content --}}
                        <div>
                            @if ($tab === 'roles')
                                @livewire('users.user-role-index')
                            @elseif ($tab === 'permissions')
                                @livewire('users.user-permission-index')
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{-- End of Role & Permission Main Page --}}