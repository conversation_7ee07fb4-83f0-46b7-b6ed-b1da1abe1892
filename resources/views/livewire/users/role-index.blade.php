{{-- Role Index Page --}}
<x-content>
    <x-card key="role-index-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <div class="lg:pr-3">
                        <label for="roles-search" class="sr-only">Search</label>
                        <div class="relative mt-1 lg:w-64 xl:w-96">
                            <input
                                type="text"
                                wire:model.live.debounce.300ms="search"
                                id="roles-search"
                                class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                placeholder="Search for role">
                        </div>
                    </div>
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <a href="{{ route('users.roles.create') }}" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                        Add Role
                    </a>
                </div>
            </div>
        </div>
    </x-card>
    <x-card key="role-index-table" class="overflow-x-auto mt-4">
    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="p-4 w-4">
                    <input
                        type="checkbox"
                        wire:model.live="selectAll"
                        wire:key="select-all-roles-{{ $selectAll ? 'checked' : 'unchecked' }}"
                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                        aria-label="Select all roles"
                    >
                </th>
                <th class="cursor-pointer px-6 py-3">Name</th>
                <th class="px-6 py-3">Guard</th>
                <th class="px-6 py-3">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($roles as $role)
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                    <td class="p-4 w-4">
                        <input type="checkbox" wire:model.live="selectedRoles" value="{{ $role->id }}" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" aria-label="Select role">
                    </td>
                    <td class="px-6 py-4 text-gray-900 dark:text-white">{{ $role->name }}</td>
                    <td class="px-6 py-4 text-gray-900 dark:text-white">{{ $role->guard_name }}</td>
                    <td class="px-6 py-4">
                        <!-- Placeholder for actions (edit/delete) -->
                        <a href="{{ route('users.roles.edit', $role->id) }}" class="text-primary-600 dark:text-primary-400 hover:underline">Edit</a>
                    </td>
                </tr>
            @empty
                <tr><td colspan="4" class="px-6 py-4 text-center text-gray-900 dark:text-white">No roles found.</td></tr>
            @endforelse
        </tbody>
    </table>
</x-card>
</x-content>
