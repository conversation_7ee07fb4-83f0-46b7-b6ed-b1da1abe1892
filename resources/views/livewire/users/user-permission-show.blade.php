<x-content>
    <div>
        <x-content-header title="Permission Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-user-permission-edit', { permission: {{ $permission->id }} })" data-action="$dispatch('to-user-permission-edit', { permission: {{ $permission->id }} })" class="page-button"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-user-permission-delete', { permission: {{ $permission->id }} })" data-action="$dispatch('to-user-permission-delete', { permission: {{ $permission->id }} })" class="page-button"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-permission-index')" data-action="$dispatch('to-permission-index')" class="page-button"/>
        </x-content-header>
        
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Left Column -->
                <div class="col-span-full xl:col-auto">
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <div class="flex items-center justify-center w-20 h-20 mb-4 rounded-lg bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300">
                                <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">{{ $permission->name }}</h3>
                                <div class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                                    <p>Created: {{ $permission->created_at->format('M d, Y') }}</p>
                                    <p>Last Updated: {{ $permission->updated_at->format('M d, Y') }}</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-3 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                        {{ $permission->roles->count() }} Roles
                                    </span>
                                    <span class="px-3 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300">
                                        {{ $permission->users->count() }} Direct Users
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Permission Details -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Permission Details</h3>
                        <dl class="space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Permission Name</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $permission->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Guard Name</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $permission->guard_name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created At</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $permission->created_at->format('M d, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $permission->updated_at->format('M d, Y') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="col-span-2">
                    <!-- Assigned Roles -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold dark:text-white">Assigned Roles ({{ $permission->roles->count() }})</h3>
                        </div>
                        
                        @if($permission->roles->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">Role Name</th>
                                            <th scope="col" class="px-6 py-3">Guard</th>
                                            <th scope="col" class="px-6 py-3">Users</th>
                                            <th scope="col" class="px-6 py-3 text-right">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($permission->roles as $role)
                                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {{ $role->name }}
                                                </td>
                                                <td class="px-6 py-4">{{ $role->guard_name }}</td>
                                                <td class="px-6 py-4">
                                                    <span class="px-2 py-1 text-xs font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">
                                                        {{ $role->users->count() }} users
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-right">
                                                    <a href="#" wire:click.prevent="$dispatch('to-user-role-show', { role: {{ $role->id }} })" class="font-medium text-primary-600 hover:text-primary-800 dark:text-primary-500 dark:hover:text-primary-400">View</a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="p-4 text-center text-gray-500 bg-gray-50 rounded-lg dark:bg-gray-700 dark:text-gray-400">
                                No roles assigned to this permission.
                            </div>
                        @endif
                    </div>

                    <!-- Direct Users -->
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold dark:text-white">Directly Assigned Users ({{ $permission->users->count() }})</h3>
                        </div>
                        
                        @if($permission->users->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">Name</th>
                                            <th scope="col" class="px-6 py-3">Email</th>
                                            <th scope="col" class="px-6 py-3">Status</th>
                                            <th scope="col" class="px-6 py-3 text-right">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($permission->users as $user)
                                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                                <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                    {{ $user->name }}
                                                </td>
                                                <td class="px-6 py-4">{{ $user->email }}</td>
                                                <td class="px-6 py-4">
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $user->status === 'active' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' : 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' }}">
                                                        {{ ucfirst($user->status) }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-right">
                                                    <a href="#" wire:click.prevent="$dispatch('to-user-show', { user: {{ $user->id }} })" class="font-medium text-primary-600 hover:text-primary-800 dark:text-primary-500 dark:hover:text-primary-400">View</a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="p-4 text-center text-gray-500 bg-gray-50 rounded-lg dark:bg-gray-700 dark:text-gray-400">
                                No users directly assigned to this permission.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </x-content-body>
    </div>
</x-content>
