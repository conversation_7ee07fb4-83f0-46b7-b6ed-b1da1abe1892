<x-content>
    <div>
        <x-content-header title="User Details">
            <x-page-button type="edit" label="Edit" action="$dispatch('to-user-edit', { user: {{ $user->id }} })"/>
            <x-page-button type="delete" label="Delete" action="$dispatch('to-user-delete', { user: {{ $user->id }} })"/>
            <x-page-button type="back" label="Back" action="$dispatch('to-user-index')"/>
        </x-content-header>
        <x-content-body>
            <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
                <!-- Right Content -->
                <div class="col-span-full xl:col-auto">
                    @if($this->shouldShowSection('profile', auth()->user()->role_id))
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="items-center sm:flex xl:block 2xl:flex sm:space-x-4 xl:space-x-0 2xl:space-x-4">
                            <img class="mb-4 rounded-lg w-28 h-28 sm:mb-0 xl:mb-4 2xl:mb-0" src="{{ $profilePictureUrl }}" alt="{{ $user->first_name }} {{ $user->last_name }}">
                            <div>
                                <h3 class="mb-1 text-xl font-bold text-gray-900 dark:text-white">{{ $user->first_name }} {{ $user->last_name }}</h3>
                                <div class="mb-4 text-sm text-gray-500 dark:text-gray-400">
                                    <p>{{ $user->role->name }}</p>
                                    @if($user->campaign)
                                        <p>Campaign: {{ $user->campaign->name }}</p>
                                    @endif
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-3 py-1 text-xs font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">{{ ucfirst($user->status) }}</span>
                                    @if($user->registration_number)
                                        <span class="px-3 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">{{ $user->registration_number }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($this->shouldShowSection('contact', auth()->user()->role_id))
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Contact Information</h3>
                        <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
                            <div class="col-span-1 sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $user->email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $user->phone_number ?: 'Not provided' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Birth Date</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $user->birth_date ? $user->birth_date->format('M d, Y') : 'Not provided' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Hire Date</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $user->hire_date ? $user->hire_date->format('M d, Y') : 'Not provided' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Country</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $user->country ?: 'Not provided' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">City</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $user->city ?: 'Not provided' }}</dd>
                            </div>
                            <div class="col-span-1 sm:col-span-2">
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Address</dt>
                                <dd class="mt-1 text-sm font-semibold text-gray-900 dark:text-white">{{ $user->address ?: 'Not provided' }}</dd>
                            </div>
                        </dl>
                    </div>
                    @endif
                </div>
                <!-- Left Content -->
                <div class="col-span-2">
                    @if($this->shouldShowSection('documents', auth()->user()->role_id))
                    <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Documents</h3>

                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            {{-- Resume --}}
                            <div>
                                <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Resume/CV</h4>
                                @if(isset($userDocuments['resume']))
                                    <div class="space-y-2">
                                        @foreach($userDocuments['resume'] as $document)
                                            <div class="flex flex-col p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                                <div class="flex items-center mb-2">
                                                    <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <div class="flex-1 min-w-0">
                                                        <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                            {{ $document['document_title'] ?: $document['file_name'] }}
                                                        </p>
                                                        <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                            {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                        </p>
                                                    </div>
                                                    <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                        View
                                                    </a>
                                                </div>

                                                @if($document['description'])
                                                    <p class="mb-2 text-sm text-gray-600 dark:text-gray-300">{{ $document['description'] }}</p>
                                                @endif

                                                <div class="flex flex-wrap gap-2 mt-2">
                                                    @if($document['expiry_date'])
                                                        <span class="px-2 py-1 text-xs font-medium {{ \Carbon\Carbon::parse($document['expiry_date'])->isPast() ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' : 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' }} rounded-full">
                                                            Expires: {{ \Carbon\Carbon::parse($document['expiry_date'])->format('M d, Y') }}
                                                        </span>
                                                    @endif

                                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                                        {{ $document['verification_status'] === 'verified' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' :
                                                           ($document['verification_status'] === 'rejected' ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' :
                                                           'text-yellow-800 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                                        {{ ucfirst($document['verification_status']) }}
                                                    </span>

                                                    @if(isset($document['department']) && !empty($document['department']))
                                                        <span class="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                                            {{ $document['department'] }}
                                                        </span>
                                                    @endif
                                                </div>

                                                @php $docId = $document['id']; @endphp

                                                @if(isset($documentVerification[$docId]) && $documentVerification[$docId]['editing'])
                                                    <!-- Inline Verification Form -->
                                                    <div class="mt-3 p-3 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                                                        <div class="mb-3">
                                                            <label class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Verification Status</label>
                                                            <div class="flex space-x-3">
                                                                <label class="inline-flex items-center">
                                                                    <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="verified" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                    <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Verify</span>
                                                                </label>
                                                                <label class="inline-flex items-center">
                                                                    <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="rejected" class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                    <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Reject</span>
                                                                </label>
                                                            </div>
                                                        </div>

                                                        @if($documentVerification[$docId]['status'] === 'rejected')
                                                            <div class="mb-3">
                                                                <label for="rejection_reason_{{ $docId }}" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Rejection Reason</label>
                                                                <textarea
                                                                    id="rejection_reason_{{ $docId }}"
                                                                    wire:model="documentVerification.{{ $docId }}.reason"
                                                                    rows="2"
                                                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                                    placeholder="Provide a reason for rejection"
                                                                ></textarea>
                                                            </div>
                                                        @endif

                                                        <div class="flex justify-end space-x-2">
                                                            <button
                                                                wire:click="cancelDocumentVerification({{ $docId }})"
                                                                type="button"
                                                                class="px-2 py-1 text-xs font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                                            >
                                                                Cancel
                                                            </button>
                                                            <button
                                                                wire:click="saveDocumentVerification({{ $docId }})"
                                                                type="button"
                                                                class="px-2 py-1 text-xs font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-2 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                                                            >
                                                                Save
                                                            </button>
                                                        </div>
                                                    </div>
                                                @else
                                                    <!-- Verify/Re-verify Button -->
                                                    <button
                                                        wire:click="toggleVerificationForm({{ $docId }})"
                                                        class="mt-2 inline-flex items-center px-2 py-1 text-xs font-medium {{ $document['verification_status'] === 'pending' ? 'text-white bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700' : 'text-gray-900 bg-white border border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700' }} rounded-lg focus:ring-2 focus:ring-primary-300 focus:outline-none dark:focus:ring-primary-800"
                                                    >
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        {{ $document['verification_status'] === 'pending' ? 'Verify Document' : 'Re-verify Document' }}
                                                    </button>
                                                @endif

                                                @if($document['verification_status'] === 'rejected' && $document['rejection_reason'])
                                                    <div class="p-2 mt-2 text-xs text-red-800 bg-red-100 rounded dark:bg-red-900 dark:text-red-300">
                                                        <strong>Rejection reason:</strong> {{ $document['rejection_reason'] }}
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-sm text-gray-500 dark:text-gray-400">No resume uploaded</p>
                                @endif
                            </div>

                            {{-- ID Card --}}
                            <div>
                                <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">ID Card</h4>
                                @if(isset($userDocuments['id_card']))
                                    <div class="space-y-2">
                                        @foreach($userDocuments['id_card'] as $document)
                                            <div class="flex flex-col p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                                <div class="flex items-center mb-2">
                                                    <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <div class="flex-1 min-w-0">
                                                        <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                            {{ $document['document_title'] ?: $document['file_name'] }}
                                                        </p>
                                                        <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                            {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                        </p>
                                                    </div>
                                                    <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                        View
                                                    </a>
                                                </div>

                                                @if($document['description'])
                                                    <p class="mb-2 text-sm text-gray-600 dark:text-gray-300">{{ $document['description'] }}</p>
                                                @endif

                                                <div class="flex flex-wrap gap-2 mt-2">
                                                    @if($document['expiry_date'])
                                                        <span class="px-2 py-1 text-xs font-medium {{ \Carbon\Carbon::parse($document['expiry_date'])->isPast() ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' : 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' }} rounded-full">
                                                            Expires: {{ \Carbon\Carbon::parse($document['expiry_date'])->format('M d, Y') }}
                                                        </span>
                                                    @endif

                                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                                        {{ $document['verification_status'] === 'verified' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' :
                                                           ($document['verification_status'] === 'rejected' ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' :
                                                           'text-yellow-800 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                                        {{ ucfirst($document['verification_status']) }}
                                                    </span>

                                                    @if(isset($document['department']) && !empty($document['department']))
                                                        <span class="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                                            {{ $document['department'] }}
                                                        </span>
                                                    @endif
                                                </div>

                                                @php $docId = $document['id']; @endphp

                                                @if(isset($documentVerification[$docId]) && $documentVerification[$docId]['editing'])
                                                    <!-- Inline Verification Form -->
                                                    <div class="mt-3 p-3 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                                                        <div class="mb-3">
                                                            <label class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Verification Status</label>
                                                            <div class="flex space-x-3">
                                                                <label class="inline-flex items-center">
                                                                    <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="verified" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                    <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Verify</span>
                                                                </label>
                                                                <label class="inline-flex items-center">
                                                                    <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="rejected" class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                    <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Reject</span>
                                                                </label>
                                                            </div>
                                                        </div>

                                                        @if($documentVerification[$docId]['status'] === 'rejected')
                                                            <div class="mb-3">
                                                                <label for="rejection_reason_{{ $docId }}" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Rejection Reason</label>
                                                                <textarea
                                                                    id="rejection_reason_{{ $docId }}"
                                                                    wire:model="documentVerification.{{ $docId }}.reason"
                                                                    rows="2"
                                                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                                    placeholder="Provide a reason for rejection"
                                                                ></textarea>
                                                            </div>
                                                        @endif

                                                        <div class="flex justify-end space-x-2">
                                                            <button
                                                                wire:click="cancelDocumentVerification({{ $docId }})"
                                                                type="button"
                                                                class="px-2 py-1 text-xs font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                                            >
                                                                Cancel
                                                            </button>
                                                            <button
                                                                wire:click="saveDocumentVerification({{ $docId }})"
                                                                type="button"
                                                                class="px-2 py-1 text-xs font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-2 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                                                            >
                                                                Save
                                                            </button>
                                                        </div>
                                                    </div>
                                                @else
                                                    <!-- Verify/Re-verify Button -->
                                                    <button
                                                        wire:click="toggleVerificationForm({{ $docId }})"
                                                        class="mt-2 inline-flex items-center px-2 py-1 text-xs font-medium {{ $document['verification_status'] === 'pending' ? 'text-white bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700' : 'text-gray-900 bg-white border border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700' }} rounded-lg focus:ring-2 focus:ring-primary-300 focus:outline-none dark:focus:ring-primary-800"
                                                    >
                                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        {{ $document['verification_status'] === 'pending' ? 'Verify Document' : 'Re-verify Document' }}
                                                    </button>
                                                @endif

                                                @if($document['verification_status'] === 'rejected' && $document['rejection_reason'])
                                                    <div class="p-2 mt-2 text-xs text-red-800 bg-red-100 rounded dark:bg-red-900 dark:text-red-300">
                                                        <strong>Rejection reason:</strong> {{ $document['rejection_reason'] }}
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-sm text-gray-500 dark:text-gray-400">No ID card uploaded</p>
                                @endif
                            </div>
                        </div>

                        {{-- Certificates --}}
                        <div class="mt-6">
                            <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Certificates</h4>
                            @if(isset($userDocuments['certificate']) && count($userDocuments['certificate']) > 0)
                                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    @foreach($userDocuments['certificate'] as $document)
                                        <div class="flex flex-col p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                            <div class="flex items-center mb-2">
                                                <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M5 2a1 1 0 00-1 1v1h12V3a1 1 0 00-1-1H5zm12 3H3v10a1 1 0 001 1h12a1 1 0 001-1V5zM9 8a1 1 0 011-1h4a1 1 0 110 2h-4a1 1 0 01-1-1zm0 3a1 1 0 011-1h4a1 1 0 110 2h-4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                        {{ $document['document_title'] ?: $document['file_name'] }}
                                                    </p>
                                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                        {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                    </p>
                                                </div>
                                                <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                    View
                                                </a>
                                            </div>

                                            @if($document['description'])
                                                <p class="mb-2 text-sm text-gray-600 dark:text-gray-300">{{ $document['description'] }}</p>
                                            @endif

                                            <div class="flex flex-wrap gap-2 mt-2">
                                                @if($document['expiry_date'])
                                                    <span class="px-2 py-1 text-xs font-medium {{ \Carbon\Carbon::parse($document['expiry_date'])->isPast() ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' : 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' }} rounded-full">
                                                        Expires: {{ \Carbon\Carbon::parse($document['expiry_date'])->format('M d, Y') }}
                                                    </span>
                                                @endif

                                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                                    {{ $document['verification_status'] === 'verified' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' :
                                                       ($document['verification_status'] === 'rejected' ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' :
                                                       'text-yellow-800 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                                    {{ ucfirst($document['verification_status']) }}
                                                </span>

                                                @if(isset($document['department']) && !empty($document['department']))
                                                    <span class="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                                        {{ $document['department'] }}
                                                    </span>
                                                @endif
                                            </div>

                                            @php $docId = $document['id']; @endphp

                                            @if(isset($documentVerification[$docId]) && $documentVerification[$docId]['editing'])
                                                <!-- Inline Verification Form -->
                                                <div class="mt-3 p-3 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                                                    <div class="mb-3">
                                                        <label class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Verification Status</label>
                                                        <div class="flex space-x-3">
                                                            <label class="inline-flex items-center">
                                                                <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="verified" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Verify</span>
                                                            </label>
                                                            <label class="inline-flex items-center">
                                                                <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="rejected" class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Reject</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    @if($documentVerification[$docId]['status'] === 'rejected')
                                                        <div class="mb-3">
                                                            <label for="rejection_reason_{{ $docId }}" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Rejection Reason</label>
                                                            <textarea
                                                                id="rejection_reason_{{ $docId }}"
                                                                wire:model="documentVerification.{{ $docId }}.reason"
                                                                rows="2"
                                                                class="bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                                placeholder="Provide a reason for rejection"
                                                            ></textarea>
                                                        </div>
                                                    @endif

                                                    <div class="flex justify-end space-x-2">
                                                        <button
                                                            wire:click="cancelDocumentVerification({{ $docId }})"
                                                            type="button"
                                                            class="px-2 py-1 text-xs font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            wire:click="saveDocumentVerification({{ $docId }})"
                                                            type="button"
                                                            class="px-2 py-1 text-xs font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-2 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            @else
                                                <!-- Verify/Re-verify Button -->
                                                <button
                                                    wire:click="toggleVerificationForm({{ $docId }})"
                                                    class="mt-2 inline-flex items-center px-2 py-1 text-xs font-medium {{ $document['verification_status'] === 'pending' ? 'text-white bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700' : 'text-gray-900 bg-white border border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700' }} rounded-lg focus:ring-2 focus:ring-primary-300 focus:outline-none dark:focus:ring-primary-800"
                                                >
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ $document['verification_status'] === 'pending' ? 'Verify Document' : 'Re-verify Document' }}
                                                </button>
                                            @endif

                                            @if($document['verification_status'] === 'rejected' && $document['rejection_reason'])
                                                <div class="p-2 mt-2 text-xs text-red-800 bg-red-100 rounded dark:bg-red-900 dark:text-red-300">
                                                    <strong>Rejection reason:</strong> {{ $document['rejection_reason'] }}
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No certificates uploaded</p>
                            @endif
                        </div>

                        {{-- Other Documents --}}
                        <div class="mt-6">
                            <h4 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Other Documents</h4>
                            @if(isset($userDocuments['other_document']) && count($userDocuments['other_document']) > 0)
                                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    @foreach($userDocuments['other_document'] as $document)
                                        <div class="flex flex-col p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                            <div class="flex items-center mb-2">
                                                <svg class="w-8 h-8 mr-3 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                                        {{ $document['document_title'] ?: $document['file_name'] }}
                                                    </p>
                                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                                        {{ \Carbon\Carbon::parse($document['created_at'])->format('M d, Y') }}
                                                    </p>
                                                </div>
                                                <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-center text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                    View
                                                </a>
                                            </div>

                                            @if($document['description'])
                                                <p class="mb-2 text-sm text-gray-600 dark:text-gray-300">{{ $document['description'] }}</p>
                                            @endif

                                            <div class="flex flex-wrap gap-2 mt-2">
                                                @if($document['expiry_date'])
                                                    <span class="px-2 py-1 text-xs font-medium {{ \Carbon\Carbon::parse($document['expiry_date'])->isPast() ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' : 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' }} rounded-full">
                                                        Expires: {{ \Carbon\Carbon::parse($document['expiry_date'])->format('M d, Y') }}
                                                    </span>
                                                @endif

                                                <span class="px-2 py-1 text-xs font-medium rounded-full
                                                    {{ $document['verification_status'] === 'verified' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' :
                                                       ($document['verification_status'] === 'rejected' ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' :
                                                       'text-yellow-800 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                                    {{ ucfirst($document['verification_status']) }}
                                                </span>

                                                @if(isset($document['department']) && !empty($document['department']))
                                                    <span class="px-2 py-1 text-xs font-medium text-blue-800 bg-blue-100 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                                        {{ $document['department'] }}
                                                    </span>
                                                @endif
                                            </div>

                                            @php $docId = $document['id']; @endphp

                                            @if(isset($documentVerification[$docId]) && $documentVerification[$docId]['editing'])
                                                <!-- Inline Verification Form -->
                                                <div class="mt-3 p-3 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                                                    <div class="mb-3">
                                                        <label class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Verification Status</label>
                                                        <div class="flex space-x-3">
                                                            <label class="inline-flex items-center">
                                                                <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="verified" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Verify</span>
                                                            </label>
                                                            <label class="inline-flex items-center">
                                                                <input type="radio" wire:model="documentVerification.{{ $docId }}.status" value="rejected" class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600">
                                                                <span class="ml-2 text-xs text-gray-900 dark:text-gray-300">Reject</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    @if($documentVerification[$docId]['status'] === 'rejected')
                                                        <div class="mb-3">
                                                            <label for="rejection_reason_{{ $docId }}" class="block mb-1 text-xs font-medium text-gray-900 dark:text-white">Rejection Reason</label>
                                                            <textarea
                                                                id="rejection_reason_{{ $docId }}"
                                                                wire:model="documentVerification.{{ $docId }}.reason"
                                                                rows="2"
                                                                class="bg-gray-50 border border-gray-300 text-gray-900 text-xs rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                                placeholder="Provide a reason for rejection"
                                                            ></textarea>
                                                        </div>
                                                    @endif

                                                    <div class="flex justify-end space-x-2">
                                                        <button
                                                            wire:click="cancelDocumentVerification({{ $docId }})"
                                                            type="button"
                                                            class="px-2 py-1 text-xs font-medium text-gray-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            wire:click="saveDocumentVerification({{ $docId }})"
                                                            type="button"
                                                            class="px-2 py-1 text-xs font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-2 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                                                        >
                                                            Save
                                                        </button>
                                                    </div>
                                                </div>
                                            @else
                                                <!-- Verify/Re-verify Button -->
                                                <button
                                                    wire:click="toggleVerificationForm({{ $docId }})"
                                                    class="mt-2 inline-flex items-center px-2 py-1 text-xs font-medium {{ $document['verification_status'] === 'pending' ? 'text-white bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700' : 'text-gray-900 bg-white border border-gray-200 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700' }} rounded-lg focus:ring-2 focus:ring-primary-300 focus:outline-none dark:focus:ring-primary-800"
                                                >
                                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    {{ $document['verification_status'] === 'pending' ? 'Verify Document' : 'Re-verify Document' }}
                                                </button>
                                            @endif

                                            @if($document['verification_status'] === 'rejected' && $document['rejection_reason'])
                                                <div class="p-2 mt-2 text-xs text-red-800 bg-red-100 rounded dark:bg-red-900 dark:text-red-300">
                                                    <strong>Rejection reason:</strong> {{ $document['rejection_reason'] }}
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <p class="text-sm text-gray-500 dark:text-gray-400">No other documents uploaded</p>
                            @endif
                        </div>
                    </div>
                    @endif

                    @if($this->shouldShowSection('document_status', auth()->user()->role_id))
                    <!-- Document Expiration Summary -->
                    <div class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Document Status</h3>

                        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <!-- Expired Documents -->
                            <div class="p-4 bg-red-50 rounded-lg dark:bg-red-900">
                                <div class="flex items-center">
                                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg dark:bg-red-800 dark:text-red-200">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 text-sm font-medium text-red-700 dark:text-red-200">
                                        Expired Documents
                                    </div>
                                </div>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-200">
                                    @php
                                        $expiredCount = 0;
                                        foreach ($userDocuments as $category => $documents) {
                                            foreach ($documents as $document) {
                                                if ($document['expiry_date'] && \Carbon\Carbon::parse($document['expiry_date'])->isPast()) {
                                                    $expiredCount++;
                                                }
                                            }
                                        }
                                    @endphp

                                    @if($expiredCount > 0)
                                        <p>{{ $expiredCount }} document(s) expired</p>
                                    @else
                                        <p>No expired documents</p>
                                    @endif
                                </div>
                            </div>

                            <!-- Pending Verification -->
                            <div class="p-4 bg-yellow-50 rounded-lg dark:bg-yellow-900">
                                <div class="flex items-center">
                                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-yellow-500 bg-yellow-100 rounded-lg dark:bg-yellow-800 dark:text-yellow-200">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 text-sm font-medium text-yellow-700 dark:text-yellow-200">
                                        Pending Verification
                                    </div>
                                </div>
                                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-200">
                                    @php
                                        $pendingCount = 0;
                                        foreach ($userDocuments as $category => $documents) {
                                            foreach ($documents as $document) {
                                                if ($document['verification_status'] === 'pending') {
                                                    $pendingCount++;
                                                }
                                            }
                                        }
                                    @endphp

                                    @if($pendingCount > 0)
                                        <p>{{ $pendingCount }} document(s) pending verification</p>
                                    @else
                                        <p>No documents pending verification</p>
                                    @endif
                                </div>
                            </div>

                            <!-- Verified Documents -->
                            <div class="p-4 bg-green-50 rounded-lg dark:bg-green-900">
                                <div class="flex items-center">
                                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg dark:bg-green-800 dark:text-green-200">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 text-sm font-medium text-green-700 dark:text-green-200">
                                        Verified Documents
                                    </div>
                                </div>
                                <div class="mt-2 text-sm text-green-700 dark:text-green-200">
                                    @php
                                        $verifiedCount = 0;
                                        foreach ($userDocuments as $category => $documents) {
                                            foreach ($documents as $document) {
                                                if ($document['verification_status'] === 'verified') {
                                                    $verifiedCount++;
                                                }
                                            }
                                        }
                                    @endphp

                                    @if($verifiedCount > 0)
                                        <p>{{ $verifiedCount }} document(s) verified</p>
                                    @else
                                        <p>No verified documents</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($this->shouldShowSection('skills', auth()->user()->role_id) || $this->shouldShowSection('certifications', auth()->user()->role_id))
                    <!-- Skills and Certifications -->
                    <div class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-xl font-semibold dark:text-white">Skills & Certifications</h3>
                            <div class="flex space-x-2">
                                <button type="button" onclick="Livewire.dispatch('openTab', { tab: 'skills' })" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                    Manage Skills
                                </button>
                                <button type="button" onclick="Livewire.dispatch('openTab', { tab: 'certifications' })" class="text-sm font-medium text-primary-600 hover:underline dark:text-primary-500">
                                    Manage Certifications
                                </button>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                            <!-- Skills -->
                            <div>
                                <h4 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Skills</h4>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-4 py-3">Skill</th>
                                                <th scope="col" class="px-4 py-3">Proficiency</th>
                                                <th scope="col" class="px-4 py-3">Verified</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($userSkills as $skill)
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                        {{ $skill->name }}
                                                        @if($skill->category)
                                                            <span class="block text-xs text-gray-500 dark:text-gray-400">{{ ucfirst(str_replace('_', ' ', $skill->category)) }}</span>
                                                        @endif
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <div class="flex items-center">
                                                            <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                                                <div class="bg-primary-600 h-2.5 rounded-full" style="width: {{ ($skill->pivot->proficiency_level / 5) * 100 }}%"></div>
                                                            </div>
                                                            <span class="ml-2">{{ $skill->pivot->proficiency_level }}/5</span>
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        @if($skill->pivot->last_verified_at)
                                                            <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300">
                                                                {{ \Carbon\Carbon::parse($skill->pivot->last_verified_at)->format('M d, Y') }}
                                                            </span>
                                                        @else
                                                            <span class="px-2 py-1 text-xs font-medium text-yellow-800 bg-yellow-100 rounded-full dark:bg-yellow-900 dark:text-yellow-300">
                                                                Not verified
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="3" class="px-4 py-6 text-center">
                                                        <p class="text-gray-500 dark:text-gray-400">No skills added yet</p>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Certifications -->
                            <div>
                                <h4 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Certifications</h4>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-4 py-3">Certification</th>
                                                <th scope="col" class="px-4 py-3">Issued</th>
                                                <th scope="col" class="px-4 py-3">Expires</th>
                                                <th scope="col" class="px-4 py-3">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($userCertifications as $certification)
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                        {{ $certification->name }}
                                                        @if($certification->issuer)
                                                            <span class="block text-xs text-gray-500 dark:text-gray-400">{{ $certification->issuer }}</span>
                                                        @endif
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        {{ \Carbon\Carbon::parse($certification->pivot->issued_at)->format('M d, Y') }}
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        @if($certification->pivot->expires_at)
                                                            <span class="px-2 py-1 text-xs font-medium {{ \Carbon\Carbon::parse($certification->pivot->expires_at)->isPast() ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' : 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' }} rounded-full">
                                                                {{ \Carbon\Carbon::parse($certification->pivot->expires_at)->format('M d, Y') }}
                                                            </span>
                                                        @else
                                                            <span class="text-gray-500">No expiry</span>
                                                        @endif
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                                            {{ $certification->pivot->status === 'active' ? 'text-green-800 bg-green-100 dark:bg-green-900 dark:text-green-300' :
                                                               ($certification->pivot->status === 'expired' ? 'text-red-800 bg-red-100 dark:bg-red-900 dark:text-red-300' :
                                                               'text-yellow-800 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                                            {{ ucfirst($certification->pivot->status) }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="4" class="px-4 py-6 text-center">
                                                        <p class="text-gray-500 dark:text-gray-400">No certifications added yet</p>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Campaign Eligibility -->
                        @if($user->role_id == 6 && $this->shouldShowSection('campaign_eligibility', auth()->user()->role_id)) <!-- Only show for agents -->
                            <div class="mt-6">
                                <h4 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Campaign Eligibility</h4>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                            <tr>
                                                <th scope="col" class="px-4 py-3">Campaign</th>
                                                <th scope="col" class="px-4 py-3">Status</th>
                                                <th scope="col" class="px-4 py-3">Missing Requirements</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse($campaignEligibility as $campaign)
                                                <tr class="border-b dark:border-gray-700">
                                                    <td class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                                        {{ $campaign['name'] }}
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        @if($campaign['is_eligible'])
                                                            <span class="px-2 py-1 text-xs font-medium text-green-800 bg-green-100 rounded-full dark:bg-green-900 dark:text-green-300">
                                                                Eligible
                                                            </span>
                                                        @else
                                                            <span class="px-2 py-1 text-xs font-medium text-red-800 bg-red-100 rounded-full dark:bg-red-900 dark:text-red-300">
                                                                Not Eligible
                                                            </span>
                                                        @endif
                                                    </td>
                                                    <td class="px-4 py-3">
                                                        @if(!$campaign['is_eligible'])
                                                            <ul class="list-disc list-inside text-xs">
                                                                @foreach($campaign['missing_skills'] as $skill)
                                                                    <li>Skill: {{ $skill['name'] }} (Level {{ $skill['required_level'] }} required, current: {{ $skill['current_level'] }})</li>
                                                                @endforeach
                                                                @foreach($campaign['missing_certifications'] as $cert)
                                                                    <li>Certification: {{ $cert['name'] }} ({{ $cert['issuer'] }})</li>
                                                                @endforeach
                                                            </ul>
                                                        @else
                                                            <span class="text-green-600 dark:text-green-400">All requirements met</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="3" class="px-4 py-6 text-center">
                                                        <p class="text-gray-500 dark:text-gray-400">No campaigns available</p>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif
                    </div>
                    @endif

                    @if($this->shouldShowSection('performance', auth()->user()->role_id) && $user->role_id == 6)
                    <!-- Agent Performance -->
                    <div class="p-4 mt-4 bg-white border border-gray-200 rounded-lg shadow-sm 2xl:col-span-2 dark:border-gray-700 sm:p-6 dark:bg-gray-800">
                        <h3 class="mb-4 text-xl font-semibold dark:text-white">Performance Metrics</h3>

                        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                            <!-- Calls Handled -->
                            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-lg font-semibold dark:text-white">Calls Handled</h5>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-blue-100 rounded-full dark:bg-blue-900">
                                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <h6 class="text-xl font-bold dark:text-white">{{ $user->calls_count ?? 0 }}</h6>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Total calls</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Appointments Set -->
                            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-lg font-semibold dark:text-white">Appointments</h5>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-green-100 rounded-full dark:bg-green-900">
                                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <h6 class="text-xl font-bold dark:text-white">{{ $user->appointments_count ?? 0 }}</h6>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Appointments set</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Conversion Rate -->
                            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="text-lg font-semibold dark:text-white">Conversion Rate</h5>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 w-12 h-12 flex items-center justify-center bg-purple-100 rounded-full dark:bg-purple-900">
                                        <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                                            <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <h6 class="text-xl font-bold dark:text-white">
                                            @if(($user->calls_count ?? 0) > 0)
                                                {{ round((($user->appointments_count ?? 0) / ($user->calls_count ?? 1)) * 100, 1) }}%
                                            @else
                                                0%
                                            @endif
                                        </h6>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Conversion rate</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Rating -->
                        <div class="mt-6">
                            <h4 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">Agent Rating</h4>
                            <div class="flex items-center mb-2">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mr-2">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ (($user->rating ?? 0) / 3) * 100 }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $user->rating ?? '0' }}/3</span>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </x-content-body>
    </div>


</x-content>
