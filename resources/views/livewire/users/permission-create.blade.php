{{-- Permission Create Page --}}
<x-content>
    <x-card class="p-4">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Create Permission</h2>
            <a href="{{ route('users.permissions') }}" class="text-primary-600 dark:text-primary-400 hover:underline">Back</a>
        </div>
        <form wire:submit.prevent="createPermission" class="space-y-6">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Permission Name</label>
                <div class="mt-1">
                    <input type="text" 
                        id="name" 
                        wire:model="name" 
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                        placeholder="Enter permission name">
                </div>
                @error('name')
                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="guard_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Guard Name</label>
                <div class="mt-1">
                    <input type="text" 
                        id="guard_name" 
                        wire:model="guard_name" 
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                        placeholder="Enter guard name (default: web)">
                </div>
                @error('guard_name')
                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
                <div class="mt-1">
                    <input type="text" 
                        id="description" 
                        wire:model="description" 
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                        placeholder="Enter description">
                </div>
                @error('description')
                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <button type="submit" 
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800">
                    Create Permission
                </button>
            </div>
        </form>
    </x-card>
</x-content>
