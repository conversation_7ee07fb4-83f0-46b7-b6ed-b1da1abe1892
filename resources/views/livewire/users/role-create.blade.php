{{-- Role Create Page --}}
<x-content>
    <x-card class="p-4">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-bold">Create Role</h2>
            <a href="{{ route('users.roles') }}" class="text-primary-600 dark:text-primary-400 hover:underline">Back</a>
        </div>
        <form wire:submit.prevent="createRole" class="space-y-6">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Role Name</label>
                <div class="mt-1">
                    <input type="text" 
                        id="name" 
                        wire:model="name" 
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                        placeholder="Enter role name">
                </div>
                @error('name')
                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="guard_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Guard Name</label>
                <div class="mt-1">
                    <input type="text" 
                        id="guard_name" 
                        wire:model="guard_name" 
                        class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                        placeholder="Enter guard name (default: web)">
                </div>
                @error('guard_name')
                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Permissions</label>
                <div class="mt-2 space-y-2">
                    @foreach($permissions as $permissionId => $permissionName)
                        <div class="flex items-center">
                            <input type="checkbox" 
                                id="permission-{{ $permissionId }}" 
                                wire:model="selectedPermissions" 
                                value="{{ $permissionId }}"
                                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600 dark:ring-offset-gray-800">
                            <label for="permission-{{ $permissionId }}" class="ml-3 text-sm text-gray-700 dark:text-gray-300">{{ $permissionName }}</label>
                        </div>
                    @endforeach
                </div>
            </div>

            <div>
                <button type="submit" 
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:focus:ring-offset-gray-800">
                    Create Role
                </button>
            </div>
        </form>
    </x-card>
</x-content>
