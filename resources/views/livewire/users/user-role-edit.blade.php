<x-content>
    <form wire:submit.prevent="save" wire:key="role-edit-form-{{ $role->id }}">
        <x-content-header title="Edit Role: {{ $role->name }}">
            <x-page-button type="save" label="Save Changes" action="submit" class="page-button" />
            <x-page-button 
                type="cancel" 
                label="Cancel" 
                action="$dispatch('to-user-role-show', { role: {{ $role->id }} })" 
                data-action="$dispatch('to-user-role-show', { role: {{ $role->id }} })" 
                class="page-button"
            />
        </x-content-header>

        @if (session()->has('message'))
            <div class="p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400" role="alert">
                <span class="font-medium">Success!</span> {{ session('message')['text'] ?? '' }}
            </div>
        @endif

        @error('error')
            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
                <span class="font-medium">Error!</span> {{ $message }}
            </div>
        @enderror

        <x-content-body>
            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                <!-- Role Information -->
                <div class="space-y-6">
                    <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                        <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Role Information</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Name</label>
                                <div class="p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                                    {{ $role->name }}
                                </div>
                            </div>
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Guard</label>
                                <div class="p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                                    {{ $role->guard_name }}
                                </div>
                            </div>
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Created At</label>
                                <div class="p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                                    {{ $role->created_at->format('M d, Y') }}
                                </div>
                            </div>
                            <div>
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Last Updated</label>
                                <div class="p-2.5 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg w-full dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white">
                                    {{ $role->updated_at->format('M d, Y') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions -->
                <div class="md:col-span-2">
                    <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Permissions</h3>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Select the permissions to assign to this role. You can search and filter permissions below.
                            </p>
                        </div>

                        <!-- Search and Filter -->
                        <div class="mb-6">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                    </svg>
                                </div>
                                <input 
                                    type="text" 
                                    wire:model.live.debounce.300ms="search"
                                    class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" 
                                    placeholder="Search permissions..."
                                >
                            </div>
                            @error('selectedPermissions')
                                <p class="mt-1 text-sm text-red-600 dark:text-red-500">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Permissions Table -->
                        <div class="overflow-x-auto border border-gray-200 rounded-lg dark:border-gray-700">
                            <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-300">
                                    <tr>
                                        <th scope="col" class="px-6 py-3">
                                            <div class="flex items-center">
                                                Permission
                                                <button type="button" wire:click="sortBy('name')" class="ml-1">
                                                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M8.574 11.024h6.852a2.075 2.075 0 0 0 1.847-1.086 1.9 1.9 0 0 0-.11-1.986L13.736 2.9a2.122 2.122 0 0 0-3.472 0L6.837 7.952a1.9 1.9 0 0 0-.11 1.986 2.074 2.074 0 0 0 1.847 1.086Zm6.852 1.952H8.574a2.072 2.072 0 0 0-1.847 1.087 1.9 1.9 0 0 0 .11 1.985l3.426 5.05a2.123 2.123 0 0 0 3.472 0l3.427-5.05a1.9 1.9 0 0 0 .11-1.985 2.074 2.074 0 0 0-1.846-1.087Z"/>
                                                    </svg>
                                                </button>
                                            </div>
                                        </th>
                                        <th scope="col" class="px-6 py-3">
                                            Description
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right">
                                            <span class="sr-only">Select</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                                    @forelse($permissions as $permission)
                                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <td class="px-6 py-4">
                                                <div class="font-medium text-gray-900 dark:text-white">
                                                    {{ $permission->name }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-gray-600 dark:text-gray-300">
                                                    {{ $permission->description ?? 'No description available' }}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-right">
                                                <label class="inline-flex items-center cursor-pointer">
                                                    <input 
                                                        type="checkbox" 
                                                        wire:model.live="selectedPermissions"
                                                        value="{{ $permission->id }}"
                                                        class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                    >
                                                    <span class="sr-only">Select permission</span>
                                                </label>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="3" class="px-6 py-4 text-center">
                                                <div class="text-gray-500 dark:text-gray-400">
                                                    No permissions found. Try adjusting your search.
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($permissions->hasPages())
                            <div class="mt-4">
                                {{ $permissions->links() }}
                            </div>
                        @endif

                        <!-- Selected Permissions Count -->
                        <div class="mt-4 text-sm text-gray-600 dark:text-gray-400">
                            {{ count($selectedPermissions) }} {{ Str::plural('permission', count($selectedPermissions)) }} selected
                        </div>
                    </div>
                </div>
            </div>
        </x-content-body>
    </form>
</x-content>
