<!-- Role & Permission Main Page -->
<div>
    <div class="flex items-start max-md:flex-col w-full">
        {{-- Page content --}}
        <div class="flex-1 self-stretch">
            <div class="w-full md:px-0">
                <div class="grid grid-cols-1 xl:grid-cols-12 xl:gap-4 dark:bg-gray-900">
                    <div class="xl:col-span-12">
                        {{-- Tab Navigation --}}
                        <div class="mb-4 text-gray-900 border-b border-gray-200 dark:border-gray-700 dark:text-white">
                            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
                                <li class="mr-2" role="presentation">
                                    <button class="inline-block p-4 border-b-2 rounded-t-lg"
                                        :class="{'text-primary-600 border-primary-600 active dark:text-primary-500 dark:border-primary-500': $tab === 'roles', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 border-transparent': $tab !== 'roles'}"
                                        wire:click="selectTab('roles')"
                                        type="button"
                                        role="tab"
                                        aria-selected="@js($tab === 'roles')">
                                        <i class="fas fa-user-tag mr-2"></i>Roles
                                    </button>
                                </li>
                                <li class="mr-2" role="presentation">
                                    <button class="inline-block p-4 border-b-2 rounded-t-lg"
                                        :class="{'text-primary-600 border-primary-600 active dark:text-primary-500 dark:border-primary-500': $tab === 'permissions', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 border-transparent': $tab !== 'permissions'}"
                                        wire:click="selectTab('permissions')"
                                        type="button"
                                        role="tab"
                                        aria-selected="@js($tab === 'permissions')">
                                        <i class="fas fa-key mr-2"></i>Permissions
                                    </button>
                                </li>
                            </ul>
                        </div>

                        {{-- Main Content --}}
                        <div>
                            <x-card key="role-permission-header" class="p-4">
                                <div class="w-full">
                                    <div class="sm:flex">
                                        <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                                            <div class="lg:pr-3">
                                                <label for="{{ $tab }}-search" class="sr-only">Search</label>
                                                <div class="relative mt-1 lg:w-64 xl:w-96">
                                                    <input
                                                        type="text"
                                                        wire:model.live.debounce.300ms="search"
                                                        id="{{ $tab }}-search"
                                                        class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500"
                                                        placeholder="Search {{ $tab }}">
                                                </div>
                                            </div>
                                            <div class="flex pl-0 mt-3 space-x-1 sm:pl-2 sm:mt-0">
                                                <button
                                                    wire:click="deleteSelected"
                                                    wire:confirm="Are you sure you want to delete the selected {{ $tab }}?"
                                                    class="inline-flex justify-center p-1 text-gray-500 rounded {{$selectedItems ? 'cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white' : 'opacity-50 cursor-not-allowed'}}"
                                                    {{$selectedItems ? '' : 'disabled'}}
                                                >
                                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                                            <a href="{{ route('users.' . $tab . '.create') }}" class="inline-flex items-center justify-center w-1/2 px-3 py-2 text-sm font-medium text-center text-white rounded-lg bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                                <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                                                </svg>
                                                Add {{ $tab }}
                                            </a>
                                            <select wire:model.live="perPage" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                                @foreach ([5, 10, 25, 50, 100] as $value)
                                                    <option class="mr-8" value="{{ $value }}">{{ $value }} per page</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </x-card>
                            <x-card key="role-permission-table" class="overflow-x-auto mt-4">
                                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                        <tr>
                                            <th scope="col" class="px-6 py-3">
                                                <div class="flex items-center">
                                                    <input
                                                        type="checkbox"
                                                        wire:change="selectAll($event.target.checked)"
                                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                        :checked="selectAll"
                                                        aria-label="Select all">
                                                </div>
                                            </th>
                                            <th wire:click="sortBy('name')" class="cursor-pointer px-6 py-3">
                                                Name {{ $sortField === 'name' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                            </th>
                                            <th wire:click="sortBy('guard_name')" class="cursor-pointer px-6 py-3">
                                                Guard {{ $sortField === 'guard_name' ? ($sortDirection === 'asc' ? '↑' : '↓') : '' }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($items as $item)
                                            <tr wire:key="{{ $tab }}-{{ $item->id }}" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                                                wire:click="navigateTo('{{ route('users.roles.edit', ['role' => $item->id]) }}')">
                                                <td class="p-4 w-4">
                                                    <input
                                                        type="checkbox"
                                                        wire:change="toggleSelection({{ $item->id }})"
                                                        wire:key="checkbox-{{ $item->id }}-{{ in_array($item->id, $selectedItems) ? 'checked' : 'unchecked' }}"
                                                        value="{{ $item->id }}"
                                                        {{ in_array($item->id, $selectedItems) ? 'checked' : '' }}
                                                        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                        aria-label="Select {{ $tab }} {{ $item->name }}">
                                                    </input>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <div class="font-medium text-gray-900 dark:text-white">{{ $item->name }}</div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    {{ $item->guard_name }}
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="3" class="px-6 py-4 text-center text-gray-900 dark:text-white">No {{ $tab }} found.</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                <div class="p-4">
                                    {{ $items->links() }}
                                </div>
                            </x-card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
