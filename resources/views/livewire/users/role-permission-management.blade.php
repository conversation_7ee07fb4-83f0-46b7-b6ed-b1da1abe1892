<x-content x-data="{ tab: @entangle('tab') }">
    <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="tabs" role="tablist">
            <li class="mr-2" role="presentation">
                <button class="inline-block p-4 border-b-2 rounded-t-lg" 
                    :class="{'text-primary-600 border-primary-600 active dark:text-primary-500 dark:border-primary-500': tab === 'roles', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 border-transparent': tab !== 'roles'}"
                    id="roles-tab" 
                    x-on:click.prevent="tab = 'roles'" 
                    type="button" 
                    role="tab" 
                    aria-controls="roles" 
                    aria-selected="true">
                    <i class="fas fa-user-tag mr-2"></i>Roles
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button class="inline-block p-4 border-b-2 rounded-t-lg"
                    :class="{'text-primary-600 border-primary-600 active dark:text-primary-500 dark:border-primary-500': tab === 'permissions', 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 border-transparent': tab !== 'permissions'}"
                    id="permissions-tab"
                    x-on:click.prevent="tab = 'permissions'"
                    type="button"
                    role="tab"
                    aria-controls="permissions"
                    aria-selected="false">
                    <i class="fas fa-key mr-2"></i>Permissions
                </button>
            </li>
        </ul>
    </div>
    
    <div id="tabContentExample">
        <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800"
            :class="{'block': tab === 'roles', 'hidden': tab !== 'roles'}"
            id="roles"
            role="tabpanel"
            aria-labelledby="roles-tab">
            
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">Role Management</h2>
                <button wire:click="openRoleModal" class="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700">
                    <i class="fas fa-plus mr-2"></i>Add Role
                </button>
            </div>
            
            <div class="mb-4">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="fas fa-search text-gray-500"></i>
                    </div>
                    <input type="text" wire:model.debounce.300ms="searchTerm" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm" placeholder="Search roles...">
                </div>
            </div>
            
            <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">Role Name</th>
                            <th scope="col" class="px-6 py-3">Permissions</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($roles as $role)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <td class="px-6 py-4 font-medium text-gray-900 dark:text-white whitespace-nowrap">
                                    {{ $role->name }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-wrap gap-1">
                                        @foreach($role->permissions->take(3) as $permission)
                                            <span class="px-2 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700">
                                                {{ $permission->name }}
                                            </span>
                                        @endforeach
                                        @if($role->permissions->count() > 3)
                                            <span class="px-2 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700">
                                                +{{ $role->permissions->count() - 3 }} more
                                            </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <button wire:click="openRoleModal({{ $role->id }})" class="font-medium text-primary-600 dark:text-primary-500 hover:underline mr-3">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button wire:click="deleteRole({{ $role->id }})" class="font-medium text-red-600 dark:text-red-500 hover:underline">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-center">No roles found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4">
                {{ $roles->links() }}
            </div>
        </div>
        
        <div class="p-4 rounded-lg bg-gray-50 dark:bg-gray-800"
            :class="{'block': tab === 'permissions', 'hidden': tab !== 'permissions'}"
            id="permissions"
            role="tabpanel"
            aria-labelledby="permissions-tab">
            
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">Permission Management</h2>
                <button wire:click="openPermissionModal" class="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700">
                    <i class="fas fa-plus mr-2"></i>Add Permission
                </button>
            </div>
            
            <div class="mb-4">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <i class="fas fa-search text-gray-500"></i>
                    </div>
                    <input type="text" wire:model.debounce.300ms="searchTerm" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm" placeholder="Search permissions...">
                </div>
            </div>
            
            <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">Permission Name</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($permissions as $permission)
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                                <td class="px-6 py-4 font-medium text-gray-900 dark:text-white whitespace-nowrap">
                                    {{ $permission->name }}
                                </td>
                                <td class="px-6 py-4">
                                    <button wire:click="openPermissionModal({{ $permission->id }})" class="font-medium text-primary-600 dark:text-primary-500 hover:underline mr-3">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button wire:click="deletePermission({{ $permission->id }})" class="font-medium text-red-600 dark:text-red-500 hover:underline">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="2" class="px-6 py-4 text-center">No permissions found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4">
                {{ $permissions->links() }}
            </div>
        </div>
    </div>

    <!-- Role Modal -->
    <div x-data="{ open: @entangle('showRoleModal') }" x-show="open" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75 dark:bg-gray-900 dark:opacity-80"></div>
            </div>

            <!-- Modal panel -->
            <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                {{ $editMode ? 'Edit Role' : 'Add New Role' }}
                            </h3>
                            <div class="mt-4 w-full">
                                <form wire:submit.prevent="saveRole">
                                    <div>
                                        <label for="roleName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Role Name</label>
                                        <input type="text" wire:model="roleName" id="roleName" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        @error('roleName') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                    </div>
                                    
                                    <div class="mt-4">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Permissions</label>
                                        <div class="mt-2 max-h-60 overflow-y-auto p-2 border border-gray-300 rounded-md">
                                            @foreach(App\Models\Permission::all() as $permission)
                                            <div class="flex items-center mb-2">
                                                <input type="checkbox" wire:model="selectedPermissions" value="{{ $permission->id }}" id="permission-{{ $permission->id }}" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                                <label for="permission-{{ $permission->id }}" class="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                                                    {{ $permission->name }}
                                                </label>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button wire:click="saveRole" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Save
                    </button>
                    <button wire:click="closeRoleModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Permission Modal -->
    <div x-data="{ open: @entangle('showPermissionModal') }" x-show="open" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75 dark:bg-gray-900 dark:opacity-80"></div>
            </div>

            <!-- Modal panel -->
            <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                {{ $editMode ? 'Edit Permission' : 'Add New Permission' }}
                            </h3>
                            <div class="mt-4 w-full">
                                <form wire:submit.prevent="savePermission">
                                    <div>
                                        <label for="permissionName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Permission Name</label>
                                        <input type="text" wire:model="permissionName" id="permissionName" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                        @error('permissionName') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button wire:click="savePermission" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Save
                    </button>
                    <button wire:click="closePermissionModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alpine.js is initialized with x-data at the root element -->
</x-content>
