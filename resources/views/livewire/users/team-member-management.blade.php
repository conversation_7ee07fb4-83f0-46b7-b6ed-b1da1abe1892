<x-content>
    <x-card key="team-member-management-header" class="p-4">
        <div class="w-full">
            <div class="sm:flex">
                <div class="items-center hidden mb-3 sm:flex sm:divide-x sm:divide-gray-100 sm:mb-0 dark:divide-gray-700">
                    <div class="lg:pr-3">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Manage Team Members</h3>
                    </div>
                </div>
                <div class="flex items-center ml-auto space-x-2 sm:space-x-3">
                    <button wire:click="cancel" class="inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-center text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 sm:w-auto dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">
                        <svg class="w-5 h-5 mr-2 -ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                        </svg>
                        Back
                    </button>
                </div>
            </div>
        </div>
    </x-card>

    <x-card key="team-member-management-form" class="p-4 mt-4">
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <h3 class="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
                Manage Members for {{ $team->name }}
            </h3>
            
            <div class="mb-4">
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $team->description }}</p>
                <div class="mt-2">
                    <span class="bg-blue-100 text-blue-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">{{ $team->code }}</span>
                    @if($team->leader)
                        <span class="text-sm text-gray-500 dark:text-gray-400">Leader: {{ $team->leader->first_name }} {{ $team->leader->last_name }}</span>
                    @endif
                    @if($team->department)
                        <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">Department: {{ $team->department->name }}</span>
                    @endif
                </div>
            </div>
            
            <div class="mb-4">
                <label for="team-role" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Default Role for New Members</label>
                <select wire:model="teamRole" id="team-role" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                    <option value="member">Member</option>
                    <option value="coordinator">Coordinator</option>
                    <option value="specialist">Specialist</option>
                    <option value="advisor">Advisor</option>
                </select>
            </div>

            <div class="mb-4">
                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Team Members</label>
                <div class="h-64 overflow-y-auto p-4 border border-gray-300 rounded-lg dark:border-gray-600">
                    @foreach($potentialTeamMembers as $member)
                        <div class="flex items-center mb-3">
                            <input wire:model="teamMemberIds" type="checkbox" value="{{ $member->id }}" id="member-{{ $member->id }}" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="member-{{ $member->id }}" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">
                                {{ $member->first_name }} {{ $member->last_name }}
                                <span class="text-xs text-gray-500 dark:text-gray-400">({{ $member->email }})</span>
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="flex items-center space-x-4">
                <button type="button" wire:click="saveTeamMembers" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Save</button>
                <button type="button" wire:click="cancel" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-primary-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">Cancel</button>
            </div>
        </div>
    </x-card>
</x-content>
