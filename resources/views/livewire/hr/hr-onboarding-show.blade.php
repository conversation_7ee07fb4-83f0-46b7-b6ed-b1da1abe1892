<x-content>
    <x-content-header title="Onboarding Details">
        <div class="flex space-x-2">
            <x-page-button type="back" label="Back to Onboarding" action="$dispatch('to-hr-onboarding')" />
        </div>
    </x-content-header>

    <x-content-body>
        <!-- Main container for onboarding details -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <!-- Left column - Employee info and status -->
            <div class="lg:col-span-1">
                <!-- Employee Information Card -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <h3 class="mb-4 text-xl font-semibold text-gray-900 dark:text-white">Employee Information</h3>

                    <div class="flex items-center mb-4">
                        <div class="h-16 w-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-600 mr-4">
                            @if ($onboarding->user->getProfilePictureUrl() !== '/images/users/default.png')
                                <img src="{{ $onboarding->user->getProfilePictureUrl() }}" alt="{{ $onboarding->user->name }}" class="h-full w-full object-cover">
                            @else
                                <div class="h-full w-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ $onboarding->user->name }}</h4>
                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $onboarding->user->email }}</p>
                            @if ($onboarding->user->phone_number)
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ $onboarding->user->phone_number }}</p>
                            @endif
                        </div>
                    </div>

                    <div class="space-y-2">
                        @if ($onboarding->user->role)
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Role:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $onboarding->user->role->name }}</span>
                            </div>
                        @endif

                        @if ($onboarding->user->department)
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Department:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $onboarding->user->department->name }}</span>
                            </div>
                        @endif

                        @if ($onboarding->user->job_title)
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Job Title:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ $onboarding->user->job_title }}</span>
                            </div>
                        @endif

                        @if ($onboarding->user->hire_date)
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Hire Date:</span>
                                <span class="text-sm text-gray-900 dark:text-white">{{ \Carbon\Carbon::parse($onboarding->user->hire_date)->format('M d, Y') }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Onboarding Status Card -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <h3 class="mb-4 text-xl font-semibold text-gray-900 dark:text-white">Onboarding Status</h3>

                    <form wire:submit.prevent="updateOnboarding">
                        <div class="space-y-4">
                            <!-- Progress Bar -->
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Progress:</span>
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $onboarding->progress_percentage }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    <div class="bg-primary-600 h-2.5 rounded-full" style="width: {{ $onboarding->progress_percentage }}%"></div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="status" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                                <select wire:model="onboarding.status" id="status" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>

                            <!-- Assigned To -->
                            <div>
                                <label for="assigned_to" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Assigned To</label>
                                <select wire:model="onboarding.assigned_to" id="assigned_to" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                    <option value="">Not assigned</option>
                                    @foreach ($hrStaff as $staff)
                                        <option value="{{ $staff->id }}">{{ $staff->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Dates -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Start Date:</label>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $onboarding->start_date->format('M d, Y') }}</div>
                                </div>
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Target Date:</label>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $onboarding->target_completion_date ? $onboarding->target_completion_date->format('M d, Y') : 'Not set' }}
                                    </div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div>
                                <label for="notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                                <textarea wire:model="onboarding.notes" id="notes" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Add notes about this onboarding process"></textarea>
                            </div>

                            <!-- Template Info -->
                            @if ($onboarding->template)
                                <div>
                                    <label class="block mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">Template:</label>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $onboarding->template->name }}</div>
                                </div>
                            @endif

                            <!-- Save Button -->
                            <div>
                                <button type="submit" class="w-full px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                    Update Status
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Documents Card -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Documents</h3>
                        <button type="button" wire:click="openDocumentModal()" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                            <svg class="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            Add
                        </button>
                    </div>

                    <div class="space-y-3">
                        @forelse ($documents as $document)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                                <div class="flex items-center">
                                    <div class="mr-3 text-gray-500 dark:text-gray-400">
                                        @if (in_array($document->file_extension, ['jpg', 'jpeg', 'png', 'gif']))
                                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                            </svg>
                                        @elseif (in_array($document->file_extension, ['pdf']))
                                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                            </svg>
                                        @elseif (in_array($document->file_extension, ['doc', 'docx']))
                                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 3a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                            </svg>
                                        @else
                                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                            </svg>
                                        @endif
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ $document->name }}</h4>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ $document->file_size }} • Uploaded {{ $document->created_at->diffForHumans() }}
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            By {{ $document->uploader->name }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="{{ Storage::url($document->file_path) }}" target="_blank" class="text-blue-600 dark:text-blue-500 hover:underline">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"></path>
                                            <path d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"></path>
                                        </svg>
                                    </a>
                                    <button wire:click="deleteDocument({{ $document->id }})" class="text-red-600 dark:text-red-500 hover:underline">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                                No documents uploaded yet
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Right column - Tasks -->
            <div class="lg:col-span-2">
                <!-- Tasks Card -->
                <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Onboarding Tasks</h3>
                        <div>
                            <button type="button" wire:click="openTaskModal(null)" class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                                <svg class="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                                </svg>
                                Add Task
                            </button>
                        </div>
                    </div>

                    <!-- Progress Summary -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Overall Progress:</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $onboarding->progress_percentage }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="bg-primary-600 h-2.5 rounded-full" style="width: {{ $onboarding->progress_percentage }}%"></div>
                        </div>
                    </div>

                    <!-- Tasks by Category -->
                    @forelse ($tasksByCategory as $category => $tasks)
                        <div class="mb-6">
                            <h4 class="mb-3 text-lg font-medium text-gray-900 dark:text-white">
                                {{ $tasks->first()->category_name }}
                            </h4>

                            <div class="space-y-3">
                                @foreach ($tasks as $task)
                                    <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600
                                        @if($task->status === 'completed') border-green-300 dark:border-green-700 @endif
                                        @if($task->isOverdue()) border-red-300 dark:border-red-700 @endif">
                                        <div class="flex items-start justify-between">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0 mr-3">
                                                    @if ($task->status === 'completed')
                                                        <span class="flex items-center justify-center w-6 h-6 bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-300">
                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    @elseif ($task->status === 'in_progress')
                                                        <span class="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-300">
                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    @elseif ($task->status === 'skipped')
                                                        <span class="flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-800 rounded-full dark:bg-gray-800 dark:text-gray-300">
                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    @elseif ($task->isOverdue())
                                                        <span class="flex items-center justify-center w-6 h-6 bg-red-100 text-red-800 rounded-full dark:bg-red-900 dark:text-red-300">
                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    @else
                                                        <span class="flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-800 rounded-full dark:bg-gray-800 dark:text-gray-300">
                                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    @endif
                                                </div>
                                                <div>
                                                    <h5 class="text-sm font-medium text-gray-900 dark:text-white">{{ $task->name }}</h5>
                                                    @if ($task->description)
                                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">{{ $task->description }}</p>
                                                    @endif
                                                    <div class="mt-2 flex flex-wrap gap-2">
                                                        @if ($task->due_date)
                                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full
                                                                @if($task->isOverdue() && $task->status !== 'completed') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                                                @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 @endif">
                                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                                                </svg>
                                                                Due: {{ $task->due_date->format('M d, Y') }}
                                                            </span>
                                                        @endif

                                                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full
                                                            @if($task->status === 'completed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                                            @elseif($task->status === 'in_progress') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                                            @elseif($task->status === 'skipped') bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                                            @else bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300 @endif">
                                                            {{ ucfirst($task->status) }}
                                                        </span>

                                                        @if ($task->assignedUser)
                                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full dark:bg-gray-700 dark:text-gray-300">
                                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                                </svg>
                                                                {{ $task->assignedUser->name }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <button wire:click="openTaskModal({{ $task->id }})" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                            No tasks found. Add tasks to start the onboarding process.
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Task Modal -->
        <div x-data="{ open: @entangle('showTaskModal') }" x-show="open" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full dark:bg-gray-800">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 dark:bg-gray-800">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                    {{ $taskId ? 'Edit Task' : 'Add Task' }}
                                </h3>
                                <div class="mt-4 space-y-4">
                                    <!-- Task Status -->
                                    <div>
                                        <label for="taskStatus" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Status</label>
                                        <select wire:model="taskStatus" id="taskStatus" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                            <option value="pending">Pending</option>
                                            <option value="in_progress">In Progress</option>
                                            <option value="completed">Completed</option>
                                            <option value="skipped">Skipped</option>
                                        </select>
                                    </div>

                                    <!-- Assigned To -->
                                    <div>
                                        <label for="taskAssignedTo" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Assigned To</label>
                                        <select wire:model="taskAssignedTo" id="taskAssignedTo" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                            <option value="">Not assigned</option>
                                            @foreach ($hrStaff as $staff)
                                                <option value="{{ $staff->id }}">{{ $staff->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <!-- Notes -->
                                    <div>
                                        <label for="taskNotes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                                        <textarea wire:model="taskNotes" id="taskNotes" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Add notes about this task"></textarea>
                                    </div>

                                    <!-- Document Upload Button -->
                                    <div>
                                        <button type="button" wire:click="openDocumentModal({{ $taskId }})" class="text-blue-600 dark:text-blue-500 hover:underline text-sm font-medium">
                                            <svg class="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            Attach Document
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse dark:bg-gray-700">
                        <button type="button" wire:click="updateTask" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:bg-primary-700 dark:hover:bg-primary-800">
                            Save
                        </button>
                        <button type="button" wire:click="closeTaskModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Upload Modal -->
        <div x-data="{ open: @entangle('showDocumentModal') }" x-show="open" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full dark:bg-gray-800">
                    <form wire:submit.prevent="uploadDocument">
                        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 dark:bg-gray-800">
                            <div class="sm:flex sm:items-start">
                                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                        Upload Document
                                    </h3>
                                    <div class="mt-4 space-y-4">
                                        <!-- Document Name -->
                                        <div>
                                            <label for="documentName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Document Name</label>
                                            <input type="text" wire:model="documentName" id="documentName" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter document name">
                                            @error('documentName') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                        </div>

                                        <!-- Document File -->
                                        <div>
                                            <label for="documentFile" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">File</label>
                                            <input type="file" wire:model="documentFile" id="documentFile" class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400">
                                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">PDF, DOC, DOCX, JPG, PNG, or other document formats (max 10MB)</p>
                                            @error('documentFile') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                        </div>

                                        <!-- Task Association -->
                                        @if ($documentTaskId)
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                This document will be associated with the selected task.
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse dark:bg-gray-700">
                            <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:bg-primary-700 dark:hover:bg-primary-800">
                                Upload
                            </button>
                            <button type="button" wire:click="closeDocumentModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
