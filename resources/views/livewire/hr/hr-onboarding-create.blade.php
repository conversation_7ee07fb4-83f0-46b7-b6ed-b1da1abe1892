<x-content>
    <x-content-header title="Start New Onboarding Process">
        <div class="flex space-x-2">
            <x-page-button type="back" label="Back to Onboarding" action="$dispatch('to-hr-onboarding')" />
        </div>
    </x-content-header>

    <x-content-body>
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <form wire:submit.prevent="save">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Employee Selection -->
                    <div>
                        <label for="employee" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Employee</label>
                        <div class="relative">
                            <input type="text" wire:model.live.debounce.300ms="searchQuery" id="employee" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search by name or email">
                            
                            @if ($searchQuery && $users->count() > 0)
                                <div class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg dark:bg-gray-700 dark:border-gray-600">
                                    <ul class="max-h-60 overflow-y-auto">
                                        @foreach ($users as $user)
                                            <li wire:click="selectUser({{ $user->id }})" class="px-4 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600">
                                                <div class="flex items-center">
                                                    <div class="h-8 w-8 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-600 mr-3">
                                                        @if ($user->getProfilePictureUrl() !== '/images/users/default.png')
                                                            <img src="{{ $user->getProfilePictureUrl() }}" alt="{{ $user->name }}" class="h-full w-full object-cover">
                                                        @else
                                                            <div class="h-full w-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                                </svg>
                                                            </div>
                                                        @endif
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-gray-900 dark:text-white">{{ $user->name }}</div>
                                                        <div class="text-xs text-gray-500 dark:text-gray-400">{{ $user->email }}</div>
                                                    </div>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif
                        </div>
                        
                        @if ($selectedUser)
                            <div class="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-600 mr-3">
                                        @if ($selectedUser->getProfilePictureUrl() !== '/images/users/default.png')
                                            <img src="{{ $selectedUser->getProfilePictureUrl() }}" alt="{{ $selectedUser->name }}" class="h-full w-full object-cover">
                                        @else
                                            <div class="h-full w-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                        @endif
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-white">{{ $selectedUser->name }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ $selectedUser->email }}</div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            @if ($selectedUser->role)
                                                Role: {{ $selectedUser->role->name }}
                                            @endif
                                            @if ($selectedUser->department)
                                                | Department: {{ $selectedUser->department->name }}
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        
                        @error('userId') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                    </div>
                    
                    <!-- Template Selection -->
                    <div>
                        <label for="templateId" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Onboarding Template</label>
                        <select wire:model.live="templateId" id="templateId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Select a template</option>
                            @foreach ($templates as $template)
                                <option value="{{ $template->id }}">{{ $template->name }}</option>
                            @endforeach
                        </select>
                        @error('templateId') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Start Date -->
                    <div>
                        <label for="startDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Start Date</label>
                        <input type="date" wire:model.live="startDate" id="startDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        @error('startDate') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                    </div>
                    
                    <!-- Target Completion Date -->
                    <div>
                        <label for="targetCompletionDate" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Target Completion Date</label>
                        <input type="date" wire:model.live="targetCompletionDate" id="targetCompletionDate" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                        @error('targetCompletionDate') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                    </div>
                    
                    <!-- Assigned To -->
                    <div>
                        <label for="assignedTo" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Assigned To</label>
                        <select wire:model.live="assignedTo" id="assignedTo" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="">Not assigned</option>
                            @foreach ($hrStaff as $staff)
                                <option value="{{ $staff->id }}">{{ $staff->name }}</option>
                            @endforeach
                        </select>
                        @error('assignedTo') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                    </div>
                </div>
                
                <!-- Notes -->
                <div class="mb-6">
                    <label for="notes" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Notes</label>
                    <textarea wire:model.live="notes" id="notes" rows="4" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Add any additional notes about this onboarding process"></textarea>
                    @error('notes') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                </div>
                
                <div class="flex justify-end">
                    <button type="button" wire:click="$dispatch('to-hr-onboarding')" class="px-4 py-2 mr-2 text-sm font-medium text-gray-900 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700">Cancel</button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-700 rounded-lg hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">Start Onboarding</button>
                </div>
            </form>
        </div>
    </x-content-body>
</x-content>
