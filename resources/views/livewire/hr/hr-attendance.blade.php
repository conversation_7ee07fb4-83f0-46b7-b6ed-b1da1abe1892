<x-content>
    <x-content-header title="Attendance Management">
        <div class="flex space-x-2">
            <x-page-button type="add" label="Add Attendance Record" action="$dispatch('to-hr-attendance-create')" />
        </div>
    </x-content-header>

    <x-content-body>
        <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <h2 class="text-xl font-semibold mb-4">Attendance Management</h2>
            <p class="text-gray-500 dark:text-gray-400 mb-4">This feature is coming soon. It will allow you to track employee attendance, manage time-off requests, and generate attendance reports.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg shadow-sm dark:border-blue-700 dark:bg-blue-900">
                    <h3 class="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-2">Time Tracking</h3>
                    <p class="text-gray-600 dark:text-gray-300">Record clock-in and clock-out times for employees with support for breaks and overtime.</p>
                </div>
                
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg shadow-sm dark:border-green-700 dark:bg-green-900">
                    <h3 class="text-lg font-semibold text-green-700 dark:text-green-300 mb-2">Leave Management</h3>
                    <p class="text-gray-600 dark:text-gray-300">Manage vacation, sick leave, and other time-off requests with approval workflows.</p>
                </div>
                
                <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg shadow-sm dark:border-purple-700 dark:bg-purple-900">
                    <h3 class="text-lg font-semibold text-purple-700 dark:text-purple-300 mb-2">Shift Scheduling</h3>
                    <p class="text-gray-600 dark:text-gray-300">Create and manage employee work schedules with conflict detection and notifications.</p>
                </div>
                
                <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg shadow-sm dark:border-yellow-700 dark:bg-yellow-900">
                    <h3 class="text-lg font-semibold text-yellow-700 dark:text-yellow-300 mb-2">Reporting</h3>
                    <p class="text-gray-600 dark:text-gray-300">Generate detailed attendance reports and analytics for management review.</p>
                </div>
            </div>
            
            <div class="flex items-center p-4 mb-4 text-sm text-blue-800 border border-blue-300 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800" role="alert">
                <svg class="flex-shrink-0 inline w-4 h-4 mr-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                <span class="font-medium">Coming Soon!</span> We're currently developing this feature. Check back soon for updates.
            </div>
            
            <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">Employee</th>
                            <th scope="col" class="px-6 py-3">Date</th>
                            <th scope="col" class="px-6 py-3">Clock In</th>
                            <th scope="col" class="px-6 py-3">Clock Out</th>
                            <th scope="col" class="px-6 py-3">Total Hours</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                Sample Employee
                            </td>
                            <td class="px-6 py-4">May 6, 2023</td>
                            <td class="px-6 py-4">09:00 AM</td>
                            <td class="px-6 py-4">05:30 PM</td>
                            <td class="px-6 py-4">8.5</td>
                            <td class="px-6 py-4">
                                <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Present</span>
                            </td>
                            <td class="px-6 py-4">
                                <a href="#" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Edit</a>
                            </td>
                        </tr>
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                            <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                Sample Employee 2
                            </td>
                            <td class="px-6 py-4">May 6, 2023</td>
                            <td class="px-6 py-4">08:45 AM</td>
                            <td class="px-6 py-4">04:45 PM</td>
                            <td class="px-6 py-4">8.0</td>
                            <td class="px-6 py-4">
                                <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Present</span>
                            </td>
                            <td class="px-6 py-4">
                                <a href="#" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Edit</a>
                            </td>
                        </tr>
                        <tr class="bg-white dark:bg-gray-800">
                            <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                Sample Employee 3
                            </td>
                            <td class="px-6 py-4">May 6, 2023</td>
                            <td class="px-6 py-4">-</td>
                            <td class="px-6 py-4">-</td>
                            <td class="px-6 py-4">0.0</td>
                            <td class="px-6 py-4">
                                <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Absent</span>
                            </td>
                            <td class="px-6 py-4">
                                <a href="#" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Edit</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </x-content-body>
</x-content>
