<x-content>
    <x-content-header title="Human Resources Dashboard">
        <x-page-button type="add" label="Add Employee" action="$dispatch('to-hr-employees-create')" />
    </x-content-header>

    <x-content-body>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <!-- HR Dashboard Cards -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ $totalEmployees }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Total Employees</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900">
                        <svg class="w-6 h-6 text-blue-600 dark:text-blue-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <a href="#" wire:click.prevent="$dispatch('to-hr-employees')" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">View all employees</a>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ $activeContracts }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Active Contracts</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-green-100 dark:bg-green-900">
                        <svg class="w-6 h-6 text-green-600 dark:text-green-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.96 2.96 0 0 0 .13 5H5Z"/>
                            <path d="M6.737 11.061a2.961 2.961 0 0 1 .81-1.515l6.117-6.116A4.839 4.839 0 0 1 16 2.141V2a1.97 1.97 0 0 0-1.933-2H7v5a2 2 0 0 1-2 2H0v11a1.969 1.969 0 0 0 1.933 2h12.134A1.97 1.97 0 0 0 16 18v-3.093l-1.546 1.546c-.413.413-.94.695-1.513.81l-3.4.679a2.947 2.947 0 0 1-1.85-.227 2.96 2.96 0 0 1-1.635-3.257l.681-3.397Z"/>
                            <path d="M8.961 16a.93.93 0 0 0 .189-.019l3.4-.679a.961.961 0 0 0 .49-.263l6.118-6.117a2.884 2.884 0 0 0-4.079-4.078l-6.117 6.117a.96.96 0 0 0-.263.491l-.679 3.4A.961.961 0 0 0 8.961 16Z"/>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <a href="#" wire:click.prevent="$dispatch('to-hr-contracts')" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">View all contracts</a>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ $recentReviews }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Recent Reviews</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900">
                        <svg class="w-6 h-6 text-purple-600 dark:text-purple-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M5 11.424V1a1 1 0 0 0-2 0v10.424a3.228 3.228 0 0 0 0 6.152V19a1 1 0 1 0 2 0v-1.424a3.228 3.228 0 0 0 0-6.152ZM19.25 14.5A3.243 3.243 0 0 0 17 11.424V1a1 1 0 0 0-2 0v10.424a3.227 3.227 0 0 0 0 6.152V19a1 1 0 1 0 2 0v-1.424a3.243 3.243 0 0 0 2.25-3.076Zm-6-9A3.243 3.243 0 0 0 11 2.424V1a1 1 0 0 0-2 0v1.424a3.228 3.228 0 0 0 0 6.152V19a1 1 0 1 0 2 0V8.576A3.243 3.243 0 0 0 13.25 5.5Z"/>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <a href="#" wire:click.prevent="$dispatch('to-hr-performance')" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">View performance reviews</a>
                </div>
            </div>

            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="leading-none text-3xl font-bold text-gray-900 dark:text-white pb-2">{{ $totalDocuments }}</h5>
                        <p class="text-base font-normal text-gray-500 dark:text-gray-400">Documents</p>
                    </div>
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-orange-100 dark:bg-orange-900">
                        <svg class="w-6 h-6 text-orange-600 dark:text-orange-300" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 16 20">
                            <path d="M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM10.5 6a1.5 1.5 0 1 1 0 2.999A1.5 1.5 0 0 1 10.5 6Zm2.221 10.515a1 1 0 0 1-.858.485h-8a1 1 0 0 1-.9-1.43L5.6 10.039a.978.978 0 0 1 .936-.57 1 1 0 0 1 .9.632l1.181 2.981.541-1a.945.945 0 0 1 .883-.522 1 1 0 0 1 .879.529l1.832 3.438a1 1 0 0 1-.031.988Z"/>
                            <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z"/>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center mt-4">
                    <a href="#" wire:click.prevent="$dispatch('to-hr-documents')" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">View all documents</a>
                </div>
            </div>
        </div>

        <!-- Quick Access Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <a href="#" wire:click.prevent="$dispatch('to-hr-employees')" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div class="flex flex-col items-center">
                    <svg class="w-8 h-8 text-blue-600 dark:text-blue-300 mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                    </svg>
                    <h5 class="text-lg font-semibold text-gray-900 dark:text-white">Employee Management</h5>
                </div>
            </a>

            <a href="#" wire:click.prevent="$dispatch('to-hr-documents')" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div class="flex flex-col items-center">
                    <svg class="w-8 h-8 text-green-600 dark:text-green-300 mb-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                    <h5 class="text-lg font-semibold text-gray-900 dark:text-white">Document Management</h5>
                </div>
            </a>

            <a href="#" wire:click.prevent="$dispatch('to-hr-contracts')" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div class="flex flex-col items-center">
                    <svg class="w-8 h-8 text-yellow-600 dark:text-yellow-300 mb-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.96 2.96 0 0 0 .13 5H5Z"/>
                        <path d="M6.737 11.061a2.961 2.961 0 0 1 .81-1.515l6.117-6.116A4.839 4.839 0 0 1 16 2.141V2a1.97 1.97 0 0 0-1.933-2H7v5a2 2 0 0 1-2 2H0v11a1.969 1.969 0 0 0 1.933 2h12.134A1.97 1.97 0 0 0 16 18v-3.093l-1.546 1.546c-.413.413-.94.695-1.513.81l-3.4.679a2.947 2.947 0 0 1-1.85-.227 2.96 2.96 0 0 1-1.635-3.257l.681-3.397Z"/>
                        <path d="M8.961 16a.93.93 0 0 0 .189-.019l3.4-.679a.961.961 0 0 0 .49-.263l6.118-6.117a2.884 2.884 0 0 0-4.079-4.078l-6.117 6.117a.96.96 0 0 0-.263.491l-.679 3.4A.961.961 0 0 0 8.961 16Z"/>
                    </svg>
                    <h5 class="text-lg font-semibold text-gray-900 dark:text-white">Contract Management</h5>
                </div>
            </a>

            <a href="#" wire:click.prevent="$dispatch('to-hr-performance')" class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div class="flex flex-col items-center">
                    <svg class="w-8 h-8 text-purple-600 dark:text-purple-300 mb-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M5 11.424V1a1 1 0 0 0-2 0v10.424a3.228 3.228 0 0 0 0 6.152V19a1 1 0 1 0 2 0v-1.424a3.228 3.228 0 0 0 0-6.152ZM19.25 14.5A3.243 3.243 0 0 0 17 11.424V1a1 1 0 0 0-2 0v10.424a3.227 3.227 0 0 0 0 6.152V19a1 1 0 1 0 2 0v-1.424a3.243 3.243 0 0 0 2.25-3.076Zm-6-9A3.243 3.243 0 0 0 11 2.424V1a1 1 0 0 0-2 0v1.424a3.228 3.228 0 0 0 0 6.152V19a1 1 0 1 0 2 0V8.576A3.243 3.243 0 0 0 13.25 5.5Z"/>
                    </svg>
                    <h5 class="text-lg font-semibold text-gray-900 dark:text-white">Performance Reviews</h5>
                </div>
            </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <!-- Recent Employees -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Recent Employees</h5>
                    <a href="#" wire:click.prevent="$dispatch('to-hr-employees')" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">
                        View all
                    </a>
                </div>
                <div class="flow-root">
                    <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($recentEmployees as $user)
                        <li class="py-3 sm:py-4">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <img class="w-8 h-8 rounded-full" src="{{ $user->getProfilePictureUrl() }}" alt="{{ $user->first_name }} {{ $user->last_name }}">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                        {{ $user->first_name }} {{ $user->last_name }}
                                    </p>
                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                        {{ $user->email }}
                                    </p>
                                </div>
                                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                    {{ $user->created_at->diffForHumans() }}
                                </div>
                            </div>
                        </li>
                        @empty
                        <li class="py-3 sm:py-4">
                            <p class="text-sm text-gray-500 dark:text-gray-400">No recent employees found.</p>
                        </li>
                        @endforelse
                    </ul>
                </div>
            </div>

            <!-- Expiring Contracts -->
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex items-center justify-between mb-4">
                    <h5 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Expiring Contracts</h5>
                    <a href="#" wire:click.prevent="$dispatch('to-hr-contracts')" class="text-sm font-medium text-blue-600 hover:underline dark:text-blue-500">
                        View all
                    </a>
                </div>
                <div class="flow-root">
                    <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($expiringContracts as $contract)
                        <li class="py-3 sm:py-4">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <img class="w-8 h-8 rounded-full" src="{{ $contract->user->getProfilePictureUrl() }}" alt="{{ $contract->user->first_name }} {{ $contract->user->last_name }}">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                        {{ $contract->user->first_name }} {{ $contract->user->last_name }}
                                    </p>
                                    <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                        {{ ucfirst($contract->type) }} Contract
                                    </p>
                                </div>
                                <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-white">
                                    Expires: {{ $contract->end_date->format('M d, Y') }}
                                </div>
                            </div>
                        </li>
                        @empty
                        <li class="py-3 sm:py-4">
                            <p class="text-sm text-gray-500 dark:text-gray-400">No contracts expiring soon.</p>
                        </li>
                        @endforelse
                    </ul>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Department Distribution</h3>
                </div>
                <div id="department-chart" class="h-80"></div>
            </div>
            <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold leading-none text-gray-900 dark:text-white">Contract Types</h3>
                </div>
                <div id="contract-chart" class="h-80"></div>
            </div>
        </div>
    </x-content-body>

    @push('scripts')
    <script>
        document.addEventListener('livewire:initialized', function () {
            // Department Distribution Chart
            const departmentData = @json($departmentData);

            if (document.getElementById('department-chart')) {
                const departmentChart = new ApexCharts(document.getElementById('department-chart'), {
                    series: departmentData.values,
                    chart: {
                        type: 'donut',
                        height: 320,
                    },
                    labels: departmentData.labels,
                    colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#9061F9', '#E74694', '#34D399'],
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Inter, sans-serif',
                        fontSize: '14px',
                        fontWeight: 500,
                        labels: {
                            colors: window.theme === 'dark' ? '#FFFFFF' : '#111827',
                        },
                        itemMargin: {
                            horizontal: 10,
                            vertical: 5
                        },
                    },
                    dataLabels: {
                        enabled: false
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '65%',
                                background: 'transparent',
                            }
                        }
                    },
                    stroke: {
                        width: 2,
                        colors: window.theme === 'dark' ? ['#1F2937'] : ['#FFFFFF'],
                    },
                    tooltip: {
                        shared: true,
                        followCursor: false,
                        theme: window.theme === 'dark' ? 'dark' : 'light',
                    },
                });
                departmentChart.render();
            }

            // Contract Types Chart
            const contractData = @json($contractData);

            if (document.getElementById('contract-chart')) {
                const contractChart = new ApexCharts(document.getElementById('contract-chart'), {
                    series: contractData.values,
                    chart: {
                        type: 'donut',
                        height: 320,
                    },
                    labels: contractData.labels,
                    colors: ['#1A56DB', '#FDBA8C', '#16BDCA', '#9061F9', '#E74694'],
                    legend: {
                        position: 'bottom',
                        fontFamily: 'Inter, sans-serif',
                        fontSize: '14px',
                        fontWeight: 500,
                        labels: {
                            colors: window.theme === 'dark' ? '#FFFFFF' : '#111827',
                        },
                        itemMargin: {
                            horizontal: 10,
                            vertical: 5
                        },
                    },
                    dataLabels: {
                        enabled: false
                    },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '65%',
                                background: 'transparent',
                            }
                        }
                    },
                    stroke: {
                        width: 2,
                        colors: window.theme === 'dark' ? ['#1F2937'] : ['#FFFFFF'],
                    },
                    tooltip: {
                        shared: true,
                        followCursor: false,
                        theme: window.theme === 'dark' ? 'dark' : 'light',
                    },
                });
                contractChart.render();
            }
        });
    </script>
    @endpush
</x-content>
