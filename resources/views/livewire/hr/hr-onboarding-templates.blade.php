<x-content>
    <x-content-header title="Onboarding Templates">
        <div class="flex space-x-2">
            <x-page-button type="back" label="Back to Onboarding" action="$dispatch('to-hr-onboarding')" />
            <x-page-button type="add" label="Create Template" action="openTemplateModal()" />
        </div>
    </x-content-header>

    <x-content-body>
        <!-- Templates List -->
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <div class="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 mb-4">
                <div class="w-full md:w-1/2">
                    <form class="flex items-center">
                        <label for="simple-search" class="sr-only">Search</label>
                        <div class="relative w-full">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <input wire:model.live.debounce.300ms="search" type="text" id="simple-search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Search templates">
                        </div>
                    </form>
                </div>
                <div class="w-full md:w-auto flex flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
                    <div class="flex items-center space-x-3 w-full md:w-auto">
                        <select wire:model.live="perPage" id="per-page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                            <option value="10">10 per page</option>
                            <option value="20">20 per page</option>
                            <option value="50">50 per page</option>
                            <option value="100">100 per page</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Templates Table -->
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-4 py-3">Template Name</th>
                            <th scope="col" class="px-4 py-3">Role</th>
                            <th scope="col" class="px-4 py-3">Department</th>
                            <th scope="col" class="px-4 py-3">Tasks</th>
                            <th scope="col" class="px-4 py-3">Status</th>
                            <th scope="col" class="px-4 py-3">Created By</th>
                            <th scope="col" class="px-4 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($templates as $template)
                            <tr class="border-b dark:border-gray-700">
                                <td class="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                    {{ $template->name }}
                                </td>
                                <td class="px-4 py-3">
                                    {{ $template->role ? $template->role->name : 'Any Role' }}
                                </td>
                                <td class="px-4 py-3">
                                    {{ $template->department ? $template->department->name : 'Any Department' }}
                                </td>
                                <td class="px-4 py-3">
                                    {{ $template->tasks->count() }}
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        @if($template->is_active) bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                        @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 @endif">
                                        {{ $template->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    {{ $template->creator ? $template->creator->name : 'Unknown' }}
                                </td>
                                <td class="px-4 py-3 flex items-center justify-end space-x-2">
                                    <button wire:click="openTemplateModal({{ $template->id }})" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Edit</button>
                                    <button wire:click="cloneTemplate({{ $template->id }})" class="font-medium text-green-600 dark:text-green-500 hover:underline">Clone</button>
                                    <button wire:click="openTaskModal({{ $template->id }})" class="font-medium text-primary-600 dark:text-primary-500 hover:underline">Add Task</button>
                                </td>
                            </tr>

                            <!-- Tasks for this template -->
                            @if ($template->tasks->count() > 0)
                                <tr class="bg-gray-50 dark:bg-gray-800">
                                    <td colspan="7" class="px-4 py-2">
                                        <div class="pl-4 border-l-2 border-gray-300 dark:border-gray-600">
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Tasks</h4>
                                            <div class="space-y-2">
                                                @foreach ($template->tasks->sortBy('order') as $task)
                                                    <div class="flex items-center justify-between bg-white dark:bg-gray-700 p-2 rounded-lg border border-gray-200 dark:border-gray-600">
                                                        <div>
                                                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $task->name }}</span>
                                                            <div class="flex mt-1 space-x-2">
                                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                                    {{ $task->category_name }}
                                                                </span>
                                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                                                    Due: {{ $task->due_days }} days
                                                                </span>
                                                                @if ($task->responsible_role)
                                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">
                                                                        {{ ucfirst(str_replace('_', ' ', $task->responsible_role)) }}
                                                                    </span>
                                                                @endif
                                                            </div>
                                                        </div>
                                                        <div class="flex space-x-2">
                                                            <button wire:click="openTaskModal({{ $template->id }}, {{ $task->id }})" class="text-blue-600 dark:text-blue-500 hover:underline">
                                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                                                </svg>
                                                            </button>
                                                            <button wire:click="deleteTask({{ $task->id }})" class="text-red-600 dark:text-red-500 hover:underline">
                                                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @empty
                            <tr>
                                <td colspan="7" class="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
                                    No templates found. Create your first onboarding template to get started.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $templates->links() }}
            </div>
        </div>

        <!-- Template Modal -->
        <div x-data="{ open: @entangle('showTemplateModal') }" x-show="open" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full dark:bg-gray-800">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 dark:bg-gray-800">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                    {{ $templateId ? 'Edit Template' : 'Create Template' }}
                                </h3>
                                <div class="mt-4 space-y-4">
                                    <!-- Template Name -->
                                    <div>
                                        <label for="name" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Template Name</label>
                                        <input type="text" wire:model="name" id="name" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter template name">
                                        @error('name') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Template Description -->
                                    <div>
                                        <label for="description" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                                        <textarea wire:model="description" id="description" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter template description"></textarea>
                                        @error('description') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Role -->
                                    <div>
                                        <label for="roleId" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Role (Optional)</label>
                                        <select wire:model="roleId" id="roleId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                            <option value="">Any Role</option>
                                            @foreach ($roles as $role)
                                                <option value="{{ $role->id }}">{{ $role->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('roleId') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Department -->
                                    <div>
                                        <label for="departmentId" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Department (Optional)</label>
                                        <select wire:model="departmentId" id="departmentId" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                            <option value="">Any Department</option>
                                            @foreach ($departments as $department)
                                                <option value="{{ $department->id }}">{{ $department->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('departmentId') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Status -->
                                    <div class="flex items-center">
                                        <input wire:model="isActive" id="isActive" type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="isActive" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Active</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse dark:bg-gray-700">
                        <button type="button" wire:click="saveTemplate" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:bg-primary-700 dark:hover:bg-primary-800">
                            Save
                        </button>
                        <button type="button" wire:click="closeTemplateModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Task Modal -->
        <div x-data="{ open: @entangle('showTaskModal') }" x-show="open" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

                <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full dark:bg-gray-800">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 dark:bg-gray-800">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                                    {{ $taskId ? 'Edit Task' : 'Add Task' }}
                                </h3>
                                <div class="mt-4 space-y-4">
                                    <!-- Task Name -->
                                    <div>
                                        <label for="taskName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Task Name</label>
                                        <input type="text" wire:model="taskName" id="taskName" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter task name">
                                        @error('taskName') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Task Description -->
                                    <div>
                                        <label for="taskDescription" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Description</label>
                                        <textarea wire:model="taskDescription" id="taskDescription" rows="3" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Enter task description"></textarea>
                                        @error('taskDescription') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Category -->
                                    <div>
                                        <label for="taskCategory" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Category</label>
                                        <select wire:model="taskCategory" id="taskCategory" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                            @foreach ($categories as $key => $category)
                                                <option value="{{ $key }}">{{ $category }}</option>
                                            @endforeach
                                        </select>
                                        @error('taskCategory') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Due Days -->
                                    <div>
                                        <label for="taskDueDays" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Due Days</label>
                                        <input type="number" wire:model="taskDueDays" id="taskDueDays" min="1" max="90" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Days from start date">
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Number of days from onboarding start date when this task is due</p>
                                        @error('taskDueDays') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Responsible Role -->
                                    <div>
                                        <label for="taskResponsibleRole" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Responsible Role</label>
                                        <select wire:model="taskResponsibleRole" id="taskResponsibleRole" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
                                            <option value="">Not Assigned</option>
                                            @foreach ($responsibleRoles as $key => $role)
                                                <option value="{{ $key }}">{{ $role }}</option>
                                            @endforeach
                                        </select>
                                        @error('taskResponsibleRole') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Order -->
                                    <div>
                                        <label for="taskOrder" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Order</label>
                                        <input type="number" wire:model="taskOrder" id="taskOrder" min="0" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="Task order">
                                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Order in which tasks are displayed</p>
                                        @error('taskOrder') <span class="text-sm text-red-600 dark:text-red-500">{{ $message }}</span> @enderror
                                    </div>

                                    <!-- Required -->
                                    <div class="flex items-center">
                                        <input wire:model="taskIsRequired" id="taskIsRequired" type="checkbox" class="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                        <label for="taskIsRequired" class="ml-2 text-sm font-medium text-gray-900 dark:text-white">Required</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse dark:bg-gray-700">
                        <button type="button" wire:click="saveTask" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm dark:bg-primary-700 dark:hover:bg-primary-800">
                            Save
                        </button>
                        <button type="button" wire:click="closeTaskModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </x-content-body>
</x-content>
