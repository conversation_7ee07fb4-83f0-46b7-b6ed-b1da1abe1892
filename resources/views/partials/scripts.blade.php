
<!-- Load statistics charts script for statistics pages -->
@if(request()->is('statistics*'))
    <script src="{{ asset('js/statistics-charts.js') }}"></script>
@endif

<!-- Conditionally load chart scripts only on dashboard -->
@if(request()->routeIs('dashboard'))
    <!-- Set global flag to prevent individual chart initializations -->
    <script>
        // This flag must be set before any other scripts run
        window.usingGlobalDashboardCharts = true;
        console.log('Global dashboard charts system activated');
    </script>

    <script>
        // Set a global flag to prevent duplicate chart initializations
        (function() {
            // Use window property to avoid redeclaration issues
            window.dashboardChartsInitialized = window.dashboardChartsInitialized || false;
        })();

        // Function to clean up any existing charts
        function cleanupExistingCharts() {
            console.log('Cleaning up any existing charts...');

            // Remove all ApexCharts canvases
            document.querySelectorAll('.apexcharts-canvas').forEach(element => {
                console.log('Removing existing ApexCharts canvas');
                element.remove();
            });

            // Reset chart initialization flags on containers
            document.querySelectorAll('.chart-initialized').forEach(element => {
                element.classList.remove('chart-initialized');
            });

            console.log('Chart cleanup complete');
        }

        // Clean up any existing charts immediately
        cleanupExistingCharts();

        // Initialize charts when the page is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard page loaded, preparing charts...');

            // Clean up any charts that might have been created
            cleanupExistingCharts();

            // Check if ApexCharts is loaded
            if (typeof ApexCharts !== 'undefined') {
                console.log('ApexCharts is loaded and ready');

                // Ensure charts are initialized only once
                if (!window.dashboardChartsInitialized && typeof window.initializeDashboardCharts === 'function') {
                    console.log('Scheduling dashboard charts initialization');

                    // Set the flag to prevent duplicate initializations
                    window.dashboardChartsInitialized = true;

                    // Initialize charts with a delay to ensure DOM is ready
                    setTimeout(function() {
                        if (typeof window.initializeDashboardCharts === 'function') {
                            window.initializeDashboardCharts();
                        }
                    }, 300);
                }
            } else {
                console.error('ApexCharts is not loaded! Attempting to load it now...');

                // Dynamically load ApexCharts if it's not available
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.js';
                script.onload = function() {
                    console.log('ApexCharts loaded successfully');

                    // Initialize charts after ApexCharts is loaded
                    if (!window.dashboardChartsInitialized && typeof window.initializeDashboardCharts === 'function') {
                        console.log('Initializing charts after dynamic ApexCharts load');
                        window.dashboardChartsInitialized = true;
                        window.initializeDashboardCharts();
                    }
                };
                document.head.appendChild(script);
            }
        });

        // Handle page visibility changes to ensure charts are visible when tab becomes active
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible' && window.dashboardChartsInitialized) {
                console.log('Page became visible, checking if charts need reinitialization');

                // Check if charts are visible
                const chartElements = document.querySelectorAll('.apexcharts-canvas');
                if (chartElements.length === 0 && typeof window.reinitializeDashboardCharts === 'function') {
                    console.log('No charts found, reinitializing...');
                    window.reinitializeDashboardCharts();
                }
            }
        });

        // Add a function to detect and remove duplicate charts
        function removeDuplicateCharts() {
            const chartContainers = document.querySelectorAll('[id]');
            let duplicatesFound = false;

            chartContainers.forEach(container => {
                const canvases = container.querySelectorAll('.apexcharts-canvas');
                if (canvases.length > 1) {
                    console.log(`Found ${canvases.length} charts in container ${container.id}, removing duplicates`);
                    duplicatesFound = true;

                    // Keep only the first canvas
                    for (let i = 1; i < canvases.length; i++) {
                        canvases[i].remove();
                    }
                }
            });

            return duplicatesFound;
        }

        // Check for duplicates periodically
        setInterval(function() {
            if (removeDuplicateCharts()) {
                console.log('Duplicate charts removed');
            }
        }, 1000);
    </script>
@endif

<script>
    // Log Livewire navigation events
    document.addEventListener('livewire:navigate', (event) => {
        console.log('Livewire is navigating to:', event.currentTarget);
        console.log('Livewire is navigating from:', event.target);
    });
</script>

<script>
    // Add CSRF token to all AJAX requests
    document.addEventListener('DOMContentLoaded', function() {
        // Get the CSRF token from the meta tag
        const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        // Add the CSRF token to all AJAX requests
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (!options.headers) {
                options.headers = {};
            }

            // Convert headers to a regular object if it's a Headers instance
            if (options.headers instanceof Headers) {
                const originalHeaders = options.headers;
                options.headers = {};
                for (const [key, value] of originalHeaders.entries()) {
                    options.headers[key] = value;
                }
            }

            // Add CSRF token if not already present
            if (!options.headers['X-CSRF-TOKEN']) {
                options.headers['X-CSRF-TOKEN'] = token;
            }

            return originalFetch(url, options);
        };

        // Handle session expiration
        document.addEventListener('livewire:navigating', () => {
            // Refresh the CSRF token on navigation
            (function() {
                fetch('/csrf-token')
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.token) {
                            const csrfMeta = document.querySelector('meta[name="csrf-token"]');
                            if (csrfMeta) {
                                csrfMeta.setAttribute('content', data.token);
                                console.log('CSRF token refreshed during navigation');
                            }
                        }
                    })
                    .catch(error => console.error('Error refreshing CSRF token:', error));
            })();
        });

        // Handle page expiration errors
        window.addEventListener('error', function(event) {
            if (event.detail &&
                (event.detail.message && event.detail.message.includes('page has expired') ||
                 event.detail.message && event.detail.message.includes('CSRF token mismatch'))) {
                // Dispatch the pageExpired event to the current Livewire component
                if (window.Livewire) {
                    window.Livewire.dispatch('pageExpired');
                }

                // Prevent the default error handling
                event.preventDefault();
            }
        });

        // Handle AJAX errors
        document.addEventListener('ajax:error', function(event) {
            if (event.detail && event.detail.status === 419) {
                // Page expired, refresh the CSRF token
                (function() {
                    fetch('/csrf-token')
                        .then(response => response.json())
                        .then(data => {
                            if (data && data.token) {
                                const csrfMeta = document.querySelector('meta[name="csrf-token"]');
                                if (csrfMeta) {
                                    csrfMeta.setAttribute('content', data.token);
                                    console.log('CSRF token refreshed after AJAX error');
                                }

                                // Dispatch the pageExpired event to the current Livewire component
                                if (window.Livewire) {
                                    window.Livewire.dispatch('pageExpired');
                                }
                            }
                        })
                        .catch(error => console.error('Error refreshing CSRF token:', error));
                })();
            }
        });
    });
</script>
