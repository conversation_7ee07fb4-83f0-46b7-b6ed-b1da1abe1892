<!-- Add this right before the closing </body> tag -->
@if(request()->routeIs('dashboard'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if ApexCharts is loaded
        if (typeof ApexCharts === 'undefined') {
            console.error('ApexCharts is not loaded. Loading it now...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/apexcharts';
            script.onload = function() {
                console.log('ApexCharts loaded successfully');
                // Trigger chart initialization
                document.dispatchEvent(new Event('apexcharts-loaded'));
            };
            document.head.appendChild(script);
        } else {
            console.log('ApexCharts is already loaded');
        }
    });
</script>
@endif