<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Global\Page;
use App\Livewire\Documents\DocumentPage;
use App\Http\Controllers\DocumentViewController;


// Document Management
Route::prefix('documents')->middleware(['auth', 'permission:access_document_management_module'])->name('documents.')->group(function () {
    // Document Dashboard
    Route::get('/', DocumentPage::class)
        ->name('dashboard')
        ->defaults('component', 'documents.document-dashboard')
        ->middleware('permission:show_document_dashboard');

    // Document Verification
    Route::get('/verification', DocumentPage::class)
        ->name('verification')
        ->defaults('component', 'documents.document-verification')
        ->middleware('permission:verify_document');

    // Document Expiration
    Route::get('/expiration', DocumentPage::class)
        ->name('expiration')
        ->defaults('component', 'documents.document-expiration')
        ->middleware('permission:check_document_expiration');

    // Document viewing - must be last to avoid conflicts with other routes
    Route::get('/{id}', [DocumentViewController::class, 'show'])
        ->name('view')
        ->middleware('permission:show_document');
});

// Alias for document.view route (for backward compatibility)
Route::get('/document/{id}', function($id) {
    return redirect()->route('documents.view', $id);
})->middleware(['auth'])->name('document.view');

