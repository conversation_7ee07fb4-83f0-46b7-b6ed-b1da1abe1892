<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Global\Page;

// CSRF token refresh route - no auth required
Route::get('/csrf-token', [\App\Http\Controllers\CsrfController::class, 'refreshToken'])->name('csrf.token');

Route::middleware(['auth'])->group(function () {
    // Permission examples page (only for development)
    if (app()->environment('local')) {
        Route::get('/permission-examples', function () {
            return view('examples.permissions');
        })->name('permission.examples');
    }
    
    // Dashboard
    Route::get('dashboard', action: Page::class)
        ->name('dashboard')
        ->middleware(['auth', 'permission:access_dashboard_module'])
        ->defaults('component', 'global.dashboard');
    Route::get('/', Page::class)
        ->name('home')
        ->middleware(['auth', 'permission:access_dashboard_module'])
        ->defaults('component', 'global.dashboard');
});

// Include extra routes
require __DIR__ . '/auth.php';
require __DIR__ . '/user.php';
require __DIR__ . '/agent.php';
require __DIR__ . '/campaign.php';
require __DIR__ . '/training.php';
require __DIR__ . '/appointment.php';
require __DIR__ . '/report.php';
require __DIR__ . '/statistics.php';
require __DIR__ . '/accountant.php';
require __DIR__ . '/hr.php';
require __DIR__ . '/site.php';
require __DIR__ . '/call-center.php';
require __DIR__ . '/document.php';
require __DIR__ . '/skill-certification.php';
require __DIR__ . '/call-quality.php';
require __DIR__ . '/setting.php';
