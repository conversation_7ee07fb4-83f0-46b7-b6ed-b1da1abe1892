<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Reports\ReportPage;
use App\Livewire\Reports\DayReports;

// Reports
Route::prefix('reports')->group(function () {
    Route::get('/', ReportPage::class)
        ->name('reports.index')
        ->middleware('auth')
        ->defaults('component', 'reports.report-index');
    Route::get('/create', ReportPage::class)
        ->name('reports.create')
        ->middleware('auth')
        ->defaults('component', 'reports.report-create');
    Route::get('/{report}/edit', ReportPage::class)
        ->name('reports.edit')
        ->middleware('auth')
        ->defaults('component', 'reports.report-edit');
    Route::get('/{report}/delete', ReportPage::class)
        ->name('reports.delete')
        ->middleware('auth')
        ->defaults('component', 'reports.report-delete')
        ->defaults('report', '{$report}');
    Route::get('/{report}/show', ReportPage::class)
        ->name('reports.show')
        ->middleware('auth')
        ->defaults('component', 'reports.report-show');
    Route::get('/{report}/edit', ReportPage::class)
        ->name('reports.edit')
        ->middleware('auth')
        ->defaults('component', 'reports.report-edit');

    Route::get('/day', DayReports::class)->name('reports.day');
});
