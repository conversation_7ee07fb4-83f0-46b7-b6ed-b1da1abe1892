<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Appointments\AppointmentPage;

// Appointments
Route::prefix('appointments')->middleware(['auth', 'permission:access_appointment_module'])->group(function () {
    Route::get('/', AppointmentPage::class)
        ->name('appointments.index')
        ->middleware('permission:manage_appointment')
        ->defaults('component', 'appointments.appointment-index');
    Route::get('/create', AppointmentPage::class)
        ->name('appointments.create')
        ->middleware('permission:create_appointment')
        ->defaults('component', 'appointments.appointment-create');
    Route::get('/{appointment}/edit', AppointmentPage::class)
        ->name('appointments.edit')
        ->middleware('permission:edit_appointment')
        ->defaults('component', 'appointments.appointment-edit');
    Route::get('/{appointment}/delete', AppointmentPage::class)
        ->name('appointments.delete')
        ->middleware('permission:delete_appointment')
        ->defaults('component', 'appointments.appointment-delete')
        ->defaults('appointment', '{$appointment}');
    Route::get('/{appointment}/show', AppointmentPage::class)
        ->name('appointments.show')
        ->middleware('permission:show_appointment')
        ->defaults('component', 'appointments.appointment-show');

    Route::get('/validate', AppointmentPage::class)
        ->name('appointments.validate')
        ->middleware('permission:validate_appointment')
        ->defaults('component', 'appointments.appointment-validate');
    Route::get('/review/{appointment}', AppointmentPage::class)
        ->name('appointments.review')
        ->middleware('permission:review_appointment')
        ->defaults('component', 'appointments.appointment-review');
    Route::get('/notify/{appointment}', AppointmentPage::class)
        ->name('appointments.notify')
        ->middleware('permission:notify_appointment')
        ->defaults('component', 'appointments.appointment-notify');
    Route::get('/notify-bulk', AppointmentPage::class)
        ->name('appointments.notify-bulk')
        ->middleware('permission:notify_bulk_appointment')
        ->defaults('component', 'appointments.appointment-bulk-notify');
    Route::get('/calendar', AppointmentPage::class)
        ->name('appointments.calendar')
        ->middleware('permission:calendar_appointment')
        ->defaults('component', 'appointments.appointment-calendar');
    Route::get('/followups', AppointmentPage::class)
        ->name('appointments.followups')
        ->middleware('permission:followups_appointments')
        ->defaults('component', 'appointments.appointment-followups');

    Route::get('/statistics', AppointmentPage::class)
        ->name('appointments.statistics')
        ->middleware('permission:show_appointment_statistics')
        ->defaults('component', 'appointments.appointment-statistics');


    // Redirect customer-interactions to validate (merged functionality)
    Route::get('/customer-interactions', function() {
        return redirect()->route('appointments.validate');
    })->name('appointments.customer-interactions')
    ->middleware('permission:manage_appointments');
});
