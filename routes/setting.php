<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Settings\SettingsPage;

Route::prefix('settings')->middleware(['auth', 'permission:access_setting_module'])->group(function () {
    // General Settings
    Route::get('/general', SettingsPage::class)
        ->name('settings.general.index')
        ->middleware('permission:manage_general_settings')
        ->defaults('component', 'settings.general');
    Route::get('/calls', SettingsPage::class)
        ->name('settings.calls')
        ->middleware('permission:manage_call_settings')
        ->defaults('component', 'settings.calls');
    Route::get('/campaigns', SettingsPage::class)
        ->name('settings.campaigns')
        ->middleware('permission:manage_campaign_settings')
        ->defaults('component', 'settings.campaigns');
    Route::get('/notifications', SettingsPage::class)
        ->name('settings.notifications')
        ->middleware('permission:manage_notification_settings')
        ->defaults('component', 'settings.notifications');
    Route::get('/integrations', SettingsPage::class)
        ->name('settings.integrations')
        ->middleware('permission:manage_integrations')
        ->defaults('component', 'settings.integrations');

    // Permissions Management
    Route::get('/permissions/roles', SettingsPage::class)
        ->name('settings.permissions.roles')
        ->middleware('permission:manage_roles')
        ->defaults('component', 'settings.role-permissions');
    Route::get('/permissions/users', SettingsPage::class)
        ->name('settings.permissions.users')
        ->middleware('permission:manage_user_permissions')
        ->defaults('component', 'settings.user-permissions');

    // Department Management
    Route::get('/departments', SettingsPage::class)
        ->name('settings.departments')
        ->middleware('permission:manage_departments')
        ->defaults('component', 'settings.department-management');
    Route::get('/departments/create', SettingsPage::class)
        ->name('settings.departments.create')
        ->middleware('permission:create_departments')
        ->defaults('component', 'settings.department-create');
    Route::get('/departments/{department}/edit', SettingsPage::class)
        ->name('settings.departments.edit')
        ->middleware('permission:edit_departments')
        ->defaults('component', 'settings.department-edit');
    Route::get('/departments/{department}/delete', SettingsPage::class)
        ->name('settings.departments.delete')
        ->middleware('permission:delete_departments')
        ->defaults('component', 'settings.department-delete');
});
