<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Agents\AgentPage;

// Agents
Route::prefix('agents')->middleware(['auth', 'permission:access_agent_module'])->group(function () {
    Route::get('/', AgentPage::class)
        ->middleware('permission:manage_agent')
        ->name('agents.index')
        ->defaults('component', 'agents.agent-index');
    Route::get('/create', AgentPage::class)
        ->name('agents.create')
        ->middleware('permission:create_agent')
        ->defaults('component', 'agents.agent-create');
    Route::get('/{agent}/edit', AgentPage::class)
        ->name('agents.edit')
        ->middleware('permission:edit_agent')
        ->defaults('component', 'agents.agent-edit');
    Route::get('/{agent}/delete', AgentPage::class)
        ->name('agents.delete')
        ->middleware('permission:delete_agent')
        ->defaults('component', 'agents.agent-delete')
        ->defaults('agent', '{$agent}');
    Route::get('/{agent}/show', AgentPage::class)
        ->name('agents.show')
        ->middleware('permission:show_agent')
        ->defaults('component', 'agents.agent-show');

    Route::get('/presence', AgentPage::class)
        ->name('agents.presence.index')
        ->middleware('permission:manage_agent_presence')
        ->defaults('component', 'agents.agent-presence');
    Route::get('/presence/create', AgentPage::class)
        ->name('agents.presence.create')
        ->middleware('permission:create_agent_presence')
        ->defaults('component', 'agents.agent-presence-create');
    Route::get('/presence/{shift}/edit', AgentPage::class)
        ->name('agents.presence.edit')
        ->middleware('permission:create_agent_presence')
        ->defaults('component', 'agents.agent-presence-edit');
    Route::get('/presence/{shift}/delete', AgentPage::class)
        ->name('agents.presence.delete')
        ->middleware('permission:delete_agent_presence')
        ->defaults('component', 'agents.agent-presence-delete');
    Route::get('/presence/{shift}/show', AgentPage::class)
        ->name('agents.presence.show')
        ->middleware('permission:show_agent_presence')
        ->defaults('component', 'agents.agent-presence-show');

    Route::get('/performance', AgentPage::class)
        ->name('agents.performance')
        ->middleware('permission:show_agent_performance')
        ->defaults('component', 'agents.agent-performance');

    Route::get('/enhanced-presence', AgentPage::class)
        ->name('agents.enhanced-presence')
        ->middleware('permission:manage_agent_presence')
        ->defaults('component', 'agents.enhanced-agent-presence');

    // Consolidated Agent Module
    Route::get('/optimized', AgentPage::class)
        ->name('agents.optimized')
        ->defaults('component', 'agents.agent-index');

    // Consolidated Agent Module
    Route::get('/dynamic', AgentPage::class)
        ->name('agents.dynamic')
        ->defaults('component', 'agents.agent-index');

});