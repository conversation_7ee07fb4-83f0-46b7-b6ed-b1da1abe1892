<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Statistics\StatisticPage;

// Statistics
Route::prefix('statistics')->middleware(['auth', 'permission:access_statistic_module'])->group(function () {
    Route::get('/', function() {
        return redirect()->route('statistics.general');
    })->name('statistics.index');

    Route::get('/day', StatisticPage::class)
        ->name('statistics.day')
        ->middleware('permission:show_daily_statistics')
        ->defaults('component', 'statistics.day-statistics');

    Route::get('/general', StatisticPage::class)
        ->name('statistics.general')
        ->middleware('permission:show_general_statistics')
        ->defaults('component', 'statistics.general-statistics');

    Route::get('/kpi', StatisticPage::class)
        ->name('statistics.kpi')
        ->middleware('permission:show_kpi_statistics')
        ->defaults('component', 'statistics.kpi-charts');

    Route::get('/performance', StatisticPage::class)
        ->name('statistics.performance')
        ->middleware('permission:show_performance_statistics')
        ->defaults('component', 'statistics.performance-monitoring');

    Route::get('/agent', StatisticPage::class)
        ->name('statistics.agent')
        ->middleware('permission:show_agent_statistics')
        ->defaults('component', 'statistics.agent-statistics');

    Route::get('/campaign', StatisticPage::class)
        ->name('statistics.campaign')
        ->middleware('permission:show_campaign_statistics')
        ->defaults('component', 'statistics.campaign-statistics');
});