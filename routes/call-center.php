<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\CallCenters\CallCenterPage;

// Call Center routes
Route::middleware(['auth', 'permission:access_call_center_module'])->prefix('call-centers')->name('call-centers.')->group(function () {
    // Main Call Center routes
    Route::get('/', CallCenterPage::class)
        ->defaults('component', 'call-centers.dashboard')
        ->name('index')
        ->middleware('permission:manage_call_center');
    Route::get('/list', CallCenterPage::class)
        ->defaults('component', 'call-centers.call-center-index')
        ->name('list')
        ->middleware('permission:manage_call_center');
    Route::get('/create', CallCenterPage::class)
        ->defaults('component', 'call-centers.call-center-create')
        ->name('create')
        ->middleware('permission:create_call_center');
    Route::get('/{callCenter}/edit', CallCenterPage::class)
        ->defaults('component', 'call-centers.call-center-edit')
        ->name('edit')
        ->middleware('permission:edit_call_center')
        ->whereNumber('callCenter');
    Route::get('/{callCenter}/delete', CallCenterPage::class)
        ->defaults('component', 'call-centers.call-center-delete')
        ->name('delete')
        ->middleware('permission:delete_call_center')
        ->whereNumber('callCenter');
    Route::get('/{callCenter}', CallCenterPage::class)
        ->defaults('component', 'call-centers.call-center-show')
        ->name('show')
        ->middleware('permission:show_call_center')
        ->whereNumber('callCenter');

    // Sites Submodule
    Route::prefix('/{callCenter}/sites')->name('sites.')->group(function () {
        Route::get('/', CallCenterPage::class)
            ->defaults('component', 'call-centers.sites.site-index')
            ->name('index')
            ->whereNumber('callCenter');
        Route::get('/create', CallCenterPage::class)
            ->defaults('component', 'call-centers.sites.site-create')
            ->name('create')
            ->whereNumber('callCenter');
        Route::get('/{site}/edit', CallCenterPage::class)
            ->defaults('component', 'call-centers.sites.site-edit')
            ->name('edit')
            ->whereNumber('callCenter')
            ->whereNumber('site');
        Route::get('/{site}/delete', CallCenterPage::class)
            ->defaults('component', 'call-centers.sites.site-delete')
            ->name('delete')
            ->whereNumber('callCenter')
            ->whereNumber('site');
        Route::get('/{site}', CallCenterPage::class)
            ->defaults('component', 'call-centers.sites.site-show')
            ->name('show')
            ->whereNumber('callCenter')
            ->whereNumber('site');

        // Platform Submodule
        Route::prefix('/{site}/platforms')->name('platforms.')->group(function () {
            Route::get('/', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-platforms')
                ->name('index');
            Route::get('/create', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-platform-create')
                ->name('create');
            Route::get('/{platform}/edit', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-platform-edit')
                ->name('edit');
            Route::get('/{platform}/delete', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-platform-delete')
                ->name('delete');
            Route::get('/{platform}', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-platform-show')
                ->name('show');
        });

        // Personnel Submodule
        Route::prefix('/{site}/personnels')->name('personnels.')->group(function () {
            Route::get('/', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-personnels')
                ->name('index');
            Route::get('/assign', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-personnels-assign')
                ->name('assign');
        });

        // Equipment Submodule
        Route::prefix('/{site}/equipments')->name('equipments.')->group(function () {
            Route::get('/', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-equipments')
                ->name('index');
            Route::get('/create', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-equipment-create')
                ->name('create');
            Route::get('/{equipment}/edit', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-equipment-edit')
                ->name('edit');
            Route::get('/{equipment}/delete', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-equipment-delete')
                ->name('delete');
            Route::get('/{equipment}', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-equipment-show')
                ->name('show');
        });

        // Reports Submodule
        Route::prefix('/{site}/reports')->name('reports.')->group(function () {
            Route::get('/', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-reports')
                ->name('index');
            Route::get('/create', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-report-create')
                ->name('create');
            Route::get('/{report}/edit', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-report-edit')
                ->name('edit');
            Route::get('/{report}/delete', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-report-delete')
                ->name('delete');
            Route::get('/{report}', CallCenterPage::class)
                ->defaults('component', 'call-centers.sites.site-report-show')
                ->name('show');
        });
    });

    // Departments Submodule
    Route::prefix('/{callCenter}/departments')->name('departments.')->group(function () {
        Route::get('/', CallCenterPage::class)
            ->defaults('component', 'call-centers.departments.department-index')
            ->name('index')
            ->whereNumber('callCenter');
        Route::get('/create', CallCenterPage::class)
            ->defaults('component', 'call-centers.departments.department-create')
            ->name('create')
            ->whereNumber('callCenter');
        Route::get('/{department}/edit', CallCenterPage::class)
            ->defaults('component', 'call-centers.departments.department-edit')
            ->name('edit')
            ->whereNumber('callCenter')
            ->whereNumber('department');
        Route::get('/{department}/delete', CallCenterPage::class)
            ->defaults('component', 'call-centers.departments.department-delete')
            ->name('delete')
            ->whereNumber('callCenter')
            ->whereNumber('department');
        Route::get('/{department}', CallCenterPage::class)
            ->defaults('component', 'call-centers.departments.department-show')
            ->name('show')
            ->whereNumber('callCenter')
            ->whereNumber('department');
    });
});