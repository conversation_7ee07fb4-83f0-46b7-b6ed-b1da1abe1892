<?php

use App\Livewire\Users\UserPage;
use Illuminate\Support\Facades\Route;


// Users
Route::prefix('users')->middleware(['auth', 'permission:access_user_module'])->group(function () {
    Route::get('/', UserPage::class)
        ->name('users.index')
        ->middleware(['permission:manage_user'])
        ->defaults('component', 'users.user-index');
    Route::get('/create', UserPage::class)
        ->name('users.create')
        ->middleware(['permission:create_user'])
        ->defaults('component', 'users.user-create');
    Route::get('/{user}/edit', UserPage::class)
        ->name('users.edit')
        ->middleware(['permission:edit_user'])
        ->defaults('component', 'users.user-edit');
    Route::get('/{user}/delete', UserPage::class)
        ->name('users.delete')
        ->middleware(['permission:delete_user'])
        ->defaults('component', 'users.user-delete')
        ->defaults('user', '{$user}');
    Route::get('/{user}/show', UserPage::class)
        ->name('users.show')
        ->middleware(['permission:show_user'])
        ->defaults('component', 'users.enhanced-user-show');

    // User Hierarchy and Team Management
    Route::get('/hierarchy', UserPage::class)
        ->name('users.hierarchy.index')
        ->middleware(['permission:manage_user_hierarchy'])
        ->defaults('component', 'users.user-hierarchy');
    // User Hierarchy Edit
    Route::get('/hierarchy/edit/{userId}', UserPage::class)
        ->name('users.hierarchy.edit')
        ->middleware(['permission:edit_user_hierarchy'])
        ->defaults('component', 'users.user-hierarchy-edit');

    // Team Member Management
    Route::get('/team/{teamId}/members', UserPage::class)
        ->name('users.team.members')
        ->middleware(['permission:manage_team_members'])
        ->defaults('component', 'users.team-member-management');

    // Organization Chart
    Route::get('/org-chart', UserPage::class)
        ->name('users.org-chart')
        ->middleware(['permission:show_user_org_chart'])
        ->defaults('component', 'users.org-chart');

    // Role and Permission Management Submodule
    Route::prefix('/roles-permissions')->group(function () {
        // Role Management
        Route::get('/roles', UserPage::class)
            ->name('users.roles.index')
            ->middleware('permission:manage_user_roles')
            ->defaults('component', 'users.user-role-index');
        Route::get('/roles/create', UserPage::class)
            ->name('users.roles.create')
            ->middleware('permission:create_user_roles')
            ->defaults('component', 'users.user-role-create');
        Route::get('/roles/{role}', UserPage::class)
            ->name('users.roles.show')
            ->middleware('permission:show_user_roles')
            ->defaults('component', 'users.user-role-show');
        Route::get('/roles/{role}/edit', UserPage::class)
            ->name('users.roles.edit')
            ->middleware('permission:edit_user_roles')
            ->defaults('component', 'users.user-role-edit');
        Route::get('/roles/{role}/delete', UserPage::class)
            ->name('users.roles.delete')
            ->middleware('permission:delete_user_roles')
            ->defaults('component', 'users.user-role-delete');
            
        // Permission Management
        Route::get('/permissions', UserPage::class)
            ->name('users.permissions.index')
            ->middleware('permission:manage_user_permissions')
            ->defaults('component', 'users.user-permission-index');
        Route::get('/permissions/create', UserPage::class)
            ->name('users.permissions.create')
            ->middleware('permission:create_user_permissions')
            ->defaults('component', 'users.user-permission-create');
        Route::get('/permissions/{permission}', UserPage::class)
            ->name('users.permissions.show')
            ->middleware('permission:show_user_permissions')
            ->defaults('component', 'users.user-permission-show');
        Route::get('/permissions/{permission}/edit', UserPage::class)
            ->name('users.permissions.edit')
            ->middleware('permission:edit_user_permisssions')
            ->defaults('component', 'users.user-permission-edit');
        Route::get('/permissions/{permission}/delete', UserPage::class)
            ->name('users.permissions.delete')
            ->middleware('permission:delete_user_permissions')
            ->defaults('component', 'users.user-permission-delete');
    });
});

