<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Campaigns\CampaignPage;

// Campaign
Route::prefix('campaigns')->middleware(['auth', 'permission:access_campaign_module'])->group(function () {
    Route::get('/', CampaignPage::class)
        ->name('campaigns.index')
        ->middleware('permission:manage_campaign')
        ->defaults('component', 'campaigns.campaign-index');
    Route::get('/create', CampaignPage::class)
        ->name('campaigns.create')
        ->middleware('permission:create_campaign')
        ->defaults('component', 'campaigns.campaign-create');
    Route::get('/{campaign}/edit', CampaignPage::class)
        ->name('campaigns.edit')
        ->middleware('permission:edit_campaign')
        ->defaults('component', 'campaigns.campaign-edit');
    Route::get('/{campaign}/delete', CampaignPage::class)
        ->name('campaigns.delete')
        ->middleware('permission:delete_campaign')
        ->defaults('component', 'campaigns.campaign-delete')
        ->defaults('campaign', '{$campaign}');
    Route::get('/{campaign}/show', CampaignPage::class)
        ->name('campaigns.show')
        ->middleware('permission:show_campaign')
        ->defaults('component', 'campaigns.campaign-show');

    // Customer Management Routes
    Route::prefix('customers')->group(function () {
        Route::get('/', CampaignPage::class)
            ->name('campaigns.customers.index')
            ->middleware('permission:manage_campaign_customers')
            ->defaults('component', 'campaigns.campaign-customers');
        Route::get('/create', CampaignPage::class)
            ->name('campaigns.customers.create')
            ->middleware('permission:create_campaign_customers')
            ->defaults('component', 'campaigns.campaign-customer-create');
        Route::get('/{customer}/edit', CampaignPage::class)
            ->name('campaigns.customers.edit')
            ->middleware('permission:edit_campaign_customers')
            ->defaults('component', 'campaigns.campaign-customer-edit');
        Route::get('/{customer}/delete', CampaignPage::class)
            ->name('campaigns.customers.delete')
            ->middleware('permission:delete_campaign_customers')
            ->defaults('component', 'campaigns.campaign-customer-delete');
        Route::get('/{customer}/contacts/{contact}/delete', CampaignPage::class)
            ->name('campaigns.customers.contacts.delete')
            ->middleware('permission:delete_campaign_customer_contacts')
            ->defaults('component', 'campaigns.campaign-customer-contact-delete');
        Route::get('/{customer}/documents/{document}/delete', CampaignPage::class)
            ->name('campaigns.customers.documents.delete')
            ->middleware('permission:delete_campaign_customer_documents')
            ->defaults('component', 'campaigns.campaign-customer-document-delete');
        Route::get('/{customer}', CampaignPage::class)
            ->name('campaigns.customers.show')
            ->middleware('permission:show_campaign_customer_documents')
            ->defaults('component', 'campaigns.campaign-customer-show');
    });

    Route::get('/agents', CampaignPage::class)
        ->name('campaigns.agents')
        ->middleware('permission:manage_campaign_agents')
        ->defaults('component', 'campaigns.campaign-agents');
    Route::get('/agents/add', CampaignPage::class)
        ->name('campaigns.add-agents')
        ->middleware('permission:add_campaign_agents')
        ->defaults('component', 'campaigns.campaign-add-agents');
    Route::get('/agents/{agent}/remove', CampaignPage::class)
        ->name('campaigns.agent.remove')
        ->middleware('permission:remove_campaign_agents')
        ->defaults('component', 'campaigns.campaign-agent-remove');
    Route::get('/agents/{agent}/observation', CampaignPage::class)
        ->name('campaigns.agent.observation')
        ->middleware('permission:observe_campaign_agents')
        ->defaults('component', 'campaigns.campaign-agent-observation');
    Route::get('/agents/{agent}/report', CampaignPage::class)
        ->name('campaigns.agent.report')
        ->middleware('permission:report_campaign_agents')
        ->defaults('component', 'campaigns.campaign-agent-report');

    // Campaign Reports Submodule
    Route::prefix('/reports')->group(function () {
        Route::get('/', CampaignPage::class)
            ->name('campaigns.reports')
            ->middleware('permission:manage_campaign_reports')
            ->defaults('component', 'campaigns.campaign-reports');
        Route::get('/create', CampaignPage::class)
            ->name('campaigns.reports.create')
            ->middleware('permission:create_campaign_reports')
            ->defaults('component', 'campaigns.campaign-report-create');
        Route::get('/{report}/edit', CampaignPage::class)
            ->name('campaigns.reports.edit')
            ->middleware('permission:edit_campaign_reports')
            ->defaults('component', 'campaigns.campaign-report-edit');
        Route::get('/{report}/delete', CampaignPage::class)
            ->name('campaigns.reports.delete')
            ->middleware('permission:delete_campaign_reports')
            ->defaults('component', 'campaigns.campaign-report-delete');
        Route::get('/{report}', CampaignPage::class)
            ->name('campaigns.reports.show')
            ->middleware('permission:show_campaign_reports')
            ->defaults('component', 'campaigns.campaign-report-show');
        });

    Route::get('/statistics', CampaignPage::class)
        ->name('campaigns.statistics')
        ->middleware('permission:show_campaign_statistics')
        ->defaults('component', 'campaigns.campaign-statistics');
});
