<?php

use Illuminate\Support\Facades\Route;
use App\Livewire\Accountant\AccountantPage;

// Accountant
Route::prefix('accountant')->middleware(['auth', 'permission:access_accountant_module'])->name('accountant.')->group(function () {
    Route::get('/', AccountantPage::class)
        ->defaults('component', 'accountant.accountant-index')
        ->name('index')
        ->middleware('permission:manage_accountant');

    // Agent Attendance
    Route::get('/attendance', AccountantPage::class)
        ->defaults('component', 'accountant.agent-attendance')
        ->name('agent.attendance')
        ->middleware('permission:show_agent_attendance');

    // Payment Management
    Route::get('/payment/management', AccountantPage::class)
        ->defaults('component', 'accountant.payment-management')
        ->name('payment.management')
        ->middleware('permission:manage_payment');

    // Payment History
    Route::get('/payment/history', AccountantPage::class)
        ->defaults('component', 'accountant.payment-history')
        ->name('payment.history')
        ->middleware('permission:show_payment_history');

    // Salary Calculation
    Route::get('/salary/calculation', AccountantPage::class)
        ->defaults('component', 'accountant.salary-calculation')
        ->name('salary.calculation')
        ->middleware('permission:manage_salary_calculation');

    // Financial Reports
    Route::get('/reports', AccountantPage::class)
        ->defaults('component', 'accountant.financial-reports')
        ->name('reports')
        ->middleware('permission:manage_financial_reports');

    // Invoices Management
    Route::get('/invoices', AccountantPage::class)
        ->defaults('component', 'accountant.invoices-index')
        ->name('invoices')
        ->middleware('permission:manage_invoice');
    Route::get('/invoices/create', AccountantPage::class)
        ->defaults('component', 'accountant.invoices-create')
        ->name('invoices.create')
        ->middleware('permission:create_invoice');
    Route::get('/invoices/{invoice}', AccountantPage::class)
        ->defaults('component', 'accountant.invoices-show')
        ->name('invoices.show')
        ->middleware('permission:show_invoice');
    Route::get('/invoices/{invoice}/edit', AccountantPage::class)
        ->defaults('component', 'accountant.invoices-edit')
        ->name('invoices.edit')
        ->middleware('permission:edit_invoice');
    Route::get('/invoices/{invoice}/delete', AccountantPage::class)
        ->defaults('component', 'accountant.invoices-delete')
        ->name('invoices.delete')
        ->middleware('permission:delete_invoice');
});